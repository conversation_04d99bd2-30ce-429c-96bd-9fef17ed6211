require('dotenv').config();
const getConnection = require('./platform/salesforce/common/getConnection');

async function testDuplicateContactPrevention() {
    try {
        console.log('🔍 Testing Duplicate Contact Prevention');
        console.log('=' .repeat(70));
        console.log('🎯 CHECKING FOR DUPLICATE CONTACTS:');
        console.log('   Similar to duplicate Leads issue');
        console.log('   Verifying Contact workaround doesn\'t create duplicates');
        console.log('=' .repeat(70));
        
        const conn = await getConnection();
        if (!conn || !conn.accessToken) {
            console.error("❌ Invalid Salesforce connection");
            return;
        }

        // Step 1: Check for existing duplicate Contacts in production
        console.log('\n🔍 Step 1: Scanning for existing duplicate Contacts...');
        
        // Find all Contacts created by Docebo API
        const doceboContacts = await conn.sobject("Contact")
            .find({ Created_by_Docebo_API__c: true })
            .execute();
        
        console.log(`📊 Total Docebo-created Contacts: ${doceboContacts.length}`);
        
        // Group by email to find duplicates
        const emailGroups = {};
        for (const contact of doceboContacts) {
            const email = contact.Email;
            if (!emailGroups[email]) {
                emailGroups[email] = [];
            }
            emailGroups[email].push(contact);
        }
        
        // Find emails with multiple Contacts
        const duplicateEmails = Object.keys(emailGroups).filter(email => emailGroups[email].length > 1);
        
        console.log(`📊 Emails with duplicate Contacts: ${duplicateEmails.length}`);
        
        if (duplicateEmails.length > 0) {
            console.log('\n🚨 DUPLICATE CONTACTS FOUND!');
            console.log('=' .repeat(50));
            
            for (let i = 0; i < Math.min(duplicateEmails.length, 5); i++) { // Show first 5
                const email = duplicateEmails[i];
                const contacts = emailGroups[email];
                
                console.log(`\n📧 Email: ${email}`);
                console.log(`   Duplicate Contacts: ${contacts.length}`);
                
                for (let j = 0; j < contacts.length; j++) {
                    const contact = contacts[j];
                    console.log(`   Contact ${j + 1}: ${contact.Id}`);
                    console.log(`     Name: ${contact.FirstName} ${contact.LastName}`);
                    console.log(`     Created: ${contact.CreatedDate}`);
                    console.log(`     Active Portal User: ${contact.Active_Portal_User__c}`);
                    console.log(`     Account: ${contact.AccountId}`);
                }
                
                // Calculate time gaps between Contact creations
                if (contacts.length > 1) {
                    const times = contacts.map(c => new Date(c.CreatedDate)).sort();
                    console.log(`\n   ⏱️ TIME GAPS:`);
                    for (let k = 1; k < times.length; k++) {
                        const gap = (times[k] - times[k-1]) / 1000; // seconds
                        console.log(`     Contact ${k} → Contact ${k+1}: ${gap} seconds`);
                    }
                }
            }
            
            if (duplicateEmails.length > 5) {
                console.log(`\n... and ${duplicateEmails.length - 5} more emails with duplicates`);
            }
        } else {
            console.log('\n✅ No duplicate Contacts found in production');
        }

        // Step 2: Test the Contact workaround logic directly
        console.log('\n🔍 Step 2: Testing Contact workaround duplicate prevention...');
        
        const testEmail = '<EMAIL>';
        
        // Clean up any existing test data
        const existingTestContacts = await conn.sobject("Contact")
            .find({ Email: testEmail })
            .execute();
        
        for (const contact of existingTestContacts) {
            await conn.sobject("Contact").delete(contact.Id);
            console.log(`   🗑️ Deleted existing test Contact: ${contact.Id}`);
        }

        // Step 3: Create a test Contact manually (simulating existing Contact)
        console.log('\n📋 Step 3: Creating initial test Contact...');
        
        // First create an Account
        const testAccountData = {
            Name: "Test Duplicate Contact Company",
            Type: "Customer"
        };
        
        const accountResult = await conn.sobject("Account").create(testAccountData);
        if (!accountResult.success) {
            console.error('❌ Failed to create test Account:', accountResult.errors);
            return;
        }
        
        console.log(`✅ Created test Account: ${accountResult.id}`);
        
        // Create initial Contact
        const initialContactData = {
            FirstName: "Initial",
            LastName: "Contact Test",
            Email: testEmail,
            Title: "Initial Manager",
            AccountId: accountResult.id,
            Active_Portal_User__c: false, // NOT active portal user initially
            Created_by_Docebo_API__c: false, // NOT created by Docebo initially
            LeadSource: "Manual"
        };
        
        const initialContactResult = await conn.sobject("Contact").create(initialContactData);
        if (!initialContactResult.success) {
            console.error('❌ Failed to create initial Contact:', initialContactResult.errors);
            return;
        }
        
        console.log(`✅ Created initial Contact: ${initialContactResult.id}`);
        console.log(`   Active Portal User: ${initialContactData.Active_Portal_User__c}`);
        console.log(`   Created by Docebo API: ${initialContactData.Created_by_Docebo_API__c}`);

        // Step 4: Test the Contact workaround function
        console.log('\n🧪 Step 4: Testing Contact workaround with existing Contact...');
        
        // Import the workaround function (we'll simulate it since it's not exported)
        const mockTmpUserInfo = {
            First_Name__c: "Updated",
            Last_Name__c: "Contact Test",
            Email__c: testEmail,
            Job_Title__c: "Updated Manager",
            Organization_Name__c: "Test Duplicate Contact Company",
            Organization_URL__c: "https://testduplicate.com"
        };
        
        const mockUserInfo = {
            user_data: {
                timezone: "America/New_York"
            }
        };
        
        // Simulate the Contact workaround logic
        console.log('🔍 Simulating Contact workaround search...');
        
        // This is the exact search from the workaround function
        const existingContacts = await conn.sobject("Contact")
            .find({
                Email: mockTmpUserInfo.Email__c,
                Active_Portal_User__c: true // Only active portal users
            })
            .limit(1)
            .execute();
        
        console.log(`📊 Contacts found with Active_Portal_User__c=true: ${existingContacts.length}`);
        
        if (existingContacts.length === 0) {
            console.log('⚠️ POTENTIAL DUPLICATE ISSUE DETECTED!');
            console.log('   The workaround will create a new Contact because:');
            console.log('   • Existing Contact has Active_Portal_User__c = false');
            console.log('   • Workaround only searches for Active_Portal_User__c = true');
            console.log('   • This could create duplicate Contacts for same email');
            
            // Test broader search
            console.log('\n🔍 Testing broader Contact search (any Active_Portal_User__c value)...');
            
            const allContactsWithEmail = await conn.sobject("Contact")
                .find({ Email: mockTmpUserInfo.Email__c })
                .execute();
            
            console.log(`📊 All Contacts with this email: ${allContactsWithEmail.length}`);
            
            if (allContactsWithEmail.length > 0) {
                console.log('✅ SOLUTION: Use broader search criteria');
                console.log('   Should find existing Contact regardless of Active_Portal_User__c value');
                console.log('   Then update the existing Contact instead of creating new one');
            }
        } else {
            console.log('✅ Existing Contact found, would update instead of creating duplicate');
        }

        // Step 5: Clean up test data
        console.log('\n🗑️ Cleaning up test data...');
        
        await conn.sobject("Contact").delete(initialContactResult.id);
        console.log(`   ✅ Deleted test Contact: ${initialContactResult.id}`);
        
        await conn.sobject("Account").delete(accountResult.id);
        console.log(`   ✅ Deleted test Account: ${accountResult.id}`);

        // Step 6: Analysis and recommendations
        console.log('\n📊 DUPLICATE CONTACT ANALYSIS:');
        console.log('=' .repeat(70));
        
        const hasDuplicatesInProd = duplicateEmails.length > 0;
        const hasLogicIssue = existingContacts.length === 0; // Would create duplicate
        
        console.log('🔍 FINDINGS:');
        console.log(`   Production duplicates: ${hasDuplicatesInProd ? '❌ YES' : '✅ NO'} (${duplicateEmails.length} emails affected)`);
        console.log(`   Logic issue detected: ${hasLogicIssue ? '❌ YES' : '✅ NO'}`);
        
        if (hasLogicIssue) {
            console.log('\n🚨 ISSUE IDENTIFIED:');
            console.log('   Contact workaround search criteria too restrictive');
            console.log('   Current: Email + Active_Portal_User__c = true');
            console.log('   Problem: Misses existing Contacts with Active_Portal_User__c = false');
            console.log('   Result: Creates duplicate Contacts for same email');
            
            console.log('\n🔧 RECOMMENDED FIX:');
            console.log('   1. Use broader search: Email only (ignore Active_Portal_User__c)');
            console.log('   2. Update existing Contact to set Active_Portal_User__c = true');
            console.log('   3. Add Created_by_Docebo_API__c = true to existing Contact');
            console.log('   4. This prevents duplicates and maintains data integrity');
        }
        
        if (hasDuplicatesInProd) {
            console.log('\n🗑️ CLEANUP NEEDED:');
            console.log(`   ${duplicateEmails.length} emails have duplicate Contacts`);
            console.log('   Recommend merging or deleting duplicate Contacts');
            console.log('   Keep the most recent or most complete Contact record');
        }
        
        console.log('\n💡 PREVENTION STRATEGY:');
        console.log('   • Always search by email only (not email + other criteria)');
        console.log('   • Update existing Contacts instead of creating new ones');
        console.log('   • Use consistent search criteria across all functions');
        console.log('   • Monitor for duplicate creation in production logs');

        return {
            success: true,
            duplicatesInProduction: duplicateEmails.length,
            logicIssueDetected: hasLogicIssue,
            affectedEmails: duplicateEmails
        };

    } catch (error) {
        console.error('💥 Error in duplicate Contact prevention test:', error);
        return {
            success: false,
            error: error.message
        };
    }
}

// Execute the test
console.log('🔄 Starting duplicate Contact prevention test...');
testDuplicateContactPrevention()
    .then((result) => {
        console.log('\n✅ Duplicate Contact prevention test completed');
        if (result.success) {
            if (result.duplicatesInProduction > 0) {
                console.log(`🚨 DUPLICATES FOUND: ${result.duplicatesInProduction} emails have duplicate Contacts`);
            }
            if (result.logicIssueDetected) {
                console.log('⚠️ LOGIC ISSUE: Contact workaround may create duplicates');
                console.log('🔧 Fix needed: Broaden search criteria in Contact workaround');
            }
            if (result.duplicatesInProduction === 0 && !result.logicIssueDetected) {
                console.log('🎉 No duplicate Contact issues detected!');
            }
        } else {
            console.log('❌ Test failed. Check the logs above.');
        }
        process.exit(0);
    })
    .catch(err => {
        console.error('💥 Test failed:', err);
        process.exit(1);
    });
