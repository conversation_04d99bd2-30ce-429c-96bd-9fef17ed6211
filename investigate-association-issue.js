require('dotenv').config();
const getConnection = require('./platform/salesforce/common/getConnection');

async function investigateAssociationIssue() {
    try {
        console.log('🔍 Investigating Lead Association Issue');
        console.log('=' .repeat(60));
        
        const conn = await getConnection();
        if (!conn || !conn.accessToken) {
            console.error("❌ Invalid Salesforce connection");
            return;
        }

        // Check the specific Docebo_Users__c record mentioned
        const doceboUserId = 'a5aO4000000bPp3IAE';
        
        console.log(`\n📋 Checking Docebo_Users__c record: ${doceboUserId}`);
        console.log('-'.repeat(50));
        
        const doceboUser = await conn.sobject("Docebo_Users__c")
            .findOne({ Id: doceboUserId });
        
        if (doceboUser) {
            console.log(`✅ Found Docebo_Users__c record:`);
            console.log(`   Name: ${doceboUser.First_Name__c} ${doceboUser.Last_Name__c}`);
            console.log(`   Email: ${doceboUser.Email__c}`);
            console.log(`   User ID: ${doceboUser.User_Unique_Id__c}`);
            console.log(`   Lead__c: ${doceboUser.Lead__c || 'NOT ASSOCIATED'}`);
            console.log(`   Contact__c: ${doceboUser.Contact__c || 'NOT ASSOCIATED'}`);
            console.log(`   Created Date: ${doceboUser.CreatedDate}`);
            
            // Look for Leads with the same email
            console.log(`\n🔍 Searching for Leads with email: ${doceboUser.Email__c}`);
            const leads = await conn.sobject("Lead")
                .find({ Email: doceboUser.Email__c })
                .execute();
            
            if (leads.length > 0) {
                console.log(`✅ Found ${leads.length} Lead(s) with matching email:`);
                leads.forEach((lead, index) => {
                    console.log(`\n   Lead ${index + 1}:`);
                    console.log(`   ID: ${lead.Id}`);
                    console.log(`   Name: ${lead.FirstName} ${lead.LastName}`);
                    console.log(`   Company: ${lead.Company}`);
                    console.log(`   Status: ${lead.Status}`);
                    console.log(`   Created by Docebo API: ${lead.Created_by_Docebo_API__c}`);
                    console.log(`   Owner ID: ${lead.OwnerId}`);
                    console.log(`   Created Date: ${lead.CreatedDate}`);
                });
                
                // Try to associate with the first Lead
                const leadToAssociate = leads[0];
                console.log(`\n🔗 Attempting to associate with Lead: ${leadToAssociate.Id}`);
                
                try {
                    const linkResult = await conn.sobject("Docebo_Users__c").update({
                        Id: doceboUserId,
                        Lead__c: leadToAssociate.Id
                    });
                    
                    if (linkResult.success) {
                        console.log(`✅ SUCCESS! Docebo_Users__c ${doceboUserId} linked to Lead ${leadToAssociate.Id}`);
                    } else {
                        console.log(`❌ Failed to link:`, linkResult.errors);
                    }
                } catch (linkError) {
                    console.log(`❌ Error linking:`, linkError.message);
                    console.log(`   Error Code: ${linkError.errorCode}`);
                    console.log(`   Fields: ${linkError.data?.fields || 'N/A'}`);
                    
                    if (linkError.errorCode === 'FIELD_FILTER_VALIDATION_EXCEPTION') {
                        console.log(`\n💡 FIELD_FILTER_VALIDATION_EXCEPTION Analysis:`);
                        console.log(`   This indicates a lookup filter on the Lead__c field.`);
                        console.log(`   The filter is preventing this Lead from being associated.`);
                        
                        // Let's check the Lead field details
                        console.log(`\n🔍 Analyzing Lead field values that might be causing the filter issue:`);
                        console.log(`   Lead Status: "${leadToAssociate.Status}"`);
                        console.log(`   Lead Owner: "${leadToAssociate.OwnerId}"`);
                        console.log(`   Created by Docebo API: ${leadToAssociate.Created_by_Docebo_API__c}`);
                        console.log(`   Lead Source: "${leadToAssociate.LeadSource}"`);
                        
                        // Check if there are other Leads that might work
                        if (leads.length > 1) {
                            console.log(`\n🔄 Trying other Leads...`);
                            for (let i = 1; i < leads.length; i++) {
                                try {
                                    const altLinkResult = await conn.sobject("Docebo_Users__c").update({
                                        Id: doceboUserId,
                                        Lead__c: leads[i].Id
                                    });
                                    
                                    if (altLinkResult.success) {
                                        console.log(`✅ SUCCESS with alternative Lead! ${leads[i].Id}`);
                                        break;
                                    }
                                } catch (altError) {
                                    console.log(`❌ Alternative Lead ${leads[i].Id} also failed: ${altError.message}`);
                                }
                            }
                        }
                    }
                }
            } else {
                console.log(`❌ No Leads found with email: ${doceboUser.Email__c}`);
            }
            
            // Also check for Contacts
            console.log(`\n🔍 Searching for Contacts with email: ${doceboUser.Email__c}`);
            const contacts = await conn.sobject("Contact")
                .find({ Email: doceboUser.Email__c })
                .execute();
            
            if (contacts.length > 0) {
                console.log(`✅ Found ${contacts.length} Contact(s) with matching email:`);
                contacts.forEach((contact, index) => {
                    console.log(`\n   Contact ${index + 1}:`);
                    console.log(`   ID: ${contact.Id}`);
                    console.log(`   Name: ${contact.FirstName} ${contact.LastName}`);
                    console.log(`   Account: ${contact.AccountId}`);
                    console.log(`   Owner ID: ${contact.OwnerId}`);
                });
                
                // Try to associate with the first Contact
                const contactToAssociate = contacts[0];
                console.log(`\n🔗 Attempting to associate with Contact: ${contactToAssociate.Id}`);
                
                try {
                    const contactLinkResult = await conn.sobject("Docebo_Users__c").update({
                        Id: doceboUserId,
                        Contact__c: contactToAssociate.Id
                    });
                    
                    if (contactLinkResult.success) {
                        console.log(`✅ SUCCESS! Docebo_Users__c ${doceboUserId} linked to Contact ${contactToAssociate.Id}`);
                    } else {
                        console.log(`❌ Failed to link to Contact:`, contactLinkResult.errors);
                    }
                } catch (contactLinkError) {
                    console.log(`❌ Error linking to Contact:`, contactLinkError.message);
                }
            } else {
                console.log(`❌ No Contacts found with email: ${doceboUser.Email__c}`);
            }
            
        } else {
            console.log(`❌ Docebo_Users__c record not found: ${doceboUserId}`);
        }

        // Let's also describe the Lead__c field to understand the lookup filter
        console.log(`\n🔍 Analyzing Lead__c field configuration...`);
        try {
            const doceboUserDescription = await conn.sobject("Docebo_Users__c").describe();
            const leadField = doceboUserDescription.fields.find(f => f.name === 'Lead__c');
            
            if (leadField) {
                console.log(`\n📋 Lead__c Field Details:`);
                console.log(`   Type: ${leadField.type}`);
                console.log(`   Required: ${leadField.nillable ? 'No' : 'Yes'}`);
                console.log(`   Updateable: ${leadField.updateable}`);
                console.log(`   Has Lookup Filter: ${leadField.filteredLookupInfo ? 'Yes' : 'No'}`);
                
                if (leadField.filteredLookupInfo) {
                    console.log(`\n🔍 LOOKUP FILTER DETECTED:`);
                    console.log(`   Controlling Field: ${leadField.filteredLookupInfo.controllingFields || 'N/A'}`);
                    console.log(`   Dependent: ${leadField.filteredLookupInfo.dependent}`);
                    console.log(`   Optional Filter: ${leadField.filteredLookupInfo.optionalFilter}`);
                    
                    console.log(`\n💡 SOLUTION RECOMMENDATIONS:`);
                    console.log(`   1. Contact Salesforce Admin to review the lookup filter`);
                    console.log(`   2. Check if Lead Status needs to be specific value`);
                    console.log(`   3. Check if Lead Owner needs to match certain criteria`);
                    console.log(`   4. Consider using Contact association instead`);
                }
            }
        } catch (describeError) {
            console.log(`❌ Error describing field: ${describeError.message}`);
        }

    } catch (error) {
        console.error('💥 Error in investigation:', error);
    }
}

// Execute the investigation
console.log('🔄 Starting association issue investigation...');
investigateAssociationIssue()
    .then(() => {
        console.log('\n✅ Investigation completed');
        process.exit(0);
    })
    .catch(err => {
        console.error('💥 Investigation failed:', err);
        process.exit(1);
    });
