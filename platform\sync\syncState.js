const fs = require('fs');
const path = require('path');

// Define the path for the sync state file
const SYNC_STATE_FILE = path.join(__dirname, '../../data/syncState.json');

// Ensure the directory exists
function ensureDirectoryExists() {
  const dir = path.dirname(SYNC_STATE_FILE);
  if (!fs.existsSync(dir)) {
    fs.mkdirSync(dir, { recursive: true });
  }
}

// Initialize the sync state file if it doesn't exist
function initSyncState() {
  ensureDirectoryExists();
  
  if (!fs.existsSync(SYNC_STATE_FILE)) {
    const initialState = {
      lastSyncTimestamp: new Date().toISOString(),
      lastSyncedIds: {
        users: 0,
        courses: 0,
        sessions: 0,
        learningPlans: 0,
        learningPlanEnrollments: 0,
        instructors: 0
      }
    };
    
    fs.writeFileSync(SYNC_STATE_FILE, JSON.stringify(initialState, null, 2));
    return initialState;
  }
  
  return JSON.parse(fs.readFileSync(SYNC_STATE_FILE, 'utf8'));
}

// Get the current sync state
function getSyncState() {
  try {
    if (!fs.existsSync(SYNC_STATE_FILE)) {
      return initSyncState();
    }
    
    return JSON.parse(fs.readFileSync(SYNC_STATE_FILE, 'utf8'));
  } catch (err) {
    console.error('Error reading sync state file:', err);
    return initSyncState();
  }
}

// Update the sync state
function updateSyncState(entityType, lastId) {
  try {
    const currentState = getSyncState();
    
    // Update the specific entity's last synced ID
    currentState.lastSyncedIds[entityType] = lastId;
    currentState.lastSyncTimestamp = new Date().toISOString();
    
    fs.writeFileSync(SYNC_STATE_FILE, JSON.stringify(currentState, null, 2));
    console.log(`Updated sync state for ${entityType} to ID: ${lastId}`);
    
    return currentState;
  } catch (err) {
    console.error('Error updating sync state:', err);
    return null;
  }
}

// Get the last synced ID for a specific entity type
function getLastSyncedId(entityType) {
  const state = getSyncState();
  return state.lastSyncedIds[entityType] || 0;
}

// Get the last sync timestamp
function getLastSyncTimestamp() {
  const state = getSyncState();
  return state.lastSyncTimestamp;
}

module.exports = {
  initSyncState,
  getSyncState,
  updateSyncState,
  getLastSyncedId,
  getLastSyncTimestamp
};