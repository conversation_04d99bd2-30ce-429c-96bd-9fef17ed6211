require('dotenv').config();
const { createIltSessionEnrollment } = require('./platform/salesforce/services');
const getConnection = require('./platform/salesforce/common/getConnection');

async function testIltFix() {
    try {
        console.log('🔧 Testing ILT Session Enrollment MALFORMED_ID Fix');
        console.log('=' .repeat(60));
        
        const conn = await getConnection();
        if (!conn || !conn.accessToken) {
            console.error("❌ Invalid Salesforce connection");
            return;
        }

        // Get sample data
        const sampleUser = await conn.sobject("Docebo_Users__c")
            .findOne({}, ['Id', 'User_Unique_Id__c', 'First_Name__c', 'Last_Name__c']);
        
        const sampleCourse = await conn.sobject("Docebo_Course__c")
            .findOne({}, ['Id', 'Course_External_Id__c', 'Course_Name__c']);
        
        if (!sampleUser || !sampleCourse) {
            console.log('⚠️ No sample data found');
            return;
        }
        
        console.log(`✅ Testing with User: ${sampleUser.First_Name__c} ${sampleUser.Last_Name__c}`);
        console.log(`✅ Testing with Course: ${sampleCourse.Course_Name__c}`);

        // Test enrollment data
        const testData = {
            course_id: sampleCourse.Course_External_Id__c,
            user_id: sampleUser.User_Unique_Id__c,
            completion: 75,
            score: 88,
            status: "enrolled",
            enrollment_date: new Date().toISOString(),
            total_time: 3600
        };

        console.log('\n🧪 Creating ILT session enrollment...');
        const result = await createIltSessionEnrollment(testData);
        
        if (result) {
            console.log('✅ ILT session enrollment created successfully!');
            
            // Verify the enrollment
            const enrollmentId = `${testData.course_id}-${testData.user_id}`.substring(0, 16);
            const enrollment = await conn.sobject("Docebo_CourseEnrollment__c")
                .findOne({ Enrollment_ID__c: enrollmentId });
            
            if (enrollment) {
                console.log('\n📊 VERIFICATION:');
                console.log(`   Enrollment ID: ${enrollment.Id}`);
                console.log(`   Course (SF): ${enrollment.Course__c}`);
                console.log(`   User (SF): ${enrollment.Docebo_User__c}`);
                console.log(`   Status: ${enrollment.Status__c}`);
                console.log(`   Time: ${enrollment.Time_in_course__c} seconds`);
                
                // Check if IDs are correct Salesforce IDs
                const courseCorrect = enrollment.Course__c === sampleCourse.Id;
                const userCorrect = enrollment.Docebo_User__c === sampleUser.Id;
                
                console.log('\n🎯 FIX VERIFICATION:');
                console.log(`   Course ID: ${courseCorrect ? '✅' : '❌'} ${courseCorrect ? 'CORRECT' : 'INCORRECT'}`);
                console.log(`   User ID: ${userCorrect ? '✅' : '❌'} ${userCorrect ? 'CORRECT' : 'INCORRECT'}`);
                
                if (courseCorrect && userCorrect) {
                    console.log('\n🎉 MALFORMED_ID ERROR FIXED!');
                    console.log('   ✅ No more "id value of incorrect type" errors');
                    console.log('   ✅ Proper Salesforce ID lookup working');
                } else {
                    console.log('\n❌ Fix verification failed');
                }
                
                // Clean up
                await conn.sobject("Docebo_CourseEnrollment__c").delete(enrollment.Id);
                console.log('🗑️ Test enrollment cleaned up');
                
            } else {
                console.log('❌ Enrollment not found after creation');
            }
            
        } else {
            console.log('❌ ILT session enrollment creation failed');
        }

        console.log('\n📊 SUMMARY:');
        console.log('🔧 FIXES APPLIED:');
        console.log('   ✅ Fixed Enrollment_ID__c format (16 char limit)');
        console.log('   ✅ Fixed Status__c to use text field');
        console.log('   ✅ Added Time_in_course__c field mapping');
        console.log('   ✅ Proper Salesforce ID lookup for Course__c and Docebo_User__c');
        
        console.log('\n🎯 ERROR RESOLUTION:');
        console.log('   ❌ BEFORE: "Docebo User: id value of incorrect type: 18872"');
        console.log('   ✅ AFTER: Proper Salesforce ID lookup and mapping');

        return { success: true };

    } catch (error) {
        console.error('💥 Error in ILT fix test:', error);
        return { success: false, error: error.message };
    }
}

// Execute the test
testIltFix()
    .then((result) => {
        if (result.success) {
            console.log('\n🎉 ILT MALFORMED_ID error has been fixed!');
        } else {
            console.log('\n❌ Fix test failed');
        }
        process.exit(0);
    })
    .catch(err => {
        console.error('💥 Test failed:', err);
        process.exit(1);
    });
