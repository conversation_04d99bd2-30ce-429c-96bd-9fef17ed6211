require('dotenv').config();
const getConnection = require('./platform/salesforce/common/getConnection');

async function checkFieldPermissions() {
    try {
        console.log('🔍 CHECKING FIELD PERMISSIONS AND RECORD...');
        
        const conn = await getConnection();
        console.log('✅ Connected to Salesforce');

        // First, let's describe the object to see what fields exist
        console.log('\n📋 CHECKING OBJECT FIELDS...');
        const describe = await conn.sobject('Docebo_Course__c').describe();
        
        const missingFields = [
            'Course_Category_Code__c',
            'Course_Start_Date__c', 
            'Course_End_Date__c',
            'Session_Time_min__c',
            'Enrollment_Date__c'
        ];
        
        console.log('Field existence check:');
        missingFields.forEach(fieldName => {
            const field = describe.fields.find(f => f.name === fieldName);
            if (field) {
                console.log(`✅ ${fieldName}: EXISTS (Type: ${field.type}, Updateable: ${field.updateable})`);
            } else {
                console.log(`❌ ${fieldName}: NOT FOUND`);
            }
        });

        // Now let's try to query the specific record with a simple approach
        console.log('\n🔍 QUERYING SPECIFIC RECORD...');
        
        try {
            const query = `SELECT Id, Course_Name__c, Course_External_Id__c, 
                          Course_Category_Code__c, Course_Start_Date__c, Course_End_Date__c, 
                          Session_Time_min__c, Enrollment_Date__c, LastModifiedDate 
                          FROM Docebo_Course__c 
                          WHERE Id = 'a6VO40000029QLVMA2'`;
                          
            console.log('Query:', query);
            
            const result = await conn.query(query);
            
            if (result.records && result.records.length > 0) {
                const course = result.records[0];
                console.log('\n📋 RECORD FOUND:');
                console.log('Name:', course.Course_Name__c);
                console.log('External ID:', course.Course_External_Id__c);
                console.log('Last Modified:', course.LastModifiedDate);
                
                console.log('\n🎯 FIELD VALUES:');
                missingFields.forEach(fieldName => {
                    const value = course[fieldName];
                    const status = value ? '✅ HAS VALUE' : '❌ NULL/EMPTY';
                    console.log(`${fieldName}: "${value}" ${status}`);
                });
                
                // If fields are empty, let's try to update this specific record
                const emptyFields = missingFields.filter(field => !course[field]);
                if (emptyFields.length > 0) {
                    console.log(`\n⚠️ Found ${emptyFields.length} empty fields. Attempting manual update...`);
                    
                    // Get the external ID to fetch from Docebo
                    if (course.Course_External_Id__c) {
                        console.log(`\n🔄 Fetching course data from Docebo (ID: ${course.Course_External_Id__c})...`);
                        
                        const doceboService = require('./platform/docebo/services');
                        const courseResponse = await doceboService.getCourseInfo(course.Course_External_Id__c);
                        
                        if (courseResponse && courseResponse.status === 200 && courseResponse.data) {
                            console.log('✅ Retrieved course data from Docebo');
                            
                            const { mapDoceboCourseToSalesforce } = require('./platform/salesforce/courses/mapCourseData');
                            const mappedData = mapDoceboCourseToSalesforce(courseResponse.data, {});
                            
                            console.log('\n📊 MAPPED DATA:');
                            missingFields.forEach(field => {
                                console.log(`${field}: "${mappedData[field] || 'NULL'}"`);
                            });
                            
                            // Prepare update object
                            const updateData = { Id: course.Id };
                            let hasUpdates = false;
                            
                            emptyFields.forEach(field => {
                                if (mappedData[field]) {
                                    updateData[field] = mappedData[field];
                                    hasUpdates = true;
                                }
                            });
                            
                            if (hasUpdates) {
                                console.log('\n🔧 UPDATING RECORD...');
                                console.log('Update data:', JSON.stringify(updateData, null, 2));
                                
                                const updateResult = await conn.sobject('Docebo_Course__c').update(updateData);
                                
                                if (updateResult.success) {
                                    console.log('✅ UPDATE SUCCESSFUL!');
                                    
                                    // Verify the update
                                    const verifyResult = await conn.query(query);
                                    if (verifyResult.records && verifyResult.records.length > 0) {
                                        const updatedCourse = verifyResult.records[0];
                                        console.log('\n✅ VERIFICATION - UPDATED VALUES:');
                                        missingFields.forEach(fieldName => {
                                            const value = updatedCourse[fieldName];
                                            const status = value ? '✅ POPULATED' : '❌ STILL EMPTY';
                                            console.log(`${fieldName}: "${value}" ${status}`);
                                        });
                                    }
                                } else {
                                    console.log('❌ UPDATE FAILED:', JSON.stringify(updateResult.errors, null, 2));
                                }
                            } else {
                                console.log('ℹ️ No mapped data available for empty fields');
                            }
                        } else {
                            console.log('❌ Could not retrieve course data from Docebo');
                        }
                    } else {
                        console.log('❌ No external ID available to fetch from Docebo');
                    }
                } else {
                    console.log('\n✅ ALL FIELDS ARE ALREADY POPULATED!');
                }
                
            } else {
                console.log('❌ No record found with that ID');
            }
            
        } catch (queryError) {
            console.error('❌ Query error:', queryError.message);
        }

    } catch (error) {
        console.error('❌ Error:', error.message);
    }
}

checkFieldPermissions();
