require('dotenv').config();
const getConnection = require('./platform/salesforce/common/getConnection');

async function checkInstructorObject() {
  try {
    console.log('Checking Instructor__c object...');
    
    const conn = await getConnection();
    if (!conn || !conn.accessToken) {
      console.error("Invalid Salesforce connection");
      return;
    }

    // Try to describe the Instructor__c object
    try {
      const description = await conn.sobject("Instructor__c").describe();
      
      console.log(`\n✅ Instructor__c object exists!`);
      console.log(`Label: ${description.label}`);
      console.log(`API Name: ${description.name}`);
      
      console.log('\n=== Available fields ===');
      description.fields.forEach(field => {
        if (!field.name.startsWith('System') && field.createable) {
          console.log(`- ${field.name} (${field.type}) - ${field.label} ${field.required ? '[Required]' : ''}`);
        }
      });
      
      // Check if there are any existing instructor records
      console.log('\n=== Existing Instructor records ===');
      const existingInstructors = await conn.sobject("Instructor__c")
        .find({}, 'Id, Name')
        .limit(10)
        .execute();
        
      if (existingInstructors.length > 0) {
        console.log(`Found ${existingInstructors.length} existing instructor records:`);
        existingInstructors.forEach(instructor => {
          console.log(`- ${instructor.Id}: ${instructor.Name}`);
        });
      } else {
        console.log('No existing instructor records found');
      }
      
    } catch (error) {
      if (error.errorCode === 'NOT_FOUND') {
        console.log('\n❌ Instructor__c object does not exist');
        console.log('You need to create this custom object in Salesforce first');
      } else {
        console.error('Error describing Instructor__c object:', error);
      }
    }

  } catch (error) {
    console.error('Error checking instructor object:', error);
  }
}

checkInstructorObject()
  .then(() => console.log('\nDone'))
  .catch(err => console.error('Script failed:', err));
