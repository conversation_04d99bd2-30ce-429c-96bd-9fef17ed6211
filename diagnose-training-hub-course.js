require('dotenv').config();
const getConnection = require('./platform/salesforce/common/getConnection');
const getApiData = require("./common/docebo/fetcher");
const APP_BASE = process.env.DOCEBO_API_BASE_URL;

async function diagnoseTrainingHubCourse() {
    try {
        console.log('🔍 DIAGNOSING "Welcome to The Training Hub!" COURSE (ID: 43)');
        console.log('=' .repeat(80));
        
        const conn = await getConnection();
        if (!conn || !conn.accessToken) {
            throw new Error('Failed to connect to Salesforce');
        }

        console.log('✅ Connected to Salesforce successfully');

        // Step 1: Verify course exists in both systems
        console.log('\n📚 STEP 1: Verifying Course in Both Systems...');
        console.log('-'.repeat(60));
        
        // Check Salesforce
        const sfCourse = await conn.sobject("Docebo_Course__c")
            .findOne({ Course_External_Id__c: 43 });
            
        if (!sfCourse) {
            console.log('❌ Course 43 not found in Salesforce');
            
            // Search for similar course names
            const similarCourses = await conn.sobject("Docebo_Course__c")
                .find({ Course_Name__c: { $like: '%Training Hub%' } })
                .limit(10)
                .execute();
                
            if (similarCourses.length > 0) {
                console.log('\n🔍 Found similar courses in Salesforce:');
                similarCourses.forEach(course => {
                    console.log(`   - ID: ${course.Id}, External ID: ${course.Course_External_Id__c}, Name: ${course.Course_Name__c}`);
                });
            }
            
            return { success: false, error: 'Course not found in Salesforce' };
        }
        
        console.log('✅ Salesforce Course Found:');
        console.log(`   Course ID: ${sfCourse.Id}`);
        console.log(`   Course Name: ${sfCourse.Course_Name__c}`);
        console.log(`   External ID: ${sfCourse.Course_External_Id__c}`);

        // Check Docebo
        let doceboCourse = null;
        try {
            const doceboResponse = await getApiData('GET', `${APP_BASE}/course/v1/courses/43`, null);
            if (doceboResponse && doceboResponse.status === 200) {
                doceboCourse = doceboResponse.data;
                console.log('✅ Docebo Course Found:');
                console.log(`   Course ID: ${doceboCourse.id}`);
                console.log(`   Course Name: ${doceboCourse.name}`);
                console.log(`   Course Status: ${doceboCourse.status}`);
                console.log(`   Course Type: ${doceboCourse.course_type}`);
            }
        } catch (doceboError) {
            console.log('❌ Error fetching course from Docebo:', doceboError.message);
        }

        // Step 2: Count enrollments in Docebo API
        console.log('\n🔍 STEP 2: Counting Enrollments in Docebo API...');
        console.log('-'.repeat(60));
        
        let doceboEnrollmentCount = 0;
        let sampleEnrollments = [];
        
        try {
            let page = 1;
            let hasMoreData = true;
            
            while (hasMoreData && page <= 30) { // Limit to 30 pages for counting
                console.log(`   📄 Checking Docebo API page ${page}...`);
                
                const enrollmentResponse = await getApiData(
                    'GET', 
                    `${APP_BASE}/learn/v1/enrollments?course_id=43&page=${page}&page_size=200`, 
                    null
                );
                
                if (enrollmentResponse && enrollmentResponse.status === 200) {
                    const items = enrollmentResponse.data?.items || [];
                    doceboEnrollmentCount += items.length;
                    
                    // Collect sample enrollments from first page
                    if (page === 1) {
                        sampleEnrollments = items.slice(0, 5);
                    }
                    
                    console.log(`      Found ${items.length} enrollments (Total: ${doceboEnrollmentCount})`);
                    
                    hasMoreData = enrollmentResponse.data?.has_more_data || false;
                    if (items.length === 0) hasMoreData = false;
                    page++;
                } else {
                    console.log(`      No data on page ${page}`);
                    hasMoreData = false;
                }
            }
            
            console.log(`✅ Total Docebo Enrollments Found: ${doceboEnrollmentCount}`);
            
            if (sampleEnrollments.length > 0) {
                console.log('\n📋 Sample Docebo Enrollments:');
                sampleEnrollments.forEach((enrollment, index) => {
                    console.log(`   ${index + 1}. User ${enrollment.user_id}: Status=${enrollment.status}, Progress=${enrollment.completion_percentage || 0}%`);
                });
            }
            
        } catch (enrollmentError) {
            console.log('❌ Error fetching enrollments from Docebo:', enrollmentError.message);
        }

        // Step 3: Count Salesforce enrollments
        console.log('\n📊 STEP 3: Counting Salesforce Course Enrollments...');
        console.log('-'.repeat(60));
        
        const sfEnrollments = await conn.sobject("Docebo_CourseEnrollment__c")
            .find({ Course__c: sfCourse.Id })
            .execute();
            
        console.log(`Salesforce Enrollments Found: ${sfEnrollments.length}`);
        
        if (sfEnrollments.length > 0) {
            console.log('\n📋 Sample Salesforce Enrollments:');
            sfEnrollments.slice(0, 5).forEach((enrollment, index) => {
                console.log(`   ${index + 1}. ID: ${enrollment.Id}, Status: ${enrollment.Status__c}, Enrollment ID: ${enrollment.Enrollment_ID__c}`);
            });
        }

        // Step 4: Check for enrollments with course 43 pattern
        console.log('\n🔍 STEP 4: Checking for Course 43 Enrollment Patterns...');
        console.log('-'.repeat(60));
        
        const enrollmentsWithCourse43 = await conn.sobject("Docebo_CourseEnrollment__c")
            .find({ Enrollment_ID__c: { $like: '43-%' } })
            .limit(50)
            .execute();
            
        console.log(`Enrollments with "43-" pattern: ${enrollmentsWithCourse43.length}`);
        
        if (enrollmentsWithCourse43.length > 0) {
            console.log('\n📋 Enrollments with Course 43 Pattern:');
            enrollmentsWithCourse43.slice(0, 10).forEach((enrollment, index) => {
                console.log(`   ${index + 1}. Enrollment ID: ${enrollment.Enrollment_ID__c}, Course Link: ${enrollment.Course__c ? 'YES' : 'NO'}, User: ${enrollment.Docebo_User__c ? 'YES' : 'NO'}`);
            });
        }

        // Step 5: Check webhook logs for course 43
        console.log('\n📝 STEP 5: Checking Recent Webhook Activity...');
        console.log('-'.repeat(60));
        
        const fs = require('fs');
        const path = require('path');
        
        try {
            const logPath = path.join(__dirname, 'logs', '2025-06-16.log');
            if (fs.existsSync(logPath)) {
                const logContent = fs.readFileSync(logPath, 'utf8');
                const course43Lines = logContent.split('\n').filter(line => 
                    line.includes('course_id":43') || 
                    line.includes('course_id": 43') ||
                    line.includes('"43"') ||
                    line.includes('Training Hub')
                );
                
                console.log(`Found ${course43Lines.length} log entries mentioning course 43`);
                
                if (course43Lines.length > 0) {
                    console.log('\n📋 Recent Course 43 Log Entries:');
                    course43Lines.slice(-5).forEach((line, index) => {
                        const timestamp = line.substring(0, 19);
                        const message = line.substring(20);
                        console.log(`   ${index + 1}. ${timestamp}: ${message.substring(0, 100)}...`);
                    });
                }
            } else {
                console.log('⚠️ Log file not found');
            }
        } catch (logError) {
            console.log('❌ Error reading log file:', logError.message);
        }

        // Step 6: Analyze the gap
        console.log('\n🔧 STEP 6: Analyzing Enrollment Gap...');
        console.log('-'.repeat(60));
        
        const enrollmentGap = doceboEnrollmentCount - sfEnrollments.length;
        const gapPercentage = doceboEnrollmentCount > 0 ? ((enrollmentGap / doceboEnrollmentCount) * 100).toFixed(1) : 0;
        
        console.log(`📊 Enrollment Gap Analysis:`);
        console.log(`   Docebo Enrollments: ${doceboEnrollmentCount}`);
        console.log(`   Salesforce Enrollments: ${sfEnrollments.length}`);
        console.log(`   Missing Enrollments: ${enrollmentGap}`);
        console.log(`   Gap Percentage: ${gapPercentage}%`);

        const issues = [];
        const recommendations = [];
        
        if (enrollmentGap > 1000) {
            issues.push(`Large enrollment gap: ${enrollmentGap} missing enrollments`);
            recommendations.push('Run bulk enrollment sync for course 43');
            recommendations.push('Check webhook processing for course enrollment events');
        }
        
        if (enrollmentsWithCourse43.length > sfEnrollments.length) {
            issues.push('Enrollment records exist but not properly linked to course');
            recommendations.push('Check course linking logic in enrollment creation');
        }
        
        if (doceboEnrollmentCount > 5000 && sfEnrollments.length < 500) {
            issues.push('Massive sync failure - webhook processing likely broken');
            recommendations.push('Investigate webhook configuration for course 43');
            recommendations.push('Check if course enrollment webhooks are enabled');
        }

        // Step 7: Test sample enrollment sync
        console.log('\n🧪 STEP 7: Testing Sample Enrollment Sync...');
        console.log('-'.repeat(60));
        
        if (sampleEnrollments.length > 0 && doceboEnrollmentCount > sfEnrollments.length) {
            const testEnrollment = sampleEnrollments[0];
            console.log(`Testing sync for User ${testEnrollment.user_id}`);
            
            // Check if this user exists in Salesforce
            const sfUser = await conn.sobject("Docebo_Users__c")
                .findOne({ User_Unique_Id__c: testEnrollment.user_id });
                
            if (sfUser) {
                console.log(`✅ User found in Salesforce: ${sfUser.Id}`);
                
                // Check if enrollment already exists
                const existingEnrollment = await conn.sobject("Docebo_CourseEnrollment__c")
                    .findOne({ Enrollment_ID__c: `43-${testEnrollment.user_id}` });
                    
                if (existingEnrollment) {
                    console.log(`✅ Enrollment already exists: ${existingEnrollment.Id}`);
                } else {
                    console.log(`❌ Enrollment missing for user ${testEnrollment.user_id}`);
                    recommendations.push('Missing enrollments need to be created via bulk sync');
                }
            } else {
                console.log(`❌ User ${testEnrollment.user_id} not found in Salesforce`);
                issues.push('User sync issues may be preventing enrollment creation');
            }
        }

        return {
            success: true,
            doceboEnrollments: doceboEnrollmentCount,
            salesforceEnrollments: sfEnrollments.length,
            enrollmentsWithPattern: enrollmentsWithCourse43.length,
            enrollmentGap: enrollmentGap,
            gapPercentage: parseFloat(gapPercentage),
            issues: issues,
            recommendations: recommendations
        };

    } catch (error) {
        console.error('💥 Error in diagnosis:', error);
        return {
            success: false,
            error: error.message
        };
    }
}

// Execute the diagnosis
console.log('🔄 Starting Training Hub Course Diagnosis...');
diagnoseTrainingHubCourse()
    .then((result) => {
        console.log('\n📋 DIAGNOSIS SUMMARY:');
        console.log('=' .repeat(60));
        
        if (result.success) {
            console.log(`📊 Docebo Enrollments: ${result.doceboEnrollments.toLocaleString()}`);
            console.log(`📊 Salesforce Enrollments: ${result.salesforceEnrollments.toLocaleString()}`);
            console.log(`📊 Enrollments with Course 43 Pattern: ${result.enrollmentsWithPattern}`);
            console.log(`📊 Missing Enrollments: ${result.enrollmentGap.toLocaleString()}`);
            console.log(`📊 Gap Percentage: ${result.gapPercentage}%`);
            
            if (result.issues.length > 0) {
                console.log('\n🚨 ISSUES IDENTIFIED:');
                result.issues.forEach((issue, index) => {
                    console.log(`   ${index + 1}. ${issue}`);
                });
            }
            
            if (result.recommendations.length > 0) {
                console.log('\n💡 RECOMMENDATIONS:');
                result.recommendations.forEach((rec, index) => {
                    console.log(`   ${index + 1}. ${rec}`);
                });
            }
            
            if (result.enrollmentGap > 1000) {
                console.log('\n🚨 CRITICAL: Large enrollment sync required!');
                console.log('   Consider running bulk sync during off-peak hours');
            }
            
        } else {
            console.log(`❌ Diagnosis failed: ${result.error}`);
        }
        
        console.log('\n✅ Diagnosis completed');
        process.exit(0);
    })
    .catch(err => {
        console.error('💥 Diagnosis failed:', err);
        process.exit(1);
    });
