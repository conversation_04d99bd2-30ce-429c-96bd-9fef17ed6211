const axios = require('axios');
require('dotenv').config();

const TOKEN_URL = process.env.DOCEBO_API_SUB_DOMAIN
const CLIENT_NAME = process.env.DOCEBO_API_CLIENT_NAME
const CLIENT_SECRET = process.env.DOCEBO_API_CLIENT_SECRET
const GRANT_TYPE = process.env.DOCEBO_API_GRANT_TYPE
const API_SCOPE = process.env.DOCEBO_API_SCOPE
const USER_NAME = process.env.DOCEBO_API_USER_NAME
const PASSWORD = process.env.DOCEBO_API_PASSWORD

let accessToken = "";

module.exports = async function getAccessToken() {
    try {
        const response = await axios.post(
            TOKEN_URL,
            new URLSearchParams({
                grant_type: GRANT_TYPE,
                client_id: CLIENT_NAME,
                client_secret: CLIENT_SECRET,
                scope: API_SCOPE,
                username: USER_NAME,
                password: PASSWORD
            }),
            {
                headers: { 'Content-Type': 'application/x-www-form-urlencoded' }
            }
        );
        accessToken = response.data.access_token;
        if (accessToken != "") {
            return {
                status: 200,
                accessToken: accessToken
            }
        } else {
            return {
                status: 203,
                accessToken: "No token"
            }
        }
    } catch (error) {
        return {
            status: 505,
            message: error.response?.data || error.message
        }
    }
}
