require('dotenv').config();
const getConnection = require('./platform/salesforce/common/getConnection');

async function fixAllEnrollmentAssociations() {
    try {
        console.log('🔧 Fixing All Course Enrollment Associations');
        console.log('=' .repeat(60));
        
        const conn = await getConnection();
        if (!conn || !conn.accessToken) {
            console.error("❌ Invalid Salesforce connection");
            return;
        }

        // Step 1: Find all enrollments missing course associations
        console.log('\n📊 Finding enrollments missing course associations...');
        console.log('-'.repeat(50));
        
        const enrollmentsMissingCourse = await conn.sobject("Docebo_CourseEnrollment__c")
            .find({ Course__c: null })
            .execute();
        
        console.log(`📋 Found ${enrollmentsMissingCourse.length} enrollments missing course associations`);
        
        if (enrollmentsMissingCourse.length === 0) {
            console.log('✅ All enrollments already have course associations!');
            return;
        }
        
        // Step 2: Process each enrollment
        let fixedCount = 0;
        let errorCount = 0;
        let notFoundCount = 0;
        
        console.log('\n🔧 Processing enrollments...');
        console.log('-'.repeat(50));
        
        for (let i = 0; i < enrollmentsMissingCourse.length; i++) {
            const enrollment = enrollmentsMissingCourse[i];
            console.log(`\n📋 Processing ${i + 1}/${enrollmentsMissingCourse.length}: ${enrollment.Id}`);
            console.log(`   Enrollment_ID__c: ${enrollment.Enrollment_ID__c}`);
            
            try {
                // Parse the Enrollment_ID
                if (!enrollment.Enrollment_ID__c) {
                    console.log(`   ❌ No Enrollment_ID__c found`);
                    errorCount++;
                    continue;
                }
                
                const parts = enrollment.Enrollment_ID__c.split('-');
                if (parts.length !== 3 || parts[0] !== 'UE') {
                    console.log(`   ❌ Invalid Enrollment_ID format: ${enrollment.Enrollment_ID__c}`);
                    errorCount++;
                    continue;
                }
                
                const courseId = parts[1];
                const userId = parts[2];
                
                // Find the course
                let foundCourse = null;
                try {
                    foundCourse = await conn.sobject("Docebo_Course__c")
                        .findOne({ Course_External_Id__c: parseInt(courseId) });
                } catch (courseError) {
                    // Try as string if int fails
                    try {
                        foundCourse = await conn.sobject("Docebo_Course__c")
                            .findOne({ Course_External_Id__c: courseId });
                    } catch (courseError2) {
                        // Try Course_Unique_Id__c
                        try {
                            foundCourse = await conn.sobject("Docebo_Course__c")
                                .findOne({ Course_Unique_Id__c: courseId });
                        } catch (courseError3) {
                            console.log(`   ❌ Course not found with ID: ${courseId}`);
                        }
                    }
                }
                
                if (!foundCourse) {
                    console.log(`   ❌ Course not found: ${courseId}`);
                    notFoundCount++;
                    continue;
                }
                
                // Update the enrollment with the course association
                const updateResult = await conn.sobject("Docebo_CourseEnrollment__c").update({
                    Id: enrollment.Id,
                    Course__c: foundCourse.Id
                });
                
                if (updateResult.success) {
                    console.log(`   ✅ Fixed! Associated with course: ${foundCourse.Course_Name__c}`);
                    fixedCount++;
                } else {
                    console.log(`   ❌ Update failed:`, updateResult.errors);
                    errorCount++;
                }
                
            } catch (error) {
                console.log(`   ❌ Error processing enrollment: ${error.message}`);
                errorCount++;
            }
            
            // Add a small delay to avoid rate limits
            if (i % 10 === 9) {
                console.log(`   ⏳ Processed ${i + 1} enrollments, pausing briefly...`);
                await new Promise(resolve => setTimeout(resolve, 1000));
            }
        }
        
        // Step 3: Summary
        console.log('\n📊 SUMMARY');
        console.log('=' .repeat(50));
        console.log(`✅ Successfully Fixed: ${fixedCount}`);
        console.log(`❌ Errors: ${errorCount}`);
        console.log(`🔍 Course Not Found: ${notFoundCount}`);
        console.log(`📋 Total Processed: ${enrollmentsMissingCourse.length}`);
        
        const successRate = Math.round((fixedCount / enrollmentsMissingCourse.length) * 100);
        console.log(`📈 Success Rate: ${successRate}%`);
        
        // Step 4: Verify the fixes
        console.log('\n🔍 Verifying fixes...');
        console.log('-'.repeat(50));
        
        const remainingMissing = await conn.sobject("Docebo_CourseEnrollment__c")
            .find({ Course__c: null })
            .execute();
        
        console.log(`📋 Enrollments still missing course associations: ${remainingMissing.length}`);
        
        if (remainingMissing.length === 0) {
            console.log('🎉 SUCCESS! All enrollments now have course associations!');
        } else {
            console.log(`⚠️ ${remainingMissing.length} enrollments still need attention`);
            
            // Show a few examples of what's still missing
            console.log('\n📋 Examples of remaining issues:');
            for (let i = 0; i < Math.min(5, remainingMissing.length); i++) {
                const remaining = remainingMissing[i];
                console.log(`   ${i + 1}. ${remaining.Id} - Enrollment_ID__c: ${remaining.Enrollment_ID__c}`);
            }
        }
        
        // Step 5: Final statistics
        console.log('\n📊 FINAL STATISTICS');
        console.log('=' .repeat(50));
        
        const totalEnrollments = await conn.sobject("Docebo_CourseEnrollment__c")
            .find({})
            .execute();
        
        const withCourse = totalEnrollments.filter(e => e.Course__c).length;
        const withUser = totalEnrollments.filter(e => e.Docebo_User__c).length;
        const fullyAssociated = totalEnrollments.filter(e => e.Course__c && e.Docebo_User__c).length;
        
        console.log(`📋 Total Enrollments: ${totalEnrollments.length}`);
        console.log(`✅ With Course Association: ${withCourse}/${totalEnrollments.length} (${Math.round(withCourse/totalEnrollments.length*100)}%)`);
        console.log(`✅ With User Association: ${withUser}/${totalEnrollments.length} (${Math.round(withUser/totalEnrollments.length*100)}%)`);
        console.log(`🎯 Fully Associated: ${fullyAssociated}/${totalEnrollments.length} (${Math.round(fullyAssociated/totalEnrollments.length*100)}%)`);
        
        if (fullyAssociated === totalEnrollments.length) {
            console.log('\n🎉 PERFECT! All course enrollments are now fully associated!');
        } else {
            console.log('\n💡 RECOMMENDATIONS:');
            console.log('1. 🔍 Check courses that couldn\'t be found - they may need to be created');
            console.log('2. 🔧 Review enrollment creation logic to prevent future issues');
            console.log('3. 📊 Monitor new enrollments to ensure associations work');
        }

    } catch (error) {
        console.error('💥 Error in fix:', error);
    }
}

// Execute the fix
console.log('🔄 Starting comprehensive enrollment association fix...');
fixAllEnrollmentAssociations()
    .then(() => {
        console.log('\n✅ Fix completed');
        process.exit(0);
    })
    .catch(err => {
        console.error('💥 Fix failed:', err);
        process.exit(1);
    });
