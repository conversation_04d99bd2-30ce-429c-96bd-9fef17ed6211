const fs = require('fs');
const path = require('path');

// Ensure logs directory exists
const logsDir = path.join(__dirname, '../logs');
if (!fs.existsSync(logsDir)) {
    fs.mkdirSync(logsDir, { recursive: true });
}

/**
 * Gets the current log file path based on the current date
 * @returns {string} Path to the log file for today
 */
function getLogFilePath() {
    const now = new Date();
    const dateStr = now.toISOString().split('T')[0]; // YYYY-MM-DD format
    return path.join(logsDir, `${dateStr}.log`);
}

/**
 * Writes a log entry to the current day's log file
 * @param {string} level - Log level (info, error, etc.)
 * @param {Array} args - Arguments to log
 */
function writeToLogFile(level, ...args) {
    const now = new Date();
    // Format: YYYY-MM-DD HH:MM:SS.mmm
    const timestamp = now.toISOString().replace('T', ' ').replace('Z', '');
    // Add timezone info
    const timezoneOffset = now.getTimezoneOffset();
    const timezone = `UTC${timezoneOffset <= 0 ? '+' : '-'}${Math.abs(Math.floor(timezoneOffset / 60)).toString().padStart(2, '0')}:${Math.abs(timezoneOffset % 60).toString().padStart(2, '0')}`;
    
    const logFilePath = getLogFilePath();
    
    // Convert arguments to strings
    const logMessage = args.map(arg => {
        if (typeof arg === 'object') {
            return JSON.stringify(arg, null, 2);
        }
        return String(arg);
    }).join(' ');
    
    // Format the log entry with more detailed timestamp
    const logEntry = `[${timestamp} ${timezone}] [${level.toUpperCase()}] ${logMessage}\n`;
    
    // Append to the log file
    fs.appendFileSync(logFilePath, logEntry);
    
    // Also output to console if needed
    // console.log(`[${level.toUpperCase()}]`, ...args);
}

// Create logger object with methods for different log levels
const logger = {
    info: (...args) => writeToLogFile('info', ...args),
    error: (...args) => writeToLogFile('error', ...args),
    warn: (...args) => writeToLogFile('warn', ...args),
    debug: (...args) => writeToLogFile('debug', ...args)
};

// Override console methods to use our logger
const originalConsoleLog = console.log;
const originalConsoleError = console.error;
const originalConsoleWarn = console.warn;

// Replace console methods with our logging functions
console.log = (...args) => {
    logger.info(...args);
    // Uncomment if you want to keep console output as well
    // originalConsoleLog(...args);
};

console.error = (...args) => {
    logger.error(...args);
    // originalConsoleError(...args);
};

console.warn = (...args) => {
    logger.warn(...args);
    // originalConsoleWarn(...args);
};

module.exports = logger;