require('dotenv').config();
const getAccessToken = require('./platform/salesforce/common/getAccessToken');
const getConnection = require('./platform/salesforce/common/getConnection');

async function testSalesforceConnection() {
    try {
        console.log('🔍 TESTING SALESFORCE CONNECTION');
        console.log('=' .repeat(50));
        
        // Step 1: Test getting access token
        console.log('📡 STEP 1: Testing Access Token...');
        console.log('-'.repeat(30));
        
        const tokenResult = await getAccessToken();
        console.log(`Token Status: ${tokenResult.status}`);
        
        if (tokenResult.status === 200) {
            console.log('✅ Access token obtained successfully');
            console.log(`Instance URL: ${tokenResult.data.instanceUrl}`);
            console.log(`Access Token: ${tokenResult.data.accessToken.substring(0, 20)}...`);
        } else {
            console.log('❌ Failed to get access token');
            console.log(`Error: ${JSON.stringify(tokenResult, null, 2)}`);
            return;
        }
        
        // Step 2: Test connection
        console.log('\n🔗 STEP 2: Testing Connection...');
        console.log('-'.repeat(30));
        
        const conn = await getConnection();
        
        if (conn && conn.accessToken) {
            console.log('✅ Connection established successfully');
            
            // Step 3: Test a simple query
            console.log('\n📊 STEP 3: Testing Simple Query...');
            console.log('-'.repeat(30));
            
            try {
                const result = await conn.query("SELECT COUNT() FROM User LIMIT 1");
                console.log(`✅ Query successful: ${result.totalSize} users found`);
            } catch (queryError) {
                console.log('❌ Query failed:');
                console.log(`Error: ${queryError.message}`);
                console.log(`Error Code: ${queryError.errorCode || 'N/A'}`);
            }
            
        } else {
            console.log('❌ Connection failed');
            console.log(`Error: ${conn.error || 'Unknown error'}`);
        }
        
    } catch (error) {
        console.error('💥 Test failed:', error.message);
        console.error('Full error:', error);
    }
}

console.log('🔄 Starting Salesforce Connection Test...');
testSalesforceConnection()
    .then(() => {
        console.log('\n✅ Salesforce connection test completed');
        process.exit(0);
    })
    .catch(err => {
        console.error('💥 Salesforce connection test failed:', err);
        process.exit(1);
    });
