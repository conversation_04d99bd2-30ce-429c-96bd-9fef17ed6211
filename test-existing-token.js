require('dotenv').config();
const jsforce = require('jsforce');

async function testExistingToken() {
    try {
        console.log('🔍 TESTING EXISTING ACCESS TOKEN');
        console.log('=' .repeat(50));
        
        const existingToken = process.env.SF_API_ACCESS_TOKEN;
        const instanceUrl = process.env.SF_API_INSTANCE;
        
        console.log(`Instance URL: ${instanceUrl}`);
        console.log(`Access Token: ${existingToken?.substring(0, 20)}... (length: ${existingToken?.length})`);
        
        if (!existingToken) {
            console.log('❌ No existing access token found in .env file');
            return;
        }
        
        console.log('\n🔗 Creating connection with existing token...');
        
        const conn = new jsforce.Connection({
            instanceUrl: instanceUrl,
            accessToken: existingToken,
            version: '60.0'
        });
        
        console.log('✅ Connection created, testing with simple query...');
        
        try {
            const result = await conn.query("SELECT COUNT() FROM User LIMIT 1");
            console.log(`✅ SUCCESS! Query successful: ${result.totalSize} users found`);
            console.log('🎉 The existing access token is still valid!');
            
            // Test a Docebo-specific query
            try {
                const doceboResult = await conn.query("SELECT COUNT() FROM Docebo_Learning_Plan_Course_Enrollment__c");
                console.log(`📊 Learning Plan Course Enrollments: ${doceboResult.totalSize}`);
            } catch (doceboError) {
                console.log(`⚠️ Docebo query failed: ${doceboError.message}`);
            }
            
        } catch (queryError) {
            console.log('❌ Query failed with existing token:');
            console.log(`Error: ${queryError.message}`);
            console.log(`Error Code: ${queryError.errorCode || 'N/A'}`);
            
            if (queryError.message.includes('expired') || queryError.message.includes('invalid')) {
                console.log('🔄 The existing access token has expired');
            }
        }
        
    } catch (error) {
        console.error('💥 Test failed:', error.message);
    }
}

console.log('🔄 Starting existing token test...');
testExistingToken()
    .then(() => {
        console.log('\n✅ Existing token test completed');
        process.exit(0);
    })
    .catch(err => {
        console.error('💥 Existing token test failed:', err);
        process.exit(1);
    });
