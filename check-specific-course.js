require('dotenv').config();
const getConnection = require('./platform/salesforce/common/getConnection');

async function checkSpecificCourse() {
    try {
        console.log('🔍 CHECKING SPECIFIC COURSE RECORD...');
        console.log('Course ID: a6VO40000029QLVMA2');
        console.log('=' .repeat(50));

        const conn = await getConnection();
        
        if (!conn || !conn.accessToken) {
            throw new Error('Failed to connect to Salesforce');
        }

        console.log('✅ Connected to Salesforce successfully');

        // First, let's check if the record exists at all
        const basicCourse = await conn.sobject('Docebo_Course__c')
            .findOne({ Id: 'a6VO40000029QLVMA2' }, ['Id', 'Course_Name__c']);

        if (!basicCourse) {
            console.log('❌ Course record not found with ID: a6VO40000029QLVMA2');
            
            // Let's search for similar records
            console.log('\n🔍 Searching for similar course records...');
            const similarCourses = await conn.sobject('Docebo_Course__c')
                .find({}, ['Id', 'Course_Name__c', 'Course_External_Id__c'])
                .limit(5)
                .execute();
                
            console.log('Found courses:');
            similarCourses.forEach(course => {
                console.log(`  - ${course.Id}: ${course.Course_Name__c} (External ID: ${course.Course_External_Id__c})`);
            });
            return;
        }

        console.log(`✅ Found course: ${basicCourse.Course_Name__c}`);

        // Now get the full record with all fields
        const fullCourse = await conn.sobject('Docebo_Course__c')
            .findOne({ Id: 'a6VO40000029QLVMA2' }, [
                'Id',
                'Course_Name__c',
                'Course_External_Id__c',
                'Course_Category_Code__c',
                'Course_Start_Date__c', 
                'Course_End_Date__c',
                'Session_Time_min__c',
                'Enrollment_Date__c',
                'LastModifiedDate',
                'CreatedDate'
            ]);

        console.log('\n📋 COMPLETE COURSE RECORD:');
        console.log('=' .repeat(50));
        console.log(`ID: ${fullCourse.Id}`);
        console.log(`Name: ${fullCourse.Course_Name__c}`);
        console.log(`External ID: ${fullCourse.Course_External_Id__c}`);
        console.log(`Created Date: ${fullCourse.CreatedDate}`);
        console.log(`Last Modified: ${fullCourse.LastModifiedDate}`);

        console.log('\n🎯 MISSING FIELDS STATUS:');
        console.log('-' .repeat(30));
        
        const fields = [
            { name: 'Course_Category_Code__c', value: fullCourse.Course_Category_Code__c },
            { name: 'Course_Start_Date__c', value: fullCourse.Course_Start_Date__c },
            { name: 'Course_End_Date__c', value: fullCourse.Course_End_Date__c },
            { name: 'Session_Time_min__c', value: fullCourse.Session_Time_min__c },
            { name: 'Enrollment_Date__c', value: fullCourse.Enrollment_Date__c }
        ];

        let hasAnyMissingFields = false;
        fields.forEach(field => {
            const status = field.value ? '✅ POPULATED' : '❌ EMPTY';
            const displayValue = field.value || 'NULL/EMPTY';
            console.log(`${field.name}: ${displayValue} ${status}`);
            if (!field.value) hasAnyMissingFields = true;
        });

        if (hasAnyMissingFields) {
            console.log('\n⚠️ ISSUE DETECTED: Some fields are still empty!');
            console.log('This suggests the batch update may not have processed this record.');
            
            // Let's check if this course was in our batch update
            console.log('\n🔍 CHECKING BATCH UPDATE LOGS...');
            
            // Get the external ID to check if it was processed
            if (fullCourse.Course_External_Id__c) {
                console.log(`External ID: ${fullCourse.Course_External_Id__c}`);
                console.log('This should have been processed in the batch update.');
                
                // Let's try to manually update this record
                console.log('\n🔧 ATTEMPTING MANUAL UPDATE...');
                
                const doceboService = require('./platform/docebo/services');
                const { mapDoceboCourseToSalesforce } = require('./platform/salesforce/courses/mapCourseData');
                
                try {
                    // Get course data from Docebo
                    const courseResponse = await doceboService.getCourseInfo(fullCourse.Course_External_Id__c);
                    
                    if (courseResponse && courseResponse.status === 200 && courseResponse.data) {
                        const doceboCourseData = courseResponse.data;
                        console.log('✅ Retrieved course data from Docebo');
                        
                        // Map the data
                        const mappedData = mapDoceboCourseToSalesforce(doceboCourseData, {});
                        
                        // Prepare update
                        const updateData = {
                            Id: fullCourse.Id
                        };
                        
                        if (!fullCourse.Course_Category_Code__c && mappedData.Course_Category_Code__c) {
                            updateData.Course_Category_Code__c = mappedData.Course_Category_Code__c;
                        }
                        if (!fullCourse.Course_Start_Date__c && mappedData.Course_Start_Date__c) {
                            updateData.Course_Start_Date__c = mappedData.Course_Start_Date__c;
                        }
                        if (!fullCourse.Course_End_Date__c && mappedData.Course_End_Date__c) {
                            updateData.Course_End_Date__c = mappedData.Course_End_Date__c;
                        }
                        if (!fullCourse.Session_Time_min__c && mappedData.Session_Time_min__c) {
                            updateData.Session_Time_min__c = mappedData.Session_Time_min__c;
                        }
                        if (!fullCourse.Enrollment_Date__c && mappedData.Enrollment_Date__c) {
                            updateData.Enrollment_Date__c = mappedData.Enrollment_Date__c;
                        }
                        
                        if (Object.keys(updateData).length > 1) { // More than just the Id
                            console.log('📝 Updating course with mapped data...');
                            console.log('Update data:', JSON.stringify(updateData, null, 2));
                            
                            const updateResult = await conn.sobject('Docebo_Course__c').update(updateData);
                            
                            if (updateResult.success) {
                                console.log('✅ Course updated successfully!');
                                
                                // Verify the update
                                const updatedCourse = await conn.sobject('Docebo_Course__c')
                                    .findOne({ Id: 'a6VO40000029QLVMA2' }, [
                                        'Course_Category_Code__c',
                                        'Course_Start_Date__c', 
                                        'Course_End_Date__c',
                                        'Session_Time_min__c',
                                        'Enrollment_Date__c'
                                    ]);
                                    
                                console.log('\n✅ UPDATED FIELD VALUES:');
                                console.log('-' .repeat(30));
                                console.log(`Course_Category_Code__c: ${updatedCourse.Course_Category_Code__c || 'NULL'}`);
                                console.log(`Course_Start_Date__c: ${updatedCourse.Course_Start_Date__c || 'NULL'}`);
                                console.log(`Course_End_Date__c: ${updatedCourse.Course_End_Date__c || 'NULL'}`);
                                console.log(`Session_Time_min__c: ${updatedCourse.Session_Time_min__c || 'NULL'}`);
                                console.log(`Enrollment_Date__c: ${updatedCourse.Enrollment_Date__c || 'NULL'}`);
                                
                            } else {
                                console.log('❌ Update failed:', updateResult.errors);
                            }
                        } else {
                            console.log('ℹ️ No fields need updating (all are already populated)');
                        }
                        
                    } else {
                        console.log('❌ Could not retrieve course data from Docebo');
                    }
                    
                } catch (doceboError) {
                    console.log('❌ Error retrieving/updating course data:', doceboError.message);
                }
                
            } else {
                console.log('❌ No external ID found - cannot retrieve from Docebo');
            }
        } else {
            console.log('\n✅ ALL FIELDS ARE POPULATED!');
            console.log('The batch update was successful for this record.');
        }

    } catch (error) {
        console.error('❌ Error checking course record:', error.message);
        console.error('Stack trace:', error.stack);
    }
}

checkSpecificCourse();
