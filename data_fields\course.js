
p24479088_docebo_courses

[
    'course_category__c',
    'course_category_code__c',
    'course_code__c',
    'course_creation_date__c',
    'course_duration__c',
    'course_end_date__c',
    'course_external_id__c',
    'course_has_expired__c',
    'course_internal_id__c',
    'course_link__c',
    'course_name__c',
    'course_progress__c',
    'course_start_date__c',
    'course_status__c',
    'course_type__c',
    'course_unique_id',
    'credits_ceus__c',
    'deleted__c',
    'deletion_date__c',
    'description__c',
    'effective__c',
    'enrollment_date__c',
    'hs_all_accessible_team_ids',
    'hs_all_assigned_business_unit_ids',
    'hs_all_owner_ids',
    'hs_all_team_ids',
    'hs_created_by_user_id',
    'hs_createdate',
    'hs_lastmodifieddate',
    'hs_merged_object_ids',
    'hs_object_id',
    'hs_object_source',
    'hs_object_source_detail_1',
    'hs_object_source_detail_2',
    'hs_object_source_detail_3',
    'hs_object_source_id',
    'hs_object_source_label',
    'hs_object_source_user_id',
    'hs_pinned_engagement_id',
    'hs_read_only',
    'hs_shared_team_ids',
    'hs_shared_user_ids',
    'hs_unique_creation_key',
    'hs_updated_by_user_id',
    'hs_user_ids_of_all_notification_followers',
    'hs_user_ids_of_all_notification_unfollowers',
    'hs_user_ids_of_all_owners',
    'hs_was_imported',
    'hubspot_owner_assigneddate',
    'hubspot_owner_id',
    'hubspot_team_id',
    'language__c',
    'last_update_date__c',
    'number_of_actions__c',
    'number_of_sessions__c',
    'reset_password_link__c',
    'salesforcedeleted',
    'salesforcelastsynctime',
    'salesforceobjectid',
    'score__c',
    'session_time_min__c',
    'skills_in_course__c',
    'slug__c',
    'thumbnail__c',
    'time_in_training_material_from_mobile_ap__c',
    'training_material_access_from_mobile_app__c',
    'training_material_from_mobile_app__c',
    'training_material_time_sec__c',
    'type__c',
    'user_course_level__c'
  ]
  

  {
    // Core Course Identification
    Course_Unique_Id__c: "", // Maps to: id
    Course_External_Id__c: 0, // Maps to: uid
    Course_Internal_ID__c: 0, // Maps to: id
    Course_Code__c: "", // Maps to: code
    Course_Status__c: "", // Maps to: status

    // Course Information
    Course_Name__c: "", // Maps to: name
    Name: "", // Salesforce standard Name field
    Description__c: "", // Maps to: description
    Course_Type__c: "", // Maps to: type
    Type__c: "", // Maps to: type
    Skills_in_course__c: "", // Maps to: skills (array)
    Language__c: "", // Maps to: language.name

    // Course Metadata
    Course_Creation_Date__c: "", // Maps to: created_on
    Course_Duration__c: 0, // Maps to: time_options.duration.days
    Course_Start_Date__c: "", // Maps to: time_options.date_begin
    Course_End_Date__c: "", // Maps to: time_options.date_end
    Last_Update_Date__c: "", // Maps to: updated_on

    // Course Category
    Course_Category__c: "", // Maps to: category.name
    Course_Category_Code__c: "", // Maps to: category.code

    // Course Content
    Slug__c: "", // Maps to: slug_name
    Thumbnail__c: "", // Maps to: thumbnail.url
    Credits_CEUs__c: 0, // Maps to: credits

    // Course Settings
    Effective__c: false, // Derived from status
    Deleted__c: false, // Derived from status
    Deletion_Date__c: "",
    Course_has_expired__c: "",

    // Enrollment & Progress (from enrollment data)
    Enrollment_Date__c: "",
    Course_Progress__c: 0.0,
    Score__c: 0,
    Session_Time_min__c: 0,

    // System Fields
    CreatedById: "", // Maps to: created_by.id
    LastModifiedById: "",
    Owner__c: "", // Maps to: created_by.fullname
    OwnerId: "",

    // Additional Fields
    Course_Link__c: "", // Could be derived from course URL
    Number_of_actions__c: 0,
    Number_of_sessions__c: 0,
    User_Course_Level__c: "",
    Reset_Password_Link__c: "",
    Training_Material_from_Mobile_App__c: 0.0,
    Time_in_Training_Material_from_Mobile_Ap__c: 0,
    Training_Material_Access_from_Mobile_App__c: 0,
    Training_Material_Time_sec__c: 0
}
  === Code Execution Successful ===