case "user.selfregistrationrequest.approved":
        if (payload.payloads) {
          for (let i = 0; i < payload.payloads.length; i++) {
            let userInfo = await doceboService.getUserInfo(
              payload.payloads[i].user_id
            );
            if (userInfo.status === 200) {
              let userData = userInfo.data;
              let userListedInfo = await doceboService.getUserListedInfo(
                payload.payloads[i].user_id
              );
              userData["fired_at"] = payload.payloads[i].fired_at;
              userData["expiration_date"] = payload.payloads[i].fired_at;
              let saveRes = await createNewUser(userData, userListedInfo);
              console.log("Multi users creation result: ", saveRes);
            }
          }
        } else {
          let userInfo = await doceboService.getUserInfo(
            payload.payload.user_id
          );
          let userListedInfo = await doceboService.getUserListedInfo(
            payload.payload.user_id
          );
          if (userInfo.status == 200) {
            let userData = userInfo.data;
            userData["fired_at"] = payload.payload.fired_at;
            userData["expiration_date"] = payload.payload.fired_at;
            let saveRes = await createNewUser(userData, userListedInfo);
            console.log("Single user creation result: ", saveRes);
          }
        }
        break;