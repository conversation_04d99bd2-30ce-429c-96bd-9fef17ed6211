require('dotenv').config();
const { fetchAllDoceboUsers, processUser } = require('./platform/salesforce/users/historicalDataUpdate');
const getConnection = require('./platform/salesforce/common/getConnection');

async function runUserBatchSyncStreamlined() {
    try {
        console.log('🚀 Starting Streamlined User Batch Synchronization');
        console.log('=' .repeat(80));
        console.log('📋 SYNC RULES:');
        console.log('   1. If user does not exist → Create Docebo_Users__c + Lead');
        console.log('   2. If user exists → Update Docebo_Users__c + Lead/Contact');
        console.log('   3. Process in batches of 50 users for optimal performance');
        console.log('=' .repeat(80));
        
        const startTime = Date.now();
        
        // Step 1: Get Salesforce connection
        console.log('\n🔗 Establishing Salesforce connection...');
        const conn = await getConnection();
        if (!conn || !conn.accessToken) {
            console.error("❌ Salesforce connection failed");
            return false;
        }
        console.log('✅ Salesforce connection established');
        
        // Step 2: Fetch all users from Docebo
        console.log('\n📥 Fetching all users from Docebo...');
        const doceboUsers = await fetchAllDoceboUsers();
        
        if (!doceboUsers || doceboUsers.length === 0) {
            console.error("❌ No users found in Docebo or fetch failed");
            return false;
        }
        
        console.log(`✅ Found ${doceboUsers.length} users in Docebo`);
        
        // Step 3: Process users in batches
        const batchSize = 50;
        let processedCount = 0;
        let updatedContacts = 0;
        let updatedLeads = 0;
        let createdLeads = 0;
        let updatedDoceboUsers = 0;
        let createdDoceboUsers = 0;
        let errors = 0;
        
        console.log(`\n🔄 Processing ${doceboUsers.length} users in batches of ${batchSize}...`);
        console.log('-'.repeat(80));
        
        for (let i = 0; i < doceboUsers.length; i += batchSize) {
            const batch = doceboUsers.slice(i, i + batchSize);
            const batchNumber = Math.floor(i / batchSize) + 1;
            const totalBatches = Math.ceil(doceboUsers.length / batchSize);
            
            console.log(`\n📦 Processing batch ${batchNumber}/${totalBatches} (${batch.length} users)`);
            console.log(`   Progress: ${Math.round((i / doceboUsers.length) * 100)}%`);
            
            for (const doceboUser of batch) {
                try {
                    const result = await processUser(conn, doceboUser);
                    
                    if (result.contactUpdated) updatedContacts++;
                    if (result.leadUpdated) updatedLeads++;
                    if (result.leadCreated) createdLeads++;
                    if (result.doceboUserUpdated) updatedDoceboUsers++;
                    if (result.doceboUserCreated) createdDoceboUsers++;
                    
                    processedCount++;
                    
                    // Show progress every 10 users
                    if (processedCount % 10 === 0) {
                        const progressPercent = Math.round((processedCount / doceboUsers.length) * 100);
                        console.log(`   ✅ Processed ${processedCount}/${doceboUsers.length} users (${progressPercent}%)`);
                    }
                    
                } catch (userError) {
                    errors++;
                    const userId = doceboUser.userInfo?.user_data?.user_id || 'unknown';
                    console.error(`   ❌ Error processing user ${userId}:`, userError.message);
                }
            }
            
            // Small delay between batches to avoid overwhelming the APIs
            if (i + batchSize < doceboUsers.length) {
                console.log(`   ⏳ Batch ${batchNumber} completed. Pausing 2 seconds before next batch...`);
                await new Promise(resolve => setTimeout(resolve, 2000));
            }
        }
        
        const endTime = Date.now();
        const duration = Math.round((endTime - startTime) / 1000);
        const minutes = Math.floor(duration / 60);
        const seconds = duration % 60;
        
        // Final Results
        console.log('\n🎉 USER BATCH SYNCHRONIZATION COMPLETED!');
        console.log('=' .repeat(80));
        console.log(`⏱️  Total Duration: ${minutes}m ${seconds}s`);
        console.log(`📊 Total Users Processed: ${processedCount}/${doceboUsers.length}`);
        console.log(`❌ Errors: ${errors}`);
        console.log('');
        console.log('📈 DETAILED RESULTS:');
        console.log(`   📝 Docebo Users Created: ${createdDoceboUsers}`);
        console.log(`   🔄 Docebo Users Updated: ${updatedDoceboUsers}`);
        console.log(`   🆕 Leads Created: ${createdLeads}`);
        console.log(`   🔄 Leads Updated: ${updatedLeads}`);
        console.log(`   🔄 Contacts Updated: ${updatedContacts}`);
        console.log('');
        console.log('✅ SYNC ACCOMPLISHMENTS:');
        console.log('   • All Docebo users synchronized to Salesforce');
        console.log('   • Docebo_Users__c records created/updated');
        console.log('   • Lead records created for new users');
        console.log('   • Contact records updated for existing users');
        console.log('   • All field mappings applied correctly');
        console.log('   • Record associations established');
        
        console.log('\n🔧 FIELD MAPPINGS APPLIED:');
        console.log('   • Email__c, First_Name__c, Last_Name__c');
        console.log('   • Languages__c, Time_Zone__c');
        console.log('   • Organization_Name__c, Job_Title__c');
        console.log('   • City__c, State__c (from additional fields)');
        console.log('   • User_Creation_Date__c, User_Expiration_Date__c');
        console.log('   • Annual Revenue (AnnualRevenue for Lead, Annual_Revenue__c for Contact)');
        console.log('   • All other custom field mappings');
        
        const successRate = Math.round(((processedCount - errors) / processedCount) * 100);
        console.log(`\n📊 SUCCESS RATE: ${successRate}% (${processedCount - errors}/${processedCount} users)`);
        
        if (errors > 0) {
            console.log(`\n⚠️  ${errors} users had processing errors - check logs above for details`);
        }
        
        console.log('\n🎯 NEXT STEPS:');
        console.log('   ✅ User synchronization complete');
        console.log('   🔄 Ready to proceed with COURSES batch sync');
        console.log('   📊 Ready to proceed with ENROLLMENTS batch sync');
        
        return {
            success: true,
            totalUsers: doceboUsers.length,
            processedUsers: processedCount,
            errors: errors,
            createdDoceboUsers,
            updatedDoceboUsers,
            createdLeads,
            updatedLeads,
            updatedContacts,
            duration: duration
        };
        
    } catch (error) {
        console.error('💥 Error in user batch sync:', error);
        console.log('\n🔧 TROUBLESHOOTING:');
        console.log('   1. Check Salesforce connection');
        console.log('   2. Verify Docebo API credentials');
        console.log('   3. Check field permissions in Salesforce');
        console.log('   4. Review error logs above');
        return false;
    }
}

// Execute the streamlined user batch sync
console.log('🔄 Initializing streamlined user batch synchronization...');
runUserBatchSyncStreamlined()
    .then((result) => {
        if (result && result.success) {
            console.log('\n🎉 USER BATCH SYNC COMPLETED SUCCESSFULLY!');
            console.log(`📊 ${result.processedUsers} users processed in ${Math.floor(result.duration/60)}m ${result.duration%60}s`);
            console.log('🚀 Ready for next batch sync (courses)');
        } else {
            console.log('\n❌ User batch sync failed');
            console.log('🔧 Please check the error logs and try again');
        }
        process.exit(result && result.success ? 0 : 1);
    })
    .catch(err => {
        console.error('💥 User batch sync failed:', err);
        process.exit(1);
    });
