require('dotenv').config();
const getConnection = require('./platform/salesforce/common/getConnection');

async function testDoceboUsersFieldMapping() {
    try {
        console.log('🧪 Testing Docebo_Users__c Field Mapping');
        console.log('=' .repeat(70));
        
        const conn = await getConnection();
        if (!conn || !conn.accessToken) {
            console.error("❌ Invalid Salesforce connection");
            return;
        }

        // Step 1: Check Docebo_Users__c object structure
        console.log('\n🔍 SALESFORCE DOCEBO_USERS__C OBJECT ANALYSIS:');
        console.log('-'.repeat(50));
        
        const doceboUsersObjectDesc = await conn.sobject("Docebo_Users__c").describe();
        const doceboUsersFields = doceboUsersObjectDesc.fields;
        
        console.log(`📋 Docebo_Users__c object has ${doceboUsersFields.length} fields`);

        // Step 2: Check your required fields against Docebo_Users__c object
        console.log('\n📋 REQUIRED DOCEBO_USERS__C FIELDS VERIFICATION:');
        console.log('-'.repeat(50));
        
        const requiredFields = [
            'Email__c',
            'First_Name__c',
            'Last_Name__c',
            'Languages__c',
            'Time_Zone__c',
            'Network_Partnership_Association__c',
            'Organization_Name__c',
            'Job_Title__c',
            'Race_Identity__c',
            'Initiative__c',
            'Role_Type__c',
            'Gender_Identity__c',
            'City__c',
            'State__c'
        ];
        
        const fieldMap = {};
        const existingFields = [];
        const missingFields = [];
        const picklistFields = [];
        
        // Check each required field
        requiredFields.forEach(reqField => {
            // Try different case variations
            const variations = [
                reqField,
                reqField.charAt(0).toUpperCase() + reqField.slice(1),
                reqField.toLowerCase(),
                reqField.toUpperCase()
            ];
            
            let found = false;
            for (const variation of variations) {
                const doceboField = doceboUsersFields.find(f => f.name === variation);
                if (doceboField) {
                    fieldMap[reqField] = doceboField.name;
                    existingFields.push({
                        required: reqField,
                        actual: doceboField.name,
                        type: doceboField.type,
                        label: doceboField.label
                    });
                    
                    if (doceboField.type === 'picklist') {
                        picklistFields.push({
                            name: doceboField.name,
                            label: doceboField.label,
                            values: doceboField.picklistValues
                        });
                    }
                    found = true;
                    break;
                }
            }
            
            if (!found) {
                missingFields.push(reqField);
            }
        });
        
        console.log(`\n✅ EXISTING FIELDS (${existingFields.length}/${requiredFields.length}):`);
        existingFields.forEach(field => {
            console.log(`   ${field.required} → ${field.actual} (${field.type}) - ${field.label}`);
        });
        
        if (missingFields.length > 0) {
            console.log(`\n❌ MISSING FIELDS (${missingFields.length}):`);
            missingFields.forEach(field => {
                console.log(`   ${field}`);
            });
        }

        // Step 3: Check current Docebo_Users__c data population
        console.log(`\n🔍 CURRENT DOCEBO_USERS__C DATA POPULATION:`);
        console.log('-'.repeat(50));
        
        // Get a sample of existing Docebo_Users__c records
        const doceboUsers = await conn.sobject("Docebo_Users__c")
            .find({})
            .limit(10)
            .execute();
        
        if (doceboUsers.length > 0) {
            console.log(`📋 Found ${doceboUsers.length} Docebo_Users__c records:`);
            
            const sampleUser = doceboUsers[0];
            console.log(`\n📋 Sample Docebo_Users__c field values:`);
            
            existingFields.forEach(field => {
                const value = sampleUser[field.actual];
                const hasValue = value !== null && value !== undefined && value !== '';
                console.log(`   ${field.actual}: ${hasValue ? '✅' : '❌'} "${value}"`);
            });
        } else {
            console.log(`❌ No Docebo_Users__c records found`);
        }

        // Step 4: Check current tidyData function mapping
        console.log('\n🔍 CURRENT TIDYDATA FUNCTION ANALYSIS:');
        console.log('-'.repeat(50));
        
        // Check what fields are currently being mapped in tidyData
        const currentlyMappedFields = [
            'Email__c', // ✅ Mapped from user_data.email
            'First_Name__c', // ✅ Mapped from user_data.first_name
            'Last_Name__c', // ✅ Mapped from user_data.last_name
            'Organization_Name__c', // ✅ Mapped from additional field ID 14
            'Job_Title__c', // ✅ Mapped from additional field ID 8
            'Race_Identity__c', // ✅ Mapped from additional field ID 12
            'Initiative__c', // ✅ Mapped from additional field ID 20
            'Role_Type__c', // ✅ Mapped from additional field ID 9
            'Gender_Identity__c' // ✅ Mapped from additional field ID 13
        ];
        
        const notCurrentlyMapped = [
            'Languages__c', // ❌ Not mapped (should be from user_data.language)
            'Time_Zone__c', // ❌ Not mapped (should be from user_data.timezone)
            'Network_Partnership_Association__c', // ❌ Not mapped
            'City__c', // ❌ Not mapped (should be from additional field)
            'State__c' // ❌ Not mapped (should be from additional field)
        ];
        
        console.log(`\n✅ CURRENTLY MAPPED FIELDS (${currentlyMappedFields.length}):`);
        currentlyMappedFields.forEach(field => {
            console.log(`   • ${field}`);
        });
        
        console.log(`\n❌ NOT CURRENTLY MAPPED (${notCurrentlyMapped.length}):`);
        notCurrentlyMapped.forEach(field => {
            console.log(`   • ${field}`);
        });

        // Step 5: Check if missing fields exist on the object
        console.log('\n🔍 CHECKING IF MISSING FIELDS EXIST ON OBJECT:');
        console.log('-'.repeat(50));
        
        notCurrentlyMapped.forEach(field => {
            const existsOnObject = existingFields.find(f => f.required === field);
            if (existsOnObject) {
                console.log(`   ${field}: ✅ EXISTS on object but NOT MAPPED in tidyData`);
            } else {
                console.log(`   ${field}: ❌ DOES NOT EXIST on object`);
            }
        });

        // Step 6: Summary and action plan
        console.log('\n📊 DOCEBO_USERS__C FIELD MAPPING ACTION PLAN:');
        console.log('=' .repeat(50));
        
        const mappingPercentage = Math.round((existingFields.length / requiredFields.length) * 100);
        const currentMappingPercentage = Math.round((currentlyMappedFields.length / requiredFields.length) * 100);
        
        console.log(`\n🎯 CURRENT STATUS:`);
        console.log(`   • Field Coverage: ${existingFields.length}/${requiredFields.length} (${mappingPercentage}%)`);
        console.log(`   • Currently Mapped: ${currentlyMappedFields.length}/${requiredFields.length} (${currentMappingPercentage}%)`);
        console.log(`   • Missing Fields: ${missingFields.length}`);
        console.log(`   • Unmapped but Available: ${notCurrentlyMapped.filter(f => existingFields.find(ef => ef.required === f)).length}`);
        
        console.log(`\n🔧 IMMEDIATE FIXES NEEDED:`);
        console.log(`   1. Add mapping for available but unmapped fields`);
        console.log(`   2. Check if missing fields need to be created in Salesforce`);
        console.log(`   3. Improve data population from Docebo API`);
        
        console.log(`\n💡 PRIORITY ACTIONS:`);
        console.log(`   🔥 HIGH: Add Languages__c mapping from user_data.language`);
        console.log(`   🔥 HIGH: Add Time_Zone__c mapping from user_data.timezone`);
        console.log(`   📊 MEDIUM: Check if Network_Partnership_Association__c field exists`);
        console.log(`   📊 MEDIUM: Check if City__c and State__c fields exist`);
        console.log(`   🔧 LOW: Add remaining optional fields if they exist`);

        // Step 7: Show specific mapping recommendations
        console.log('\n💡 SPECIFIC MAPPING RECOMMENDATIONS:');
        console.log('-'.repeat(50));
        
        console.log(`\n🔧 ADD TO TIDYDATA FUNCTION:`);
        console.log(`   // Add these mappings if fields exist:`);
        console.log(`   tmpUserInfo.Languages__c = newUser.user_data.language || "";`);
        console.log(`   tmpUserInfo.Time_Zone__c = newUser.user_data.timezone || "";`);
        console.log(`   tmpUserInfo.Network_Partnership_Association__c = getAdditionalData(additionFields, "XX");`);
        console.log(`   tmpUserInfo.City__c = getAdditionalData(additionFields, "24"); // Mailing City`);
        console.log(`   tmpUserInfo.State__c = getStateLabel(additionFields, "25"); // Mailing State`);

    } catch (error) {
        console.error('💥 Error in Docebo_Users__c field mapping test:', error);
    }
}

// Execute the test
console.log('🔄 Starting Docebo_Users__c field mapping analysis...');
testDoceboUsersFieldMapping()
    .then(() => {
        console.log('\n✅ Analysis completed');
        process.exit(0);
    })
    .catch(err => {
        console.error('💥 Analysis failed:', err);
        process.exit(1);
    });
