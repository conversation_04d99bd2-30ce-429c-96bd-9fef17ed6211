require('dotenv').config();
const { createNewUser } = require('./platform/salesforce/users/createUser');
const getConnection = require('./platform/salesforce/common/getConnection');

async function testDuplicateLeadPrevention() {
    try {
        console.log('🔧 Testing Duplicate Lead Prevention');
        console.log('=' .repeat(70));
        console.log('🎯 TESTING FIX FOR:');
        console.log('   Multiple Leads created for same email (e.g., <EMAIL>)');
        console.log('   Root Cause: Lead creation before user existence check');
        console.log('   Solution: Create Lead AFTER successful user creation');
        console.log('=' .repeat(70));
        
        const conn = await getConnection();
        if (!conn || !conn.accessToken) {
            console.error("❌ Invalid Salesforce connection");
            return;
        }

        // Step 1: Clean up any existing test data
        console.log('\n🧹 Cleaning up existing test data...');
        
        const testEmail = '<EMAIL>';
        
        // Clean up existing records
        const existingDoceboUsers = await conn.sobject("Docebo_Users__c")
            .find({ Email__c: testEmail })
            .execute();
        
        for (const user of existingDoceboUsers) {
            await conn.sobject("Docebo_Users__c").delete(user.Id);
            console.log(`   🗑️ Deleted existing Docebo_Users__c: ${user.Id}`);
        }
        
        const existingLeads = await conn.sobject("Lead")
            .find({ Email: testEmail })
            .execute();
        
        for (const lead of existingLeads) {
            await conn.sobject("Lead").delete(lead.Id);
            console.log(`   🗑️ Deleted existing Lead: ${lead.Id}`);
        }
        
        const existingContacts = await conn.sobject("Contact")
            .find({ Email: testEmail })
            .execute();
        
        for (const contact of existingContacts) {
            await conn.sobject("Contact").delete(contact.Id);
            console.log(`   🗑️ Deleted existing Contact: ${contact.Id}`);
        }

        // Step 2: Create test user data
        console.log('\n📋 Creating test user data...');
        
        const testUserInfo = {
            user_data: {
                user_id: 99998,
                first_name: "Duplicate",
                last_name: "Prevention Test",
                email: testEmail,
                username: "duplicate.prevention.test",
                level: "3",
                language: "english",
                timezone: "America/New_York",
                email_validation_status: "1",
                valid: "1",
                manager_username: ""
            },
            additional_fields: [
                { id: "8", value: "Test Prevention Manager", enabled: true },
                { id: "9", value: "1", enabled: true, options: [{ id: "1", label: "Communications" }] },
                { id: "10", value: "1", enabled: true, options: [{ id: "1", label: "Full-Time" }] },
                { id: "12", value: "1", enabled: true, options: [{ id: "1", label: "Asian" }] },
                { id: "13", value: "1", enabled: true, options: [{ id: "1", label: "Woman" }] },
                { id: "14", value: "Test Prevention Company", enabled: true },
                { id: "20", value: "Test Prevention Initiative", enabled: true },
                { id: "23", value: "https://testprevention.com", enabled: true },
                { id: "24", value: "Prevention City", enabled: true },
                { id: "25", value: "1", enabled: true, options: [{ id: "1", label: "Alabama" }] }
            ],
            branches: [
                {
                    name: "Test Prevention Branch",
                    path: "/test/prevention/branch",
                    codes: "999"
                }
            ],
            fired_at: "2024-01-01 10:00:00",
            expiration_date: "2025-12-31 23:59:59"
        };

        const testUserListedInfo = {
            last_access_date: "2024-06-01 15:30:00"
        };

        console.log('📝 Test user data:');
        console.log(`   User ID: ${testUserInfo.user_data.user_id}`);
        console.log(`   Email: ${testUserInfo.user_data.email}`);
        console.log(`   Name: ${testUserInfo.user_data.first_name} ${testUserInfo.user_data.last_name}`);

        // Step 3: Test first user creation
        console.log('\n🧪 Test 1: First user creation...');
        
        const startTime = new Date();
        const result1 = await createNewUser(testUserInfo, testUserListedInfo);
        const endTime = new Date();
        
        console.log(`⏱️ First creation took: ${endTime - startTime}ms`);
        
        if (result1) {
            console.log('✅ First user creation completed');
            
            // Check how many Leads were created
            const leadsAfterFirst = await conn.sobject("Lead")
                .find({ Email: testEmail })
                .execute();
            
            console.log(`📊 Leads after first creation: ${leadsAfterFirst.length}`);
            
            if (leadsAfterFirst.length === 1) {
                console.log(`   ✅ Correct: Only 1 Lead created`);
                console.log(`   Lead ID: ${leadsAfterFirst[0].Id}`);
                console.log(`   Created: ${leadsAfterFirst[0].CreatedDate}`);
            } else if (leadsAfterFirst.length === 0) {
                console.log(`   ⚠️ No Lead created (user might have been associated with existing Contact)`);
            } else {
                console.log(`   ❌ PROBLEM: ${leadsAfterFirst.length} Leads created!`);
                for (let i = 0; i < leadsAfterFirst.length; i++) {
                    console.log(`     Lead ${i + 1}: ${leadsAfterFirst[i].Id} (${leadsAfterFirst[i].CreatedDate})`);
                }
            }
            
            // Check Docebo_Users__c
            const doceboUsersAfterFirst = await conn.sobject("Docebo_Users__c")
                .find({ Email__c: testEmail })
                .execute();
            
            console.log(`📊 Docebo_Users__c after first creation: ${doceboUsersAfterFirst.length}`);
            
        } else {
            console.log('❌ First user creation failed');
        }

        // Step 4: Test second user creation (should update existing, not create new Lead)
        console.log('\n🧪 Test 2: Second user creation (should update existing)...');
        
        // Modify user ID to simulate a different user with same email
        testUserInfo.user_data.user_id = 99999;
        testUserInfo.user_data.first_name = "Duplicate2";
        
        const startTime2 = new Date();
        const result2 = await createNewUser(testUserInfo, testUserListedInfo);
        const endTime2 = new Date();
        
        console.log(`⏱️ Second creation took: ${endTime2 - startTime2}ms`);
        
        if (result2) {
            console.log('✅ Second user creation completed');
            
            // Check how many Leads exist now
            const leadsAfterSecond = await conn.sobject("Lead")
                .find({ Email: testEmail })
                .execute();
            
            console.log(`📊 Leads after second creation: ${leadsAfterSecond.length}`);
            
            if (leadsAfterSecond.length === 1) {
                console.log(`   ✅ EXCELLENT: Still only 1 Lead (no duplicate created)`);
                console.log(`   Lead ID: ${leadsAfterSecond[0].Id}`);
            } else if (leadsAfterSecond.length === 0) {
                console.log(`   ⚠️ No Leads (Contact association used)`);
            } else {
                console.log(`   ❌ DUPLICATE PREVENTION FAILED: ${leadsAfterSecond.length} Leads found!`);
                
                // Analyze the duplicates
                console.log('\n🔍 DUPLICATE ANALYSIS:');
                for (let i = 0; i < leadsAfterSecond.length; i++) {
                    const lead = leadsAfterSecond[i];
                    console.log(`   Lead ${i + 1}: ${lead.Id}`);
                    console.log(`     Created: ${lead.CreatedDate}`);
                    console.log(`     Name: ${lead.FirstName} ${lead.LastName}`);
                    console.log(`     Company: ${lead.Company}`);
                }
                
                // Calculate time gaps
                if (leadsAfterSecond.length > 1) {
                    const times = leadsAfterSecond.map(l => new Date(l.CreatedDate)).sort();
                    for (let i = 1; i < times.length; i++) {
                        const gap = (times[i] - times[i-1]) / 1000; // seconds
                        console.log(`     Gap ${i}: ${gap} seconds`);
                    }
                }
            }
            
            // Check Docebo_Users__c records
            const doceboUsersAfterSecond = await conn.sobject("Docebo_Users__c")
                .find({ Email__c: testEmail })
                .execute();
            
            console.log(`📊 Docebo_Users__c after second creation: ${doceboUsersAfterSecond.length}`);
            
        } else {
            console.log('❌ Second user creation failed');
        }

        // Step 5: Verify associations
        console.log('\n🔍 Verifying Lead associations...');
        
        const finalDoceboUsers = await conn.sobject("Docebo_Users__c")
            .find({ Email__c: testEmail })
            .execute();
        
        const finalLeads = await conn.sobject("Lead")
            .find({ Email: testEmail })
            .execute();
        
        console.log('\n📊 FINAL STATE:');
        console.log(`   Docebo_Users__c records: ${finalDoceboUsers.length}`);
        console.log(`   Lead records: ${finalLeads.length}`);
        
        let associatedCount = 0;
        for (const user of finalDoceboUsers) {
            console.log(`\n   Docebo_Users__c: ${user.Id}`);
            console.log(`     User ID: ${user.User_Unique_Id__c}`);
            console.log(`     Name: ${user.First_Name__c} ${user.Last_Name__c}`);
            console.log(`     Lead Association: ${user.Lead__c || 'None'}`);
            console.log(`     Contact Association: ${user.Contact__c || 'None'}`);
            
            if (user.Lead__c || user.Contact__c) {
                associatedCount++;
            }
        }
        
        console.log(`\n📊 ASSOCIATION SUMMARY:`);
        console.log(`   Users with associations: ${associatedCount}/${finalDoceboUsers.length}`);
        console.log(`   Association rate: ${Math.round((associatedCount / finalDoceboUsers.length) * 100)}%`);

        // Step 6: Clean up test data
        console.log('\n🗑️ Cleaning up test data...');
        
        for (const user of finalDoceboUsers) {
            await conn.sobject("Docebo_Users__c").delete(user.Id);
            console.log(`   ✅ Deleted Docebo_Users__c: ${user.Id}`);
        }
        
        for (const lead of finalLeads) {
            await conn.sobject("Lead").delete(lead.Id);
            console.log(`   ✅ Deleted Lead: ${lead.Id}`);
        }
        
        // Clean up any test accounts
        const testAccounts = await conn.sobject("Account")
            .find({ Name: { $like: '%Duplicate%99998%' } })
            .execute();
        
        for (const account of testAccounts) {
            await conn.sobject("Account").delete(account.Id);
            console.log(`   ✅ Deleted Account: ${account.Id}`);
        }

        // Step 7: Summary
        console.log('\n📊 DUPLICATE LEAD PREVENTION TEST SUMMARY:');
        console.log('=' .repeat(70));
        
        const preventionWorking = finalLeads.length <= 1;
        
        console.log('🔧 FIX IMPLEMENTED:');
        console.log('   ✅ Lead creation moved AFTER successful user creation');
        console.log('   ✅ Duplicate Lead check before creation');
        console.log('   ✅ Reuse existing Leads when found');
        console.log('   ✅ No Lead creation if user creation fails');
        
        console.log('\n🎯 TEST RESULTS:');
        if (preventionWorking) {
            console.log('   ✅ DUPLICATE PREVENTION WORKING!');
            console.log('   ✅ No duplicate Leads created');
            console.log('   ✅ Logic order fixed');
        } else {
            console.log('   ❌ DUPLICATE PREVENTION FAILED');
            console.log('   ❌ Multiple Leads still being created');
            console.log('   ❌ Further investigation needed');
        }
        
        console.log('\n🚀 PRODUCTION BENEFITS:');
        console.log('   • No more duplicate Leads for same email');
        console.log('   • Cleaner data in Salesforce');
        console.log('   • Better Lead management');
        console.log('   • Reduced storage usage');
        console.log('   • Improved reporting accuracy');

        return {
            success: true,
            preventionWorking: preventionWorking,
            leadCount: finalLeads.length,
            userCount: finalDoceboUsers.length
        };

    } catch (error) {
        console.error('💥 Error in duplicate Lead prevention test:', error);
        return {
            success: false,
            error: error.message
        };
    }
}

// Execute the test
console.log('🔄 Starting duplicate Lead prevention test...');
testDuplicateLeadPrevention()
    .then((result) => {
        console.log('\n✅ Duplicate Lead prevention test completed');
        if (result.success) {
            if (result.preventionWorking) {
                console.log('🎉 DUPLICATE LEAD PREVENTION IS WORKING!');
                console.log('🚀 No more duplicate Leads will be created!');
            } else {
                console.log('⚠️ Duplicate prevention needs more work');
            }
        } else {
            console.log('❌ Test failed. Check the logs above.');
        }
        process.exit(0);
    })
    .catch(err => {
        console.error('💥 Test failed:', err);
        process.exit(1);
    });
