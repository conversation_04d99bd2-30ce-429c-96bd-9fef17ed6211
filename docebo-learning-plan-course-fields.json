{"Id": {"type": "id", "label": "Record ID", "updateable": false, "createable": false}, "IsDeleted": {"type": "boolean", "label": "Deleted", "updateable": false, "createable": false}, "Name": {"type": "string", "label": "Learning Plan Course Id", "updateable": false, "createable": false}, "CreatedDate": {"type": "datetime", "label": "Created Date", "updateable": false, "createable": false}, "CreatedById": {"type": "reference", "label": "Created By ID", "updateable": false, "createable": false}, "LastModifiedDate": {"type": "datetime", "label": "Last Modified Date", "updateable": false, "createable": false}, "LastModifiedById": {"type": "reference", "label": "Last Modified By ID", "updateable": false, "createable": false}, "SystemModstamp": {"type": "datetime", "label": "System Modstamp", "updateable": false, "createable": false}, "LastViewedDate": {"type": "datetime", "label": "Last Viewed Date", "updateable": false, "createable": false}, "LastReferencedDate": {"type": "datetime", "label": "Last Referenced Date", "updateable": false, "createable": false}, "Composite_Foreign_Key__c": {"type": "string", "label": "Composite Foreign Key", "updateable": true, "createable": true}, "CourseId__c": {"type": "reference", "label": "Course", "updateable": false, "createable": true}, "Learning_Plan_Id__c": {"type": "reference", "label": "Learning Plan", "updateable": false, "createable": true}}