require('dotenv').config();
const doceboService = require('./platform/docebo/services');

async function getDoceboFieldOptionsDetailed() {
    try {
        console.log('🔍 Getting Detailed Docebo Field Options');
        console.log('=' .repeat(70));
        
        const allOptions = {
            courseCategories: new Set(),
            courseCategoryCodes: new Set(),
            courseTypes: new Set(),
            courseStatuses: new Set(),
            languages: new Set(),
            userRoles: new Map(), // Use Map to store ID -> Label mapping
            employmentTypes: new Map(),
            genderIdentities: new Map(),
            raceIdentities: new Map(),
            initiatives: new Map(),
            organizationTypes: new Map(),
            states: new Map(),
            branchNames: new Set(),
            userLevels: new Set(),
            userStatuses: new Set()
        };

        // Step 1: Get course data
        console.log('\n📚 ANALYZING COURSES...');
        console.log('-'.repeat(50));
        
        try {
            const coursesData = await doceboService.getTotalCourseListedInfo();
            
            if (coursesData && coursesData.length > 0) {
                console.log(`📋 Analyzing ${coursesData.length} courses...`);
                
                // Analyze first 100 courses
                const coursesToAnalyze = coursesData.slice(0, 100);
                
                coursesToAnalyze.forEach(course => {
                    if (course.category) {
                        if (course.category.name) allOptions.courseCategories.add(course.category.name);
                        if (course.category.code) allOptions.courseCategoryCodes.add(course.category.code);
                    }
                    if (course.type) allOptions.courseTypes.add(course.type);
                    if (course.status) allOptions.courseStatuses.add(course.status);
                    if (course.language && course.language.name) allOptions.languages.add(course.language.name);
                });
                
                console.log(`✅ Found ${allOptions.courseCategories.size} categories, ${allOptions.courseTypes.size} types, ${allOptions.courseStatuses.size} statuses`);
            }
        } catch (courseError) {
            console.error('❌ Error fetching courses:', courseError.message);
        }

        // Step 2: Get detailed user field mappings
        console.log('\n👥 ANALYZING USER FIELD MAPPINGS...');
        console.log('-'.repeat(50));
        
        try {
            const usersData = await doceboService.getTotalUserListedInfo();
            
            if (usersData && usersData.length > 0) {
                console.log(`📋 Analyzing ${usersData.length} users...`);
                
                // Get detailed info for first 20 users to map field options
                const sampleUsers = usersData.slice(0, 20);
                
                for (const user of sampleUsers) {
                    try {
                        const userDetails = await doceboService.getUserInfo(user.user_id);
                        
                        if (userDetails && userDetails.data && userDetails.data.additional_fields) {
                            userDetails.data.additional_fields.forEach(field => {
                                // Map field options to get actual text values
                                if (field.options && Array.isArray(field.options)) {
                                    field.options.forEach(option => {
                                        switch(field.id) {
                                            case "9": // Role Type
                                                allOptions.userRoles.set(option.id, option.label);
                                                break;
                                            case "10": // Employment Type
                                                allOptions.employmentTypes.set(option.id, option.label);
                                                break;
                                            case "12": // Race Identity
                                                allOptions.raceIdentities.set(option.id, option.label);
                                                break;
                                            case "13": // Gender Identity
                                                allOptions.genderIdentities.set(option.id, option.label);
                                                break;
                                            case "20": // Initiative
                                                allOptions.initiatives.set(option.id, option.label);
                                                break;
                                            case "21": // Organization Type
                                                allOptions.organizationTypes.set(option.id, option.label);
                                                break;
                                            case "25": // State
                                                allOptions.states.set(option.id, option.label);
                                                break;
                                        }
                                    });
                                }
                                
                                // Also collect actual values being used
                                if (field.enabled && field.value) {
                                    switch(field.id) {
                                        case "9": // Role Type
                                            if (!allOptions.userRoles.has(field.value)) {
                                                allOptions.userRoles.set(field.value, `Role ${field.value}`);
                                            }
                                            break;
                                        case "10": // Employment Type
                                            if (!allOptions.employmentTypes.has(field.value)) {
                                                allOptions.employmentTypes.set(field.value, `Employment ${field.value}`);
                                            }
                                            break;
                                        case "12": // Race Identity
                                            if (!allOptions.raceIdentities.has(field.value)) {
                                                allOptions.raceIdentities.set(field.value, `Race ${field.value}`);
                                            }
                                            break;
                                        case "13": // Gender Identity
                                            if (!allOptions.genderIdentities.has(field.value)) {
                                                allOptions.genderIdentities.set(field.value, `Gender ${field.value}`);
                                            }
                                            break;
                                        case "20": // Initiative
                                            if (!allOptions.initiatives.has(field.value)) {
                                                allOptions.initiatives.set(field.value, `Initiative ${field.value}`);
                                            }
                                            break;
                                        case "21": // Organization Type
                                            if (!allOptions.organizationTypes.has(field.value)) {
                                                allOptions.organizationTypes.set(field.value, `Org Type ${field.value}`);
                                            }
                                            break;
                                    }
                                }
                            });
                        }
                        
                        // Collect user levels and languages
                        if (user.level) allOptions.userLevels.add(user.level);
                        if (user.language) allOptions.languages.add(user.language);
                        if (user.valid !== undefined) {
                            allOptions.userStatuses.add(user.valid === '1' ? 'Active' : 'Inactive');
                        }
                        
                        // Get branch information
                        if (userDetails && userDetails.data && userDetails.data.branches) {
                            userDetails.data.branches.forEach(branch => {
                                if (branch.name) allOptions.branchNames.add(branch.name);
                            });
                        }
                        
                    } catch (userDetailError) {
                        console.log(`   ⚠️ Could not get details for user ${user.user_id}: ${userDetailError.message}`);
                    }
                }
                
                console.log(`✅ Mapped field options from ${sampleUsers.length} users`);
            }
        } catch (userError) {
            console.error('❌ Error fetching users:', userError.message);
        }

        // Step 3: Display comprehensive results
        console.log('\n📊 COMPREHENSIVE DOCEBO OPTIONS:');
        console.log('=' .repeat(70));
        
        const displaySetOptions = (title, optionsSet, fieldName) => {
            console.log(`\n🔧 ${title} (${optionsSet.size} values):`);
            console.log(`   Salesforce Field: ${fieldName}`);
            if (optionsSet.size > 0) {
                const sortedOptions = Array.from(optionsSet).sort();
                sortedOptions.forEach(option => {
                    console.log(`     • "${option}"`);
                });
            } else {
                console.log(`     ❌ No values found`);
            }
        };

        const displayMapOptions = (title, optionsMap, fieldName) => {
            console.log(`\n🔧 ${title} (${optionsMap.size} values):`);
            console.log(`   Salesforce Field: ${fieldName}`);
            if (optionsMap.size > 0) {
                const sortedOptions = Array.from(optionsMap.entries()).sort((a, b) => a[1].localeCompare(b[1]));
                sortedOptions.forEach(([id, label]) => {
                    console.log(`     • "${label}" (ID: ${id})`);
                });
            } else {
                console.log(`     ❌ No values found`);
            }
        };

        displaySetOptions('COURSE CATEGORIES', allOptions.courseCategories, 'Course_Category__c');
        displaySetOptions('COURSE CATEGORY CODES', allOptions.courseCategoryCodes, 'Course_Category_Code__c');
        displaySetOptions('COURSE TYPES', allOptions.courseTypes, 'Course_Type__c / Type__c');
        displaySetOptions('COURSE STATUSES', allOptions.courseStatuses, 'Course_Status__c');
        displaySetOptions('LANGUAGES', allOptions.languages, 'Language__c / Languages__c');
        
        displayMapOptions('USER ROLES', allOptions.userRoles, 'Role_Type__c');
        displayMapOptions('EMPLOYMENT TYPES', allOptions.employmentTypes, 'Employment_Type__c');
        displayMapOptions('GENDER IDENTITIES', allOptions.genderIdentities, 'GenderIdentity / Gender_Identity__c');
        displayMapOptions('RACE IDENTITIES', allOptions.raceIdentities, 'Race__c / Race_Identity__c');
        displayMapOptions('INITIATIVES', allOptions.initiatives, 'Initiative__c');
        displayMapOptions('ORGANIZATION TYPES', allOptions.organizationTypes, 'Organization_Type__c');
        displayMapOptions('STATES', allOptions.states, 'State__c / mailingstate__c');
        
        displaySetOptions('BRANCH NAMES', allOptions.branchNames, 'Branch_Name__c');
        displaySetOptions('USER LEVELS', allOptions.userLevels, 'User_Level__c');
        displaySetOptions('USER STATUSES', allOptions.userStatuses, 'User_Status__c');

        // Step 4: Generate Salesforce picklist values (text only)
        console.log('\n📋 SALESFORCE PICKLIST VALUES (COPY-PASTE READY):');
        console.log('=' .repeat(70));
        
        const generatePicklistValues = (title, optionsSet) => {
            if (optionsSet.size > 0) {
                console.log(`\n${title}:`);
                const sortedOptions = Array.from(optionsSet).sort();
                sortedOptions.forEach(option => {
                    console.log(`${option}`);
                });
            }
        };

        const generateMapPicklistValues = (title, optionsMap) => {
            if (optionsMap.size > 0) {
                console.log(`\n${title}:`);
                const sortedOptions = Array.from(optionsMap.values()).sort();
                sortedOptions.forEach(option => {
                    console.log(`${option}`);
                });
            }
        };

        generatePicklistValues('Course_Category__c', allOptions.courseCategories);
        generatePicklistValues('Course_Type__c', allOptions.courseTypes);
        generateMapPicklistValues('Role_Type__c', allOptions.userRoles);
        generateMapPicklistValues('Employment_Type__c', allOptions.employmentTypes);
        generateMapPicklistValues('GenderIdentity', allOptions.genderIdentities);
        generateMapPicklistValues('Race__c', allOptions.raceIdentities);
        generateMapPicklistValues('Initiative__c', allOptions.initiatives);
        generateMapPicklistValues('Organization_Type__c', allOptions.organizationTypes);
        generateMapPicklistValues('State__c', allOptions.states);

        // Step 5: Summary
        console.log('\n📊 SUMMARY:');
        console.log('=' .repeat(70));
        
        const totalOptions = allOptions.courseCategories.size + allOptions.courseTypes.size + 
                           allOptions.userRoles.size + allOptions.employmentTypes.size + 
                           allOptions.genderIdentities.size + allOptions.raceIdentities.size + 
                           allOptions.initiatives.size + allOptions.organizationTypes.size + 
                           allOptions.states.size;
        
        console.log(`🎯 Total unique picklist values: ${totalOptions}`);
        console.log(`📚 Course-related: ${allOptions.courseCategories.size + allOptions.courseTypes.size}`);
        console.log(`👥 User-related: ${allOptions.userRoles.size + allOptions.employmentTypes.size + allOptions.genderIdentities.size + allOptions.raceIdentities.size}`);
        console.log(`🏢 Organization-related: ${allOptions.initiatives.size + allOptions.organizationTypes.size}`);
        console.log(`📍 Location-related: ${allOptions.states.size}`);

    } catch (error) {
        console.error('💥 Error in getDoceboFieldOptionsDetailed:', error);
    }
}

// Execute the analysis
console.log('🔄 Starting detailed Docebo field options analysis...');
getDoceboFieldOptionsDetailed()
    .then(() => {
        console.log('\n✅ Detailed analysis completed');
        process.exit(0);
    })
    .catch(err => {
        console.error('💥 Analysis failed:', err);
        process.exit(1);
    });
