require('dotenv').config();
const doceboService = require('./platform/docebo/services');

async function testCourseFieldsComprehensive() {
    try {
        console.log('🧪 Testing Comprehensive Course Fields from Docebo API...');
        
        // Test with a sample course ID (you can change this to an actual course ID)
        const testCourseId = 612; // Using one from the learning plan mapping
        
        console.log(`\n📋 Fetching course data for Course ID: ${testCourseId}`);
        
        // Get course info from Docebo API
        const courseResult = await doceboService.getCourseInfo(testCourseId);
        
        if (courseResult && courseResult.status === 200 && courseResult.data) {
            console.log('✅ Course data retrieved successfully from Docebo API');
            
            const courseData = courseResult.data;
            
            console.log('\n📊 COMPREHENSIVE COURSE FIELD ANALYSIS:');
            console.log('=' .repeat(80));
            
            // Your comprehensive field list
            const requiredFields = [
                'Course Unique Id',
                'Course External Id', 
                'Course Internal ID',
                'Course Code',
                'Course Status',
                'Course Name',
                'Description',
                'Course Duration',
                'Course Type',
                'Type',
                'Skills in course',
                'Course Link',
                'Language',
                'User Course Level',
                'Number of actions',
                'Number of sessions',
                'Course Creation Date',
                'Owner',
                'Course Category',
                'Course Category Code',
                'Course Start Date',
                'Course End Date',
                'Session Time (min)',
                'Enrollment Date',
                'Effective',
                'Deleted',
                'Deletion Date',
                'Course has expired',
                'Course Progress (%)',
                'Created By',
                'Last Modified By',
                'Last Update Date',
                'Score',
                'Slug',
                'Thumbnail',
                'Training Material Time (sec)'
            ];
            
            // Map to potential Docebo API field names
            const fieldMapping = {
                'Course Unique Id': ['id', 'course_id', 'unique_id'],
                'Course External Id': ['external_id', 'external_course_id'],
                'Course Internal ID': ['internal_id', 'id'],
                'Course Code': ['code', 'course_code'],
                'Course Status': ['status', 'course_status'],
                'Course Name': ['name', 'title', 'course_name'],
                'Description': ['description'],
                'Course Duration': ['duration', 'course_duration'],
                'Course Type': ['type', 'course_type'],
                'Type': ['type'],
                'Skills in course': ['skills', 'course_skills'],
                'Course Link': ['link', 'url', 'course_url'],
                'Language': ['language', 'lang'],
                'User Course Level': ['level', 'user_level'],
                'Number of actions': ['actions', 'num_actions'],
                'Number of sessions': ['sessions', 'num_sessions'],
                'Course Creation Date': ['creation_date', 'created_at', 'date_created'],
                'Owner': ['owner', 'created_by'],
                'Course Category': ['category', 'course_category'],
                'Course Category Code': ['category_code'],
                'Course Start Date': ['start_date', 'date_begin'],
                'Course End Date': ['end_date', 'date_end'],
                'Session Time (min)': ['session_time', 'duration_minutes'],
                'Enrollment Date': ['enrollment_date'],
                'Effective': ['effective', 'is_effective'],
                'Deleted': ['deleted', 'is_deleted'],
                'Deletion Date': ['deletion_date', 'deleted_at'],
                'Course has expired': ['expired', 'has_expired', 'is_expired'],
                'Course Progress (%)': ['progress', 'completion_percentage'],
                'Created By': ['created_by', 'author'],
                'Last Modified By': ['modified_by', 'last_modified_by'],
                'Last Update Date': ['updated_at', 'last_modified', 'last_update'],
                'Score': ['score'],
                'Slug': ['slug'],
                'Thumbnail': ['thumbnail', 'image'],
                'Training Material Time (sec)': ['training_time', 'material_time']
            };
            
            console.log('\n🔍 FIELD AVAILABILITY ANALYSIS:');
            console.log('-'.repeat(80));
            
            const availableFields = [];
            const missingFields = [];
            
            requiredFields.forEach(fieldName => {
                const possibleKeys = fieldMapping[fieldName] || [fieldName.toLowerCase().replace(/\s+/g, '_')];
                let found = false;
                let foundValue = null;
                let foundKey = null;
                
                for (const key of possibleKeys) {
                    if (courseData.hasOwnProperty(key)) {
                        found = true;
                        foundValue = courseData[key];
                        foundKey = key;
                        break;
                    }
                }
                
                if (found) {
                    availableFields.push({
                        field: fieldName,
                        key: foundKey,
                        value: foundValue
                    });
                    console.log(`✅ ${fieldName}: ${foundKey} = ${foundValue}`);
                } else {
                    missingFields.push(fieldName);
                    console.log(`❌ ${fieldName}: NOT FOUND`);
                }
            });
            
            console.log('\n📊 SUMMARY:');
            console.log('=' .repeat(50));
            console.log(`✅ Available Fields: ${availableFields.length}/${requiredFields.length} (${Math.round(availableFields.length/requiredFields.length*100)}%)`);
            console.log(`❌ Missing Fields: ${missingFields.length}/${requiredFields.length} (${Math.round(missingFields.length/requiredFields.length*100)}%)`);
            
            if (missingFields.length > 0) {
                console.log('\n❌ MISSING FIELDS:');
                missingFields.forEach((field, index) => {
                    console.log(`   ${index + 1}. ${field}`);
                });
            }
            
            console.log('\n📋 COMPLETE DOCEBO COURSE DATA STRUCTURE:');
            console.log('-'.repeat(60));
            console.log(JSON.stringify(courseData, null, 2));
            
            // Save the complete data structure for reference
            const fs = require('fs');
            fs.writeFileSync('docebo-course-data-sample.json', JSON.stringify(courseData, null, 2));
            console.log('\n💾 Complete course data saved to: docebo-course-data-sample.json');
            
            console.log('\n🎯 FIELD MAPPING RECOMMENDATIONS:');
            console.log('-'.repeat(50));
            availableFields.forEach(field => {
                console.log(`${field.field} → ${field.key}`);
            });
            
            return {
                total: requiredFields.length,
                available: availableFields.length,
                missing: missingFields.length,
                availableFields: availableFields,
                missingFields: missingFields,
                courseData: courseData
            };
            
        } else {
            console.log('❌ Failed to retrieve course data from Docebo API');
            console.log('Response:', courseResult);
            return null;
        }
        
    } catch (error) {
        console.error('💥 Error testing course fields:', error);
        return null;
    }
}

// Execute the test
console.log('🔄 Starting comprehensive course fields test...');
testCourseFieldsComprehensive()
    .then((result) => {
        if (result) {
            console.log(`\n✅ Test completed successfully!`);
            console.log(`📊 Field Coverage: ${result.available}/${result.total} fields available`);
            
            if (result.available === result.total) {
                console.log('🎉 ALL FIELDS ARE AVAILABLE! Your course webhook is ready!');
            } else {
                console.log(`⚠️ ${result.missing} fields need attention or may not be available in Docebo API`);
            }
        } else {
            console.log('\n⚠️ Test completed but no results obtained');
        }
        process.exit(0);
    })
    .catch(err => {
        console.error('💥 Test failed:', err);
        process.exit(1);
    });
