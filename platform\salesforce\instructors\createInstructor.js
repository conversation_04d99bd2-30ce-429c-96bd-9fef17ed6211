const getConnection = require("../common/getConnection");

const instructorTemplate = {
  Name: "",  // UserID field
  First_Name__c: "",
  Last_Name__c: "",
  Email__c: "",
  Idst__c: "",
  Avatar__c: "",
  urlAvatar__c: ""
};

async function createInstructor(instructorData, userId, courseId, sessionId = null, instructorInfo = null) {
  let conn;
  try {
    conn = await getConnection();
  } catch (err) {
    console.error("Error getting Salesforce connection:", err);
    return false;
  }

  if (!conn || !conn.accessToken) {
    console.error("Invalid Salesforce connection in createInstructor");
    return false;
  }

  if (!sessionId) {
    console.error("Session ID is required for instructor creation");
    return false;
  }

  // Extract instructor information
  let firstName = "";
  let lastName = "";
  let email = "";
  let idst = "";
  let avatar = "";
  let urlAvatar = "";

  if (instructorInfo) {
    firstName = instructorInfo.firstname || "";
    lastName = instructorInfo.lastname || "";
  }

  if (instructorData && instructorData.instructors && instructorData.instructors.length > 0) {
    const instructor = instructorData.instructors[0]; // Get first instructor from API response
    firstName = firstName || instructor.firstname || "";
    lastName = lastName || instructor.lastname || "";
    email = instructor.email || "";
    idst = instructor.idst || "";
    avatar = instructor.avatar || "";
    urlAvatar = instructor.urlAvatar || "";
  }

  const fullName = `${firstName} ${lastName}`.trim() || `Instructor ${userId}`;
  console.log(`Processing instructor: ${fullName} (User ID: ${userId})`);

  // Check if instructor already exists
  let instructorRecordId = null;
  try {
    const existingInstructor = await conn.sobject("Instructor__c")
      .findOne({ Name: userId.toString() });

    if (existingInstructor) {
      console.log(`Instructor record already exists: ${existingInstructor.Id}`);
      instructorRecordId = existingInstructor.Id;
    }
  } catch (err) {
    console.error("Error checking for existing instructor:", err);
  }

  // Create instructor record if it doesn't exist
  if (!instructorRecordId) {
    const instructorRecord = { ...instructorTemplate };
    instructorRecord.Name = userId.toString(); // UserID as the Name field
    instructorRecord.First_Name__c = firstName;
    instructorRecord.Last_Name__c = lastName;
    instructorRecord.Email__c = email;
    instructorRecord.Idst__c = idst;
    instructorRecord.Avatar__c = avatar;
    instructorRecord.urlAvatar__c = urlAvatar;

    try {
      const result = await conn.sobject("Instructor__c").create(instructorRecord);

      if (result.success) {
        instructorRecordId = result.id;
        console.log(`Instructor record created successfully: ${instructorRecordId}`);
      } else {
        console.error("Error creating instructor record:", result.errors);
        return false;
      }
    } catch (err) {
      console.error("Error creating instructor record:", err);
      return false;
    }
  }

  // Now link the instructor to the session
  if (instructorRecordId) {
    try {
      // Get the session Salesforce ID
      const sessionRecord = await conn.sobject("Docebo_Session__c")
        .findOne({ Session_External_ID__c: sessionId.toString() });

      if (sessionRecord) {
        // Update the session with the instructor lookup
        const updateResult = await conn.sobject("Docebo_Session__c")
          .update({
            Id: sessionRecord.Id,
            Instructor__c: instructorRecordId
          });

        if (updateResult.success) {
          console.log(`Session updated with instructor lookup: ${sessionRecord.Id}`);
          console.log(`Instructor ${fullName} linked to session successfully`);
          return true;
        } else {
          console.error("Error updating session with instructor:", updateResult.errors);
          return false;
        }
      } else {
        console.error(`Session not found with External ID: ${sessionId}`);
        return false;
      }
    } catch (err) {
      console.error("Error linking instructor to session:", err);
      return false;
    }
  }

  return false;
}

module.exports = {
  createInstructor
};