require('dotenv').config();
const doceboServices = require('./platform/docebo/services');
const { createInstructor } = require('./platform/salesforce/instructors/createInstructor');
const { getCourseSalesForceId } = require('./platform/salesforce/courses/createCourse');
const { getIltSessionSalesForceId } = require('./platform/salesforce/session/createSession');

// Configuration for small test
const MAX_COURSES_TO_TEST = 5; // Only test first 5 courses
const DELAY_BETWEEN_REQUESTS = 300; // 0.3 seconds between requests

function delay(ms) {
  return new Promise(resolve => setTimeout(resolve, ms));
}

async function testInstructorAssociationsSmall() {
  try {
    console.log('🧪 Starting small test of instructor association process...');
    console.log(`📋 Will test maximum ${MAX_COURSES_TO_TEST} courses`);
    
    // Get all courses
    console.log('\n📚 Fetching courses from Docebo...');
    const allCourses = await doceboServices.getTotalCourseListedInfo();
    
    if (!allCourses || allCourses.length === 0) {
      console.log('❌ No courses found in Docebo');
      return;
    }
    
    // Limit to first few courses for testing
    const coursesToTest = allCourses.slice(0, MAX_COURSES_TO_TEST);
    console.log(`✅ Testing ${coursesToTest.length} courses out of ${allCourses.length} total`);
    
    let stats = {
      coursesProcessed: 0,
      coursesWithSessions: 0,
      totalSessions: 0,
      sessionsWithInstructors: 0,
      totalInstructors: 0,
      successfulAssociations: 0,
      errors: 0
    };
    
    // Process each course
    for (let i = 0; i < coursesToTest.length; i++) {
      const course = coursesToTest[i];
      console.log(`\n📖 Testing course ${i + 1}/${coursesToTest.length}: ${course.name} (ID: ${course.id})`);
      
      try {
        // Check if course exists in Salesforce
        const courseSalesforceId = await getCourseSalesForceId(course.id);
        if (!courseSalesforceId) {
          console.log(`   ⚠️  Course not found in Salesforce, skipping...`);
          stats.coursesProcessed++;
          continue;
        }
        
        console.log(`   ✅ Course found in Salesforce: ${courseSalesforceId}`);
        
        // Get sessions for this course
        const sessionsResult = await doceboServices.getSessionListedInfo(course.id);
        
        if (!sessionsResult || sessionsResult.length === 0) {
          console.log(`   ℹ️  No sessions found for this course`);
          stats.coursesProcessed++;
          continue;
        }
        
        stats.coursesWithSessions++;
        stats.totalSessions += sessionsResult.length;
        console.log(`   📅 Found ${sessionsResult.length} sessions`);
        
        // Process each session
        for (const session of sessionsResult) {
          console.log(`\n      🎯 Checking session: ${session.id}`);
          
          try {
            // Check if session exists in Salesforce
            const sessionSalesforceId = await getIltSessionSalesForceId(session.id);
            if (!sessionSalesforceId) {
              console.log(`         ⚠️  Session not found in Salesforce, skipping...`);
              continue;
            }
            
            console.log(`         ✅ Session found in Salesforce: ${sessionSalesforceId}`);
            
            // Get detailed session info
            const sessionInfo = await doceboServices.getCourseSessionInfo(session.id);
            
            if (!sessionInfo || sessionInfo.status !== 200 || !sessionInfo.data) {
              console.log(`         ❌ Failed to retrieve session data`);
              stats.errors++;
              continue;
            }
            
            const sessionData = sessionInfo.data;
            console.log(`         📝 Session name: ${sessionData.name}`);
            
            // Check if session has instructors
            if (!sessionData.instructors || sessionData.instructors.length === 0) {
              console.log(`         ℹ️  No instructors found in this session`);
              continue;
            }
            
            stats.sessionsWithInstructors++;
            stats.totalInstructors += sessionData.instructors.length;
            console.log(`         👥 Found ${sessionData.instructors.length} instructors`);
            
            // Process each instructor
            for (const instructor of sessionData.instructors) {
              console.log(`\n            👤 Processing: ${instructor.firstname} ${instructor.lastname} (ID: ${instructor.user_id})`);
              
              try {
                // Get instructor data from Docebo
                const instructorResponse = await doceboServices.getInstructorData(
                  instructor.user_id,
                  course.id,
                  session.id
                );
                
                if (!instructorResponse || instructorResponse.status !== 200 || !instructorResponse.data) {
                  console.log(`               ❌ Failed to retrieve instructor data`);
                  stats.errors++;
                  continue;
                }
                
                console.log(`               📋 Instructor data retrieved successfully`);
                
                // Create/update instructor in Salesforce
                const result = await createInstructor(
                  instructorResponse.data,
                  instructor.user_id,
                  course.id,
                  session.id,
                  instructor
                );
                
                if (result) {
                  console.log(`               ✅ Instructor successfully associated!`);
                  stats.successfulAssociations++;
                } else {
                  console.log(`               ❌ Failed to associate instructor`);
                  stats.errors++;
                }
                
                await delay(DELAY_BETWEEN_REQUESTS);
                
              } catch (instructorError) {
                console.error(`               ❌ Error processing instructor:`, instructorError.message);
                stats.errors++;
              }
            }
            
            await delay(DELAY_BETWEEN_REQUESTS);
            
          } catch (sessionError) {
            console.error(`         ❌ Error processing session:`, sessionError.message);
            stats.errors++;
          }
        }
        
        stats.coursesProcessed++;
        await delay(DELAY_BETWEEN_REQUESTS);
        
      } catch (courseError) {
        console.error(`❌ Error processing course:`, courseError.message);
        stats.errors++;
        stats.coursesProcessed++;
      }
    }
    
    // Test summary
    console.log('\n🧪 Small test completed!');
    console.log('📊 Test Results:');
    console.log(`   Courses tested: ${stats.coursesProcessed}/${coursesToTest.length}`);
    console.log(`   Courses with sessions: ${stats.coursesWithSessions}`);
    console.log(`   Total sessions found: ${stats.totalSessions}`);
    console.log(`   Sessions with instructors: ${stats.sessionsWithInstructors}`);
    console.log(`   Total instructors found: ${stats.totalInstructors}`);
    console.log(`   Successful associations: ${stats.successfulAssociations}`);
    console.log(`   Errors encountered: ${stats.errors}`);
    
    if (stats.totalInstructors > 0) {
      const successRate = ((stats.successfulAssociations / stats.totalInstructors) * 100).toFixed(1);
      console.log(`   Success rate: ${successRate}%`);
    }
    
    if (stats.successfulAssociations > 0) {
      console.log('\n✅ Test successful! You can now run the full batch process.');
      console.log('   Run: node process-instructor-associations-batch.js');
    } else if (stats.totalInstructors === 0) {
      console.log('\n⚠️  No instructors found in the tested courses.');
      console.log('   This might be normal if the first few courses don\'t have sessions with instructors.');
    } else {
      console.log('\n❌ Test had issues. Please check the errors above before running the full process.');
    }
    
  } catch (error) {
    console.error('💥 Fatal error in test:', error);
  }
}

// Execute the test
console.log('🔄 Starting small instructor association test...');
testInstructorAssociationsSmall()
  .then(() => {
    console.log('\n✅ Test script completed');
    process.exit(0);
  })
  .catch(err => {
    console.error('💥 Test script failed:', err);
    process.exit(1);
  });
