require('dotenv').config();
const getConnection = require('./platform/salesforce/common/getConnection');

async function checkCourseEnrollmentFields() {
    try {
        console.log('🔍 Checking Docebo_CourseEnrollment__c Object Fields');
        console.log('=' .repeat(70));
        
        const conn = await getConnection();
        if (!conn || !conn.accessToken) {
            console.error("❌ Invalid Salesforce connection");
            return;
        }

        // Step 1: Describe the Docebo_CourseEnrollment__c object
        console.log('\n📋 Step 1: Describing Docebo_CourseEnrollment__c object...');
        
        const objectDescription = await conn.sobject("Docebo_CourseEnrollment__c").describe();
        
        console.log(`📊 Object Label: ${objectDescription.label}`);
        console.log(`📊 Object Name: ${objectDescription.name}`);
        console.log(`📊 Total Fields: ${objectDescription.fields.length}`);
        
        // Step 2: Look for user-related fields
        console.log('\n🔍 Step 2: Looking for user-related fields...');
        
        const userFields = objectDescription.fields.filter(field => 
            field.name.toLowerCase().includes('user') || 
            field.name.toLowerCase().includes('docebo')
        );
        
        console.log(`📊 User-related fields found: ${userFields.length}`);
        
        for (const field of userFields) {
            console.log(`\n📋 Field: ${field.name}`);
            console.log(`   Label: ${field.label}`);
            console.log(`   Type: ${field.type}`);
            console.log(`   Required: ${field.nillable ? 'No' : 'Yes'}`);
            console.log(`   Updateable: ${field.updateable ? 'Yes' : 'No'}`);
            console.log(`   Createable: ${field.createable ? 'Yes' : 'No'}`);
            if (field.referenceTo && field.referenceTo.length > 0) {
                console.log(`   References: ${field.referenceTo.join(', ')}`);
            }
        }
        
        // Step 3: Check if Docebo_User__c field exists
        console.log('\n🎯 Step 3: Checking for Docebo_User__c field specifically...');
        
        const doceboUserField = objectDescription.fields.find(field => field.name === 'Docebo_User__c');
        
        if (doceboUserField) {
            console.log('✅ Docebo_User__c field EXISTS!');
            console.log(`   Label: ${doceboUserField.label}`);
            console.log(`   Type: ${doceboUserField.type}`);
            console.log(`   Required: ${doceboUserField.nillable ? 'No' : 'Yes'}`);
            console.log(`   Updateable: ${doceboUserField.updateable ? 'Yes' : 'No'}`);
            console.log(`   Createable: ${doceboUserField.createable ? 'Yes' : 'No'}`);
            if (doceboUserField.referenceTo && doceboUserField.referenceTo.length > 0) {
                console.log(`   References: ${doceboUserField.referenceTo.join(', ')}`);
            }
            
            if (!doceboUserField.createable) {
                console.log('❌ PROBLEM: Field is not createable!');
                console.log('   This explains the INVALID_FIELD_FOR_INSERT_UPDATE error');
            }
            
            if (!doceboUserField.updateable) {
                console.log('❌ PROBLEM: Field is not updateable!');
                console.log('   This could cause issues with updates');
            }
        } else {
            console.log('❌ Docebo_User__c field DOES NOT EXIST!');
            console.log('   This explains the INVALID_FIELD_FOR_INSERT_UPDATE error');
            
            // Look for alternative field names
            console.log('\n🔍 Looking for alternative user field names...');
            
            const alternativeFields = objectDescription.fields.filter(field => 
                field.type === 'reference' && 
                (field.referenceTo.includes('Docebo_Users__c') || 
                 field.name.toLowerCase().includes('user'))
            );
            
            if (alternativeFields.length > 0) {
                console.log('📋 Possible alternative fields:');
                for (const field of alternativeFields) {
                    console.log(`   ${field.name} (${field.label}) -> ${field.referenceTo.join(', ')}`);
                }
            } else {
                console.log('⚠️ No alternative user reference fields found');
            }
        }
        
        // Step 4: Check all lookup/reference fields
        console.log('\n📋 Step 4: All lookup/reference fields:');
        
        const referenceFields = objectDescription.fields.filter(field => field.type === 'reference');
        
        console.log(`📊 Total reference fields: ${referenceFields.length}`);
        
        for (const field of referenceFields) {
            console.log(`   ${field.name} (${field.label}) -> ${field.referenceTo.join(', ')}`);
        }
        
        // Step 5: Test creating a minimal record to see what's required
        console.log('\n🧪 Step 5: Testing minimal record creation...');
        
        try {
            // Try to create a minimal record to see what fields are actually required
            const testRecord = {
                // Only include fields that definitely exist
                Enrollment_ID__c: 'TEST_FIELD_CHECK'
            };
            
            console.log('🔄 Attempting to create test record with minimal fields...');
            const testResult = await conn.sobject("Docebo_CourseEnrollment__c").create(testRecord);
            
            if (testResult.success) {
                console.log('✅ Test record created successfully');
                console.log(`   Record ID: ${testResult.id}`);
                
                // Clean up the test record
                await conn.sobject("Docebo_CourseEnrollment__c").delete(testResult.id);
                console.log('🗑️ Test record deleted');
            } else {
                console.log('❌ Test record creation failed:');
                console.log('   Errors:', JSON.stringify(testResult.errors, null, 2));
                
                // Analyze the errors to understand required fields
                if (testResult.errors) {
                    for (const error of testResult.errors) {
                        if (error.statusCode === 'REQUIRED_FIELD_MISSING') {
                            console.log(`   Required field missing: ${error.fields.join(', ')}`);
                        }
                    }
                }
            }
        } catch (testError) {
            console.log('❌ Test record creation error:', testError.message);
        }
        
        // Step 6: Summary and recommendations
        console.log('\n📊 SUMMARY AND RECOMMENDATIONS:');
        console.log('=' .repeat(70));
        
        if (doceboUserField) {
            if (doceboUserField.createable && doceboUserField.updateable) {
                console.log('✅ Docebo_User__c field is available and usable');
                console.log('   The error might be due to:');
                console.log('   • Missing required fields in the record');
                console.log('   • Invalid reference ID being used');
                console.log('   • Permission issues with the specific record');
            } else {
                console.log('❌ Docebo_User__c field exists but has permission issues');
                console.log('   Field permissions need to be updated in Salesforce');
            }
        } else {
            console.log('❌ Docebo_User__c field does not exist');
            console.log('   Need to either:');
            console.log('   • Create the field in Salesforce');
            console.log('   • Use a different field name');
            console.log('   • Update the code to use correct field name');
        }

        return {
            success: true,
            fieldExists: !!doceboUserField,
            fieldCreateable: doceboUserField ? doceboUserField.createable : false,
            fieldUpdateable: doceboUserField ? doceboUserField.updateable : false,
            alternativeFields: objectDescription.fields.filter(field => 
                field.type === 'reference' && 
                field.referenceTo.includes('Docebo_Users__c')
            )
        };

    } catch (error) {
        console.error('💥 Error checking course enrollment fields:', error);
        return {
            success: false,
            error: error.message
        };
    }
}

// Execute the check
console.log('🔄 Starting course enrollment fields check...');
checkCourseEnrollmentFields()
    .then((result) => {
        console.log('\n✅ Course enrollment fields check completed');
        if (result.success) {
            if (!result.fieldExists) {
                console.log('🚨 FIELD MISSING: Docebo_User__c field does not exist!');
                console.log('🔧 Action needed: Create field or update code to use correct field name');
            } else if (!result.fieldCreateable || !result.fieldUpdateable) {
                console.log('🚨 PERMISSION ISSUE: Docebo_User__c field has permission restrictions');
                console.log('🔧 Action needed: Update field permissions in Salesforce');
            } else {
                console.log('✅ Docebo_User__c field is properly configured');
                console.log('🔍 The error might be due to other issues (invalid data, missing required fields, etc.)');
            }
        } else {
            console.log('❌ Check failed. See error details above.');
        }
        process.exit(0);
    })
    .catch(err => {
        console.error('💥 Check failed:', err);
        process.exit(1);
    });
