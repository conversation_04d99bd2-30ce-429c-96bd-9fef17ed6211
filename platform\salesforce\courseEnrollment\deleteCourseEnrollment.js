const getConnection = require("../common/getConnection");
const { getCourseEnrollmentSalesForceId } = require("./createCourseEnrollment");

async function deleteCourseEnrollment(courseId, userId) {
    try {
        const conn = await getConnection();
        if (!conn || !conn.accessToken) {
            console.error("Invalid Salesforce connection in deleteCourseEnrollment");
            return "connection failed";
        }
        
        // Get the Salesforce ID of the enrollment using the existing helper function
        const enrollmentId = await getCourseEnrollmentSalesForceId(courseId, userId);
        
        if (!enrollmentId) {
            console.log(`Enrollment not found for deletion: Course ID ${courseId}, User ID ${userId}`);
            return "not found";
        }
        
        // Delete the enrollment record
        const deleteResult = await conn.sobject("Docebo_CourseEnrollment__c")
            .destroy(enrollmentId);
            
        if (deleteResult.success) {
            console.log(`Successfully deleted course enrollment: Course ID ${courseId}, User ID ${userId}`);
            return "success";
        } else {
            console.error(`Failed to delete course enrollment: Course ID ${courseId}, User ID ${userId}`, deleteResult.errors);
            return "deletion failed";
        }
    } catch (err) {
        console.error("Error in deleteCourseEnrollment:", err);
        return "error";
    }
}

module.exports = {
    deleteCourseEnrollment
};