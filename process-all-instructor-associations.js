require('dotenv').config();
const doceboServices = require('./platform/docebo/services');
const { createInstructor } = require('./platform/salesforce/instructors/createInstructor');
const { getCourseSalesForceId } = require('./platform/salesforce/courses/createCourse');
const { getIltSessionSalesForceId } = require('./platform/salesforce/session/createSession');

// Add delay function to avoid rate limiting
function delay(ms) {
  return new Promise(resolve => setTimeout(resolve, ms));
}

async function processAllInstructorAssociations() {
  try {
    console.log('🚀 Starting instructor association process for all courses...');
    
    // Step 1: Get all courses from Docebo
    console.log('\n📚 Fetching all courses from Docebo...');
    const allCourses = await doceboServices.getTotalCourseListedInfo();
    
    if (!allCourses || allCourses.length === 0) {
      console.log('❌ No courses found in Docebo');
      return;
    }
    
    console.log(`✅ Found ${allCourses.length} courses in Docebo`);
    
    let totalProcessed = 0;
    let totalSessions = 0;
    let totalInstructors = 0;
    let successfulAssociations = 0;
    let errors = 0;
    
    // Step 2: Process each course
    for (let i = 0; i < allCourses.length; i++) {
      const course = allCourses[i];
      console.log(`\n📖 Processing course ${i + 1}/${allCourses.length}: ${course.name} (ID: ${course.id})`);
      
      try {
        // Check if course exists in Salesforce
        const courseSalesforceId = await getCourseSalesForceId(course.id);
        if (!courseSalesforceId) {
          console.log(`⚠️  Course ${course.id} not found in Salesforce, skipping...`);
          continue;
        }
        
        // Get sessions for this course
        const sessionsResult = await doceboServices.getSessionListedInfo(course.id);
        
        if (!sessionsResult || sessionsResult.length === 0) {
          console.log(`   ℹ️  No sessions found for course ${course.id}`);
          continue;
        }
        
        console.log(`   📅 Found ${sessionsResult.length} sessions for course ${course.id}`);
        totalSessions += sessionsResult.length;
        
        // Process each session
        for (const session of sessionsResult) {
          console.log(`\n   🎯 Processing session: ${session.id}`);
          
          try {
            // Check if session exists in Salesforce
            const sessionSalesforceId = await getIltSessionSalesForceId(session.id);
            if (!sessionSalesforceId) {
              console.log(`      ⚠️  Session ${session.id} not found in Salesforce, skipping...`);
              continue;
            }
            
            // Get detailed session info
            const sessionInfo = await doceboServices.getCourseSessionInfo(session.id);
            
            if (!sessionInfo || sessionInfo.status !== 200 || !sessionInfo.data) {
              console.log(`      ❌ Failed to retrieve data for session ${session.id}`);
              errors++;
              continue;
            }
            
            const sessionData = sessionInfo.data;
            
            // Check if session has instructors
            if (!sessionData.instructors || sessionData.instructors.length === 0) {
              console.log(`      ℹ️  No instructors found for session ${session.id}`);
              continue;
            }
            
            console.log(`      👥 Found ${sessionData.instructors.length} instructors for session ${session.id}`);
            totalInstructors += sessionData.instructors.length;
            
            // Process each instructor
            for (const instructor of sessionData.instructors) {
              console.log(`      👤 Processing instructor: ${instructor.firstname} ${instructor.lastname} (ID: ${instructor.user_id})`);
              
              try {
                // Get instructor data from Docebo
                const instructorResponse = await doceboServices.getInstructorData(
                  instructor.user_id,
                  course.id,
                  session.id
                );
                
                if (!instructorResponse || instructorResponse.status !== 200 || !instructorResponse.data) {
                  console.log(`         ❌ Failed to retrieve instructor data for user ID: ${instructor.user_id}`);
                  errors++;
                  continue;
                }
                
                // Create/update instructor in Salesforce
                const result = await createInstructor(
                  instructorResponse.data,
                  instructor.user_id,
                  course.id,
                  session.id,
                  instructor
                );
                
                if (result) {
                  console.log(`         ✅ Instructor successfully associated`);
                  successfulAssociations++;
                } else {
                  console.log(`         ❌ Failed to associate instructor`);
                  errors++;
                }
                
                // Add small delay to avoid overwhelming the APIs
                await delay(100);
                
              } catch (instructorError) {
                console.error(`         ❌ Error processing instructor ${instructor.user_id}:`, instructorError.message);
                errors++;
              }
            }
            
          } catch (sessionError) {
            console.error(`      ❌ Error processing session ${session.id}:`, sessionError.message);
            errors++;
          }
          
          // Add delay between sessions
          await delay(200);
        }
        
        totalProcessed++;
        
      } catch (courseError) {
        console.error(`❌ Error processing course ${course.id}:`, courseError.message);
        errors++;
      }
      
      // Add delay between courses
      await delay(500);
      
      // Progress update every 10 courses
      if ((i + 1) % 10 === 0) {
        console.log(`\n📊 Progress: ${i + 1}/${allCourses.length} courses processed`);
        console.log(`   Sessions: ${totalSessions}, Instructors: ${totalInstructors}`);
        console.log(`   Successful: ${successfulAssociations}, Errors: ${errors}`);
      }
    }
    
    // Final summary
    console.log('\n🎉 Instructor association process completed!');
    console.log('📊 Final Summary:');
    console.log(`   Total courses processed: ${totalProcessed}/${allCourses.length}`);
    console.log(`   Total sessions found: ${totalSessions}`);
    console.log(`   Total instructors found: ${totalInstructors}`);
    console.log(`   Successful associations: ${successfulAssociations}`);
    console.log(`   Errors encountered: ${errors}`);
    
    if (errors > 0) {
      console.log(`\n⚠️  ${errors} errors occurred during processing. Check the logs above for details.`);
    }
    
  } catch (error) {
    console.error('💥 Fatal error in instructor association process:', error);
  }
}

// Execute the script
console.log('🔄 Starting bulk instructor association process...');
processAllInstructorAssociations()
  .then(() => {
    console.log('\n✅ Script completed successfully');
    process.exit(0);
  })
  .catch(err => {
    console.error('💥 Script failed:', err);
    process.exit(1);
  });
