require('dotenv').config();
const getConnection = require('./platform/salesforce/common/getConnection');

async function currentLearningPlanCourseEnrollmentCheck() {
    try {
        console.log('🔍 CURRENT LEARNING PLAN COURSE ENROLLMENT CHECK');
        console.log('=' .repeat(70));
        
        const conn = await getConnection();
        if (!conn || !conn.accessToken) {
            throw new Error('Failed to connect to Salesforce');
        }

        console.log('✅ Connected to Salesforce successfully');

        // Step 1: Get current count
        console.log('\n📊 STEP 1: Getting Current Count...');
        console.log('-'.repeat(40));
        
        const countResult = await conn.query("SELECT COUNT() FROM Docebo_Learning_Plan_Course_Enrollment__c");
        console.log(`📊 Total Learning Plan Course Enrollments: ${countResult.totalSize.toLocaleString()}`);

        // Step 2: Get recent records to see what's being created
        console.log('\n📋 STEP 2: Getting Recent Records...');
        console.log('-'.repeat(40));
        
        const recentRecords = await conn.query(`
            SELECT Id, Name, Learning_Plan__c, Course__c, User__c, Status__c, 
                   Enrollment_Date__c, CreatedDate
            FROM Docebo_Learning_Plan_Course_Enrollment__c 
            ORDER BY CreatedDate DESC 
            LIMIT 10
        `);
        
        console.log(`Found ${recentRecords.totalSize} total records, showing 10 most recent:`);
        
        recentRecords.records.forEach((record, index) => {
            console.log(`\n   ${index + 1}. ${record.Name || record.Id}`);
            console.log(`      Learning Plan: ${record.Learning_Plan__c}`);
            console.log(`      Course: ${record.Course__c}`);
            console.log(`      User: ${record.User__c}`);
            console.log(`      Status: ${record.Status__c}`);
            console.log(`      Enrollment Date: ${record.Enrollment_Date__c}`);
            console.log(`      Created: ${record.CreatedDate}`);
        });

        // Step 3: Get creation date distribution
        console.log('\n📅 STEP 3: Creation Date Distribution...');
        console.log('-'.repeat(40));
        
        const dateDistribution = await conn.query(`
            SELECT CALENDAR_YEAR(CreatedDate) year, CALENDAR_MONTH(CreatedDate) month, COUNT(Id) count
            FROM Docebo_Learning_Plan_Course_Enrollment__c 
            GROUP BY CALENDAR_YEAR(CreatedDate), CALENDAR_MONTH(CreatedDate)
            ORDER BY CALENDAR_YEAR(CreatedDate) DESC, CALENDAR_MONTH(CreatedDate) DESC
        `);
        
        console.log('Creation date distribution:');
        dateDistribution.records.forEach(record => {
            const monthName = new Date(2000, record.month - 1, 1).toLocaleString('default', { month: 'long' });
            console.log(`   ${monthName} ${record.year}: ${record.count.toLocaleString()} records`);
        });

        // Step 4: Get status distribution
        console.log('\n📈 STEP 4: Status Distribution...');
        console.log('-'.repeat(40));
        
        const statusDistribution = await conn.query(`
            SELECT Status__c, COUNT(Id) count
            FROM Docebo_Learning_Plan_Course_Enrollment__c 
            GROUP BY Status__c
            ORDER BY COUNT(Id) DESC
        `);
        
        console.log('Status distribution:');
        statusDistribution.records.forEach(record => {
            console.log(`   ${record.Status__c || 'null'}: ${record.count.toLocaleString()} records`);
        });

        // Step 5: Check if sync is still running
        console.log('\n🔄 STEP 5: Checking Recent Activity...');
        console.log('-'.repeat(40));
        
        const recentActivity = await conn.query(`
            SELECT COUNT() 
            FROM Docebo_Learning_Plan_Course_Enrollment__c 
            WHERE CreatedDate = TODAY
        `);
        
        console.log(`Records created today: ${recentActivity.totalSize.toLocaleString()}`);
        
        const lastHourActivity = await conn.query(`
            SELECT COUNT() 
            FROM Docebo_Learning_Plan_Course_Enrollment__c 
            WHERE CreatedDate >= ${new Date(Date.now() - 60 * 60 * 1000).toISOString()}
        `);
        
        console.log(`Records created in last hour: ${lastHourActivity.totalSize.toLocaleString()}`);

        // Step 6: Compare with expected numbers
        console.log('\n🎯 STEP 6: Expected vs Actual Analysis...');
        console.log('-'.repeat(40));
        
        // Get learning plan count
        const lpCount = await conn.query("SELECT COUNT() FROM Docebo_Learning_Plan__c");
        console.log(`Learning Plans in Salesforce: ${lpCount.totalSize.toLocaleString()}`);
        
        // Get course count
        const courseCount = await conn.query("SELECT COUNT() FROM Docebo_Course__c");
        console.log(`Courses in Salesforce: ${courseCount.totalSize.toLocaleString()}`);
        
        // Get user count
        const userCount = await conn.query("SELECT COUNT() FROM Docebo_Users__c");
        console.log(`Users in Salesforce: ${userCount.totalSize.toLocaleString()}`);
        
        // Estimate expected enrollments
        const avgCoursesPerLP = 6; // Based on our earlier analysis
        const avgUsersPerLP = 186; // Based on our sync results
        const estimatedTotal = lpCount.totalSize * avgUsersPerLP;
        
        console.log(`\nEstimated total course enrollments: ${estimatedTotal.toLocaleString()}`);
        console.log(`Current actual enrollments: ${countResult.totalSize.toLocaleString()}`);
        
        const completionPercentage = (countResult.totalSize / estimatedTotal * 100).toFixed(1);
        console.log(`Completion percentage: ${completionPercentage}%`);
        
        if (countResult.totalSize >= estimatedTotal * 0.9) {
            console.log(`🎉 SYNC APPEARS COMPLETE! (90%+ of expected records)`);
        } else if (lastHourActivity.totalSize > 0) {
            console.log(`🔄 SYNC APPEARS TO BE RUNNING (recent activity detected)`);
        } else {
            console.log(`⚠️ SYNC MAY BE INCOMPLETE (less than 90% and no recent activity)`);
        }

        return {
            success: true,
            totalRecords: countResult.totalSize,
            todayRecords: recentActivity.totalSize,
            lastHourRecords: lastHourActivity.totalSize,
            estimatedTotal: estimatedTotal,
            completionPercentage: parseFloat(completionPercentage)
        };

    } catch (error) {
        console.error('💥 Error checking learning plan course enrollment status:', error);
        return {
            success: false,
            error: error.message
        };
    }
}

console.log('🔄 Starting Current Learning Plan Course Enrollment Check...');
currentLearningPlanCourseEnrollmentCheck()
    .then((result) => {
        console.log('\n📋 CURRENT LEARNING PLAN COURSE ENROLLMENT STATUS:');
        console.log('=' .repeat(60));
        
        if (result.success) {
            console.log(`📊 Total Records: ${result.totalRecords.toLocaleString()}`);
            console.log(`📅 Created Today: ${result.todayRecords.toLocaleString()}`);
            console.log(`🕐 Created Last Hour: ${result.lastHourRecords.toLocaleString()}`);
            console.log(`🎯 Estimated Total: ${result.estimatedTotal.toLocaleString()}`);
            console.log(`📈 Completion: ${result.completionPercentage}%`);
            
            if (result.completionPercentage >= 90) {
                console.log(`\n🎉 SUCCESS: Learning Plan Course Enrollment sync is complete!`);
                console.log(`✅ From 2 records to ${result.totalRecords.toLocaleString()} records - MASSIVE improvement!`);
            } else if (result.lastHourRecords > 0) {
                console.log(`\n🔄 IN PROGRESS: Sync is still running, check back soon!`);
            } else {
                console.log(`\n⚠️ REVIEW NEEDED: Sync may be incomplete`);
            }
        } else {
            console.log(`❌ Status check failed: ${result.error}`);
        }
        
        console.log('\n✅ Current Learning Plan Course Enrollment check completed');
        process.exit(0);
    })
    .catch(err => {
        console.error('💥 Current Learning Plan Course Enrollment check failed:', err);
        process.exit(1);
    });
