require('dotenv').config();
const { createNewUser } = require('./platform/salesforce/users/createUser');
const getConnection = require('./platform/salesforce/common/getConnection');

async function testNoLeadContactAssociations() {
    try {
        console.log('🔧 Testing User Creation WITHOUT Lead/Contact Associations');
        console.log('=' .repeat(70));
        console.log('🎯 TESTING:');
        console.log('   User creation with Lead/Contact associations DISABLED');
        console.log('   Should eliminate FIELD_FILTER_VALIDATION_EXCEPTION errors');
        console.log('   Docebo_Users__c records created without associations');
        console.log('=' .repeat(70));
        
        const conn = await getConnection();
        if (!conn || !conn.accessToken) {
            console.error("❌ Invalid Salesforce connection");
            return;
        }

        // Step 1: Clean up any existing test data
        console.log('\n🧹 Cleaning up existing test data...');
        
        const testEmail = '<EMAIL>';
        
        // Clean up existing records
        const existingDoceboUsers = await conn.sobject("Docebo_Users__c")
            .find({ Email__c: testEmail })
            .execute();
        
        for (const user of existingDoceboUsers) {
            await conn.sobject("Docebo_Users__c").delete(user.Id);
            console.log(`   🗑️ Deleted existing Docebo_Users__c: ${user.Id}`);
        }
        
        const existingLeads = await conn.sobject("Lead")
            .find({ Email: testEmail })
            .execute();
        
        for (const lead of existingLeads) {
            await conn.sobject("Lead").delete(lead.Id);
            console.log(`   🗑️ Deleted existing Lead: ${lead.Id}`);
        }
        
        const existingContacts = await conn.sobject("Contact")
            .find({ Email: testEmail })
            .execute();
        
        for (const contact of existingContacts) {
            await conn.sobject("Contact").delete(contact.Id);
            console.log(`   🗑️ Deleted existing Contact: ${contact.Id}`);
        }

        // Step 2: Create test user data
        console.log('\n📋 Creating test user data...');
        
        const testUserInfo = {
            user_data: {
                user_id: 77777,
                first_name: "No",
                last_name: "Associations Test",
                email: testEmail,
                username: "no.associations.test",
                level: "3",
                language: "english",
                timezone: "America/New_York",
                email_validation_status: "1",
                valid: "1",
                manager_username: ""
            },
            additional_fields: [
                { id: "8", value: "Test No Associations Manager", enabled: true },
                { id: "9", value: "1", enabled: true, options: [{ id: "1", label: "Communications" }] },
                { id: "10", value: "1", enabled: true, options: [{ id: "1", label: "Full-Time" }] },
                { id: "12", value: "1", enabled: true, options: [{ id: "1", label: "Asian" }] },
                { id: "13", value: "1", enabled: true, options: [{ id: "1", label: "Woman" }] },
                { id: "14", value: "Test No Associations Company", enabled: true },
                { id: "20", value: "Test No Associations Initiative", enabled: true },
                { id: "23", value: "https://testnoassociations.com", enabled: true },
                { id: "24", value: "No Associations City", enabled: true },
                { id: "25", value: "1", enabled: true, options: [{ id: "1", label: "Alabama" }] }
            ],
            branches: [
                {
                    name: "Test No Associations Branch",
                    path: "/test/no/associations/branch",
                    codes: "777"
                }
            ],
            fired_at: "2024-01-01 10:00:00",
            expiration_date: "2025-12-31 23:59:59"
        };

        const testUserListedInfo = {
            last_access_date: "2024-06-01 15:30:00"
        };

        console.log('📝 Test user data:');
        console.log(`   User ID: ${testUserInfo.user_data.user_id}`);
        console.log(`   Email: ${testUserInfo.user_data.email}`);
        console.log(`   Name: ${testUserInfo.user_data.first_name} ${testUserInfo.user_data.last_name}`);

        // Step 3: Test user creation
        console.log('\n🧪 Testing user creation WITHOUT Lead/Contact associations...');
        
        const startTime = new Date();
        const result = await createNewUser(testUserInfo, testUserListedInfo);
        const endTime = new Date();

        console.log(`⏱️ User creation took: ${endTime - startTime}ms`);

        // Step 4: Verify the created records (regardless of result)
        console.log('\n🔍 Verifying created records...');

        // Check Docebo_Users__c record
        const doceboUser = await conn.sobject("Docebo_Users__c")
            .findOne({ User_Unique_Id__c: testUserInfo.user_data.user_id });

        if (result) {
            console.log('✅ User creation completed successfully');
            
            if (doceboUser) {
                console.log('\n📊 DOCEBO USER VERIFICATION:');
                console.log(`   Docebo User ID: ${doceboUser.Id}`);
                console.log(`   User Unique ID: ${doceboUser.User_Unique_Id__c}`);
                console.log(`   Email: ${doceboUser.Email__c}`);
                console.log(`   Name: ${doceboUser.First_Name__c} ${doceboUser.Last_Name__c}`);
                console.log(`   Lead Association: ${doceboUser.Lead__c || 'None'}`);
                console.log(`   Contact Association: ${doceboUser.Contact__c || 'None'}`);
                
                // Verify NO associations were created
                const hasLeadAssociation = !!doceboUser.Lead__c;
                const hasContactAssociation = !!doceboUser.Contact__c;
                
                console.log('\n🎯 ASSOCIATION VERIFICATION:');
                console.log(`   Lead Association: ${hasLeadAssociation ? '❌ FOUND' : '✅ NONE'} ${hasLeadAssociation ? '(UNEXPECTED)' : '(EXPECTED)'}`);
                console.log(`   Contact Association: ${hasContactAssociation ? '❌ FOUND' : '✅ NONE'} ${hasContactAssociation ? '(UNEXPECTED)' : '(EXPECTED)'}`);
                
                if (!hasLeadAssociation && !hasContactAssociation) {
                    console.log('\n🎉 ASSOCIATION DISABLING SUCCESS!');
                    console.log('   ✅ No Lead association created');
                    console.log('   ✅ No Contact association created');
                    console.log('   ✅ User created without lookup filter issues');
                    console.log('   ✅ No FIELD_FILTER_VALIDATION_EXCEPTION errors');
                } else {
                    console.log('\n⚠️ Unexpected associations found');
                    console.log('   The disabling may not be complete');
                }
                
                // Check if any Leads were created
                const leadsCreated = await conn.sobject("Lead")
                    .find({ Email: testEmail })
                    .execute();
                
                console.log(`\n📊 LEAD CREATION CHECK: ${leadsCreated.length} Leads found`);
                
                if (leadsCreated.length === 0) {
                    console.log('   ✅ No Leads created (EXPECTED)');
                } else {
                    console.log('   ⚠️ Leads were created despite disabling');
                    for (let i = 0; i < leadsCreated.length; i++) {
                        console.log(`     Lead ${i + 1}: ${leadsCreated[i].Id} (${leadsCreated[i].CreatedDate})`);
                    }
                }
                
                // Check if any Contacts were created
                const contactsCreated = await conn.sobject("Contact")
                    .find({ Email: testEmail })
                    .execute();
                
                console.log(`\n📊 CONTACT CREATION CHECK: ${contactsCreated.length} Contacts found`);
                
                if (contactsCreated.length === 0) {
                    console.log('   ✅ No Contacts created (EXPECTED)');
                } else {
                    console.log('   ⚠️ Contacts were created despite disabling');
                    for (let i = 0; i < contactsCreated.length; i++) {
                        console.log(`     Contact ${i + 1}: ${contactsCreated[i].Id} (${contactsCreated[i].CreatedDate})`);
                    }
                }
                
                // Clean up test data
                console.log('\n🗑️ Cleaning up test data...');
                
                await conn.sobject("Docebo_Users__c").delete(doceboUser.Id);
                console.log('   ✅ Test Docebo User deleted');
                
                for (const lead of leadsCreated) {
                    await conn.sobject("Lead").delete(lead.Id);
                    console.log(`   ✅ Test Lead deleted: ${lead.Id}`);
                }
                
                for (const contact of contactsCreated) {
                    await conn.sobject("Contact").delete(contact.Id);
                    console.log(`   ✅ Test Contact deleted: ${contact.Id}`);
                }
                
            } else {
                console.log('❌ Docebo User not found after creation');
            }
            
        } else {
            console.log('❌ User creation failed');
        }

        // Step 5: Summary
        console.log('\n📊 NO LEAD/CONTACT ASSOCIATIONS TEST SUMMARY:');
        console.log('=' .repeat(70));
        
        const noAssociations = result && doceboUser && !doceboUser.Lead__c && !doceboUser.Contact__c;
        
        console.log('🔧 ASSOCIATIONS DISABLED:');
        console.log('   ✅ Lead creation disabled');
        console.log('   ✅ Contact workaround disabled');
        console.log('   ✅ Lead/Contact update disabled');
        console.log('   ✅ All association logic commented out');
        
        console.log('\n🎯 TEST RESULTS:');
        if (noAssociations) {
            console.log('   ✅ ASSOCIATION DISABLING WORKING!');
            console.log('   ✅ No Lead associations created');
            console.log('   ✅ No Contact associations created');
            console.log('   ✅ No FIELD_FILTER_VALIDATION_EXCEPTION errors');
            console.log('   ✅ Clean user creation without lookup filter issues');
        } else {
            console.log('   ❌ ASSOCIATION DISABLING FAILED');
            console.log('   ❌ Associations still being created');
            console.log('   ❌ Further investigation needed');
        }
        
        console.log('\n🚀 PRODUCTION BENEFITS:');
        console.log('   • No more FIELD_FILTER_VALIDATION_EXCEPTION errors');
        console.log('   • Clean user creation without association failures');
        console.log('   • Reduced error logs and noise');
        console.log('   • Faster user creation (no association attempts)');
        console.log('   • Simplified troubleshooting');
        
        console.log('\n💡 FUTURE CONSIDERATIONS:');
        console.log('   • Lead/Contact associations can be re-enabled later');
        console.log('   • Lookup filter issues need to be resolved in Salesforce');
        console.log('   • Alternative association strategies can be implemented');
        console.log('   • Manual Lead/Contact creation can be done separately');

        return {
            success: true,
            userCreated: !!result,
            noAssociations: noAssociations,
            doceboUserId: doceboUser ? doceboUser.Id : null
        };

    } catch (error) {
        console.error('💥 Error in no Lead/Contact associations test:', error);
        return {
            success: false,
            error: error.message
        };
    }
}

// Execute the test
console.log('🔄 Starting no Lead/Contact associations test...');
testNoLeadContactAssociations()
    .then((result) => {
        console.log('\n✅ No Lead/Contact associations test completed');
        if (result.success) {
            if (result.noAssociations) {
                console.log('🎉 LEAD/CONTACT ASSOCIATIONS SUCCESSFULLY DISABLED!');
                console.log('🚀 No more FIELD_FILTER_VALIDATION_EXCEPTION errors!');
            } else {
                console.log('⚠️ Association disabling needs verification');
            }
        } else {
            console.log('❌ Test failed. Check the logs above.');
        }
        process.exit(0);
    })
    .catch(err => {
        console.error('💥 Test failed:', err);
        process.exit(1);
    });
