require('dotenv').config();
const doceboServices = require('./platform/docebo/services');

async function countLearningPlanEnrollments() {
  try {
    console.log('🔍 Fetching learning plan enrollments from Docebo...');
    console.log('⏳ This may take a while as we fetch all enrollment data...\n');

    const startTime = Date.now();

    // Get all learning plan enrollments
    const enrollments = await doceboServices.getTotalLearningPlanEnrollmentListedInfo();
    
    const endTime = Date.now();
    const duration = ((endTime - startTime) / 1000).toFixed(2);

    console.log('📊 Learning Plan Enrollment Summary:');
    console.log('=' .repeat(50));
    console.log(`✅ Total Learning Plan Enrollments: ${enrollments.length}`);
    console.log(`⏱️  Fetch Duration: ${duration} seconds`);
    
    if (enrollments.length > 0) {
      // Analyze the data
      const uniqueUsers = new Set();
      const uniqueLearningPlans = new Set();
      const enrollmentsByPlan = {};
      const enrollmentsByUser = {};

      enrollments.forEach(enrollment => {
        uniqueUsers.add(enrollment.user_id);
        uniqueLearningPlans.add(enrollment.learning_plan_id);
        
        // Count enrollments per learning plan
        if (!enrollmentsByPlan[enrollment.learning_plan_id]) {
          enrollmentsByPlan[enrollment.learning_plan_id] = 0;
        }
        enrollmentsByPlan[enrollment.learning_plan_id]++;
        
        // Count enrollments per user
        if (!enrollmentsByUser[enrollment.user_id]) {
          enrollmentsByUser[enrollment.user_id] = 0;
        }
        enrollmentsByUser[enrollment.user_id]++;
      });

      console.log('\n📈 Enrollment Analytics:');
      console.log('-'.repeat(30));
      console.log(`👥 Unique Users Enrolled: ${uniqueUsers.size}`);
      console.log(`📚 Unique Learning Plans: ${uniqueLearningPlans.size}`);
      console.log(`📊 Average Enrollments per User: ${(enrollments.length / uniqueUsers.size).toFixed(2)}`);
      console.log(`📊 Average Enrollments per Plan: ${(enrollments.length / uniqueLearningPlans.size).toFixed(2)}`);

      // Show top 10 learning plans by enrollment count
      const topPlans = Object.entries(enrollmentsByPlan)
        .sort(([,a], [,b]) => b - a)
        .slice(0, 10);

      console.log('\n🏆 Top 10 Learning Plans by Enrollment Count:');
      console.log('-'.repeat(50));
      topPlans.forEach(([planId, count], index) => {
        console.log(`${index + 1}. Learning Plan ID ${planId}: ${count} enrollments`);
      });

      // Show top 10 users by enrollment count
      const topUsers = Object.entries(enrollmentsByUser)
        .sort(([,a], [,b]) => b - a)
        .slice(0, 10);

      console.log('\n👑 Top 10 Users by Enrollment Count:');
      console.log('-'.repeat(40));
      topUsers.forEach(([userId, count], index) => {
        console.log(`${index + 1}. User ID ${userId}: ${count} enrollments`);
      });

      // Show sample enrollments
      console.log('\n📋 Sample Enrollments (first 10):');
      console.log('-'.repeat(40));
      enrollments.slice(0, 10).forEach((enrollment, index) => {
        console.log(`${index + 1}. User ${enrollment.user_id} → Learning Plan ${enrollment.learning_plan_id}`);
      });

      // Distribution analysis
      const enrollmentCounts = Object.values(enrollmentsByUser);
      const maxEnrollments = Math.max(...enrollmentCounts);
      const minEnrollments = Math.min(...enrollmentCounts);

      console.log('\n📊 Enrollment Distribution:');
      console.log('-'.repeat(30));
      console.log(`📈 Max enrollments per user: ${maxEnrollments}`);
      console.log(`📉 Min enrollments per user: ${minEnrollments}`);
      
      // Count users by enrollment ranges
      const ranges = {
        '1': 0,
        '2-5': 0,
        '6-10': 0,
        '11-20': 0,
        '21+': 0
      };

      enrollmentCounts.forEach(count => {
        if (count === 1) ranges['1']++;
        else if (count <= 5) ranges['2-5']++;
        else if (count <= 10) ranges['6-10']++;
        else if (count <= 20) ranges['11-20']++;
        else ranges['21+']++;
      });

      console.log('\n👥 Users by Enrollment Count:');
      console.log('-'.repeat(30));
      Object.entries(ranges).forEach(([range, count]) => {
        const percentage = ((count / uniqueUsers.size) * 100).toFixed(1);
        console.log(`${range.padEnd(8)} enrollments: ${count.toString().padStart(4)} users (${percentage}%)`);
      });

    } else {
      console.log('\n⚠️  No learning plan enrollments found in Docebo');
    }

    console.log('\n' + '='.repeat(50));
    console.log('✅ Learning plan enrollment count completed!');

  } catch (error) {
    console.error('💥 Error counting learning plan enrollments:', error);
    
    if (error.message && error.message.includes('404')) {
      console.log('\n💡 Possible causes:');
      console.log('   - Learning plan enrollment endpoint not available');
      console.log('   - API permissions insufficient');
      console.log('   - Endpoint URL might be different');
    } else if (error.message && error.message.includes('401')) {
      console.log('\n💡 Authentication issue:');
      console.log('   - Check Docebo API credentials');
      console.log('   - Verify API token permissions');
    }
  }
}

// Execute the script
console.log('🚀 Starting learning plan enrollment count...');
countLearningPlanEnrollments()
  .then(() => {
    console.log('\n✅ Script completed successfully');
    process.exit(0);
  })
  .catch(err => {
    console.error('💥 Script failed:', err);
    process.exit(1);
  });
