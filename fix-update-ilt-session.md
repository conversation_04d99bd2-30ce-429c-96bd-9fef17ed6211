# Fix for updateILTSession Function

The error you're seeing is because `salesforceService.updateILTSession` is being called but this function doesn't exist in your codebase. To fix this issue, you need to add the `updateILTSession` function to your salesforce services.

## Step 1: Create the updateILTSession Function

First, add the updateILTSession function to your platform/salesforce/session/createSession.js file. Add this function after the existing createNewSession function:

```javascript
async function updateILTSession(sessionData, courseData) {
    let updateRes = false;
    
    // Get Salesforce connection
    let conn;
    try {
        conn = await getConnection();
    } catch (err) {
        console.error("Error getting Salesforce connection:", err);
        return false;
    }
    
    if (!conn || !conn.accessToken) {
        console.error("Invalid Salesforce connection in updateILTSession");
        return false;
    }
    
    // Find the session by its external ID
    const sessionId = await getIltSessionSalesForceId(sessionData.session_id);
    if (!sessionId) {
        console.log("Session not found in Salesforce, creating instead of updating");
        // If session doesn't exist, create it instead
        return await createNewSession(sessionData, courseData);
    }
    
    // Get the course ID
    let courseSalesForceId = await getCourseSalesForceId(courseData.id);
    if (!courseSalesForceId) {
        console.log("Course not found in Salesforce");
        return false;
    }
    
    // Prepare the session data
    let tmpSession = adjustSessionData(courseSalesForceId, sessionData);
    tmpSession.Id = sessionId; // Add the Salesforce ID for update
    
    try {
        const result = await conn.sobject("Docebo_Session__c").update(tmpSession);
        updateRes = result.success;
        
        // Update instructor information if available
        if (updateRes && sessionData.instructor_id) {
            try {
                const instructorResponse = await doceboService.getInstructorData(
                    sessionData.instructor_id,
                    courseData.id,
                    sessionData.session_id
                );
                
                if (instructorResponse.status === 200 && instructorResponse.data) {
                    await createInstructor(
                        instructorResponse.data,
                        sessionData.instructor_id,
                        courseData.id,
                        sessionData.session_id
                    );
                }
            } catch (err) {
                console.error("Error processing instructor data:", err);
                // Don't fail the session update if instructor processing fails
            }
        }
    } catch (err) {
        console.error("Error updating session:", err);
        return false;
    }
    
    return updateRes;
}
```

## Step 2: Update the module.exports

Then update the module.exports at the bottom of the platform/salesforce/session/createSession.js file:

```javascript
module.exports = {
    createNewSession,
    updateILTSession
}
```

## Step 3: Update the salesforce/services.js File

Finally, modify the platform/salesforce/services.js file to import and export the updateILTSession function:

```javascript
// Add this import at the top with the other imports
const { createNewSession, updateILTSession } = require("./session/createSession");

// Then update the module.exports to include this function
module.exports = {
    createIltSessionEnrollment,
    updateCourseEnrollment: createCourseEnrollment,
    learningPlanEnrollment,
    learningPlanEnrollmentCompleted,
    createNewSession,
    updateILTSession,
    // ... any other exports you have
};
```

After making these changes, the updateILTSession function will be properly available through the salesforceService module, and the error should be resolved.

## Continuing to Run Without Historical Data

You can still run the application without historical data by using:

```bash
set SKIP_HISTORICAL_DATA=true
nodemon app.js
```

This will skip the historical data processing but should now properly handle webhook requests without 502 errors.