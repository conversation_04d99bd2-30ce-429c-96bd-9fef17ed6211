require('dotenv').config();
const hubspot = require('@hubspot/api-client');

const HS_ACCESS_TOKEN = process.env.HS_ACCESS_TOKEN;
const hubspotClient = new hubspot.Client({ accessToken: HS_ACCESS_TOKEN });

async function saveDoceboUser(userData, userId) {
    let saveRes = false;
    const { Best_Describes_Your_Affiliation__c, Level__c, Organization_Headquarters__c, Organization_URL__c, Partner_with_a_Member_of_StriveTogether__c, StriveTogether_Network_Member__c, OwnerId, Account__c, Contact__c, ...rest } = userData;
    let data = rest;
    data = lowerCaseKey(data);
    let contactData = {
        "properties": {
            "email": data.email__c,
            "firstname": data.first_name__c,
            "lastname": data.last_name__c
        }
    };
    try {
        data.docebo_user_id = userId;
        // let response = await hubspotClient.crm.contacts.basicApi.create(contactData);
        // data.contact__c = response.id;
        console.log('===== hs docebo user ====');
        console.log(data);
        let response = await hubspotClient.crm.objects.basicApi.create('p24479088_docebo_users', { properties: data });
        console.log('Contact saved:', response);
        return true;
    } catch (error) {
        console.error('Error saving Docebo user:', error.response ? error.response.body : error.message);
        return saveRes;
    }
}

async function updateDoceboUser(userData, doceboUserId) {
    let saveRes = false;
    const { Best_Describes_Your_Affiliation__c, Level__c, Organization_Headquarters__c, Organization_URL__c, Partner_with_a_Member_of_StriveTogether__c, StriveTogether_Network_Member__c, OwnerId, Account__c, Contact__c, ...rest } = userData;
    let data = rest;
    data = lowerCaseKey(data);
    let getId = await searchUsersByDoceboId(doceboUserId);
    if (getId) {
        try {
            const updatedObject = await hubspotClient.crm.objects.basicApi.update(
                'p24479088_docebo_users',
                getId,
                { properties: data }
            );
            console.log('Update successful:', updatedObject);
            return true;
        } catch (error) {
            console.error('Error updating object:', error);
            throw false;
        }
    } else {
        return false;
    }
}
async function searchUsersByDoceboId(doceboUserId) {
    const searchRequest = {
        filterGroups: [
            {
                filters: [
                    {
                        propertyName: 'docebo_user_id',
                        operator: 'EQ',
                        value: doceboUserId.toString(),
                    },
                ],
            },
        ],
        properties: ['hs_object_id'],
    };
    const searchResponse = await hubspotClient.crm.objects.searchApi.doSearch(
        'p24479088_docebo_users',
        searchRequest
    );
    if (searchResponse.total === 0) {
        console.log('No record found with docebo_user_id:', doceboUserId);
        return null;
    } else {
        console.log('Record found with docebo_user_id:', doceboUserId);
        return searchResponse.results[0].id;
    }
}
async function searchSessionByDoceboId(doceboSessionId) {
    const searchRequest = {
        filterGroups: [
            {
                filters: [
                    {
                        propertyName: 'session_unique_id',
                        operator: 'EQ',
                        value: doceboSessionId.toString(),
                    },
                ],
            },
        ],
        properties: ['hs_object_id'],
    };
    const searchResponse = await hubspotClient.crm.objects.searchApi.doSearch(
        'p24479088_sessions',
        searchRequest
    );
    if (searchResponse.total === 0) {
        console.log('No record found with docebo_user_id:', doceboSessionId);
        return null;
    } else {
        console.log('Record found with docebo_user_id:', doceboSessionId);
        return searchResponse.results[0].id;
    }
}

async function searchCourseEnrollment(courseEnrollmentId) {
    const searchRequest = {
        filterGroups: [
            {
                filters: [
                    {
                        propertyName: 'course_enrollment_id',
                        operator: 'EQ',
                        value: courseEnrollmentId.toString(),
                    },
                ],
            },
        ],
        properties: ['hs_object_id'],
    };
    const searchResponse = await hubspotClient.crm.objects.searchApi.doSearch(
        'p24479088_docebo_course_enrollments',
        searchRequest
    );
    if (searchResponse.total === 0) {
        console.log('No record found with course_enrollment_id:', courseEnrollmentId);
        return null;
    } else {
        console.log('Record found with course_enrollment_id:', courseEnrollmentId);
        return searchResponse.results[0].id;
    }
}
async function searchILTSessionEnrollment(id) {
    console.log("=== search ITL Session enrollment");
    console.log(id);
    const searchRequest = {
        filterGroups: [
            {
                filters: [
                    {
                        propertyName: 'session_attendance_id',
                        operator: 'EQ',
                        value: id.toString(),
                    },
                ],
            },
        ],
        properties: ['hs_object_id'],
    };
    const searchResponse = await hubspotClient.crm.objects.searchApi.doSearch(
        'p24479088_docebo_sessions_attendance',
        searchRequest
    );
    if (searchResponse.total === 0) {
        console.log('No record found with session_attendance_id:', id);
        return null;
    } else {
        console.log('Record found with session_attendance_id:', id);
        return searchResponse.results[0].id;
    }
}
async function searchLearningPlanEnrollment(id) {
    const searchRequest = {
        filterGroups: [
            {
                filters: [
                    {
                        propertyName: 'learning_plan_enrollment_id',
                        operator: 'EQ',
                        value: id.toString(),
                    },
                ],
            },
        ],
        properties: ['hs_object_id'],
    };
    const searchResponse = await hubspotClient.crm.objects.searchApi.doSearch(
        'p24479088_lp_enrollments',
        searchRequest
    );
    if (searchResponse.total === 0) {
        console.log('No record found with session_attendance_id:', id);
        return null;
    } else {
        console.log('Record found with session_attendance_id:', id);
        return searchResponse.results[0].id;
    }
}
async function deleteDoceboUser(doceboUserId) {
    let delRes = false;
    let getId = await searchUsersByDoceboId(doceboUserId);
    if (getId) {
        const recordId = getId;
        try {
            await hubspotClient.crm.objects.basicApi.archive(
                'p24479088_docebo_users',
                recordId
            );
            delRes = true; // Set delRes to true if no error is thrown
        } catch (error) {
            console.error('Error deleting record:', error);
            delRes = false; // Optional: explicitly set false in case of error
        }
    }
    return delRes;
}

async function createCourse(courseData, sfCourseId, doceboCourseId) {
    let saveRes = false;

    const { OwnerId, ...rest } = courseData;
    let data = rest;
    data.hubspot_owner_id = courseData.OwnerId;
    // data.salesforceobjectid = sfCourseId;
    data.course_unique_id = doceboCourseId;
    data = lowerCaseKey(data);
    try {
        console.log('===== hs course ====');
        console.log(data);
        let response = await hubspotClient.crm.objects.basicApi.create('p24479088_docebo_courses', { properties: data });
        console.log('Hs course saved:', response);
        return response.id;
    } catch (error) {
        console.error('Error saving hs course:', error.response ? error.response.body : error.message);
        return saveRes;
    }
}

async function createNewCourseEnrollment(courseEnrollmentData, courseEnrollmentId) {
    let saveRes = false;
    const { Docebo_User__c, ...rest } = courseEnrollmentData;
    let data = rest;
    data.course_enrollment_id = courseEnrollmentId;
    data = lowerCaseKey(data);
    try {
        let response = await hubspotClient.crm.objects.basicApi.create('p24479088_docebo_course_enrollments', { properties: data });
        console.log('Hs course saved:', response);
        return response.id;
    } catch (error) {
        console.log("Error saving HubSpotcourseEnrollment: ", error);
    }

    return saveRes;
}

async function deleteCourseEnrollment(courseEnrollmentId) {
    let delRes = false;
    let getId = await searchCourseEnrollment(courseEnrollmentId);
    if (getId) {
        const recordId = getId;
        try {
            await hubspotClient.crm.objects.basicApi.archive(
                'p24479088_docebo_course_enrollments',
                recordId
            );
            delRes = true; // Set delRes to true if no error is thrown
        } catch (error) {
            console.error('Error deleting courseEnrollment record:', error);
            delRes = false; // Optional: explicitly set false in case of error
        }
    }
    return delRes;
}

async function updateCourseEnrollment(data, id) {
    let updateRes = false;
    let enrollData = lowerCaseKey(data);
    let getId = await searchCourseEnrollment(id);
    if (getId) {
        try {
            const updatedObject = await hubspotClient.crm.objects.basicApi.update(
                'p24479088_docebo_course_enrollments',
                getId,
                { properties: enrollData }
            );
            console.log('Update successful:', updatedObject);
            updateRes = true;
        } catch (error) {
            console.error('Error updating object:', error);
        }
    }

    return updateRes;
}
async function createSession(sessionData, sfSessionId, doceboSessionId) {
    let saveRes = false;

    const { OwnerId, CourseId__c, ...rest } = sessionData;
    let data = rest;
    // data.salesforceobjectid = sfSessionId;
    data.hubspot_owner_id = sessionData.OwnerId;
    data.session_unique_id = doceboSessionId;
    data = lowerCaseKey(data);
    try {
        console.log('===== hs session ====');
        console.log(data);
        let response = await hubspotClient.crm.objects.basicApi.create('p24479088_sessions', { properties: data });
        console.log('Hs session saved:', response);
        return response.id;
    } catch (error) {
        console.error('Error saving hs session:', error.response ? error.response.body : error.message);
        return saveRes;
    }
}

async function updateHSILTSession(sessionData, doceboSessionId) {
    const { ...rest } = sessionData;
    let data = rest;
    data = lowerCaseKey(data);
    let getId = await searchUsersByDoceboId(doceboSessionId);
    if (getId) {
        try {
            const updatedObject = await hubspotClient.crm.objects.basicApi.update(
                'p24479088_sessions',
                getId,
                { properties: data }
            );
            console.log('Update successful:', updatedObject);
            return true;
        } catch (error) {
            console.error('Error updating object:', error);
            throw false;
        }
    } else {
        return false;
    }
}

async function createIltSessionEnrollment(sessionAtt, sessionAttId) {
    let saveRes = false;
    const { Docebo_User__c, SessionId__c, ...rest } = sessionAtt;
    let data = rest;
    // data.salesforceobjectid = sessionAttId;
    data.session_attendance_id = sessionAttId;
    data = lowerCaseKey(data);
    if(data.docebo_user_id){
        const {docebo_user_id, ...others} = data;
        data = others;
    }
    let exist = await searchILTSessionEnrollment(sessionAttId);
    if (exist == null) {
        try {
            let response = await hubspotClient.crm.objects.basicApi.create('p24479088_docebo_sessions_attendance', { properties: data });
            console.log('Hs new iltSessionEnrollment saved:', response);
            saveRes = true;
            return response.id;
        } catch (error) {
            console.log("Error hs saving iltSessionEnrollment: ", error);
        }
    }
    return saveRes;
}

async function deleteIltSessionEnrollment(id){
    let exist = await searchILTSessionEnrollment(id);
    if(exist){
        try {
            await hubspotClient.crm.objects.basicApi.archive(
                'p24479088_docebo_sessions_attendance',
                exist
            );
            return true;
        } catch (error) {
            console.error('Error deleting iltSessionEnrollment record:', error);
            return false;
        }
    }
}

async function createLearningPlanEnrollment(data, id){
    let saveRes = false;
    let tmpData = {};
    tmpData.learning_plan_enrollment_id = id;
    // tmpData.salesforceobjectid = id;
    tmpData.enrolment_id__c = data.Enrolment_Id__c;
    tmpData.completed_courses__c = 0;
    tmpData.who__c = "";
    tmpData.unenrollment_date__c = "";
    let ext = await searchLearningPlanEnrollment(id);
    if(ext == null) {
        try {
            let response = await hubspotClient.crm.objects.basicApi.create('p24479088_lp_enrollments', { properties: tmpData });
            console.log('Hs new learningPlanEnrollment saved:', response);
            saveRes = true;
            return response.id;
        } catch (error) {
            console.log("Error hs saving learningPlanEnrollment: ", error);
        }
    }
    return saveRes;
}

async function deleteLearningPlanEnrollment(id){
    let exist = await searchLearningPlanEnrollment(id);
    if(exist){
        try {
            await hubspotClient.crm.objects.basicApi.archive(
                'p24479088_lp_enrollments',
                exist
            );
            return true;
        } catch (error) {
            console.error('Error deleting learning plan enrollment record:', error);
            return false;
        }
    }else{
        console.log("HS learning plan enrollment doesn't exist for deletion");
        return false;
    }
}

async function completeLearningPlanEnrollment(data, id){
    let tmpData = lowerCaseKey(data);
    let getId = await searchLearningPlanEnrollment(id);
    if (getId) {
        try {
            const updatedObject = await hubspotClient.crm.objects.basicApi.update(
                'p24479088_lp_enrollments',
                getId,
                { properties: tmpData }
            );
            console.log('Update successful:', updatedObject);
            return true;
        } catch (error) {
            console.error('Error updating object:', error);
            throw false;
        }
    } else {
        return false;
    }
}

function lowerCaseKey(data) {
    return Object.keys(data).reduce((acc, key) => {
        acc[key.toLowerCase()] = data[key];
        return acc;
    }, {});
}

module.exports = {
    saveDoceboUser,
    updateDoceboUser,
    deleteDoceboUser,
    createCourse,
    createSession,
    updateHSILTSession,
    createNewCourseEnrollment,
    updateCourseEnrollment,
    deleteCourseEnrollment,
    createIltSessionEnrollment,
    deleteIltSessionEnrollment,
    createLearningPlanEnrollment,
    deleteLearningPlanEnrollment,
    completeLearningPlanEnrollment
}