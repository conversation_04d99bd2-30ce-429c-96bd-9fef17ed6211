require('dotenv').config();
const getConnection = require('./platform/salesforce/common/getConnection');
const getApiData = require("./common/docebo/fetcher");
const APP_BASE = process.env.DOCEBO_API_BASE_URL;

async function debugLearningPlanEnrollments() {
    try {
        console.log('🔍 DEBUG LEARNING PLAN ENROLLMENTS');
        console.log('=' .repeat(60));
        
        const conn = await getConnection();
        if (!conn || !conn.accessToken) {
            throw new Error('Failed to connect to Salesforce');
        }

        console.log('✅ Connected to Salesforce successfully');

        // Step 1: Get a learning plan that has enrollments
        console.log('\n📚 STEP 1: Finding Learning Plans with Enrollments...');
        console.log('-'.repeat(50));
        
        const lpEnrollments = await conn.query(`
            SELECT Learning_Plan_Id__c, COUNT(Id) enrollmentCount
            FROM Docebo_Learning_Plan_Enrollment__c 
            GROUP BY Learning_Plan_Id__c
            ORDER BY COUNT(Id) DESC
            LIMIT 5
        `);
        
        console.log('Learning Plans with most enrollments:');
        lpEnrollments.records.forEach((record, index) => {
            console.log(`   ${index + 1}. LP ID: ${record.Learning_Plan_Id__c} - ${record.enrollmentCount} enrollments`);
        });
        
        if (lpEnrollments.records.length === 0) {
            console.log('❌ No learning plan enrollments found');
            return;
        }
        
        // Get the learning plan with most enrollments
        const topLpId = lpEnrollments.records[0].Learning_Plan_Id__c;
        
        // Get the learning plan details
        const lpDetails = await conn.sobject("Docebo_Learning_Plan__c")
            .findOne({ Id: topLpId });
            
        console.log(`\nUsing Learning Plan: ${lpDetails.Learning_Plan_Name__c}`);
        console.log(`External ID: ${lpDetails.Learning_Plan_External_Id__c}`);

        // Step 2: Test Docebo API call for this learning plan
        console.log('\n📡 STEP 2: Testing Docebo API Call...');
        console.log('-'.repeat(50));
        
        const learningPlanExternalId = lpDetails.Learning_Plan_External_Id__c;
        
        try {
            const response = await getApiData(
                'GET', 
                `${APP_BASE}/learn/v1/enrollments?learning_plan_id=${learningPlanExternalId}&page=1&page_size=10`, 
                null
            );
            
            if (response && response.status === 200) {
                const items = response.data?.items || [];
                console.log(`Found ${items.length} enrollment items from Docebo`);
                
                if (items.length > 0) {
                    console.log('\n📋 Sample enrollment data:');
                    const sample = items[0];
                    console.log(`   User ID: ${sample.user_id}`);
                    console.log(`   Username: ${sample.username}`);
                    console.log(`   Course UID: ${sample.uidCourse}`);
                    console.log(`   Course Name: ${sample.name}`);
                    console.log(`   Status: ${sample.status}`);
                    console.log(`   Enrollment Date: ${sample.enroll_date_of_enrollment}`);
                    
                    // Step 3: Check if we can map this data
                    console.log('\n🔍 STEP 3: Testing Data Mapping...');
                    console.log('-'.repeat(50));
                    
                    // Check course mapping
                    const sfCourses = await conn.sobject("Docebo_Course__c")
                        .find({})
                        .limit(10)
                        .execute();
                        
                    console.log(`Sample Salesforce courses:`);
                    sfCourses.slice(0, 3).forEach((course, index) => {
                        console.log(`   ${index + 1}. ${course.Course_Name__c} - External ID: ${course.Course_External_Id__c}`);
                    });
                    
                    // Check user mapping
                    const sfUsers = await conn.sobject("Docebo_Users__c")
                        .find({ User_Unique_Id__c: sample.user_id.toString() })
                        .execute();
                        
                    console.log(`\nUser mapping test for User ID ${sample.user_id}:`);
                    if (sfUsers.length > 0) {
                        console.log(`   ✅ Found user: ${sfUsers[0].Email__c}`);
                    } else {
                        console.log(`   ❌ User not found in Salesforce`);
                    }
                    
                    // Check course mapping by uidCourse
                    console.log(`\nCourse mapping test for uidCourse ${sample.uidCourse}:`);
                    
                    // Get all courses from Docebo to create mapping
                    const doceboCoursesResponse = await getApiData(
                        'GET', 
                        `${APP_BASE}/course/v1/courses?page=1&page_size=50`, 
                        null
                    );
                    
                    if (doceboCoursesResponse && doceboCoursesResponse.status === 200) {
                        const doceboCourses = doceboCoursesResponse.data?.items || [];
                        
                        // Find the course with matching uidCourse
                        const matchingDoceboCourse = doceboCourses.find(course => course.uidCourse === sample.uidCourse);
                        
                        if (matchingDoceboCourse) {
                            console.log(`   Found Docebo course: ID ${matchingDoceboCourse.id}, UID ${matchingDoceboCourse.uidCourse}`);
                            
                            // Check if this course exists in Salesforce
                            const sfCourse = sfCourses.find(course => 
                                course.Course_External_Id__c?.toString() === matchingDoceboCourse.id.toString()
                            );
                            
                            if (sfCourse) {
                                console.log(`   ✅ Found matching Salesforce course: ${sfCourse.Course_Name__c}`);
                            } else {
                                console.log(`   ❌ No matching Salesforce course found for Docebo ID ${matchingDoceboCourse.id}`);
                            }
                        } else {
                            console.log(`   ❌ No Docebo course found with uidCourse ${sample.uidCourse}`);
                        }
                    }
                    
                    // Step 4: Check Learning Plan Enrollment mapping
                    console.log('\n🔗 STEP 4: Testing Learning Plan Enrollment Mapping...');
                    console.log('-'.repeat(50));
                    
                    const userInSf = sfUsers.length > 0 ? sfUsers[0] : null;
                    
                    if (userInSf) {
                        const lpEnrollmentKey = `${topLpId}-${userInSf.Id}`;
                        console.log(`Looking for LP Enrollment with key: ${lpEnrollmentKey}`);
                        
                        const lpEnrollment = await conn.sobject("Docebo_Learning_Plan_Enrollment__c")
                            .findOne({ 
                                Learning_Plan_Id__c: topLpId,
                                Docebo_User_Id__c: userInSf.Id
                            });
                            
                        if (lpEnrollment) {
                            console.log(`   ✅ Found Learning Plan Enrollment: ${lpEnrollment.Name}`);
                        } else {
                            console.log(`   ❌ No Learning Plan Enrollment found for this user and LP`);
                        }
                    }
                    
                } else {
                    console.log('⚠️ No enrollment items returned from Docebo API');
                }
            } else {
                console.log(`❌ Docebo API call failed: ${response?.status || 'No response'}`);
            }
            
        } catch (apiError) {
            console.log(`❌ Docebo API error: ${apiError.message}`);
        }

        return {
            success: true
        };

    } catch (error) {
        console.error('💥 Error debugging learning plan enrollments:', error);
        return {
            success: false,
            error: error.message
        };
    }
}

console.log('🔄 Starting Learning Plan Enrollments Debug...');
debugLearningPlanEnrollments()
    .then((result) => {
        console.log('\n📋 DEBUG SUMMARY:');
        console.log('=' .repeat(40));
        
        if (result.success) {
            console.log(`✅ Debug completed successfully`);
            console.log(`💡 Check the mapping results above to identify issues`);
        } else {
            console.log(`❌ Debug failed: ${result.error}`);
        }
        
        console.log('\n✅ Learning Plan Enrollments debug completed');
        process.exit(0);
    })
    .catch(err => {
        console.error('💥 Learning Plan Enrollments debug failed:', err);
        process.exit(1);
    });
