const getConnection = require("../common/getConnection");
const doceboService = require("../../docebo/services");


let learningPlanTemplate = {
    Creation_Date__c: "",
    Deleted__c: false,
    Deletion_Date__c: "",
    Description__c: "",
    Effective__c: false,
    Last_Update_Date__c: "",
    Learning_Plan_Code__c: "",
    Learning_Plan_External_Id__c: 0,
    Name__c: "",
    OwnerId: "",
    Slug__c: "",
    Thumbnail__c: ""
}

function extractTextFromHtml(htmlContent) {
    return htmlContent.replace(/<[^>]+>/g, '');
}

async function getLearningPlanSalesForceId(lpCode) {
    let conn;
    try {
        conn = await getConnection();
    } catch (err) {
        console.error("Error getting Salesforce connection:", err);
        return null;
    }
    if (!conn || !conn.accessToken) {
        console.error("Invalid Salesforce connection in getLearningPlanSalesForceId");
        return null;
    }
    const record = await conn.sobject("Docebo_Learning_Plan__c")
            .findOne({ Learning_Plan_External_Id__c: lpCode })
            .then(record => {
                if (!record) {
                    console.log("Learning plan doesn't exist in Salesforce");
                    return null;
                }
                console.log(`Learning plan found. ID: ${record.Id}`);
                return record.Id;
            })
            .catch(err => {
                console.error("Error finding Learning plan record:", err);
                throw err;
            });
    return record;
}

async function createNewLearningPlanById(lpId) {
    let saveRes = false;
    const existingId = await getLearningPlanSalesForceId(lpId);
    if (existingId != null) {
        console.log("Learning plan already exists in Salesforce; skipping creation");
        return false;
    }
    let lpInfo;
    try {
        lpInfo = await doceboService.getLearningPlan(lpId);
    } catch (err) {
        console.error("Error fetching Learning Plan data:", err);
        return false;
    }
    if (!lpInfo.data) {
        console.error("No Learning Plan data returned for ID:", lpId);
        return false;
    }
    let conn;
    try {
        conn = await getConnection();
    } catch (err) {
        console.error("Error getting Salesforce connection:", err);
        return false;
    }
    if (!conn || !conn.accessToken) {
        console.error("Invalid Salesforce connection in createNewLearningPlanById");
        return false;
    }
    const tmpLearningPlan = { ...learningPlanTemplate };
    tmpLearningPlan.Creation_Date__c = lpInfo.data.create_date ? new Date(lpInfo.data.create_date.replace(' ', 'T')).toISOString() : "";
    tmpLearningPlan.Description__c = extractTextFromHtml(lpInfo.data.description);
    tmpLearningPlan.Last_Update_Date__c = lpInfo.data.create_date ? new Date(lpInfo.data.create_date.replace(' ', 'T')).toISOString() : "";
    tmpLearningPlan.Learning_Plan_Code__c = lpInfo.data.code;
    tmpLearningPlan.Learning_Plan_External_Id__c = lpInfo.data.id;
    tmpLearningPlan.Name__c = lpInfo.data.name;
    tmpLearningPlan.OwnerId = "005O400000BxnnxIAB";
    tmpLearningPlan.Slug__c = lpInfo.data.slug_name;
    tmpLearningPlan.Thumbnail__c = lpInfo.data.image || "";
    const result = await conn.sobject("Docebo_Learning_Plan__c")
        .upsert(tmpLearningPlan, "Learning_Plan_External_Id__c");
    saveRes = result.success;
    return saveRes;
}

module.exports = {
    createNewLearningPlanById,
    getLearningPlanSalesForceId
}