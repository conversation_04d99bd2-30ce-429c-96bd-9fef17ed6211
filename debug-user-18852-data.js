require('dotenv').config();
const doceboService = require('./platform/docebo/services');
const { tidyData } = require('./platform/salesforce/users/createUser');

async function debugUser18852Data() {
    try {
        console.log('🔍 Debugging User 18852 Data (<EMAIL>)...');
        
        const userId = 18852;
        
        // Step 1: Get the same data that the webhook would have received
        console.log('\n📥 Fetching user data from Docebo API...');
        
        const userInfo = await doceboService.getUserInfo(userId);
        const userListedInfo = await doceboService.getUserListedInfo(userId);
        
        if (userInfo.status !== 200) {
            console.error('❌ Failed to get user info:', userInfo);
            return;
        }
        
        console.log('✅ Successfully retrieved user data from Docebo API');
        
        // Step 2: Show the raw data structure
        console.log('\n📋 RAW DOCEBO USER DATA for User 18852:');
        console.log('=' .repeat(80));
        console.log('userInfo.data:');
        console.log(JSON.stringify(userInfo.data, null, 2));
        
        // Step 3: Simulate webhook processing
        console.log('\n🔧 SIMULATING WEBHOOK PROCESSING:');
        console.log('=' .repeat(80));
        
        // Add the webhook fields that would be added
        const userData = userInfo.data;
        userData["fired_at"] = "2025-06-06 18:45:04"; // From webhook
        userData["expiration_date"] = ""; // From webhook (might be empty)
        
        // Step 4: Test the tidyData function
        console.log('\n🧪 TESTING tidyData FUNCTION:');
        console.log('=' .repeat(80));
        
        const processedData = tidyData(userData, userListedInfo);
        
        console.log('Processed data from tidyData:');
        console.log(JSON.stringify(processedData, null, 2));
        
        // Step 5: Analyze the additional_fields
        console.log('\n📊 ADDITIONAL FIELDS ANALYSIS:');
        console.log('=' .repeat(80));
        
        const additionalFields = userData.additional_fields || [];
        console.log(`Found ${additionalFields.length} additional fields:`);
        
        additionalFields.forEach((field, index) => {
            console.log(`   ${index + 1}. ID: ${field.id}, Title: "${field.title}", Value: "${field.value}", Enabled: ${field.enabled}`);
        });
        
        // Step 6: Check specific field mappings
        console.log('\n🎯 SPECIFIC FIELD MAPPING CHECK:');
        console.log('=' .repeat(80));
        
        const fieldMappings = [
            { name: 'Languages', source: 'user_data.language', sfField: 'Languages__c', value: processedData.Languages__c },
            { name: 'TimeZone', source: 'user_data.timezone', sfField: 'TimeZone__c', value: processedData.TimeZone__c },
            { name: 'City', id: '24', sfField: 'MailingCity__c', value: processedData.MailingCity__c },
            { name: 'State', id: '25', sfField: 'MailingState__c', value: processedData.MailingState__c },
            { name: 'Organization', id: '14', sfField: 'Organization_Name__c', value: processedData.Organization_Name__c },
            { name: 'Job Title', id: '8', sfField: 'Job_Title__c', value: processedData.Job_Title__c },
            { name: 'Role Type', id: '9', sfField: 'Role_Type__c', value: processedData.Role_Type__c },
            { name: 'Race', id: '12', sfField: 'Race_Identity__c', value: processedData.Race_Identity__c },
            { name: 'Gender', id: '13', sfField: 'Gender_Identity__c', value: processedData.Gender_Identity__c },
            { name: 'Initiative', id: '20', sfField: 'Initiative__c', value: processedData.Initiative__c }
        ];
        
        let correctCount = 0;
        let totalCount = fieldMappings.length;

        fieldMappings.forEach(mapping => {
            const hasValue = mapping.value && mapping.value !== "" && mapping.value !== 0;
            const status = hasValue ? '✅' : '❌';

            if (hasValue) correctCount++;

            if (mapping.id) {
                const additionalField = additionalFields.find(f => f.id === mapping.id);
                if (additionalField) {
                    console.log(`${status} ${mapping.name}: ID ${mapping.id} = "${additionalField.value}" → ${mapping.sfField} = "${mapping.value}"`);
                } else {
                    console.log(`❌ ${mapping.name}: ID ${mapping.id} NOT FOUND in additional_fields → ${mapping.sfField} = "${mapping.value}"`);
                }
            } else {
                console.log(`${status} ${mapping.name}: ${mapping.source} → ${mapping.sfField} = "${mapping.value}"`);
            }
        });
        
        // Step 7: Check what's actually in user_data
        console.log('\n📋 USER_DATA FIELDS:');
        console.log('=' .repeat(50));
        const user_data = userData.user_data || {};
        Object.keys(user_data).forEach(key => {
            console.log(`   ${key}: ${user_data[key]}`);
        });
        
        // Step 8: Compare with User 18851 to see differences
        console.log('\n🔍 COMPARISON WITH USER 18851:');
        console.log('=' .repeat(60));
        console.log('This will help identify why some fields might be missing');
        
        // Step 9: Check if this user has different field structure
        console.log('\n💡 FIELD STRUCTURE ANALYSIS:');
        console.log('=' .repeat(50));
        
        if (additionalFields.length === 0) {
            console.log('❌ ISSUE: No additional_fields found for this user');
            console.log('   → This user may not have completed their profile in Docebo');
            console.log('   → Check if this is a test user or if additional fields are configured');
        } else {
            console.log(`✅ Found ${additionalFields.length} additional fields`);
            console.log('   → Check if the field values are populated');
        }
        
        // Step 10: Create Lead data to see what would be created
        console.log('\n🎯 LEAD DATA THAT WOULD BE CREATED:');
        console.log('=' .repeat(60));
        
        const leadData = {
            LastName: processedData.Last_Name__c || "Unknown",
            FirstName: processedData.First_Name__c,
            Email: processedData.Email__c,
            Company: processedData.Organization_Name__c || "-",
            Title: processedData.Job_Title__c || "",
            Status: "Open - Not Contacted",
            Created_by_Docebo_API__c: true,
            Gender__c: processedData.Gender_Identity__c,
            Role_Type__c: processedData.Role_Type__c,
            Race__c: processedData.Race_Identity__c,
            Languages__c: processedData.Languages__c || "",
            MailingCity__c: processedData.MailingCity__c || "",
            MailingState__c: processedData.MailingState__c || "",
            Time_Zone__c: processedData.TimeZone__c || "",
            Position_Role__c: processedData.Position_Role__c || "",
            LeadSource: "Docebo Platform"
        };

        console.log('Lead data that would be created:');
        Object.keys(leadData).forEach(key => {
            const value = leadData[key];
            const status = (value && value !== "" && value !== 0) ? '✅' : '❌';
            console.log(`   ${status} ${key}: "${value}"`);
        });

        return {
            userInfo: userInfo.data,
            userListedInfo: userListedInfo,
            processedData: processedData,
            additionalFieldsCount: additionalFields.length,
            leadData: leadData
        };
        
    } catch (error) {
        console.error('💥 Error debugging user 18852:', error);
        return null;
    }
}

// Execute the debug
console.log('🔄 Starting User 18852 data debug...');
debugUser18852Data()
    .then((result) => {
        if (result) {
            console.log(`\n✅ Debug completed successfully!`);
            console.log(`📊 Additional fields found: ${result.additionalFieldsCount}`);
            
            // Count populated Lead fields
            const populatedFields = Object.keys(result.leadData).filter(key => {
                const value = result.leadData[key];
                return value && value !== "" && value !== 0 && value !== false;
            });
            
            console.log(`📊 Populated Lead fields: ${populatedFields.length}/${Object.keys(result.leadData).length}`);
            
            if (populatedFields.length < 10) {
                console.log('\n⚠️ ISSUE IDENTIFIED: Many Lead fields are empty');
                console.log('💡 LIKELY CAUSES:');
                console.log('1. User has not completed their profile in Docebo');
                console.log('2. Additional fields are not configured for this user');
                console.log('3. Field IDs may have changed');
                console.log('4. User is a test user with minimal data');
            }
        } else {
            console.log('\n⚠️ Debug completed but no results obtained');
        }
        process.exit(0);
    })
    .catch(err => {
        console.error('💥 Debug failed:', err);
        process.exit(1);
    });
