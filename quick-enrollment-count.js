require('dotenv').config();
const doceboServices = require('./platform/docebo/services');

async function quickEnrollmentCount() {
  try {
    console.log('🔍 Getting quick count of learning plan enrollments...');
    
    const startTime = Date.now();

    // Get all learning plan enrollments (this will still take time but we'll just count)
    const enrollments = await doceboServices.getTotalLearningPlanEnrollmentListedInfo();
    
    const endTime = Date.now();
    const duration = ((endTime - startTime) / 1000).toFixed(2);

    console.log('\n📊 LEARNING PLAN ENROLLMENT COUNT:');
    console.log('=' .repeat(50));
    console.log(`🎯 TOTAL ENROLLMENTS: ${enrollments.length}`);
    console.log(`⏱️  Fetch Duration: ${duration} seconds`);
    
    if (enrollments.length > 0) {
      // Quick analysis without detailed processing
      const uniqueUsers = new Set(enrollments.map(e => e.user_id));
      const uniquePlans = new Set(enrollments.map(e => e.learning_plan_id));
      
      console.log('\n📈 Quick Stats:');
      console.log('-'.repeat(30));
      console.log(`👥 Unique Users: ${uniqueUsers.size}`);
      console.log(`📚 Unique Learning Plans: ${uniquePlans.size}`);
      console.log(`📊 Avg Enrollments per User: ${(enrollments.length / uniqueUsers.size).toFixed(2)}`);
      console.log(`📊 Avg Enrollments per Plan: ${(enrollments.length / uniquePlans.size).toFixed(2)}`);
      
      // Show first few enrollments as samples
      console.log('\n📋 Sample Enrollments:');
      console.log('-'.repeat(30));
      enrollments.slice(0, 5).forEach((enrollment, index) => {
        console.log(`${index + 1}. User ${enrollment.user_id} → Learning Plan ${enrollment.learning_plan_id}`);
      });
    }

  } catch (error) {
    console.error('💥 Error getting enrollment count:', error);
  }
}

// Execute the script
console.log('🚀 Starting quick enrollment count...');
quickEnrollmentCount()
  .then(() => {
    console.log('\n✅ Quick count completed');
    process.exit(0);
  })
  .catch(err => {
    console.error('💥 Quick count failed:', err);
    process.exit(1);
  });
