require('dotenv').config();
const getConnection = require('./platform/salesforce/common/getConnection');
const { createCourseEnrollment, saveCourseEnrollmentsInBatch } = require('./platform/salesforce/courseEnrollment/createCourseEnrollment');

async function testWebhookEnrollmentFixes() {
    try {
        console.log('🧪 Testing Webhook Course Enrollment Fixes');
        console.log('=' .repeat(60));
        
        const conn = await getConnection();
        if (!conn || !conn.accessToken) {
            console.error("❌ Invalid Salesforce connection");
            return;
        }

        // Step 1: Verify the fixes are working
        console.log('\n📊 Verifying current enrollment state...');
        console.log('-'.repeat(50));
        
        const allEnrollments = await conn.sobject("Docebo_CourseEnrollment__c")
            .find({})
            .limit(20)
            .execute();
        
        console.log(`📋 Found ${allEnrollments.length} course enrollment records`);
        
        let withCourse = 0;
        let withUser = 0;
        let fullyAssociated = 0;
        
        allEnrollments.forEach(enrollment => {
            if (enrollment.Course__c) withCourse++;
            if (enrollment.Docebo_User__c) withUser++;
            if (enrollment.Course__c && enrollment.Docebo_User__c) fullyAssociated++;
        });
        
        console.log(`✅ With Course Association: ${withCourse}/${allEnrollments.length} (${Math.round(withCourse/allEnrollments.length*100)}%)`);
        console.log(`✅ With User Association: ${withUser}/${allEnrollments.length} (${Math.round(withUser/allEnrollments.length*100)}%)`);
        console.log(`🎯 Fully Associated: ${fullyAssociated}/${allEnrollments.length} (${Math.round(fullyAssociated/allEnrollments.length*100)}%)`);

        // Step 2: Test the webhook enrollment creation logic
        console.log('\n🧪 Testing webhook enrollment creation logic...');
        console.log('-'.repeat(50));
        
        // Get sample course and user for testing
        const sampleCourse = await conn.sobject("Docebo_Course__c")
            .findOne({});
        
        const sampleUser = await conn.sobject("Docebo_Users__c")
            .findOne({});
        
        if (!sampleCourse || !sampleUser) {
            console.error("❌ No sample course or user found for testing");
            return;
        }
        
        console.log(`✅ Sample Course: ${sampleCourse.Course_Name__c} (External ID: ${sampleCourse.Course_External_Id__c})`);
        console.log(`✅ Sample User: ${sampleUser.First_Name__c} ${sampleUser.Last_Name__c} (Unique ID: ${sampleUser.User_Unique_Id__c})`);

        // Step 3: Test single enrollment creation (simulating webhook)
        console.log('\n🔧 Testing single enrollment creation...');
        console.log('-'.repeat(50));
        
        const testEnrollmentData = {
            course_id: sampleCourse.Course_External_Id__c,
            user_id: sampleUser.User_Unique_Id__c,
            enrollment_date: "2025-01-15 14:30:00",
            completion_date: null,
            status: "Enrolled",
            score: 0
        };
        
        console.log(`📋 Test enrollment data:`);
        console.log(`   Course ID: ${testEnrollmentData.course_id}`);
        console.log(`   User ID: ${testEnrollmentData.user_id}`);
        console.log(`   Expected Enrollment_ID__c: UE-${testEnrollmentData.course_id}-${testEnrollmentData.user_id}`);
        
        // Clean up any existing test enrollment
        const testEnrollmentId = `UE-${testEnrollmentData.course_id}-${testEnrollmentData.user_id}`;
        try {
            const existingEnrollment = await conn.sobject("Docebo_CourseEnrollment__c")
                .findOne({ Enrollment_ID__c: testEnrollmentId });
            
            if (existingEnrollment) {
                await conn.sobject("Docebo_CourseEnrollment__c").delete(existingEnrollment.Id);
                console.log(`🗑️ Deleted existing test enrollment: ${existingEnrollment.Id}`);
            }
        } catch (cleanupError) {
            console.log('No existing enrollment to clean up');
        }
        
        // Create the enrollment using the fixed function
        const singleResult = await createCourseEnrollment(testEnrollmentData);
        
        if (singleResult) {
            console.log('✅ Single enrollment creation successful!');
            
            // Verify the created enrollment
            const createdEnrollment = await conn.sobject("Docebo_CourseEnrollment__c")
                .findOne({ Enrollment_ID__c: testEnrollmentId });
            
            if (createdEnrollment) {
                console.log(`✅ Enrollment verified: ${createdEnrollment.Id}`);
                console.log(`   Course__c: ${createdEnrollment.Course__c || 'MISSING ❌'}`);
                console.log(`   Docebo_User__c: ${createdEnrollment.Docebo_User__c || 'MISSING ❌'}`);
                console.log(`   Status__c: ${createdEnrollment.Status__c}`);
                console.log(`   Enrollment_ID__c: ${createdEnrollment.Enrollment_ID__c}`);
                console.log(`   Enrollment_Date__c: ${createdEnrollment.Enrollment_Date__c}`);
                
                if (createdEnrollment.Course__c && createdEnrollment.Docebo_User__c) {
                    console.log('🎯 SUCCESS! Both course and user associations are working!');
                } else {
                    console.log('❌ ISSUE: Missing course or user association');
                }
            } else {
                console.log('❌ Created enrollment not found');
            }
        } else {
            console.log('❌ Single enrollment creation failed');
        }

        // Step 4: Test batch enrollment creation (simulating webhook batch)
        console.log('\n🧪 Testing batch enrollment creation...');
        console.log('-'.repeat(50));
        
        // Get additional courses and users for batch testing
        const courses = await conn.sobject("Docebo_Course__c")
            .find({})
            .limit(3)
            .execute();
        
        const users = await conn.sobject("Docebo_Users__c")
            .find({})
            .limit(3)
            .execute();
        
        if (courses.length >= 2 && users.length >= 2) {
            const batchEnrollments = [
                {
                    course_id: courses[1].Course_External_Id__c,
                    user_id: users[0].User_Unique_Id__c,
                    enrollment_date: "2025-01-16 10:00:00",
                    status: "Enrolled",
                    score: 0
                },
                {
                    course_id: courses[0].Course_External_Id__c,
                    user_id: users[1].User_Unique_Id__c,
                    enrollment_date: "2025-01-16 11:00:00",
                    status: "Enrolled",
                    score: 0
                }
            ];
            
            console.log(`📋 Testing batch with ${batchEnrollments.length} enrollments`);
            
            // Clean up existing batch enrollments
            for (const enrollment of batchEnrollments) {
                try {
                    const enrollmentId = `UE-${enrollment.course_id}-${enrollment.user_id}`;
                    const existing = await conn.sobject("Docebo_CourseEnrollment__c")
                        .findOne({ Enrollment_ID__c: enrollmentId });
                    
                    if (existing) {
                        await conn.sobject("Docebo_CourseEnrollment__c").delete(existing.Id);
                        console.log(`🗑️ Deleted existing batch enrollment: ${existing.Id}`);
                    }
                } catch (cleanupError) {
                    // Ignore cleanup errors
                }
            }
            
            const batchResult = await saveCourseEnrollmentsInBatch(batchEnrollments);
            
            if (batchResult) {
                console.log('✅ Batch enrollment creation successful!');
                
                // Verify batch enrollments
                let batchSuccessCount = 0;
                for (const enrollment of batchEnrollments) {
                    const enrollmentId = `UE-${enrollment.course_id}-${enrollment.user_id}`;
                    const created = await conn.sobject("Docebo_CourseEnrollment__c")
                        .findOne({ Enrollment_ID__c: enrollmentId });
                    
                    if (created && created.Course__c && created.Docebo_User__c) {
                        batchSuccessCount++;
                        console.log(`✅ Batch enrollment ${batchSuccessCount}: ${created.Id} - Fully associated`);
                    } else if (created) {
                        console.log(`⚠️ Batch enrollment found but missing associations: ${created.Id}`);
                    } else {
                        console.log(`❌ Batch enrollment not found: ${enrollmentId}`);
                    }
                }
                
                console.log(`🎯 Batch Success Rate: ${batchSuccessCount}/${batchEnrollments.length} (${Math.round(batchSuccessCount/batchEnrollments.length*100)}%)`);
            } else {
                console.log('❌ Batch enrollment creation failed');
            }
        } else {
            console.log('⚠️ Not enough courses/users for batch testing');
        }

        // Step 5: Test webhook field format consistency
        console.log('\n🔍 Testing webhook field format consistency...');
        console.log('-'.repeat(50));
        
        const recentEnrollments = await conn.sobject("Docebo_CourseEnrollment__c")
            .find({})
            .sort({ CreatedDate: -1 })
            .limit(5)
            .execute();
        
        console.log(`📋 Checking ${recentEnrollments.length} recent enrollments for format consistency:`);
        
        let formatConsistentCount = 0;
        recentEnrollments.forEach((enrollment, index) => {
            const hasCorrectFormat = enrollment.Enrollment_ID__c && enrollment.Enrollment_ID__c.startsWith('UE-');
            const hasAssociations = enrollment.Course__c && enrollment.Docebo_User__c;
            
            if (hasCorrectFormat && hasAssociations) {
                formatConsistentCount++;
            }
            
            console.log(`   ${index + 1}. ${enrollment.Id}`);
            console.log(`      Enrollment_ID__c: ${enrollment.Enrollment_ID__c || 'MISSING'} ${hasCorrectFormat ? '✅' : '❌'}`);
            console.log(`      Associations: ${hasAssociations ? '✅ Complete' : '❌ Missing'}`);
        });
        
        console.log(`🎯 Format Consistency: ${formatConsistentCount}/${recentEnrollments.length} (${Math.round(formatConsistentCount/recentEnrollments.length*100)}%)`);

        // Step 6: Final summary
        console.log('\n📊 WEBHOOK FIXES SUMMARY');
        console.log('=' .repeat(60));
        
        const finalEnrollments = await conn.sobject("Docebo_CourseEnrollment__c")
            .find({})
            .execute();
        
        const finalWithCourse = finalEnrollments.filter(e => e.Course__c).length;
        const finalWithUser = finalEnrollments.filter(e => e.Docebo_User__c).length;
        const finalComplete = finalEnrollments.filter(e => e.Course__c && e.Docebo_User__c).length;
        const finalCorrectFormat = finalEnrollments.filter(e => e.Enrollment_ID__c && e.Enrollment_ID__c.startsWith('UE-')).length;
        
        console.log(`📋 Total Enrollments: ${finalEnrollments.length}`);
        console.log(`✅ With Course Association: ${finalWithCourse}/${finalEnrollments.length} (${Math.round(finalWithCourse/finalEnrollments.length*100)}%)`);
        console.log(`✅ With User Association: ${finalWithUser}/${finalEnrollments.length} (${Math.round(finalWithUser/finalEnrollments.length*100)}%)`);
        console.log(`🎯 Fully Associated: ${finalComplete}/${finalEnrollments.length} (${Math.round(finalComplete/finalEnrollments.length*100)}%)`);
        console.log(`📝 Correct ID Format: ${finalCorrectFormat}/${finalEnrollments.length} (${Math.round(finalCorrectFormat/finalEnrollments.length*100)}%)`);
        
        if (finalComplete === finalEnrollments.length && finalCorrectFormat === finalEnrollments.length) {
            console.log('\n🎉 PERFECT! All webhook fixes are working correctly!');
            console.log('✅ Course enrollment webhooks will now create properly associated records');
        } else {
            console.log('\n⚠️ Some issues remain. Check the fixes and test again.');
        }

        console.log('\n💡 WEBHOOK FIXES IMPLEMENTED:');
        console.log('=' .repeat(60));
        console.log('✅ Fixed object name: Docebo_Course_Enrollment__c → Docebo_CourseEnrollment__c');
        console.log('✅ Fixed external ID field: External_Id__c → Enrollment_ID__c');
        console.log('✅ Fixed course field: Course_Id__c → Course__c');
        console.log('✅ Fixed user field: User_Id__c → Docebo_User__c');
        console.log('✅ Fixed ID format: courseId-userId → UE-courseId-userId');
        console.log('✅ Updated webhook controller to use fixed batch function');
        console.log('✅ Updated all fallback functions with correct field names');
        console.log('✅ Fixed user creation date handling in webhook');

    } catch (error) {
        console.error('💥 Error in test:', error);
    }
}

// Execute the test
console.log('🔄 Starting webhook enrollment fixes test...');
testWebhookEnrollmentFixes()
    .then(() => {
        console.log('\n✅ Test completed');
        process.exit(0);
    })
    .catch(err => {
        console.error('💥 Test failed:', err);
        process.exit(1);
    });
