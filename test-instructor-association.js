require('dotenv').config();
const doceboServices = require('./platform/docebo/services');
const { createInstructor } = require('./platform/salesforce/instructors/createInstructor');
const { getCourseSalesForceId } = require('./platform/salesforce/courses/createCourse');
const { getIltSessionSalesForceId } = require('./platform/salesforce/session/createSession');

async function testInstructorAssociation(courseId) {
  try {
    console.log(`Testing instructor association for course ID: ${courseId}`);
    
    // Step 1: Get course sessions
    const sessionsResult = await doceboServices.getSessionListedInfo(courseId);
    
    if (!sessionsResult || sessionsResult.length === 0) {
      console.log(`No sessions found for course ID: ${courseId}`);
      return;
    }
    
    console.log(`Found ${sessionsResult.length} sessions for course ID: ${courseId}`);
    
    // Step 2: Process each session
    for (const session of sessionsResult) {
      console.log(`\nProcessing session ID: ${session.id}`);
      
      // Get detailed session info
      const sessionInfo = await doceboServices.getCourseSessionInfo(session.id);
      
      if (!sessionInfo || sessionInfo.status !== 200 || !sessionInfo.data) {
        console.log(`Failed to retrieve data for session ID: ${session.id}`);
        continue;
      }
      
      const sessionData = sessionInfo.data;
      console.log(`Session name: ${sessionData.name}`);
      
      // Check if session has instructors
      if (!sessionData.instructors || sessionData.instructors.length === 0) {
        console.log(`No instructors found for session ID: ${session.id}`);
        continue;
      }
      
      // Get Salesforce IDs
      const courseSalesforceId = await getCourseSalesForceId(courseId);
      const sessionSalesforceId = await getIltSessionSalesForceId(session.id);
      
      console.log(`Course Salesforce ID: ${courseSalesforceId || 'Not found'}`);
      console.log(`Session Salesforce ID: ${sessionSalesforceId || 'Not found'}`);
      
      // Process each instructor
      for (const instructor of sessionData.instructors) {
        console.log(`\nProcessing instructor: ${instructor.firstname} ${instructor.lastname} (ID: ${instructor.user_id})`);
        
        // Get instructor data
        const instructorResponse = await doceboServices.getInstructorData(
          instructor.user_id,
          courseId,
          session.id
        );
        
        if (!instructorResponse || instructorResponse.status !== 200 || !instructorResponse.data) {
          console.log(`Failed to retrieve instructor data for user ID: ${instructor.user_id}`);
          continue;
        }
        
        console.log('Instructor data retrieved successfully');
        
        // Create/update instructor in Salesforce
        const result = await createInstructor(
          instructorResponse.data,
          instructor.user_id,
          courseId,
          session.id,
          instructor  // Pass the instructor info with firstname/lastname
        );
        
        if (result) {
          console.log('Instructor successfully associated in Salesforce');
        } else {
          console.log('Failed to associate instructor in Salesforce');
        }
      }
    }
    
  } catch (error) {
    console.error('Error in instructor association process:', error);
  }
}

// Parse command line arguments or use default
const courseId = process.argv[2] || 557;

// Execute
testInstructorAssociation(courseId)
  .then(() => console.log('Done'))
  .catch(err => console.error('Script failed:', err));