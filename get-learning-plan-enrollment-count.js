require('dotenv').config();
const getApiData = require("./common/docebo/fetcher");
const APP_BASE = process.env.DOCEBO_API_BASE_URL;

async function getLearningPlanEnrollmentCount() {
    try {
        console.log('🔍 Getting total learning plan enrollment count from Docebo...');
        console.log('⏳ This will fetch all pages to get accurate count...\n');

        const startTime = Date.now();
        let page = 1;
        let pageSize = 200; // Larger page size for efficiency
        let totalCount = 0;
        let hasMoreData = true;

        while (hasMoreData) {
            try {
                console.log(`📄 Fetching page ${page}...`);
                
                const response = await getApiData(
                    'GET', 
                    `${APP_BASE}/learningplan/v1/learningplans/enrollments?page=${page}&page_size=${pageSize}`, 
                    null
                );

                if (!response || !response.data) {
                    console.log('❌ No data received from API');
                    break;
                }

                const items = response.data.items || [];
                const pageCount = items.length;
                totalCount += pageCount;

                console.log(`   📊 Page ${page}: ${pageCount} enrollments (Total so far: ${totalCount})`);

                // Check if there's more data
                hasMoreData = response.data.has_more_data === true;
                
                if (hasMoreData) {
                    page++;
                } else {
                    console.log('✅ Reached end of data');
                }

            } catch (pageError) {
                console.error(`❌ Error fetching page ${page}:`, pageError.message);
                break;
            }
        }

        const endTime = Date.now();
        const duration = ((endTime - startTime) / 1000).toFixed(2);

        console.log('\n📊 LEARNING PLAN ENROLLMENT COUNT SUMMARY:');
        console.log('=' .repeat(60));
        console.log(`🎯 TOTAL LEARNING PLAN ENROLLMENTS: ${totalCount.toLocaleString()}`);
        console.log(`📄 Total Pages Processed: ${page}`);
        console.log(`⏱️  Total Fetch Duration: ${duration} seconds`);
        console.log(`📈 Average Enrollments per Page: ${Math.round(totalCount / page)}`);

        if (totalCount > 0) {
            console.log('\n💡 INSIGHTS:');
            console.log(`   • This represents individual user enrollments in learning plans`);
            console.log(`   • Each enrollment links a user to a specific learning plan`);
            console.log(`   • Learning plans typically contain multiple courses`);
            console.log(`   • Total course enrollments would be significantly higher`);
        }

        return {
            success: true,
            totalEnrollments: totalCount,
            pagesProcessed: page,
            duration: duration
        };

    } catch (error) {
        console.error('💥 Error getting learning plan enrollment count:', error);
        
        if (error.message && error.message.includes('404')) {
            console.log('\n💡 Possible causes:');
            console.log('   - Learning plan enrollment endpoint not available');
            console.log('   - API permissions insufficient');
            console.log('   - Endpoint URL might be different');
        } else if (error.message && error.message.includes('401')) {
            console.log('\n💡 Authentication issue:');
            console.log('   - Check Docebo API credentials');
            console.log('   - Verify API token permissions');
        }

        return {
            success: false,
            error: error.message
        };
    }
}

// Execute the script
console.log('🚀 Starting learning plan enrollment count...');
getLearningPlanEnrollmentCount()
    .then((result) => {
        if (result.success) {
            console.log('\n🎉 SUCCESS! Learning plan enrollment count completed.');
            console.log(`📊 Final Count: ${result.totalEnrollments.toLocaleString()} enrollments`);
        } else {
            console.log(`\n❌ FAILED: ${result.error}`);
        }
        
        console.log('\n✅ Script completed');
        process.exit(0);
    })
    .catch(err => {
        console.error('💥 Script failed:', err);
        process.exit(1);
    });
