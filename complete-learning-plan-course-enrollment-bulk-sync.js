require('dotenv').config();
const getConnection = require('./platform/salesforce/common/getConnection');
const getApiData = require("./common/docebo/fetcher");
const APP_BASE = process.env.DOCEBO_API_BASE_URL;

async function completeLearningPlanCourseEnrollmentBulkSync() {
    try {
        console.log('🚀 COMPLETE LEARNING PLAN COURSE ENROLLMENT BULK SYNC');
        console.log('=' .repeat(80));
        console.log('🎯 Target: Docebo_Learning_Plan_Course_Enrollment__c object');
        console.log('📡 Strategy: Get all learning plans, then get course enrollments for each');
        
        const conn = await getConnection();
        if (!conn || !conn.accessToken) {
            throw new Error('Failed to connect to Salesforce');
        }

        console.log('✅ Connected to Salesforce successfully');

        // Step 1: Get existing Salesforce Learning Plan Course Enrollments
        console.log('\n📊 STEP 1: Getting Existing Learning Plan Course Enrollments...');
        console.log('-'.repeat(50));
        
        const existingEnrollments = await conn.sobject("Docebo_Learning_Plan_Course_Enrollment__c")
            .find({})
            .execute();
            
        console.log(`Found ${existingEnrollments.length} existing learning plan course enrollments in Salesforce`);
        
        if (existingEnrollments.length > 0) {
            console.log('\n📋 Sample existing records:');
            existingEnrollments.slice(0, 3).forEach((enrollment, index) => {
                console.log(`   ${index + 1}. ${enrollment.Name} (${enrollment.Id})`);
                console.log(`      Learning Plan: ${enrollment.Learning_Plan__c}`);
                console.log(`      Course: ${enrollment.Course__c}`);
                console.log(`      User: ${enrollment.User__c}`);
            });
        }

        // Step 2: Get Learning Plans from Salesforce
        console.log('\n📚 STEP 2: Getting Learning Plans from Salesforce...');
        console.log('-'.repeat(50));
        
        const sfLearningPlans = await conn.sobject("Docebo_Learning_Plan__c")
            .find({})
            .execute();
            
        const learningPlanMapping = new Map();
        sfLearningPlans.forEach(plan => {
            if (plan.Learning_Plan_External_Id__c) {
                learningPlanMapping.set(plan.Learning_Plan_External_Id__c.toString(), {
                    id: plan.Id,
                    name: plan.Learning_Plan_Name__c,
                    externalId: plan.Learning_Plan_External_Id__c
                });
            }
        });
        
        console.log(`📊 Learning plan mapping created for ${learningPlanMapping.size} learning plans`);

        // Step 3: Get Courses from Salesforce
        console.log('\n📚 STEP 3: Getting Courses from Salesforce...');
        console.log('-'.repeat(50));
        
        const sfCourses = await conn.sobject("Docebo_Course__c")
            .find({})
            .execute();
            
        const courseMapping = new Map();
        sfCourses.forEach(course => {
            if (course.Course_External_Id__c) {
                courseMapping.set(course.Course_External_Id__c.toString(), {
                    id: course.Id,
                    name: course.Course_Name__c,
                    externalId: course.Course_External_Id__c
                });
            }
        });
        
        console.log(`📊 Course mapping created for ${courseMapping.size} courses`);

        // Step 4: Get Users from Salesforce
        console.log('\n👥 STEP 4: Getting Users from Salesforce...');
        console.log('-'.repeat(50));
        
        const sfUsers = await conn.sobject("Docebo_Users__c")
            .find({})
            .execute();
            
        const userMapping = new Map();
        sfUsers.forEach(user => {
            if (user.User_Unique_Id__c) {
                userMapping.set(user.User_Unique_Id__c.toString(), {
                    id: user.Id,
                    email: user.Email__c,
                    externalId: user.User_Unique_Id__c
                });
            }
        });
        
        console.log(`📊 User mapping created for ${userMapping.size} users`);

        // Step 5: Fetch Learning Plan Course Enrollments from Docebo
        console.log('\n🔍 STEP 5: Fetching Learning Plan Course Enrollments from Docebo...');
        console.log('-'.repeat(50));
        
        let allLearningPlanCourseEnrollments = [];
        const learningPlanIds = Array.from(learningPlanMapping.keys());
        
        console.log(`📊 Processing ${learningPlanIds.length} learning plans...`);
        
        for (let i = 0; i < learningPlanIds.length; i++) {
            const learningPlanId = learningPlanIds[i];
            const planInfo = learningPlanMapping.get(learningPlanId);
            
            console.log(`   📄 Processing LP ${i + 1}/${learningPlanIds.length}: ${learningPlanId} (${planInfo.name})...`);
            
            try {
                // Try different API endpoints to get course enrollments for this learning plan
                const endpoints = [
                    `/learn/v1/enrollments?learning_plan_id=${learningPlanId}`,
                    `/course/v1/courses/enrollments?learning_plan_id=${learningPlanId}`,
                    `/learningplan/v1/learningplans/${learningPlanId}/enrollments`
                ];
                
                let planEnrollments = [];
                let workingEndpoint = null;
                
                for (const endpoint of endpoints) {
                    try {
                        console.log(`      🧪 Trying: ${endpoint}`);
                        
                        let page = 1;
                        let hasMoreData = true;
                        let endpointEnrollments = [];
                        
                        while (hasMoreData && page <= 10) { // Limit to 10 pages per endpoint
                            const response = await getApiData(
                                'GET', 
                                `${APP_BASE}${endpoint}?page=${page}&page_size=200`, 
                                null
                            );
                            
                            if (response && response.status === 200) {
                                const items = response.data?.items || [];
                                
                                // Filter for items that have both course_id and user_id
                                const validItems = items.filter(item => 
                                    item.course_id && item.user_id && 
                                    courseMapping.has(item.course_id.toString()) &&
                                    userMapping.has(item.user_id.toString())
                                );
                                
                                endpointEnrollments.push(...validItems);
                                
                                console.log(`         📄 Page ${page}: ${items.length} items, ${validItems.length} valid course enrollments`);
                                
                                hasMoreData = response.data?.has_more_data || false;
                                if (items.length === 0) hasMoreData = false;
                                page++;
                            } else {
                                hasMoreData = false;
                            }
                        }
                        
                        if (endpointEnrollments.length > 0) {
                            console.log(`      ✅ Found ${endpointEnrollments.length} course enrollments via ${endpoint}`);
                            planEnrollments = endpointEnrollments;
                            workingEndpoint = endpoint;
                            break; // Use the first working endpoint
                        }
                        
                    } catch (endpointError) {
                        console.log(`      ❌ ${endpoint}: ${endpointError.message}`);
                    }
                }
                
                if (planEnrollments.length > 0) {
                    // Add learning plan context to each enrollment
                    planEnrollments.forEach(enrollment => {
                        enrollment.learning_plan_id = learningPlanId;
                        enrollment.learning_plan_name = planInfo.name;
                    });
                    
                    allLearningPlanCourseEnrollments.push(...planEnrollments);
                    console.log(`      ✅ Added ${planEnrollments.length} course enrollments for LP ${learningPlanId}`);
                } else {
                    console.log(`      ⚪ No course enrollments found for LP ${learningPlanId}`);
                }
                
            } catch (planError) {
                console.log(`      ❌ Error processing LP ${learningPlanId}: ${planError.message}`);
                continue;
            }
            
            // Progress indicator every 10 learning plans
            if ((i + 1) % 10 === 0) {
                console.log(`   📊 Progress: ${i + 1}/${learningPlanIds.length} LPs processed, ${allLearningPlanCourseEnrollments.length.toLocaleString()} total course enrollments found`);
            }
        }
        
        console.log(`\n✅ Docebo fetch completed:`);
        console.log(`   Learning plans processed: ${learningPlanIds.length}`);
        console.log(`   Total learning plan course enrollments found: ${allLearningPlanCourseEnrollments.length.toLocaleString()}`);

        if (allLearningPlanCourseEnrollments.length === 0) {
            console.log('⚠️ No learning plan course enrollments found via API');
            return {
                success: true,
                doceboTotal: 0,
                salesforceInitial: existingEnrollments.length,
                message: 'No data found in Docebo'
            };
        }

        // Step 6: Identify missing enrollments and prepare records
        console.log('\n🔍 STEP 6: Preparing Learning Plan Course Enrollment Records...');
        console.log('-'.repeat(50));
        
        const enrollmentsToCreate = [];
        const existingKeys = new Set();
        
        // Track existing enrollments by unique key
        existingEnrollments.forEach(enrollment => {
            if (enrollment.Learning_Plan__c && enrollment.Course__c && enrollment.User__c) {
                const key = `${enrollment.Learning_Plan__c}-${enrollment.Course__c}-${enrollment.User__c}`;
                existingKeys.add(key);
            }
        });
        
        let skippedCount = 0;
        let duplicatesSkipped = 0;
        
        for (const doceboEnrollment of allLearningPlanCourseEnrollments) {
            const learningPlanId = doceboEnrollment.learning_plan_id;
            const courseId = doceboEnrollment.course_id;
            const userId = doceboEnrollment.user_id;
            
            const salesforceLearningPlan = learningPlanMapping.get(learningPlanId.toString());
            const salesforceCourse = courseMapping.get(courseId.toString());
            const salesforceUser = userMapping.get(userId.toString());
            
            if (!salesforceLearningPlan || !salesforceCourse || !salesforceUser) {
                skippedCount++;
                continue;
            }
            
            // Check for duplicates
            const uniqueKey = `${salesforceLearningPlan.id}-${salesforceCourse.id}-${salesforceUser.id}`;
            if (existingKeys.has(uniqueKey)) {
                duplicatesSkipped++;
                continue;
            }
            
            // Add to tracking set
            existingKeys.add(uniqueKey);
            
            // Parse enrollment date
            let enrollmentDate = "";
            if (doceboEnrollment.enrollment_date || doceboEnrollment.date_inscr || doceboEnrollment.enroll_date_of_enrollment) {
                const dateStr = doceboEnrollment.enrollment_date || doceboEnrollment.date_inscr || doceboEnrollment.enroll_date_of_enrollment;
                try {
                    enrollmentDate = new Date(dateStr.replace(' ', 'T')).toISOString();
                } catch (e) {
                    enrollmentDate = "";
                }
            }
            
            // Parse completion date
            let completionDate = "";
            if (doceboEnrollment.completion_date || doceboEnrollment.course_complete_date) {
                const dateStr = doceboEnrollment.completion_date || doceboEnrollment.course_complete_date;
                try {
                    completionDate = new Date(dateStr.replace(' ', 'T')).toISOString();
                } catch (e) {
                    completionDate = "";
                }
            }
            
            // Create learning plan course enrollment record
            const enrollmentRecord = {
                Learning_Plan__c: salesforceLearningPlan.id,
                Course__c: salesforceCourse.id,
                User__c: salesforceUser.id,
                Status__c: doceboEnrollment.status || doceboEnrollment.enrollment_status || 'enrolled',
                Enrollment_Date__c: enrollmentDate,
                Completion_Date__c: completionDate,
                Score__c: doceboEnrollment.score || doceboEnrollment.enrollment_score || 0,
                Progress__c: doceboEnrollment.completion_percentage || 0,
                Time_Spent__c: doceboEnrollment.total_time || 0
            };
            
            enrollmentsToCreate.push(enrollmentRecord);
        }
        
        console.log(`Prepared ${enrollmentsToCreate.length.toLocaleString()} learning plan course enrollment records`);
        console.log(`Skipped ${skippedCount.toLocaleString()} enrollments (missing references)`);
        console.log(`🛡️ Duplicates prevented: ${duplicatesSkipped.toLocaleString()} enrollments`);

        if (enrollmentsToCreate.length === 0) {
            console.log('✅ All learning plan course enrollments are already synced!');
            return {
                success: true,
                doceboTotal: allLearningPlanCourseEnrollments.length,
                salesforceInitial: existingEnrollments.length,
                synced: 0,
                skipped: skippedCount,
                duplicates: duplicatesSkipped
            };
        }

        return {
            success: true,
            doceboTotal: allLearningPlanCourseEnrollments.length,
            salesforceInitial: existingEnrollments.length,
            toCreate: enrollmentsToCreate.length,
            skipped: skippedCount,
            duplicates: duplicatesSkipped,
            // Continue with sync phase...
            enrollmentsToCreate: enrollmentsToCreate
        };

    } catch (error) {
        console.error('💥 Error in complete learning plan course enrollment bulk sync:', error);
        return {
            success: false,
            error: error.message
        };
    }
}

// Execute the complete bulk sync
console.log('🚀 Starting Complete Learning Plan Course Enrollment Bulk Sync...');
completeLearningPlanCourseEnrollmentBulkSync()
    .then((result) => {
        if (result.success && result.enrollmentsToCreate) {
            console.log('\n📋 PHASE 1 COMPLETE - CONTINUING TO SYNC PHASE...');
            console.log('=' .repeat(60));
            console.log(`📊 Found ${result.doceboTotal.toLocaleString()} total course enrollments in learning plans`);
            console.log(`📝 Prepared ${result.toCreate.toLocaleString()} records to create`);
            console.log(`⏭️ Skipped ${result.skipped.toLocaleString()} (missing references)`);
            console.log(`🛡️ Duplicates prevented: ${result.duplicates.toLocaleString()}`);
            
            // Continue with the sync phase
            require('./complete-learning-plan-course-enrollment-sync-phase2.js')(result);
        } else {
            console.log('\n📋 LEARNING PLAN COURSE ENROLLMENT BULK SYNC SUMMARY:');
            console.log('=' .repeat(60));
            
            if (result.success) {
                console.log(`📊 Total Course Enrollments Found: ${result.doceboTotal.toLocaleString()}`);
                console.log(`📊 Salesforce Initial: ${result.salesforceInitial.toLocaleString()}`);
                if (result.synced !== undefined) {
                    console.log(`✅ Successfully Synced: ${result.synced.toLocaleString()}`);
                }
                if (result.skipped) {
                    console.log(`⏭️ Skipped: ${result.skipped.toLocaleString()}`);
                }
                if (result.duplicates) {
                    console.log(`🛡️ Duplicates Prevented: ${result.duplicates.toLocaleString()}`);
                }
                
                if (result.doceboTotal === 0) {
                    console.log(`⚠️ No learning plan course enrollments found in Docebo`);
                } else {
                    console.log(`✅ All learning plan course enrollments already synced!`);
                }
            } else {
                console.log(`❌ Learning plan course enrollment bulk sync failed: ${result.error}`);
            }
            
            console.log('\n✅ Learning plan course enrollment bulk sync completed');
            process.exit(0);
        }
    })
    .catch(err => {
        console.error('💥 Learning plan course enrollment bulk sync failed:', err);
        process.exit(1);
    });
