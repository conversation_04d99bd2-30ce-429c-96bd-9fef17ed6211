require('dotenv').config();
const getConnection = require('./platform/salesforce/common/getConnection');

async function checkSalesforceCourseFields() {
    try {
        console.log('🔍 CHECKING SALESFORCE COURSE FIELDS');
        console.log('=' .repeat(60));
        
        const conn = await getConnection();
        if (!conn || !conn.accessToken) {
            throw new Error('Failed to connect to Salesforce');
        }

        // Get all fields from Docebo_Course__c object
        console.log('📊 Getting all fields from Docebo_Course__c...');
        
        const courseMetadata = await conn.sobject("Docebo_Course__c").describe();
        
        console.log(`\n📋 All Docebo_Course__c fields:`);
        courseMetadata.fields.forEach(field => {
            console.log(`   ${field.name} (${field.type}) - ${field.label}`);
        });
        
        // Get sample course records with all fields
        console.log('\n📊 Getting sample course records...');
        
        const courses = await conn.sobject("Docebo_Course__c")
            .find({})
            .limit(5)
            .execute();
            
        console.log(`\n📋 Sample Course Records:`);
        courses.forEach((course, index) => {
            console.log(`\n   Course ${index + 1}: ${course.Course_Name__c}`);
            
            // Show all ID-related fields
            const idFields = [
                'Course_External_Id__c',
                'Course_Unique_Id__c', 
                'Course_Internal_Id__c',
                'Course_Code__c',
                'Course_Slug__c',
                'Course_UID__c'
            ];
            
            idFields.forEach(field => {
                if (course[field] !== undefined) {
                    console.log(`      ${field}: ${course[field]}`);
                }
            });
            
            // Show all fields for first course
            if (index === 0) {
                console.log(`\n      All fields for first course:`);
                Object.keys(course).forEach(key => {
                    if (key !== 'attributes' && course[key] !== null && course[key] !== undefined) {
                        console.log(`         ${key}: ${course[key]}`);
                    }
                });
            }
        });
        
        // Check if there are any courses with uidCourse-like values
        console.log('\n🔍 Searching for courses with alphanumeric IDs...');
        
        const alphanumericCourses = courses.filter(course => {
            const externalId = course.Course_External_Id__c;
            return externalId && /[A-Za-z]/.test(externalId.toString());
        });
        
        console.log(`Found ${alphanumericCourses.length} courses with alphanumeric External IDs`);
        
        alphanumericCourses.forEach(course => {
            console.log(`   ${course.Course_Name__c}: ${course.Course_External_Id__c}`);
        });
        
        // Also check User fields
        console.log('\n👥 Checking User field mappings...');
        
        const users = await conn.sobject("Docebo_Users__c")
            .find({})
            .limit(5)
            .execute();
            
        console.log(`\n📋 Sample User Records:`);
        users.forEach((user, index) => {
            console.log(`\n   User ${index + 1}: ${user.Email__c}`);
            
            // Show all ID-related fields
            const userIdFields = [
                'User_Unique_Id__c',
                'User_External_Id__c',
                'User_Internal_Id__c',
                'Username__c'
            ];
            
            userIdFields.forEach(field => {
                if (user[field] !== undefined) {
                    console.log(`      ${field}: ${user[field]}`);
                }
            });
        });
        
        return {
            success: true,
            courseFields: courseMetadata.fields.map(f => f.name),
            sampleCourses: courses.length,
            alphanumericCourses: alphanumericCourses.length
        };
        
    } catch (error) {
        console.error('💥 Error checking Salesforce course fields:', error);
        return {
            success: false,
            error: error.message
        };
    }
}

console.log('🔄 Starting Salesforce course fields check...');
checkSalesforceCourseFields()
    .then((result) => {
        console.log('\n📋 COURSE FIELDS CHECK SUMMARY:');
        console.log('=' .repeat(50));
        
        if (result.success) {
            console.log(`✅ Check completed successfully`);
            console.log(`📊 Total Course Fields: ${result.courseFields.length}`);
            console.log(`📊 Sample Courses: ${result.sampleCourses}`);
            console.log(`📊 Alphanumeric Course IDs: ${result.alphanumericCourses}`);
        } else {
            console.log(`❌ Check failed: ${result.error}`);
        }
        
        console.log('\n✅ Salesforce course fields check completed');
        process.exit(0);
    })
    .catch(err => {
        console.error('💥 Salesforce course fields check failed:', err);
        process.exit(1);
    });
