require('dotenv').config();
const getApiData = require("./common/docebo/fetcher");
const doceboService = require("./platform/docebo/services");
const { createNewLearningPlanById, getLearningPlanSalesForceId } = require("./platform/salesforce/LearningPlan/createLearningPlan");
const { createNewCourse } = require("./platform/salesforce/courses/createCourse");
const getConnection = require("./platform/salesforce/common/getConnection");
const APP_BASE = process.env.DOCEBO_API_BASE_URL;

// Function to get courses for a specific learning plan
async function getLearningPlanCourses(learningPlanId) {
    try {
        const response = await getApiData('GET', `${APP_BASE}/learningplan/v1/learningplans/${learningPlanId}/courses`, null);
        return response;
    } catch (error) {
        console.error(`Error fetching courses for learning plan ${learningPlanId}:`, error);
        return { status: 505, data: error.message };
    }
}

// Get the field names of the Docebo_Learning_Plan_Course__c object
async function getJunctionObjectFields() {
    try {
        const conn = await getConnection();
        if (!conn || !conn.accessToken) {
            console.error("Invalid Salesforce connection");
            return null;
        }
        
        // First, describe the object to get its fields
        const objectDescribe = await conn.describe("Docebo_Learning_Plan_Course__c");
        
        // Extract field names and types
        const fields = {};
        objectDescribe.fields.forEach(field => {
            fields[field.name] = {
                type: field.type,
                label: field.label,
                updateable: field.updateable,
                createable: field.createable
            };
        });
        
        // Save the field info to a file for reference
        const fs = require('fs');
        fs.writeFileSync('docebo-learning-plan-course-fields.json', JSON.stringify(fields, null, 2));
        console.log("Saved field information to docebo-learning-plan-course-fields.json");
        
        return fields;
    } catch (error) {
        console.error(`Error getting junction object fields: ${error.message}`);
        return null;
    }
}

// Create relationship between learning plan and course
async function createLearningPlanCourseRelationship(courseId, sfCourseId, learningPlanId, sfLearningPlanId) {
    try {
        const conn = await getConnection();
        if (!conn || !conn.accessToken) {
            console.error("Invalid Salesforce connection");
            return false;
        }
        
        // Get field information first
        console.log("Getting field information for Docebo_Learning_Plan_Course__c...");
        const fields = await getJunctionObjectFields();
        
        if (!fields) {
            console.error("Failed to get field information for Docebo_Learning_Plan_Course__c");
            return false;
        }
        
        // Find the learning plan ID field (look for fields that might match)
        let learningPlanField = null;
        let courseField = null;
        
        Object.entries(fields).forEach(([fieldName, fieldInfo]) => {
            // Check for fields that might be the learning plan reference
            if (fieldInfo.type === 'reference' &&
                (fieldName.toLowerCase().includes('plan') || fieldName.toLowerCase().includes('learning'))) {
                learningPlanField = fieldName;
            }
            
            // Check for fields that might be the course reference
            if (fieldInfo.type === 'reference' && fieldName.toLowerCase().includes('course')) {
                courseField = fieldName;
            }
        });
        
        if (!learningPlanField || !courseField) {
            console.error(`Could not identify fields for learning plan and course. Available fields: ${Object.keys(fields).join(', ')}`);
            return false;
        }
        
        console.log(`Using field ${learningPlanField} for learning plan and ${courseField} for course`);
        
        // Create the relationship record
        const relationshipRecord = {};
        relationshipRecord[learningPlanField] = sfLearningPlanId;
        relationshipRecord[courseField] = sfCourseId;
        
        // Add a name field if it exists and is createable
        if (fields['Name'] && fields['Name'].createable) {
            relationshipRecord['Name'] = `LP${learningPlanId}-Course${courseId}`;
        }
        
        // Try to create the record
        try {
            const result = await conn.sobject("Docebo_Learning_Plan_Course__c")
                .create(relationshipRecord);
                
            if (result.success) {
                console.log(`Successfully created relationship between learning plan ${learningPlanId} and course ${courseId} in Salesforce`);
                return true;
            } else {
                console.error(`Failed to create relationship: ${JSON.stringify(result)}`);
                return false;
            }
        } catch (error) {
            console.error(`Error creating relationship: ${error.message}`);
            return false;
        }
    } catch (error) {
        console.error(`Error in createLearningPlanCourseRelationship: ${error.message}`);
        return false;
    }
}

// Main function to count all learning plan courses and push to Salesforce
async function countAllLearningPlanCourses() {
    try {
        console.log("Fetching all learning plans...");
        const learningPlans = await doceboService.getTotalLearningPlanListedInfo();
        console.log(`Found ${learningPlans.length} learning plans.`);
        
        let totalCourses = 0;
        let coursesPerPlan = [];
        let successfulPushes = 0;
        let failedPushes = 0;
        
        // First, log the first learning plan to understand its structure
        if (learningPlans.length > 0) {
            console.log("Structure of first learning plan:", JSON.stringify(learningPlans[0], null, 2));
        }
        
        // Iterate through each learning plan and get its courses
        for (const plan of learningPlans) {
            // Based on Docebo API, the properties are likely learning_plan_id and title or name
            const planId = plan.learning_plan_id || plan.id;
            const planName = plan.title || plan.name;
            
            console.log(`Fetching courses for learning plan: ${planName} (ID: ${planId})`);
            
            // Ensure the learning plan exists in Salesforce
            let sfLearningPlanId = await getLearningPlanSalesForceId(planId);
            if (!sfLearningPlanId) {
                console.log(`Learning plan ${planName} (ID: ${planId}) not found in Salesforce. Creating it...`);
                const created = await createNewLearningPlanById(planId);
                if (created) {
                    sfLearningPlanId = await getLearningPlanSalesForceId(planId);
                    console.log(`Learning plan ${planName} created in Salesforce with ID: ${sfLearningPlanId}`);
                } else {
                    console.error(`Failed to create learning plan ${planName} in Salesforce. Skipping its courses.`);
                    continue;
                }
            } else {
                console.log(`Learning plan ${planName} found in Salesforce with ID: ${sfLearningPlanId}`);
            }
            
            const coursesResponse = await getLearningPlanCourses(planId);
            
            if (coursesResponse.status === 200 && coursesResponse.data) {
                const courses = Array.isArray(coursesResponse.data.items) ? coursesResponse.data.items : [];
                const courseCount = courses.length;
                totalCourses += courseCount;
                
                // Store detailed course information
                coursesPerPlan.push({
                    plan_id: planId,
                    plan_name: planName,
                    course_count: courseCount,
                    courses: courses,
                    salesforce_id: sfLearningPlanId
                });
                
                console.log(`Learning plan "${planName}" has ${courseCount} courses.`);
                
                // Log first course structure if available
                if (courses.length > 0) {
                    console.log(`First course structure for "${planName}":`, JSON.stringify(courses[0], null, 2));
                }
                
                // Push courses to Salesforce and create relationships
                console.log(`Pushing ${courseCount} courses from learning plan "${planName}" to Salesforce...`);
                
                for (const course of courses) {
                    const courseId = course.id_course;
                    
                    try {
                        // First, ensure the course exists in Salesforce
                        const courseData = await doceboService.getCourseInfo(courseId);
                        if (courseData.status !== 200 || !courseData.data) {
                            console.error(`Failed to fetch detailed course data for course ${courseId}`);
                            failedPushes++;
                            continue;
                        }
                        
                        // Create or update the course in Salesforce
                        const sfCourseId = await createNewCourse(courseData.data);
                        if (!sfCourseId) {
                            console.error(`Failed to create/update course ${courseId} in Salesforce`);
                            failedPushes++;
                            continue;
                        }
                        
                        // Create relationship in Salesforce junction object
                        const courseTitle = courseData.data.name || courseData.data.title || `Course ${courseId}`;
                        const relationshipCreated = await createLearningPlanCourseRelationship(
                            courseId,
                            sfCourseId,
                            planId,
                            sfLearningPlanId
                        );
                        
                        if (relationshipCreated) {
                            console.log(`Successfully linked course ${courseId} (${courseTitle}) to learning plan ${planId} in Salesforce`);
                            successfulPushes++;
                        } else {
                            console.error(`Failed to link course ${courseId} (${courseTitle}) to learning plan ${planId} in Salesforce`);
                            failedPushes++;
                        }
                    } catch (error) {
                        console.error(`Error processing course ${courseId}:`, error);
                        failedPushes++;
                    }
                }
            } else {
                console.error(`Failed to fetch courses for learning plan ${planId}:`, coursesResponse.data);
            }
        }
        
        // Print Salesforce push summary
        console.log(`\nSalesforce Push Summary:`);
        console.log(`Successfully linked ${successfulPushes} learning plan-course relationships in Salesforce`);
        console.log(`Failed to link ${failedPushes} learning plan-course relationships in Salesforce`);
        console.log(`\nAll learning plans and their courses have been synchronized with Salesforce.`);
        console.log(`Relationships have been created in the Docebo_Learning_Plan_Course__c junction object.`);
        
        // Print summary
        console.log("\n===== SUMMARY =====");
        console.log(`Total learning plans: ${learningPlans.length}`);
        console.log(`Total courses across all learning plans: ${totalCourses}`);
        console.log("\nCourses per learning plan:");
        
        // Create a more simplified detailed output object (to avoid any circular references)
        const detailedOutput = {
            total_learning_plans: learningPlans.length,
            total_courses: totalCourses,
            learning_plans: coursesPerPlan.map(plan => ({
                plan_id: plan.plan_id,
                plan_name: plan.plan_name,
                course_count: plan.course_count,
                courses: plan.courses.map(course => ({
                    id_course: course.id_course,
                    uid: course.uid,
                    title: course.title,
                    type: course.type,
                    code: course.code || '',
                    is_published: course.is_published,
                    category: course.category,
                    language: course.language ? course.language.name : 'Unknown'
                }))
            }))
        };
        
        // Try to save to file with error handling
        try {
            const fs = require('fs');
            fs.writeFileSync('learning-plan-courses.json', JSON.stringify(detailedOutput, null, 2));
            console.log("\nDetailed output saved to learning-plan-courses.json");
        } catch (error) {
            console.error("Error saving output to file:", error.message);
            console.log("Continuing with console output only...");
        }
        
        // Print simple summary to console
        coursesPerPlan.forEach(plan => {
            console.log(`- ${plan.plan_name} (ID: ${plan.plan_id}): ${plan.course_count} courses`);
            if (plan.courses.length > 0) {
                plan.courses.slice(0, 3).forEach((course, idx) => {
                    const courseId = course.course_id || course.id;
                    const courseTitle = course.title || course.name;
                    console.log(`  ${idx+1}. ${courseTitle} (ID: ${courseId})`);
                });
                if (plan.courses.length > 3) {
                    console.log(`  ... and ${plan.courses.length - 3} more courses`);
                }
            }
        });
        
        return detailedOutput;
    } catch (error) {
        console.error("Error counting learning plan courses:", error);
        return null;
    }
}

// Execute the count function
countAllLearningPlanCourses()
    .then(result => {
        if (result) {
            console.log("\nSuccessfully counted all learning plan courses.");
        } else {
            console.error("Failed to count learning plan courses.");
        }
    })
    .catch(error => {
        console.error("Error executing count:", error);
    });