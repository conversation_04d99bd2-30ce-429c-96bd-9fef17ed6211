require('dotenv').config();
const getConnection = require('./platform/salesforce/common/getConnection');

async function simpleLearningPlanCourseEnrollmentCheck() {
    try {
        console.log('🔍 SIMPLE LEARNING PLAN COURSE ENROLLMENT CHECK');
        console.log('=' .repeat(60));
        
        const conn = await getConnection();
        if (!conn || !conn.accessToken) {
            throw new Error('Failed to connect to Salesforce');
        }

        console.log('✅ Connected to Salesforce successfully');

        // Simple count query
        const countResult = await conn.query("SELECT COUNT() FROM Docebo_Learning_Plan_Course_Enrollment__c");
        console.log(`📊 Total Learning Plan Course Enrollments: ${countResult.totalSize.toLocaleString()}`);

        // Get recent records with basic fields only
        const recentRecords = await conn.query(`
            SELECT Id, Name, CreatedDate
            FROM Docebo_Learning_Plan_Course_Enrollment__c 
            ORDER BY CreatedDate DESC 
            LIMIT 5
        `);
        
        console.log(`\nRecent records:`);
        recentRecords.records.forEach((record, index) => {
            console.log(`   ${index + 1}. ${record.Name || record.Id} - Created: ${record.CreatedDate}`);
        });

        return {
            success: true,
            totalRecords: countResult.totalSize
        };

    } catch (error) {
        console.error('💥 Error in simple check:', error);
        return {
            success: false,
            error: error.message
        };
    }
}

console.log('🔄 Starting Simple Learning Plan Course Enrollment Check...');
simpleLearningPlanCourseEnrollmentCheck()
    .then((result) => {
        console.log('\n📋 SIMPLE CHECK SUMMARY:');
        console.log('=' .repeat(40));
        
        if (result.success) {
            console.log(`✅ Check completed successfully`);
            console.log(`📊 Total Records: ${result.totalRecords.toLocaleString()}`);
            
            if (result.totalRecords <= 2) {
                console.log(`\n⚠️ Still only ${result.totalRecords} records - sync hasn't created new records yet`);
                console.log(`💡 The sync script found valid data but didn't create records due to missing lookups`);
                console.log(`🔧 Need to investigate the missing Learning Plan Enrollments and Course Enrollments`);
            } else {
                console.log(`\n🎉 SUCCESS: ${result.totalRecords.toLocaleString()} records found!`);
            }
        } else {
            console.log(`❌ Check failed: ${result.error}`);
        }
        
        console.log('\n✅ Simple learning plan course enrollment check completed');
        process.exit(0);
    })
    .catch(err => {
        console.error('💥 Simple learning plan course enrollment check failed:', err);
        process.exit(1);
    });
