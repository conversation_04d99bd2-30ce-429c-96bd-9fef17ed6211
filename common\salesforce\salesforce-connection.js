const axios = require('axios')
const getAccessToken = require("./access-token");
const jsforce = require('jsforce');

module.exports = async function getConnection() {
    let crendentialResult = await getAccessToken();
    //console.log("This is getConnection function", crendentialResult)
    if(crendentialResult.status == 200) {
        const conn = new jsforce.Connection({
            instanceUrl: "https://strivetogether--full.sandbox.my.salesforce.com",
            accessToken: crendentialResult.data.accessToken,
            version: '60.0'
        });
        return conn;
    }
}