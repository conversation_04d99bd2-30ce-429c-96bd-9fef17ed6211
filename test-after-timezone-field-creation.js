require('dotenv').config();
const axios = require('axios');

// Test webhook after creating TimeZone__c field on Lead
async function testAfterTimezoneFieldCreation() {
    console.log('🧪 Testing Webhook After Creating TimeZone__c Field on Lead...');
    
    // Test with User 18854 data
    const webhookPayload = {
        event: "user.created",
        fired_by_batch_action: false,
        message_id: `test-timezone-field-${Date.now()}`,
        payload: {
            fired_at: "2025-06-06 19:06:54",
            user_id: 18854,
            username: "<EMAIL>",
            first_name: "Aa123",
            last_name: "Bb123",
            email: "<EMAIL>",
            expiration_date: null
        }
    };

    console.log('\n📤 SENDING WEBHOOK PAYLOAD (After Creating TimeZone__c Field):');
    console.log('=' .repeat(60));
    console.log(JSON.stringify(webhookPayload, null, 2));

    console.log('\n🛠️ FIELD CONFIGURATION:');
    console.log('✅ Lead.TimeZone__c - Field should now exist');
    console.log('❌ Docebo_Users__c.TimeZone__c - Removed from code (field doesn\'t exist)');

    console.log('\n🔧 CODE CHANGES APPLIED:');
    console.log('✅ Lead uses: TimeZone__c = newUser.user_data.timezone');
    console.log('✅ Docebo_Users__c: TimeZone field removed');
    console.log('✅ Direct timezone mapping from user_data');

    console.log('\n🎯 EXPECTED USER 18854 DATA:');
    console.log('✅ Name: Aa123 Bb123');
    console.log('✅ Email: <EMAIL>');
    console.log('✅ TimeZone: Europe/Podgorica');
    console.log('✅ Languages: english');
    console.log('✅ Job Title: eqweqw');
    console.log('✅ Role Type: Communications');
    console.log('✅ Race: Black or African American');
    console.log('✅ Gender: Prefer not to respond');
    console.log('✅ City: eqweqw');
    console.log('✅ State: Arizona');
    console.log('✅ Initiative: ENOUGH Act');

    try {
        console.log('\n🔄 Sending webhook to local server...');
        
        const response = await axios.post('http://localhost:3000/docebo/user-created', webhookPayload, {
            headers: {
                'Content-Type': 'application/json'
            },
            timeout: 15000
        });

        console.log('\n✅ WEBHOOK RESPONSE:');
        console.log('=' .repeat(50));
        console.log(`Status: ${response.status}`);
        console.log(`Response:`, response.data);

        console.log('\n📋 WHAT TO EXPECT IN LOGS:');
        console.log('=' .repeat(60));
        console.log('✅ 📥 COURSE ENROLLMENT USER CREATION DATA for User 18854');
        console.log('✅ 🔧 PROCESSED USER DATA with comprehensive field mappings');
        console.log('✅ 🎯 CREATING LEAD with TimeZone__c = "Europe/Podgorica"');
        console.log('✅ Lead created successfully: [Lead ID]');
        console.log('✅ User created successfully: 18854');
        console.log('❌ NO "INVALID_FIELD" errors');

        console.log('\n🔍 VERIFY IN SALESFORCE:');
        console.log('=' .repeat(50));
        console.log('1. Search for "<EMAIL>" in Salesforce');
        console.log('2. Should find a Lead record');
        console.log('3. Check Lead has TimeZone__c = "Europe/Podgorica"');
        console.log('4. Check Lead has comprehensive field data');
        console.log('5. Search for Docebo_Users__c with User_Unique_Id__c = 18854');
        console.log('6. Verify Docebo_Users__c has all mapped fields (no timezone)');

        return true;

    } catch (error) {
        if (error.code === 'ECONNREFUSED') {
            console.log('\n⚠️ SERVER NOT RUNNING');
            console.log('Please start the server first with: npm start');
            console.log('Then run this test again after creating the TimeZone__c field');
        } else if (error.response) {
            console.log('\n❌ WEBHOOK ERROR:');
            console.log(`Status: ${error.response.status}`);
            console.log(`Response:`, error.response.data);
        } else {
            console.error('\n❌ NETWORK ERROR:', error.message);
        }
        return false;
    }
}

// Execute the test
async function runTimezoneFieldTest() {
    console.log('🚀 Testing After TimeZone Field Creation...');
    console.log('=' .repeat(70));

    console.log('\n📋 REQUIRED SALESFORCE SETUP:');
    console.log('=' .repeat(50));
    console.log('✅ STEP 1: Create TimeZone__c field on Lead object');
    console.log('   - Go to Setup → Object Manager → Lead');
    console.log('   - Fields & Relationships → New');
    console.log('   - Data Type: Text');
    console.log('   - Field Label: TimeZone');
    console.log('   - Field Name: TimeZone (becomes TimeZone__c)');
    console.log('   - Length: 255');
    console.log('   - Save');

    console.log('\n✅ STEP 2: Code is already updated');
    console.log('   - Lead uses TimeZone__c field');
    console.log('   - Docebo_Users__c timezone field removed');
    console.log('   - Direct mapping from user_data.timezone');

    // Test the webhook
    const testSuccess = await testAfterTimezoneFieldCreation();
    
    console.log('\n🎯 TIMEZONE FIELD TEST SUMMARY:');
    console.log('=' .repeat(60));
    
    if (testSuccess) {
        console.log('✅ Webhook sent successfully');
        console.log('✅ Code configured for TimeZone__c field');
        console.log('✅ User 18854 should now create successfully');
        console.log('✅ Lead should include TimeZone__c = "Europe/Podgorica"');
        console.log('✅ Docebo_Users__c should create without timezone field');
    } else {
        console.log('⚠️ Test couldn\'t complete - check server status');
    }

    console.log('\n💡 NEXT STEPS:');
    console.log('1. Create TimeZone__c field on Lead object in Salesforce');
    console.log('2. Start the server: npm start');
    console.log('3. Run this test: node test-after-timezone-field-creation.js');
    console.log('4. Check server logs for successful creation');
    console.log('5. Verify records in Salesforce');

    console.log('\n📊 EXPECTED OUTCOME:');
    console.log('✅ No INVALID_FIELD errors');
    console.log('✅ Lead created with TimeZone__c = "Europe/Podgorica"');
    console.log('✅ Lead has comprehensive field data (11+ fields populated)');
    console.log('✅ Docebo_Users__c created successfully (no timezone field)');
    console.log('✅ User 18854 finally exists in Salesforce!');

    console.log('\n✅ TimeZone field test completed!');
}

// Run the test
runTimezoneFieldTest()
    .then(() => {
        console.log('\n🎉 TimeZone field test completed!');
        console.log('Create the TimeZone__c field on Lead, then test the webhook.');
        process.exit(0);
    })
    .catch(err => {
        console.error('\n💥 Test failed:', err);
        process.exit(1);
    });
