require('dotenv').config();
const axios = require('axios');

// Test webhook with corrected timezone field configuration
async function testTimezoneFieldFix() {
    console.log('🧪 Testing Webhook with Corrected Timezone Field Configuration...');
    
    // Simulate a user creation webhook payload for User 18853
    const webhookPayload = {
        event: "user.created",
        fired_by_batch_action: false,
        message_id: `test-timezone-fix-${Date.now()}`,
        payload: {
            fired_at: new Date().toISOString(),
            user_id: 18853, // Same user that was failing
            username: "<EMAIL>",
            first_name: "name",
            last_name: "last",
            email: "<EMAIL>",
            expiration_date: null
        }
    };

    console.log('\n📤 SENDING WEBHOOK PAYLOAD (Corrected Timezone Fields):');
    console.log('=' .repeat(60));
    console.log(JSON.stringify(webhookPayload, null, 2));

    console.log('\n🛠️ FIELD CONFIGURATION:');
    console.log('✅ Lead.TimeZone__c - Will store timezone data');
    console.log('❌ Docebo_Users__c.TimeZone__c - Field doesn\'t exist (removed from code)');

    console.log('\n🔧 CODE CHANGES APPLIED:');
    console.log('✅ Changed Lead field from Time_Zone__c to TimeZone__c');
    console.log('✅ Removed TimeZone__c field from Docebo_Users__c creation');
    console.log('✅ Updated logging to reflect changes');

    try {
        console.log('\n🔄 Sending webhook to local server...');
        
        const response = await axios.post('http://localhost:3000/docebo/user-created', webhookPayload, {
            headers: {
                'Content-Type': 'application/json'
            },
            timeout: 10000
        });

        console.log('\n✅ WEBHOOK RESPONSE:');
        console.log('=' .repeat(50));
        console.log(`Status: ${response.status}`);
        console.log(`Response:`, response.data);

        console.log('\n📋 WHAT TO EXPECT IN LOGS:');
        console.log('=' .repeat(60));
        console.log('✅ No INVALID_FIELD errors');
        console.log('✅ TimeZone__c: "Europe/Oslo" in Lead data');
        console.log('✅ No TimeZone__c field in Docebo_Users__c data');
        console.log('✅ Lead created successfully message');
        console.log('✅ User created successfully message');

        console.log('\n💡 CHECK THE SERVER LOGS NOW!');
        console.log('Look for:');
        console.log('- 📥 SINGLE USER WEBHOOK DATA');
        console.log('- 🔧 PROCESSED USER DATA (no TimeZone__c logging)');
        console.log('- 🎯 CREATING LEAD with TimeZone__c');
        console.log('- ✅ Lead created successfully');
        console.log('- ✅ User created successfully');
        console.log('- ❌ NO "INVALID_FIELD" errors');

        return true;

    } catch (error) {
        if (error.code === 'ECONNREFUSED') {
            console.log('\n⚠️ SERVER NOT RUNNING');
            console.log('Please start the server first with: npm start');
            console.log('Then run this test again to verify the timezone field fix');
        } else {
            console.error('\n❌ WEBHOOK TEST ERROR:', error.message);
        }
        return false;
    }
}

// Execute the test
async function runTimezoneFieldFixTest() {
    console.log('🚀 Testing Timezone Field Fix...');
    console.log('=' .repeat(70));

    console.log('\n📋 FIELD REQUIREMENTS:');
    console.log('=' .repeat(50));
    console.log('✅ REQUIRED: Lead.TimeZone__c field');
    console.log('   - Field Label: TimeZone');
    console.log('   - Field Name: TimeZone (becomes TimeZone__c)');
    console.log('   - Data Type: Text');
    console.log('   - Length: 255');
    console.log('');
    console.log('❌ NOT NEEDED: Docebo_Users__c.TimeZone__c');
    console.log('   - This field doesn\'t exist and has been removed from code');

    console.log('\n🎯 EXPECTED TIMEZONE VALUES:');
    console.log('- Europe/Oslo (User 18853)');
    console.log('- Europe/Budapest');
    console.log('- America/New_York');
    console.log('- etc...');

    // Test the webhook
    const testSuccess = await testTimezoneFieldFix();
    
    console.log('\n🎯 TIMEZONE FIELD FIX SUMMARY:');
    console.log('=' .repeat(60));
    
    if (testSuccess) {
        console.log('✅ Webhook sent successfully');
        console.log('✅ Code configured for correct field names');
        console.log('✅ User 18853 should now create successfully');
        console.log('✅ Lead should include TimeZone__c field');
        console.log('✅ Docebo_Users__c creation should work (no timezone field)');
    } else {
        console.log('⚠️ Test couldn\'t complete - check server status');
    }

    console.log('\n💡 NEXT STEPS:');
    console.log('1. Create TimeZone__c field on Lead object in Salesforce');
    console.log('2. Start the server: npm start');
    console.log('3. Run this test: node test-timezone-field-fix.js');
    console.log('4. Check server logs for successful creation');
    console.log('5. Verify Lead record has timezone data');

    console.log('\n📊 EXPECTED OUTCOME:');
    console.log('✅ No INVALID_FIELD errors');
    console.log('✅ Lead created with TimeZone__c = "Europe/Oslo"');
    console.log('✅ Docebo_Users__c created successfully (no timezone field)');
    console.log('✅ All other field mappings working correctly');

    console.log('\n🔧 SALESFORCE SETUP NEEDED:');
    console.log('Go to Setup → Object Manager → Lead → Fields & Relationships → New');
    console.log('- Data Type: Text');
    console.log('- Field Label: TimeZone');
    console.log('- Field Name: TimeZone');
    console.log('- Length: 255');
    console.log('- Save');

    console.log('\n✅ Timezone field fix test completed!');
}

// Run the test
runTimezoneFieldFixTest()
    .then(() => {
        console.log('\n🎉 Timezone field fix test completed!');
        console.log('Create the TimeZone__c field on Lead object, then test the webhook.');
        process.exit(0);
    })
    .catch(err => {
        console.error('\n💥 Test failed:', err);
        process.exit(1);
    });
