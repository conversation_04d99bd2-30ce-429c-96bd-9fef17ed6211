const getConnection = require("../common/getConnection");
const getSalesForceUserId = require("../common/getSalesForceUserId");
const { createNewUser } = require("./createUser");

async function updateUser(userInfo, userListedInfo) {
    let updatedResult = "User update successed!";
    let tmpUserInfo = tidyData(userInfo, userListedInfo);
    const { User_Creation_Date__c, User_Expiration_Date__c, Who__c, User__c, ...rest } = tmpUserInfo;
    tmpUserInfo = rest;
    const conn = await getConnection();
    if (conn.accessToken) {
        const record = await conn.sobject("Docebo_Users__c")
            .findOne({ User_Unique_Id__c: parseInt(userInfo.user_data.user_id) })
            .then(record => {
                if (!record) {
                    console.log("User doesn't exist in Salesforce");
                    return null;
                }
                console.log(`Record found for user update. ID: ${record.Id}`);
                return record.Id;
            })
            .catch(err => {
                console.error("Error finding record:", err);   
                throw err;
            });
        if (record != null) {
            const updateResult = await conn.sobject("Docebo_Users__c")
                .update({ Id: record, ...tmpUserInfo })
                .then(rets => {
                    if (rets.success) {
                        console.log(`Update successful. Record ID: ${rets.id}`);
                        return true;
                    } else {
                        return false;
                    }
                })
                .catch(err => {
                    console.error("Error updating record:", err);
                    throw err;
                });
            // const hsRes = hsService.updateDoceboUser(tmpUserInfo, updateUserId);
            // if (hsRes == false) {
            //     console.log("Hubspot contact update failed");
            // }
            if(!updateResult) {
                updatedResult = "User update failed";
            }
        } else {
            let userSavedRes = await createNewUser(userInfo, userListedInfo);
            if (userSavedRes) {
                updatedResult = "User created in update function due to he doesn't already exist";
            } else {
                updatedResult = "User creation & update failed in update function";
            }
        }
    }

    return updatedResult;
}

let saveUserInfo = {
    // "Account__c": null,
    "Back_Partner_Type__c": "",
    "Backbone_Partner__c": false,
    "Best_Describes_Your_Affiliation__c": "",
    "Branch_Name__c": "",
    "Branch_Path__c": "",
    "Branches_Codes__c": 0,
    // "Contact__c": null,
    "Deactivated__c": false,
    "Direct_Manager__c": "",
    "Email__c": "",
    "Email_Validation_Status__c": true,
    "Employment_Begin_Date__c": "",
    "Employment_Type__c": "",
    "First_Name__c": "",
    "Full_Name__c": "",
    "Gender_Identity__c": "",
    "Back_Partner_Type__c": "",
    "Job_Title__c": "",
    "Initiative__c": "",
    "Last_Name__c": "",
    "Level__c": "",
    "National_Regional_or_Local__c": "",
    "Network_Partnership_Association__c": "",
    "Organization_Name__c": "",
    "Organization_Headquarters__c": "",
    "Organization_URL__c": "",
    "OwnerId": null,
    "Partner_with_a_Member_of_StriveTogether__c": "",
    "Race_Identity__c": "",
    "Role_Type__c": "",
    "StriveTogether_Network_Member__c": "",
    "User__c": null,
    "User_Creation_Date__c": "",
    "User_Expiration_Date__c": "",
    "User_Last_Access_Date__c": "",
    "User_Level__c": "",
    "User_Suspension_Date__c": "",
    "User_Unique_Id__c": 0,
    "Username__c": "",
    "Initiative__c": "",
    "Who__c": ""
}

function tidyData(userInfo, userListedInfo) {
    let tmpUserInfo = { ...saveUserInfo };
    let newUser = userInfo;
    let additionFields = ('additional_fields' in newUser) ? newUser.additional_fields : [];

    tmpUserInfo.Best_Describes_Your_Affiliation__c = null;
    
    tmpUserInfo.Network_Partnership_Association__c = null;
    tmpUserInfo.OwnerId = "005O400000BxnnxIAB";
    tmpUserInfo.Partner_with_a_Member_of_StriveTogether__c = null;
    tmpUserInfo.StriveTogether_Network_Member__c = null;
    tmpUserInfo.User__c = null;
    tmpUserInfo.User_Suspension_Date__c = null;
    tmpUserInfo.Who__c = null;

    tmpUserInfo.Back_Partner_Type__c = getAdditionalData(additionFields, "16");
    tmpUserInfo.Backbone_Partner__c = getAdditionalData(additionFields, "15") == 'Yes' ? true : false;
    tmpUserInfo.Branch_Name__c = newUser.branches.length == 0 ? "" : newUser.branches.name;
    tmpUserInfo.Branch_Path__c = newUser.branches.length == 0 ? "" : newUser.branches.path;
    tmpUserInfo.Branches_Codes__c = newUser.branches.length == 0 ? "" : newUser.branches.codes;
    tmpUserInfo.Direct_Manager__c = newUser.user_data.manager_username;
    tmpUserInfo.Email__c = newUser.user_data.email;
    tmpUserInfo.Email_Validation_Status__c = newUser.user_data.email_validation_status == '1' ? true : false;
    tmpUserInfo.Employment_Begin_Date__c = getAdditionalData(additionFields, "17");
    tmpUserInfo.Employment_Type__c = getAdditionalData(additionFields, "10");
    tmpUserInfo.First_Name__c = newUser.user_data.first_name;
    tmpUserInfo.Full_Name__c = newUser.user_data.first_name + ' ' + newUser.user_data.last_name;
    tmpUserInfo.Gender_Identity__c = getAdditionalData(additionFields, "13");
    tmpUserInfo.Job_Title__c = getAdditionalData(additionFields, "8");
    tmpUserInfo.Initiative__c = getAdditionalData(additionFields, "20");
    tmpUserInfo.Last_Name__c = newUser.user_data.last_name;
    tmpUserInfo.Level__c = newUser.user_data.level;
    tmpUserInfo.National_Regional_or_Local__c = getAdditionalData(additionFields, "21");
    tmpUserInfo.Organization_Name__c = getAdditionalData(additionFields, "14");
    tmpUserInfo.Organization_Headquarters__c = getAdditionalData(additionFields, "22");
    tmpUserInfo.Organization_URL__c = newUser.additional_fields[1].type;
    tmpUserInfo.Race_Identity__c = getAdditionalData(additionFields, "12");
    tmpUserInfo.Role_Type__c = getAdditionalData(additionFields, "9");
    tmpUserInfo.User_Creation_Date__c = newUser.fired_at == "" ? "" : new Date(newUser.fired_at).toISOString();
    tmpUserInfo.User_Expiration_Date__c = newUser.expiration_date == "" ? "" : new Date(newUser.expiration_date).toISOString();
    tmpUserInfo.User_Level__c = newUser.user_data.level;
    tmpUserInfo.User_Unique_Id__c = parseInt(newUser.user_data.user_id);
    tmpUserInfo.Username__c = newUser.user_data.username;
    tmpUserInfo.Initiative__c = getAdditionalData(additionFields, "20");
    tmpUserInfo.Deactivated__c = newUser.user_data.valid == '1' ? true : false;
    // Check if last_access_date exists and is valid before converting to ISO string
    tmpUserInfo.User_Last_Access_Date__c = null;
    if (userListedInfo.last_access_date && userListedInfo.last_access_date !== "") {
        const lastAccessDate = new Date(userListedInfo.last_access_date);
        // Check if the date is valid (invalid dates return NaN when converted to a number)
        if (!isNaN(lastAccessDate.getTime())) {
            tmpUserInfo.User_Last_Access_Date__c = lastAccessDate.toISOString();
        } else {
            console.log(`Invalid last_access_date format: ${userListedInfo.last_access_date}`);
        }
    }

    return tmpUserInfo;
}

const getAdditionalData = (additionalArr, fieldId) => {
    let optionLabel = "";
    additionalArr.forEach(element => {
        if (element.id == fieldId) {
            if (element.enabled == true) {
                if ('options' in element) {
                    element.options.forEach(elOpt => {
                        if (elOpt.id == element.value) {
                            optionLabel = elOpt.label;
                        }
                    })
                } else {
                    optionLabel = element.value;
                }
            }
        }
    });
    return optionLabel;
}

module.exports = {
    updateUser
}