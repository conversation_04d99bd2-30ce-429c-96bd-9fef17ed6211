require('dotenv').config();
const getApiData = require("./common/docebo/fetcher");
const APP_BASE = process.env.DOCEBO_API_BASE_URL;

async function continueFromPage110() {
    try {
        console.log('🔄 CONTINUING SEARCH FROM PAGE 110 FOR COURSE 43 ENROLLMENTS');
        console.log('=' .repeat(80));
        console.log('🎯 Looking for additional course 43 enrollments beyond page 110');
        
        let course43Enrollments = [];
        let startPage = 110;
        let page = startPage;
        let hasMoreData = true;
        let totalProcessed = 0;
        let pagesWithCourse43 = [];
        
        console.log(`📡 Starting from page ${startPage}...`);
        
        while (hasMoreData && page <= 300) { // Search up to page 300
            console.log(`   📄 Fetching page ${page}...`);
            
            try {
                const response = await getApiData(
                    'GET', 
                    `${APP_BASE}/learn/v1/enrollments?page=${page}&page_size=200`, 
                    null
                );
                
                if (response && response.status === 200) {
                    const items = response.data?.items || [];
                    totalProcessed += items.length;
                    
                    // Look for course 43 enrollments in multiple ways
                    const course43Items = items.filter(item => {
                        // Check various fields that might contain course 43 identifier
                        return (
                            item.id == 43 ||
                            item.course_id == 43 ||
                            (item.url && item.url.includes('/course/43/')) ||
                            (item.uidCourse && (item.uidCourse.includes('43') || item.uidCourse === 'E-J0E815')) ||
                            (item.name && (
                                item.name.includes('Welcome to The Training Hub') ||
                                item.name === 'Welcome to The Training Hub!' ||
                                item.name.toLowerCase().includes('training hub')
                            )) ||
                            (item.slug && item.slug.includes('training-hub')) ||
                            (item.description && item.description.toLowerCase().includes('training hub'))
                        );
                    });
                    
                    if (course43Items.length > 0) {
                        course43Enrollments.push(...course43Items);
                        pagesWithCourse43.push(page);
                        console.log(`      🎯 FOUND ${course43Items.length} course 43 enrollments on page ${page}! (Total C43: ${course43Enrollments.length})`);
                        
                        // Log details of matches for verification
                        course43Items.forEach((item, index) => {
                            console.log(`         ${index + 1}. User: ${item.user_id}, Name: "${item.name}", URL: ${item.url}`);
                        });
                    }
                    
                    console.log(`      Found ${items.length} total enrollments (Page total: ${totalProcessed}, Course 43: ${course43Enrollments.length})`);
                    
                    hasMoreData = response.data?.has_more_data || false;
                    if (items.length === 0) hasMoreData = false;
                    
                    page++;
                    
                    // Progress indicator every 10 pages
                    if (page % 10 === 0) {
                        console.log(`   📊 Progress: Page ${page}, ${totalProcessed} total enrollments, ${course43Enrollments.length} course 43 found`);
                        console.log(`   📍 Pages with Course 43: [${pagesWithCourse43.join(', ')}]`);
                    }
                    
                    // If we find a significant number, we can stop early
                    if (course43Enrollments.length >= 1000) {
                        console.log(`   🎯 Found significant number of course 43 enrollments (${course43Enrollments.length}), stopping search`);
                        hasMoreData = false;
                    }
                    
                } else {
                    console.log(`      No data on page ${page}`);
                    hasMoreData = false;
                }
            } catch (pageError) {
                console.log(`      ❌ Error on page ${page}: ${pageError.message}`);
                
                // Continue to next page unless it's a critical error
                if (pageError.message.includes('401') || pageError.message.includes('403')) {
                    console.log('      🚨 Authentication error - stopping');
                    hasMoreData = false;
                } else {
                    console.log('      ⏭️ Continuing to next page...');
                    page++;
                }
            }
        }
        
        console.log(`\n✅ Search from page ${startPage} completed:`);
        console.log(`   Pages searched: ${startPage} to ${page - 1}`);
        console.log(`   Total enrollments processed: ${totalProcessed.toLocaleString()}`);
        console.log(`   Course 43 enrollments found: ${course43Enrollments.length.toLocaleString()}`);
        console.log(`   Pages containing Course 43: [${pagesWithCourse43.join(', ')}]`);
        
        if (course43Enrollments.length > 0) {
            console.log('\n🎯 COURSE 43 ENROLLMENT DETAILS:');
            console.log('-'.repeat(50));
            
            // Group by page for analysis
            const enrollmentsByPage = {};
            course43Enrollments.forEach(enrollment => {
                // We need to track which page each enrollment came from
                // For now, let's show unique users and course names
            });
            
            // Show unique users
            const uniqueUsers = [...new Set(course43Enrollments.map(e => e.user_id))];
            console.log(`   Unique users: ${uniqueUsers.length}`);
            
            // Show unique course names
            const uniqueNames = [...new Set(course43Enrollments.map(e => e.name))];
            console.log(`   Unique course names:`);
            uniqueNames.forEach((name, index) => {
                const count = course43Enrollments.filter(e => e.name === name).length;
                console.log(`     ${index + 1}. "${name}" (${count} enrollments)`);
            });
            
            // Show sample enrollments
            console.log('\n📋 Sample enrollments:');
            course43Enrollments.slice(0, 10).forEach((enrollment, index) => {
                console.log(`   ${index + 1}. User ${enrollment.user_id}: "${enrollment.name}"`);
            });
            
        } else {
            console.log('\n❌ No additional course 43 enrollments found beyond page 110');
        }
        
        // Analysis of search pattern
        console.log('\n📊 SEARCH PATTERN ANALYSIS:');
        console.log('-'.repeat(50));
        
        if (pagesWithCourse43.length > 0) {
            console.log(`✅ Course 43 enrollments found on ${pagesWithCourse43.length} pages`);
            console.log(`📍 Distribution: Pages [${pagesWithCourse43.join(', ')}]`);
            
            // Check if there's a pattern
            if (pagesWithCourse43.length > 1) {
                const gaps = [];
                for (let i = 1; i < pagesWithCourse43.length; i++) {
                    gaps.push(pagesWithCourse43[i] - pagesWithCourse43[i-1]);
                }
                console.log(`📈 Gaps between pages: [${gaps.join(', ')}]`);
            }
        } else {
            console.log(`❌ No course 43 enrollments found in pages ${startPage}-${page-1}`);
            console.log(`💡 This suggests course 43 enrollments are clustered in earlier pages`);
        }

        return {
            success: true,
            startPage: startPage,
            endPage: page - 1,
            totalProcessed: totalProcessed,
            course43Found: course43Enrollments.length,
            pagesWithCourse43: pagesWithCourse43,
            enrollments: course43Enrollments
        };

    } catch (error) {
        console.error('💥 Error in continued search:', error);
        return {
            success: false,
            error: error.message
        };
    }
}

// Execute the continued search
console.log('🔄 Starting Continued Search from Page 110...');
continueFromPage110()
    .then((result) => {
        console.log('\n📋 CONTINUED SEARCH SUMMARY:');
        console.log('=' .repeat(60));
        
        if (result.success) {
            console.log(`📊 Pages Searched: ${result.startPage} to ${result.endPage}`);
            console.log(`📊 Total Records Processed: ${result.totalProcessed.toLocaleString()}`);
            console.log(`🎯 Course 43 Enrollments Found: ${result.course43Found.toLocaleString()}`);
            console.log(`📍 Pages with Course 43: ${result.pagesWithCourse43.length} pages`);
            
            if (result.course43Found > 0) {
                console.log(`\n🎉 SUCCESS: Found ${result.course43Found} additional course 43 enrollments!`);
                console.log(`📍 Found on pages: [${result.pagesWithCourse43.join(', ')}]`);
                
                console.log('\n💡 NEXT STEPS:');
                console.log('1. 🔄 Continue searching more pages if needed');
                console.log('2. 💾 Sync these additional enrollments to Salesforce');
                console.log('3. 📊 Combine with the 62 enrollments found earlier');
                
                const totalFound = 62 + result.course43Found; // 62 from earlier search
                console.log(`\n📊 TOTAL COURSE 43 ENROLLMENTS FOUND: ${totalFound}`);
                const percentageOfExpected = (totalFound / 5544 * 100).toFixed(1);
                console.log(`📊 Percentage of Expected (5,544): ${percentageOfExpected}%`);
                
            } else {
                console.log(`\n❌ No additional course 43 enrollments found in pages ${result.startPage}-${result.endPage}`);
                console.log(`💡 Course 43 enrollments appear to be clustered in the first ~110 pages`);
                
                console.log('\n🔍 RECOMMENDATIONS:');
                console.log('1. 📊 The 62 enrollments found earlier may be all that\'s accessible via API');
                console.log('2. 📞 Contact Docebo support about accessing the full 5,544 enrollments');
                console.log('3. 📁 Consider using Docebo export functionality');
                console.log('4. 🔄 Verify if there are other API endpoints for historical data');
            }
            
        } else {
            console.log(`❌ Continued search failed: ${result.error}`);
        }
        
        console.log('\n✅ Continued search completed');
        process.exit(0);
    })
    .catch(err => {
        console.error('💥 Continued search failed:', err);
        process.exit(1);
    });
