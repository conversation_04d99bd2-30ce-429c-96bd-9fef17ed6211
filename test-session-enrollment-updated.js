require('dotenv').config();
const { updateIltSessionEnrollment } = require('./platform/salesforce/services');
const getConnection = require('./platform/salesforce/common/getConnection');

async function testSessionEnrollmentUpdated() {
    try {
        console.log('🔧 Testing ILT Session Enrollment Updated Event');
        console.log('=' .repeat(70));
        console.log('🎯 TESTING:');
        console.log('   1. ilt.session.enrollment.updated webhook event handling');
        console.log('   2. updateIltSessionEnrollment function');
        console.log('   3. Enrollment record updates in Salesforce');
        console.log('=' .repeat(70));
        
        const conn = await getConnection();
        if (!conn || !conn.accessToken) {
            console.error("❌ Invalid Salesforce connection");
            return;
        }

        // Step 1: Get required references and create test enrollment record
        console.log('\n📋 Step 1: Getting required references...');

        // Get a sample user and course for the enrollment
        const sampleUser = await conn.sobject("Docebo_Users__c")
            .findOne({});

        const sampleCourse = await conn.sobject("Docebo_Course__c")
            .findOne({});

        if (!sampleUser || !sampleCourse) {
            console.error('❌ No sample user or course found for testing');
            return;
        }

        console.log(`📋 Using sample user: ${sampleUser.First_Name__c} ${sampleUser.Last_Name__c}`);
        console.log(`📋 Using sample course: ${sampleCourse.Course_Name__c}`);

        console.log('\n📋 Creating test enrollment record...');

        const testEnrollmentData = {
            Enrollment_ID__c: 'TEST_UPD_12345', // FIX: Max 16 chars
            Status__c: 'enrolled',
            Enrollment_Date__c: new Date().toISOString(),
            Score__c: 0,
            Docebo_User__c: sampleUser.Id, // Required field
            Course__c: sampleCourse.Id // Required field
        };

        // Create the test enrollment
        const createResult = await conn.sobject("Docebo_CourseEnrollment__c").create(testEnrollmentData);
        
        if (!createResult.success) {
            console.error('❌ Failed to create test enrollment:', createResult.errors);
            return;
        }
        
        console.log(`✅ Test enrollment created: ${createResult.id}`);
        console.log(`   Enrollment ID: ${testEnrollmentData.Enrollment_ID__c}`);
        console.log(`   Initial Status: ${testEnrollmentData.Status__c}`);
        console.log(`   Initial Score: ${testEnrollmentData.Score__c}`);

        // Step 2: Test the updateIltSessionEnrollment function
        console.log('\n🧪 Step 2: Testing updateIltSessionEnrollment function...');
        
        const updateData = {
            session_attendance_id: 'TEST_UPD_12345',
            status: 'completed',
            completion_date: new Date().toISOString(),
            score: 95
        };

        console.log('📝 Update data:', JSON.stringify(updateData, null, 2));
        
        const updateResult = await updateIltSessionEnrollment(updateData);
        
        if (updateResult) {
            console.log('✅ updateIltSessionEnrollment function executed successfully');
        } else {
            console.log('❌ updateIltSessionEnrollment function failed');
        }

        // Step 3: Verify the update was applied
        console.log('\n🔍 Step 3: Verifying the update was applied...');
        
        const updatedEnrollment = await conn.sobject("Docebo_CourseEnrollment__c")
            .findOne({ Enrollment_ID__c: 'TEST_UPD_12345' });
        
        if (updatedEnrollment) {
            console.log('\n📊 UPDATED ENROLLMENT VERIFICATION:');
            console.log(`   Enrollment ID: ${updatedEnrollment.Id}`);
            console.log(`   Enrollment External ID: ${updatedEnrollment.Enrollment_ID__c}`);
            console.log(`   Status: ${updatedEnrollment.Status__c}`);
            console.log(`   Completion Date: ${updatedEnrollment.Completion_Date__c || 'Not set'}`);
            console.log(`   Score: ${updatedEnrollment.Score__c}`);
            
            // Verify the changes
            const statusUpdated = updatedEnrollment.Status__c === updateData.status;
            const scoreUpdated = updatedEnrollment.Score__c === updateData.score;
            const completionDateSet = updatedEnrollment.Completion_Date__c !== null;

            console.log('\n🎯 UPDATE VERIFICATION:');
            console.log(`   Status Updated: ${statusUpdated ? '✅' : '❌'} (${updatedEnrollment.Status__c})`);
            console.log(`   Score Updated: ${scoreUpdated ? '✅' : '❌'} (${updatedEnrollment.Score__c})`);
            console.log(`   Completion Date Set: ${completionDateSet ? '✅' : '❌'}`);

            if (statusUpdated && scoreUpdated && completionDateSet) {
                console.log('\n🎉 ALL UPDATES APPLIED SUCCESSFULLY!');
            } else {
                console.log('\n⚠️ Some updates were not applied correctly');
            }
        } else {
            console.log('❌ Could not find updated enrollment record');
        }

        // Step 4: Test webhook event simulation
        console.log('\n🧪 Step 4: Simulating webhook event...');
        
        const mockWebhookPayload = {
            event: 'ilt.session.enrollment.updated',
            payload: {
                session_attendance_id: 'TEST_UPD_12345',
                status: 'in_progress',
                score: 75,
                enrollment_date: new Date().toISOString()
            }
        };

        console.log('📡 Mock webhook payload:', JSON.stringify(mockWebhookPayload, null, 2));
        
        // Simulate the webhook processing
        const webhookResult = await updateIltSessionEnrollment(mockWebhookPayload.payload);
        
        if (webhookResult) {
            console.log('✅ Webhook simulation successful');
            
            // Verify webhook update
            const webhookUpdatedEnrollment = await conn.sobject("Docebo_CourseEnrollment__c")
                .findOne({ Enrollment_ID__c: 'TEST_UPD_12345' });
            
            console.log('\n📊 WEBHOOK UPDATE VERIFICATION:');
            console.log(`   Status after webhook: ${webhookUpdatedEnrollment.Status__c}`);
            console.log(`   Score after webhook: ${webhookUpdatedEnrollment.Score__c}`);
            
        } else {
            console.log('❌ Webhook simulation failed');
        }

        // Step 5: Test non-existent enrollment (should create new one)
        console.log('\n🧪 Step 5: Testing update of non-existent enrollment...');
        
        const nonExistentUpdateData = {
            session_attendance_id: 'NON_EXIST_67890',
            status: 'enrolled',
            enrollment_date: new Date().toISOString(),
            score: 0
        };

        const createNewResult = await updateIltSessionEnrollment(nonExistentUpdateData);
        
        if (createNewResult) {
            console.log('✅ Non-existent enrollment handled correctly (created new enrollment)');
        } else {
            console.log('❌ Failed to handle non-existent enrollment');
        }

        // Clean up test data
        console.log('\n🗑️ Cleaning up test data...');
        
        // Delete test enrollments
        const testEnrollments = await conn.sobject("Docebo_CourseEnrollment__c")
            .find({
                $or: [
                    { Enrollment_ID__c: 'TEST_UPD_12345' },
                    { Enrollment_ID__c: 'NON_EXIST_67890' }
                ]
            })
            .execute();
        
        for (const enrollment of testEnrollments) {
            await conn.sobject("Docebo_CourseEnrollment__c").delete(enrollment.Id);
            console.log(`   ✅ Deleted test enrollment: ${enrollment.Id}`);
        }

        // Step 6: Summary
        console.log('\n📊 SESSION ENROLLMENT UPDATE TEST SUMMARY:');
        console.log('=' .repeat(70));
        
        console.log('✅ FUNCTIONALITY TESTED:');
        console.log('   • updateIltSessionEnrollment function');
        console.log('   • Enrollment record updates');
        console.log('   • Webhook event simulation');
        console.log('   • Non-existent enrollment handling');
        console.log('   • Field mapping and validation');
        
        console.log('\n🎯 WEBHOOK EVENT SUPPORT:');
        console.log('   ✅ ilt.session.created');
        console.log('   ✅ ilt.session.enrollment.created');
        console.log('   ✅ ilt.session.enrollment.updated (NEW!)');
        console.log('   ✅ ilt.session.enrollment.deleted');
        
        console.log('\n🚀 PRODUCTION BENEFITS:');
        console.log('   • Complete enrollment lifecycle support');
        console.log('   • Real-time enrollment status updates');
        console.log('   • Automatic score and progress tracking');
        console.log('   • No more "Unknown session event" messages');
        
        console.log('\n💡 NEXT STEPS:');
        console.log('   • Monitor webhook logs for ilt.session.enrollment.updated events');
        console.log('   • Verify enrollment updates are syncing correctly');
        console.log('   • Consider adding more enrollment fields if needed');

        return {
            success: true,
            message: 'Session enrollment update functionality tested successfully'
        };

    } catch (error) {
        console.error('💥 Error in session enrollment update test:', error);
        return {
            success: false,
            error: error.message
        };
    }
}

// Execute the test
console.log('🔄 Starting session enrollment update test...');
testSessionEnrollmentUpdated()
    .then((result) => {
        console.log('\n✅ Session enrollment update test completed');
        if (result.success) {
            console.log('🎉 ilt.session.enrollment.updated event is now supported!');
        } else {
            console.log('❌ Test failed. Check the logs above.');
        }
        process.exit(0);
    })
    .catch(err => {
        console.error('💥 Test failed:', err);
        process.exit(1);
    });
