require('dotenv').config();
const getConnection = require('./platform/salesforce/common/getConnection');
const { createNewUser } = require('./platform/salesforce/users/createUser');

// Mock user data with ALL possible fields populated
const mockUserInfo = {
    user_data: {
        user_id: "77777",
        first_name: "<PERSON>",
        last_name: "Comprehensive Lead",
        email: "<EMAIL>",
        username: "sarah_comprehensive_lead",
        level: "Power User",
        manager_username: "sarah_manager",
        email_validation_status: "1",
        valid: "1"
    },
    additional_fields: [
        { id: "8", value: "Senior Director", enabled: true }, // Job Title
        { id: "9", value: "Executive", enabled: true }, // Role Type
        { id: "10", value: "Full-time", enabled: true }, // Employment Type
        { id: "12", value: "Multi-Racial", enabled: true }, // Race Identity
        { id: "13", value: "Non-binary", enabled: true }, // Gender Identity
        { id: "14", value: "StriveTogether National Office", enabled: true }, // Organization Name
        { id: "15", value: "Yes", enabled: true }, // Backbone Partner
        { id: "16", value: "Backbone Partner", enabled: true }, // Back Partner Type
        { id: "17", value: "2020-01-15", enabled: true }, // Employment Begin Date
        { id: "20", value: "Cradle to Career Network", enabled: true }, // Initiative
        { id: "21", value: "National", enabled: true }, // National/Regional/Local
        { id: "22", value: "Cincinnati, OH", enabled: true } // Organization Headquarters
    ],
    branches: [
        {
            name: "National Leadership Branch",
            path: "/national/leadership",
            codes: "12345" // Numeric string that can be converted
        }
    ],
    fired_at: "2020-01-15 08:00:00",
    expiration_date: "2026-12-31 23:59:59"
};

const mockUserListedInfo = {
    last_access_date: "2024-01-25T10:30:00Z"
};

async function testComprehensiveLeadCreation() {
    try {
        console.log('🧪 Testing comprehensive lead creation with ALL fields...');
        
        const conn = await getConnection();
        if (!conn || !conn.accessToken) {
            console.error("❌ Invalid Salesforce connection");
            return;
        }

        // Clean up any existing test records
        console.log('\n🧹 Cleaning up any existing test records...');
        
        try {
            // Delete existing records
            const existingUsers = await conn.sobject("Docebo_Users__c")
                .find({ Email__c: mockUserInfo.user_data.email });
            
            for (const user of existingUsers) {
                await conn.sobject("Docebo_Users__c").delete(user.Id);
                console.log(`   Deleted existing Docebo_Users__c: ${user.Id}`);
            }

            const existingLeads = await conn.sobject("Lead")
                .find({ Email: mockUserInfo.user_data.email });
            
            for (const lead of existingLeads) {
                await conn.sobject("Lead").delete(lead.Id);
                console.log(`   Deleted existing Lead: ${lead.Id}`);
            }

            const existingContacts = await conn.sobject("Contact")
                .find({ Email: mockUserInfo.user_data.email });
            
            for (const contact of existingContacts) {
                await conn.sobject("Contact").delete(contact.Id);
                console.log(`   Deleted existing Contact: ${contact.Id}`);
            }

        } catch (cleanupError) {
            console.log('   No existing records to clean up or cleanup failed:', cleanupError.message);
        }

        console.log('\n🚀 Creating new user with comprehensive lead data...');
        
        // Create the user (which should trigger lead creation)
        const result = await createNewUser(mockUserInfo, mockUserListedInfo);
        
        if (result) {
            console.log('✅ User creation completed successfully');
            
            // Verify what was created
            console.log('\n🔍 Verifying created records...');
            
            // Check Docebo_Users__c record
            const createdUser = await conn.sobject("Docebo_Users__c")
                .findOne({ Email__c: mockUserInfo.user_data.email });
            
            if (createdUser) {
                console.log(`✅ Docebo_Users__c created: ${createdUser.Id}`);
                console.log(`   Name: ${createdUser.First_Name__c} ${createdUser.Last_Name__c}`);
                console.log(`   Organization: ${createdUser.Organization_Name__c}`);
                console.log(`   Job Title: ${createdUser.Job_Title__c}`);
                console.log(`   Role Type: ${createdUser.Role_Type__c}`);
                console.log(`   Employment Type: ${createdUser.Employment_Type__c}`);
                console.log(`   Initiative: ${createdUser.Initiative__c}`);
                console.log(`   Branch: ${createdUser.Branch_Name__c}`);
            }

            // Check Lead record with comprehensive field list
            const fieldList = [
                'Id', 'FirstName', 'LastName', 'Email', 'Company', 'Status', 'Title', 'Website',
                'accountid__c', 'Back_Partner_Type__c', 'Backbone_Partner__c', 
                'Best_Describes_Your_Affiliation__c', 'Branch_Name__c', 'Branch_Path__c',
                'Direct_Manager__c', 'Employment_Begin_Date__c', 'Employment_Type__c',
                'Initiative__c', 'Level__c', 'National_Regional_or_Local__c',
                'Network_Partnership_Association__c', 'Organization_Name__c', 
                'Organization_Headquarters__c', 'Organization_URL__c',
                'Partner_with_a_Member_of_StriveTogether__c', 'StriveTogether_Network_Member__c',
                'User_Creation_Date__c', 'User_Expiration_Date__c', 'User_Last_Access_Date__c',
                'User_Level__c', 'User_Unique_Id__c', 'Username__c', 'Who__c',
                'Role_Type__c', 'Race__c', 'Gender__c', 'Created_by_Docebo_API__c',
                'Email_Validation_Status__c'
            ].join(', ');

            const createdLead = await conn.sobject("Lead")
                .findOne({ Email: mockUserInfo.user_data.email }, fieldList);
            
            if (createdLead) {
                console.log(`✅ Lead created: ${createdLead.Id}`);
                console.log(`   Name: ${createdLead.FirstName} ${createdLead.LastName}`);
                console.log(`   Company: ${createdLead.Company}`);
                console.log(`   Status: ${createdLead.Status}`);
                console.log(`   Title: ${createdLead.Title}`);
                console.log(`   Website: ${createdLead.Website}`);
                
                console.log('\n📋 ALL Enhanced Lead Fields:');
                console.log('=' .repeat(60));
                
                // Organization & Contact Info
                console.log('\n🏢 Organization & Contact:');
                console.log(`   Organization Name: ${createdLead.Organization_Name__c || 'N/A'}`);
                console.log(`   Organization HQ: ${createdLead.Organization_Headquarters__c || 'N/A'}`);
                console.log(`   Organization URL: ${createdLead.Organization_URL__c || 'N/A'}`);
                console.log(`   Direct Manager: ${createdLead.Direct_Manager__c || 'N/A'}`);
                
                // Employment Info
                console.log('\n💼 Employment Information:');
                console.log(`   Employment Type: ${createdLead.Employment_Type__c || 'N/A'}`);
                console.log(`   Employment Begin Date: ${createdLead.Employment_Begin_Date__c || 'N/A'}`);
                console.log(`   Role Type: ${createdLead.Role_Type__c || 'N/A'}`);
                console.log(`   Level: ${createdLead.Level__c || 'N/A'}`);
                console.log(`   User Level: ${createdLead.User_Level__c || 'N/A'}`);
                
                // Demographics
                console.log('\n👤 Demographics:');
                console.log(`   Race: ${createdLead.Race__c || 'N/A'}`);
                console.log(`   Gender: ${createdLead.Gender__c || 'N/A'}`);
                
                // Partnership Info
                console.log('\n🤝 Partnership Information:');
                console.log(`   Backbone Partner: ${createdLead.Backbone_Partner__c || 'N/A'}`);
                console.log(`   Back Partner Type: ${createdLead.Back_Partner_Type__c || 'N/A'}`);
                console.log(`   StriveTogether Member: ${createdLead.StriveTogether_Network_Member__c || 'N/A'}`);
                console.log(`   Partner with ST Member: ${createdLead.Partner_with_a_Member_of_StriveTogether__c || 'N/A'}`);
                console.log(`   Network Partnership: ${createdLead.Network_Partnership_Association__c || 'N/A'}`);
                console.log(`   National/Regional/Local: ${createdLead.National_Regional_or_Local__c || 'N/A'}`);
                
                // Branch & Initiative
                console.log('\n🌳 Branch & Initiative:');
                console.log(`   Branch Name: ${createdLead.Branch_Name__c || 'N/A'}`);
                console.log(`   Branch Path: ${createdLead.Branch_Path__c || 'N/A'}`);
                console.log(`   Initiative: ${createdLead.Initiative__c || 'N/A'}`);
                
                // User System Info
                console.log('\n⚙️ System Information:');
                console.log(`   User Unique ID: ${createdLead.User_Unique_Id__c || 'N/A'}`);
                console.log(`   Username: ${createdLead.Username__c || 'N/A'}`);
                console.log(`   User Creation Date: ${createdLead.User_Creation_Date__c || 'N/A'}`);
                console.log(`   User Expiration Date: ${createdLead.User_Expiration_Date__c || 'N/A'}`);
                console.log(`   User Last Access: ${createdLead.User_Last_Access_Date__c || 'N/A'}`);
                console.log(`   Email Validation: ${createdLead.Email_Validation_Status__c || 'N/A'}`);
                console.log(`   Created by Docebo API: ${createdLead.Created_by_Docebo_API__c || 'N/A'}`);
                console.log(`   Account ID: ${createdLead.accountid__c || 'N/A'}`);
                
                console.log('\n🎯 SUCCESS! Lead created with ALL comprehensive data from Docebo!');
                
            } else {
                console.log('❌ No Lead record found');
            }

        } else {
            console.log('❌ User creation failed');
        }

    } catch (error) {
        console.error('💥 Error in comprehensive lead creation test:', error);
        
        if (error.message && error.message.includes('INVALID_FIELD')) {
            console.log('\n💡 Field Error Details:');
            console.log('   Error:', error.message);
            console.log('   Some fields may not exist on the Lead object in Salesforce.');
        }
    }
}

// Execute the test
console.log('🔄 Starting comprehensive lead creation test...');
testComprehensiveLeadCreation()
    .then(() => {
        console.log('\n✅ Test completed');
        process.exit(0);
    })
    .catch(err => {
        console.error('💥 Test failed:', err);
        process.exit(1);
    });
