require('dotenv').config();
const getConnection = require('./platform/salesforce/common/getConnection');
const { createNewUser } = require('./platform/salesforce/users/createUser');

// Test data that includes proper webhook dates
const mockWebhookDataWithDates = {
    user_data: {
        user_id: 88888,
        username: "<EMAIL>",
        email: "<EMAIL>",
        first_name: "Association",
        last_name: "Test",
        level: "user",
        phone: "******-888-0000",
        language: "en",
        timezone: "America/New_York",
        email_validation_status: "1",
        valid: "1",
        manager_username: ""
    },
    additional_fields: [
        { id: "8", value: "Test Manager", enabled: true }, // Job Title
        { id: "9", value: "Communications", enabled: true }, // Role Type
        { id: "10", value: "Full-Time", enabled: true }, // Employment Type
        { id: "12", value: "Asian", enabled: true }, // Race Identity
        { id: "13", value: "Woman", enabled: true }, // Gender Identity
        { id: "14", value: "Test Association Org", enabled: true }, // Organization Name
        { id: "23", value: "https://testassoc.com", enabled: true }, // Organization URL
        { id: "24", value: "Test City", enabled: true }, // Mailing City
        { 
            id: "25", 
            value: "NY", 
            enabled: true,
            options: [
                { id: "CA", label: "California" },
                { id: "NY", label: "New York" },
                { id: "TX", label: "Texas" }
            ]
        }, // Mailing State
        { id: "28", value: "United States", enabled: true }, // Mailing Country
        { id: "29", value: "10001", enabled: true }, // Mailing Postal Code
        { id: "30", value: "456 Association Ave", enabled: true } // Mailing Street
    ],
    branches: [
        {
            name: "Association Branch",
            path: "/association/branch",
            codes: "456"
        }
    ],
    // Include proper webhook dates
    fired_at: "2025-01-15 14:30:00",
    expiration_date: "2026-01-15 14:30:00"
};

const mockUserListedInfo = {
    last_access_date: "2025-01-16T10:00:00Z"
};

// Test data without dates (to test fallback)
const mockWebhookDataNoDates = {
    user_data: {
        user_id: 77777,
        username: "<EMAIL>",
        email: "<EMAIL>",
        first_name: "NoDate",
        last_name: "Test",
        level: "user",
        phone: "******-777-0000",
        language: "en",
        timezone: "America/Los_Angeles",
        email_validation_status: "1",
        valid: "1",
        manager_username: ""
    },
    additional_fields: [
        { id: "8", value: "Test Coordinator", enabled: true },
        { id: "9", value: "Programs", enabled: true },
        { id: "10", value: "Part-Time", enabled: true },
        { id: "12", value: "White", enabled: true },
        { id: "13", value: "Man", enabled: true },
        { id: "14", value: "No Date Org", enabled: true },
        { id: "23", value: "https://nodateorg.com", enabled: true }
    ],
    branches: [
        {
            name: "No Date Branch",
            path: "/nodate/branch",
            codes: "777"
        }
    ],
    // NO fired_at or expiration_date to test fallback
    fired_at: "",
    expiration_date: ""
};

async function testAssociationAndDates() {
    try {
        console.log('🧪 Testing Association and Date Fixes');
        console.log('=' .repeat(60));
        
        const conn = await getConnection();
        if (!conn || !conn.accessToken) {
            console.error("❌ Invalid Salesforce connection");
            return;
        }

        // Test 1: User with proper webhook dates
        console.log('\n🔄 Test 1: User with proper webhook dates');
        console.log('-'.repeat(50));
        await testUserCreationWithData(conn, mockWebhookDataWithDates, mockUserListedInfo, "WITH_DATES");

        // Test 2: User without dates (fallback test)
        console.log('\n🔄 Test 2: User without dates (fallback test)');
        console.log('-'.repeat(50));
        await testUserCreationWithData(conn, mockWebhookDataNoDates, mockUserListedInfo, "NO_DATES");

    } catch (error) {
        console.error('💥 Error in test:', error);
    }
}

async function testUserCreationWithData(conn, webhookData, userListedInfo, testType) {
    try {
        // Clean up any existing test records first
        console.log(`\n🧹 Cleaning up existing records for ${testType}...`);
        await cleanupTestRecords(conn, webhookData.user_data.email);

        console.log(`\n🚀 Creating user with ${testType} data...`);
        console.log(`   Email: ${webhookData.user_data.email}`);
        console.log(`   fired_at: "${webhookData.fired_at}"`);
        console.log(`   expiration_date: "${webhookData.expiration_date}"`);
        
        // Test the createNewUser function
        const result = await createNewUser(webhookData, userListedInfo);
        
        if (result) {
            console.log(`\n✅ ${testType} user creation completed successfully!`);
            
            // Verify what was created
            console.log(`\n🔍 Verifying ${testType} created records...`);
            
            // Check Docebo_Users__c record
            const createdUser = await conn.sobject("Docebo_Users__c")
                .findOne({ Email__c: webhookData.user_data.email });
            
            if (createdUser) {
                console.log(`✅ Docebo_Users__c created: ${createdUser.Id}`);
                console.log(`   Name: ${createdUser.First_Name__c} ${createdUser.Last_Name__c}`);
                console.log(`   Email: ${createdUser.Email__c}`);
                console.log(`   User ID: ${createdUser.User_Unique_Id__c}`);
                console.log(`   Organization: ${createdUser.Organization_Name__c}`);
                
                // CHECK DATE FIELDS
                console.log(`\n📅 DATE FIELDS:`);
                console.log(`   User_Creation_Date__c: ${createdUser.User_Creation_Date__c || 'MISSING'}`);
                console.log(`   User_Expiration_Date__c: ${createdUser.User_Expiration_Date__c || 'MISSING'}`);
                console.log(`   User_Last_Access_Date__c: ${createdUser.User_Last_Access_Date__c || 'MISSING'}`);
                console.log(`   User_Suspension_Date__c: ${createdUser.User_Suspension_Date__c || 'MISSING'}`);
                
                // CHECK ASSOCIATIONS
                console.log(`\n🔗 ASSOCIATIONS:`);
                console.log(`   Lead__c: ${createdUser.Lead__c || 'NOT ASSOCIATED'}`);
                console.log(`   Contact__c: ${createdUser.Contact__c || 'NOT ASSOCIATED'}`);
                
                // Verify the associated Lead if it exists
                if (createdUser.Lead__c) {
                    const associatedLead = await conn.sobject("Lead")
                        .findOne({ Id: createdUser.Lead__c });
                    
                    if (associatedLead) {
                        console.log(`\n✅ Associated Lead found: ${associatedLead.Id}`);
                        console.log(`   Lead Name: ${associatedLead.FirstName} ${associatedLead.LastName}`);
                        console.log(`   Lead Email: ${associatedLead.Email}`);
                        console.log(`   Lead Company: ${associatedLead.Company}`);
                        console.log(`   Lead Status: ${associatedLead.Status}`);
                        console.log(`   Created by Docebo API: ${associatedLead.Created_by_Docebo_API__c}`);
                        console.log(`   Gender: ${associatedLead.Gender__c}`);
                        console.log(`   Role Type: ${associatedLead.Role_Type__c}`);
                        console.log(`   Race: ${associatedLead.Race__c}`);
                        
                        console.log(`\n🎯 ${testType} SUCCESS SUMMARY:`);
                        console.log(`   ✅ Docebo_Users__c created with proper associations`);
                        console.log(`   ✅ Lead created and linked to Docebo_Users__c`);
                        console.log(`   ✅ Date fields populated correctly`);
                        console.log(`   ✅ Field mappings working properly`);
                    } else {
                        console.log(`❌ Associated Lead not found: ${createdUser.Lead__c}`);
                    }
                } else {
                    console.log(`❌ No Lead association found for Docebo_Users__c`);
                }
                
                // Validate date fields
                console.log(`\n📊 DATE VALIDATION:`);
                validateDateField(createdUser.User_Creation_Date__c, 'User_Creation_Date__c', testType);
                validateDateField(createdUser.User_Expiration_Date__c, 'User_Expiration_Date__c', testType);
                validateDateField(createdUser.User_Last_Access_Date__c, 'User_Last_Access_Date__c', testType);
                
            } else {
                console.log(`❌ No Docebo_Users__c record found for ${testType}`);
            }

        } else {
            console.log(`❌ ${testType} user creation failed`);
        }

    } catch (error) {
        console.error(`💥 Error in ${testType} test:`, error);
    }
}

async function cleanupTestRecords(conn, email) {
    try {
        // Clean up Docebo_Users__c
        const existingUsers = await conn.sobject("Docebo_Users__c")
            .find({ Email__c: email })
            .execute();
        
        for (const user of existingUsers) {
            await conn.sobject("Docebo_Users__c").delete(user.Id);
            console.log(`   🗑️ Deleted existing Docebo_Users__c: ${user.Id}`);
        }

        // Clean up Leads
        const existingLeads = await conn.sobject("Lead")
            .find({ Email: email })
            .execute();
        
        for (const lead of existingLeads) {
            await conn.sobject("Lead").delete(lead.Id);
            console.log(`   🗑️ Deleted existing Lead: ${lead.Id}`);
        }

        // Clean up Contacts
        const existingContacts = await conn.sobject("Contact")
            .find({ Email: email })
            .execute();
        
        for (const contact of existingContacts) {
            await conn.sobject("Contact").delete(contact.Id);
            console.log(`   🗑️ Deleted existing Contact: ${contact.Id}`);
        }

    } catch (cleanupError) {
        console.log('   No existing records to clean up or cleanup failed:', cleanupError.message);
    }
}

function validateDateField(dateValue, fieldName, testType) {
    if (!dateValue) {
        console.log(`   ❌ ${fieldName}: MISSING for ${testType}`);
        return false;
    }
    
    try {
        const date = new Date(dateValue);
        if (isNaN(date.getTime())) {
            console.log(`   ❌ ${fieldName}: INVALID FORMAT (${dateValue}) for ${testType}`);
            return false;
        } else {
            console.log(`   ✅ ${fieldName}: VALID (${dateValue}) for ${testType}`);
            return true;
        }
    } catch (error) {
        console.log(`   ❌ ${fieldName}: ERROR (${error.message}) for ${testType}`);
        return false;
    }
}

// Execute the test
console.log('🔄 Starting Association and Date Test...');
testAssociationAndDates()
    .then(() => {
        console.log('\n✅ Association and Date test completed');
        process.exit(0);
    })
    .catch(err => {
        console.error('💥 Association and Date test failed:', err);
        process.exit(1);
    });
