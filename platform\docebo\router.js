const express = require("express");
const router = express.Router();

const {
    userManagement,
    sessionManagement,
    lpManagement,
    courseEnrollmentCreated,
    courseUnEnrollment,
    courseCompleted,
    getUserEnrollments,
    sessionInstructorAssigned,
    courseManagement
} = require("./controller");

router.post("/user/manage", userManagement);
router.post("/session/manage", sessionManagement);
router.post("/lp/manage", lpManagement);
router.post("/course/manage", courseManagement);
router.post("/user/enrollment/created", courseEnrollmentCreated);
router.post("/user/enrollment/deleted", courseUnEnrollment);
router.post("/user/enrollment/completed", courseCompleted);
router.post("/session/instructor/assigned", sessionInstructorAssigned);
router.get("/user/:userId/enrollments", getUserEnrollments);

module.exports = router;
