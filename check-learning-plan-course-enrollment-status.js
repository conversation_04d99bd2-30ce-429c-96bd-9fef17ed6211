require('dotenv').config();
const getConnection = require('./platform/salesforce/common/getConnection');
const getApiData = require("./common/docebo/fetcher");
const APP_BASE = process.env.DOCEBO_API_BASE_URL;

async function checkLearningPlanCourseEnrollmentStatus() {
    try {
        console.log('🔍 CHECKING LEARNING PLAN COURSE ENROLLMENT STATUS');
        console.log('=' .repeat(80));
        console.log('🎯 Target: Docebo_Learning_Plan_Course_Enrollment__c object');
        
        const conn = await getConnection();
        if (!conn || !conn.accessToken) {
            throw new Error('Failed to connect to Salesforce');
        }

        console.log('✅ Connected to Salesforce successfully');

        // Step 1: Check Salesforce Learning Plan Course Enrollments
        console.log('\n📊 STEP 1: Checking Salesforce Learning Plan Course Enrollments...');
        console.log('-'.repeat(50));
        
        // Method 1: Count using SOQL COUNT() function
        try {
            const countResult = await conn.query("SELECT COUNT() FROM Docebo_Learning_Plan_Course_Enrollment__c");
            console.log(`📊 Total Learning Plan Course Enrollments (COUNT): ${countResult.totalSize.toLocaleString()}`);
        } catch (countError) {
            console.log(`❌ COUNT query failed: ${countError.message}`);
        }

        // Method 2: Get sample records to understand structure
        const sampleRecords = await conn.sobject("Docebo_Learning_Plan_Course_Enrollment__c")
            .find({})
            .limit(10)
            .execute();
            
        console.log(`📊 Sample records found: ${sampleRecords.length}`);
        
        if (sampleRecords.length > 0) {
            console.log('\n📋 Sample Learning Plan Course Enrollment Records:');
            sampleRecords.slice(0, 3).forEach((enrollment, index) => {
                console.log(`   ${index + 1}. ID: ${enrollment.Id}`);
                console.log(`      Name: ${enrollment.Name}`);
                console.log(`      Learning Plan: ${enrollment.Learning_Plan__c}`);
                console.log(`      Course: ${enrollment.Course__c}`);
                console.log(`      User: ${enrollment.User__c}`);
                console.log(`      Status: ${enrollment.Status__c}`);
                console.log(`      Created: ${enrollment.CreatedDate}`);
                console.log('');
            });
        }

        // Step 2: Check what API endpoint might provide this data
        console.log('\n🔍 STEP 2: Investigating Docebo API for Learning Plan Course Enrollments...');
        console.log('-'.repeat(50));
        
        // Test different API endpoints that might contain learning plan course enrollment data
        const endpoints = [
            '/learningplan/v1/learningplans/enrollments', // This gives LP enrollments
            '/learn/v1/enrollments?type=learningplan',
            '/course/v1/courses/enrollments?learning_plan_id=1', // Try with a learning plan filter
            '/learningplan/v1/learningplans/1/courses', // Get courses in a learning plan
            '/learningplan/v1/learningplans/1/enrollments' // Get enrollments for specific LP
        ];
        
        let doceboData = [];
        let workingEndpoint = null;
        
        // First, let's get a learning plan ID to test with
        console.log('🔍 Getting Learning Plans to test with...');
        try {
            const lpResponse = await getApiData(
                'GET', 
                `${APP_BASE}/learningplan/v1/learningplans?page=1&page_size=5`, 
                null
            );
            
            if (lpResponse && lpResponse.status === 200) {
                const learningPlans = lpResponse.data?.items || [];
                console.log(`   Found ${learningPlans.length} learning plans to test with`);
                
                if (learningPlans.length > 0) {
                    const testLpId = learningPlans[0].id;
                    console.log(`   Using Learning Plan ID ${testLpId} for testing`);
                    
                    // Test endpoints with actual learning plan ID
                    const testEndpoints = [
                        `/learningplan/v1/learningplans/${testLpId}/courses`,
                        `/learningplan/v1/learningplans/${testLpId}/enrollments`,
                        `/learn/v1/enrollments?learning_plan_id=${testLpId}`,
                        `/course/v1/courses/enrollments?learning_plan_id=${testLpId}`
                    ];
                    
                    for (const endpoint of testEndpoints) {
                        console.log(`🧪 Testing endpoint: ${endpoint}`);
                        
                        try {
                            const response = await getApiData('GET', `${APP_BASE}${endpoint}?page=1&page_size=10`, null);
                            
                            if (response && response.status === 200) {
                                const items = response.data?.items || [];
                                console.log(`   ✅ Success: ${items.length} items found`);
                                console.log(`   📊 Total count: ${response.data?.count || 'N/A'}`);
                                
                                if (items.length > 0) {
                                    const sample = items[0];
                                    console.log(`   📋 Sample structure:`);
                                    console.log(`      Keys: ${Object.keys(sample).join(', ')}`);
                                    
                                    // Check if this looks like course enrollment data
                                    if (sample.course_id && sample.user_id) {
                                        console.log(`      🎯 This looks like course enrollment data!`);
                                        console.log(`      Course ID: ${sample.course_id}`);
                                        console.log(`      User ID: ${sample.user_id}`);
                                        console.log(`      Learning Plan ID: ${sample.learning_plan_id || testLpId}`);
                                        
                                        if (!workingEndpoint) {
                                            workingEndpoint = endpoint;
                                            doceboData = items;
                                        }
                                    }
                                }
                            } else {
                                console.log(`   ❌ No valid response`);
                            }
                        } catch (endpointError) {
                            console.log(`   ❌ Error: ${endpointError.message}`);
                        }
                    }
                }
            }
        } catch (lpError) {
            console.log(`❌ Could not get learning plans: ${lpError.message}`);
        }

        // Step 3: Try to understand the relationship
        console.log('\n📊 STEP 3: Understanding the Data Relationship...');
        console.log('-'.repeat(50));
        
        console.log('🔍 Learning Plan Course Enrollments represent:');
        console.log('   - Individual course enrollments within learning plans');
        console.log('   - When a user enrolls in a learning plan, they get enrolled in multiple courses');
        console.log('   - Each course enrollment within the LP should be a separate record');
        
        if (workingEndpoint) {
            console.log(`\n✅ Found potential data source: ${workingEndpoint}`);
            console.log(`📊 Sample data shows ${doceboData.length} course enrollments`);
            
            // Try to get more data from this endpoint
            console.log('\n🔄 Fetching more data from working endpoint...');
            try {
                let allData = [];
                let page = 1;
                let hasMore = true;
                
                while (hasMore && page <= 5) { // Limit to 5 pages for testing
                    const response = await getApiData('GET', `${APP_BASE}${workingEndpoint}?page=${page}&page_size=200`, null);
                    
                    if (response && response.status === 200) {
                        const items = response.data?.items || [];
                        allData.push(...items);
                        
                        console.log(`   📄 Page ${page}: ${items.length} items (Total: ${allData.length})`);
                        
                        hasMore = response.data?.has_more_data || false;
                        if (items.length === 0) hasMore = false;
                        page++;
                    } else {
                        hasMore = false;
                    }
                }
                
                console.log(`📊 Total course enrollments found via API: ${allData.length.toLocaleString()}`);
                
                if (allData.length > 0) {
                    // Analyze the data
                    const uniqueUsers = new Set();
                    const uniqueCourses = new Set();
                    const uniqueLearningPlans = new Set();
                    
                    allData.forEach(enrollment => {
                        if (enrollment.user_id) uniqueUsers.add(enrollment.user_id);
                        if (enrollment.course_id) uniqueCourses.add(enrollment.course_id);
                        if (enrollment.learning_plan_id) uniqueLearningPlans.add(enrollment.learning_plan_id);
                    });
                    
                    console.log(`   📊 Unique users: ${uniqueUsers.size}`);
                    console.log(`   📊 Unique courses: ${uniqueCourses.size}`);
                    console.log(`   📊 Unique learning plans: ${uniqueLearningPlans.size}`);
                }
                
            } catch (fetchError) {
                console.log(`❌ Error fetching more data: ${fetchError.message}`);
            }
        } else {
            console.log('\n❌ No working API endpoint found for learning plan course enrollments');
            console.log('💡 This might require a different approach or API endpoint');
        }

        // Step 4: Compare counts
        console.log('\n📊 STEP 4: Gap Analysis...');
        console.log('-'.repeat(50));
        
        const salesforceCount = sampleRecords.length > 0 ? 'Unknown (need COUNT query)' : 0;
        const doceboCount = doceboData.length;
        
        console.log(`Salesforce Learning Plan Course Enrollments: ${salesforceCount}`);
        console.log(`Docebo Learning Plan Course Enrollments (sample): ${doceboCount}`);
        
        if (workingEndpoint && doceboCount > sampleRecords.length) {
            console.log(`\n⚠️ POTENTIAL ISSUE: Docebo has more course enrollments than Salesforce`);
            console.log(`🔍 Need to investigate sync mechanism for learning plan course enrollments`);
        }

        return {
            success: true,
            salesforceCount: sampleRecords.length,
            doceboCount: doceboCount,
            workingEndpoint: workingEndpoint,
            sampleRecords: sampleRecords
        };

    } catch (error) {
        console.error('💥 Error checking learning plan course enrollment status:', error);
        return {
            success: false,
            error: error.message
        };
    }
}

// Execute the check
console.log('🔄 Starting Learning Plan Course Enrollment Status Check...');
checkLearningPlanCourseEnrollmentStatus()
    .then((result) => {
        console.log('\n📋 LEARNING PLAN COURSE ENROLLMENT STATUS SUMMARY:');
        console.log('=' .repeat(70));
        
        if (result.success) {
            console.log(`📊 Salesforce Learning Plan Course Enrollments: ${result.salesforceCount}`);
            console.log(`📊 Docebo Learning Plan Course Enrollments (sample): ${result.doceboCount}`);
            
            if (result.workingEndpoint) {
                console.log(`✅ Working API Endpoint: ${result.workingEndpoint}`);
            } else {
                console.log(`❌ No working API endpoint found`);
            }
            
            console.log(`\n💡 NEXT STEPS:`);
            console.log(`1. 🔍 Get accurate count from Salesforce using COUNT() query`);
            console.log(`2. 🔄 Find correct Docebo API endpoint for learning plan course enrollments`);
            console.log(`3. 🛡️ Create bulk sync script if gap exists`);
            console.log(`4. 📊 Set up monitoring for ongoing sync health`);
            
            if (result.sampleRecords.length === 0) {
                console.log(`\n⚠️ WARNING: No Learning Plan Course Enrollment records found in Salesforce!`);
                console.log(`🔍 This suggests the sync mechanism may not be working`);
            }
        } else {
            console.log(`❌ Check failed: ${result.error}`);
        }
        
        console.log('\n✅ Learning plan course enrollment status check completed');
        process.exit(0);
    })
    .catch(err => {
        console.error('💥 Learning plan course enrollment status check failed:', err);
        process.exit(1);
    });
