require('dotenv').config();
const { updateExistingContactOrLead } = require('./platform/salesforce/users/createUser');
const getConnection = require('./platform/salesforce/common/getConnection');

async function testAnnualRevenueFieldError() {
    try {
        console.log('🔧 Testing Annual Revenue Field Error Fix');
        console.log('=' .repeat(60));
        console.log('🎯 REPRODUCING ERROR:');
        console.log('   "No such column \'Annual_Revenue__c\' on sobject of type Lead"');
        console.log('   Root Cause: Using Contact field name on Lead object');
        console.log('   Solution: Use AnnualRevenue for Lead, Annual_Revenue__c for Contact');
        console.log('=' .repeat(60));
        
        const conn = await getConnection();
        if (!conn || !conn.accessToken) {
            console.error("❌ Invalid Salesforce connection");
            return;
        }

        // Step 1: Create test Lead to update
        console.log('\n📋 Creating test Lead for update test...');
        
        const testLeadData = {
            LastName: "Test Annual Revenue",
            FirstName: "Lead",
            Email: "<EMAIL>",
            Company: "Test Revenue Company",
            Title: "Revenue Test Manager",
            Status: "Open - Not Contacted",
            AnnualRevenue: 1000000, // Using correct field for Lead
            LeadSource: "Docebo Platform"
        };

        const leadResult = await conn.sobject("Lead").create(testLeadData);
        if (!leadResult.success) {
            console.error('❌ Failed to create test Lead:', leadResult.errors);
            return;
        }
        
        console.log(`✅ Test Lead created: ${leadResult.id}`);

        // Step 2: Create test Contact to update
        console.log('\n📋 Creating test Contact for update test...');
        
        // First create Account for Contact
        const accountData = {
            Name: "Test Revenue Account"
        };
        const accountResult = await conn.sobject("Account").create(accountData);
        
        const testContactData = {
            LastName: "Test Annual Revenue",
            FirstName: "Contact",
            Email: "<EMAIL>",
            Title: "Revenue Test Contact",
            AccountId: accountResult.id,
            Annual_Revenue__c: 2000000, // Using correct field for Contact
            LeadSource: "Docebo Platform"
        };

        const contactResult = await conn.sobject("Contact").create(testContactData);
        if (!contactResult.success) {
            console.error('❌ Failed to create test Contact:', contactResult.errors);
            return;
        }
        
        console.log(`✅ Test Contact created: ${contactResult.id}`);

        // Step 3: Test updateExistingContactOrLead function with Lead
        console.log('\n🧪 Testing updateExistingContactOrLead with Lead...');
        
        const tmpUserInfoForLead = {
            Last_Name__c: "Updated Lead Revenue",
            First_Name__c: "Test",
            Email__c: "<EMAIL>",
            Job_Title__c: "Updated Revenue Manager",
            Organization_Name__c: "Updated Revenue Company",
            Organization_URL__c: "https://updatedrevenue.com",
            Gender_Identity__c: "Woman",
            Role_Type__c: "Communications",
            Employment_Type__c: "Full-Time",
            Race_Identity__c: "Asian",
            User_Level__c: "6",
            Branch_Name__c: "Test Branch",
            Email_Validation_Status__c: true,
            User_Unique_Id__c: 99999,
            Annual_Revenue__c: 1500000, // This should be mapped to AnnualRevenue for Lead
            Industry__c: "Technology",
            Rating__c: "Hot",
            Initiative__c: "Test Initiative",
            NumberOfEmployees__c: 100,
            Network_Partnership__c: "Test Network",
            Account_ID__c: "",
            TimeZone__c: "America/New_York"
        };

        const userInfoForLead = {
            additional_fields: [],
            user_data: {
                phone: "555-1234",
                language: "english",
                timezone: "America/New_York"
            }
        };

        console.log('📝 Testing Lead update with Annual Revenue field...');
        try {
            await updateExistingContactOrLead(conn, tmpUserInfoForLead, userInfoForLead);
            console.log('✅ Lead update completed successfully');
        } catch (leadUpdateError) {
            console.error('❌ Lead update failed:', leadUpdateError.message);
            if (leadUpdateError.message.includes('Annual_Revenue__c')) {
                console.log('🔍 CONFIRMED: This is the error we need to fix!');
            }
        }

        // Step 4: Test updateExistingContactOrLead function with Contact
        console.log('\n🧪 Testing updateExistingContactOrLead with Contact...');
        
        const tmpUserInfoForContact = {
            Last_Name__c: "Updated Contact Revenue",
            First_Name__c: "Test",
            Email__c: "<EMAIL>",
            Job_Title__c: "Updated Revenue Contact",
            Organization_Name__c: "Updated Revenue Company",
            Organization_URL__c: "https://updatedrevenue.com",
            Gender_Identity__c: "Man",
            Role_Type__c: "Data and Research",
            Employment_Type__c: "Part-Time",
            Race_Identity__c: "Black or African American",
            User_Level__c: "4",
            Branch_Name__c: "Test Branch",
            Email_Validation_Status__c: true,
            User_Unique_Id__c: 99998,
            Annual_Revenue__c: 2500000, // This should stay as Annual_Revenue__c for Contact
            Industry__c: "Healthcare",
            Rating__c: "Warm",
            Initiative__c: "Test Initiative",
            NumberOfEmployees__c: 200,
            Network_Partnership__c: "Test Network"
        };

        const userInfoForContact = {
            additional_fields: [],
            user_data: {
                phone: "555-5678",
                language: "spanish",
                timezone: "America/Chicago"
            }
        };

        console.log('📝 Testing Contact update with Annual Revenue field...');
        try {
            await updateExistingContactOrLead(conn, tmpUserInfoForContact, userInfoForContact);
            console.log('✅ Contact update completed successfully');
        } catch (contactUpdateError) {
            console.error('❌ Contact update failed:', contactUpdateError.message);
        }

        // Step 5: Verify the updates worked correctly
        console.log('\n🔍 Verifying field mappings worked correctly...');
        
        // Check Lead
        const updatedLead = await conn.sobject("Lead")
            .findOne({ Id: leadResult.id });
        
        console.log('\n📊 UPDATED LEAD VERIFICATION:');
        console.log(`   Lead ID: ${updatedLead.Id}`);
        console.log(`   Name: ${updatedLead.FirstName} ${updatedLead.LastName}`);
        console.log(`   AnnualRevenue: ${updatedLead.AnnualRevenue}`);
        console.log(`   Company: ${updatedLead.Company}`);
        
        // Check Contact
        const updatedContact = await conn.sobject("Contact")
            .findOne({ Id: contactResult.id });
        
        console.log('\n📊 UPDATED CONTACT VERIFICATION:');
        console.log(`   Contact ID: ${updatedContact.Id}`);
        console.log(`   Name: ${updatedContact.FirstName} ${updatedContact.LastName}`);
        console.log(`   Annual_Revenue__c: ${updatedContact.Annual_Revenue__c}`);
        console.log(`   Account ID: ${updatedContact.AccountId}`);

        // Step 6: Verify field mapping correctness
        const leadRevenueCorrect = updatedLead.AnnualRevenue === tmpUserInfoForLead.Annual_Revenue__c;
        const contactRevenueCorrect = updatedContact.Annual_Revenue__c === tmpUserInfoForContact.Annual_Revenue__c;
        
        console.log('\n🎯 FIELD MAPPING VERIFICATION:');
        console.log(`   Lead AnnualRevenue: ${leadRevenueCorrect ? '✅' : '❌'} ${leadRevenueCorrect ? 'CORRECT' : 'INCORRECT'}`);
        console.log(`   Contact Annual_Revenue__c: ${contactRevenueCorrect ? '✅' : '❌'} ${contactRevenueCorrect ? 'CORRECT' : 'INCORRECT'}`);

        // Clean up test data
        await conn.sobject("Lead").delete(leadResult.id);
        await conn.sobject("Contact").delete(contactResult.id);
        await conn.sobject("Account").delete(accountResult.id);
        console.log('\n🗑️ Test data cleaned up');

        // Step 7: Summary
        console.log('\n📊 ANNUAL REVENUE FIELD ERROR ANALYSIS:');
        console.log('=' .repeat(60));
        
        if (leadRevenueCorrect && contactRevenueCorrect) {
            console.log('🎉 FIELD MAPPING IS CORRECT!');
            console.log('✅ Lead uses AnnualRevenue (standard field)');
            console.log('✅ Contact uses Annual_Revenue__c (custom field)');
            console.log('✅ No more "No such column" errors');
        } else {
            console.log('❌ FIELD MAPPING NEEDS FIXING:');
            if (!leadRevenueCorrect) {
                console.log('   • Lead AnnualRevenue field mapping failed');
            }
            if (!contactRevenueCorrect) {
                console.log('   • Contact Annual_Revenue__c field mapping failed');
            }
        }
        
        console.log('\n🔧 FIELD MAPPING RULES:');
        console.log('   • Lead Object → AnnualRevenue (standard field, no __c suffix)');
        console.log('   • Contact Object → Annual_Revenue__c (custom field, with __c suffix)');
        console.log('   • Never use Annual_Revenue__c on Lead objects');
        console.log('   • Never use AnnualRevenue on Contact objects');

        return {
            success: true,
            leadMappingCorrect: leadRevenueCorrect,
            contactMappingCorrect: contactRevenueCorrect
        };

    } catch (error) {
        console.error('💥 Error in Annual Revenue field test:', error);
        return {
            success: false,
            error: error.message
        };
    }
}

// Execute the test
console.log('🔄 Starting Annual Revenue field error test...');
testAnnualRevenueFieldError()
    .then((result) => {
        console.log('\n✅ Annual Revenue field test completed');
        if (result.success) {
            if (result.leadMappingCorrect && result.contactMappingCorrect) {
                console.log('🎉 All field mappings are working correctly!');
            } else {
                console.log('⚠️ Some field mappings need attention');
            }
        } else {
            console.log('❌ Test failed. Check the logs above.');
        }
        process.exit(0);
    })
    .catch(err => {
        console.error('💥 Test failed:', err);
        process.exit(1);
    });
