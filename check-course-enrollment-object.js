require('dotenv').config();
const getConnection = require('./platform/salesforce/common/getConnection');

async function checkCourseEnrollmentObject() {
    try {
        console.log('🔍 CHECKING COURSE ENROLLMENT OBJECT');
        console.log('=' .repeat(60));
        
        const conn = await getConnection();
        if (!conn || !conn.accessToken) {
            throw new Error('Failed to connect to Salesforce');
        }

        console.log('✅ Connected to Salesforce successfully');

        // Step 1: Check Docebo_Course_Enrollment__c object
        console.log('\n📊 STEP 1: Checking Docebo_Course_Enrollment__c Object...');
        console.log('-'.repeat(50));
        
        try {
            const courseEnrollmentMetadata = await conn.sobject("Docebo_Course_Enrollment__c").describe();
            
            console.log(`Object Label: ${courseEnrollmentMetadata.label}`);
            console.log(`Object Name: ${courseEnrollmentMetadata.name}`);
            console.log(`Total Fields: ${courseEnrollmentMetadata.fields.length}`);
            
            console.log('\n📋 Key Fields in Docebo_Course_Enrollment__c:');
            courseEnrollmentMetadata.fields.forEach(field => {
                if (field.name.includes('Course') || field.name.includes('User') || field.name.includes('External') || field.name.includes('Unique')) {
                    console.log(`   ${field.name} (${field.type}) - ${field.label}`);
                }
            });
            
            // Get sample records
            const sampleCourseEnrollments = await conn.sobject("Docebo_Course_Enrollment__c")
                .find({})
                .limit(5)
                .execute();
                
            console.log(`\n📊 Sample Course Enrollment Records: ${sampleCourseEnrollments.length}`);
            
            if (sampleCourseEnrollments.length > 0) {
                const sample = sampleCourseEnrollments[0];
                console.log('\n📋 Sample Course Enrollment Record:');
                Object.keys(sample).forEach(key => {
                    if (key !== 'attributes' && sample[key] !== null && sample[key] !== undefined) {
                        console.log(`   ${key}: ${sample[key]}`);
                    }
                });
            }
            
        } catch (courseEnrollmentError) {
            console.log(`❌ Could not access Docebo_Course_Enrollment__c: ${courseEnrollmentError.message}`);
        }

        // Step 2: Re-check Learning Plan Course Enrollment fields
        console.log('\n📊 STEP 2: Re-checking Learning Plan Course Enrollment Fields...');
        console.log('-'.repeat(50));
        
        const lpceMetadata = await conn.sobject("Docebo_Learning_Plan_Course_Enrollment__c").describe();
        
        console.log('\n📋 All Fields in Docebo_Learning_Plan_Course_Enrollment__c:');
        lpceMetadata.fields.forEach(field => {
            console.log(`   ${field.name} (${field.type}) - ${field.label}`);
        });

        // Step 3: Check relationships
        console.log('\n🔗 STEP 3: Checking Relationship Fields...');
        console.log('-'.repeat(50));
        
        const relationshipFields = lpceMetadata.fields.filter(field => 
            field.type === 'reference' || field.name.includes('__c')
        );
        
        console.log('Relationship and Custom Fields:');
        relationshipFields.forEach(field => {
            console.log(`   ${field.name} (${field.type}) - ${field.label}`);
            if (field.referenceTo && field.referenceTo.length > 0) {
                console.log(`      → References: ${field.referenceTo.join(', ')}`);
            }
        });

        return {
            success: true,
            lpceFields: lpceMetadata.fields.map(f => ({ name: f.name, type: f.type, label: f.label }))
        };

    } catch (error) {
        console.error('💥 Error checking course enrollment object:', error);
        return {
            success: false,
            error: error.message
        };
    }
}

console.log('🔄 Starting Course Enrollment Object Check...');
checkCourseEnrollmentObject()
    .then((result) => {
        console.log('\n📋 COURSE ENROLLMENT OBJECT CHECK SUMMARY:');
        console.log('=' .repeat(50));
        
        if (result.success) {
            console.log(`✅ Check completed successfully`);
            console.log(`📋 Total LPCE Fields: ${result.lpceFields.length}`);
            
            console.log('\n💡 KEY FINDINGS:');
            console.log('1. Course Enrollment should be a lookup to Docebo_Course_Enrollment__c');
            console.log('2. Remove Course Enrollment Status field usage');
            console.log('3. Focus on proper lookup relationships');
        } else {
            console.log(`❌ Check failed: ${result.error}`);
        }
        
        console.log('\n✅ Course Enrollment object check completed');
        process.exit(0);
    })
    .catch(err => {
        console.error('💥 Course Enrollment object check failed:', err);
        process.exit(1);
    });
