require('dotenv').config();
const { tidyData } = require('./platform/salesforce/users/createUser');

// Test with the exact data from our logs for User 18853
function testWithRealLogData() {
    console.log('🧪 Testing with Real Log Data from User 18853...');
    
    // This is the EXACT data from our logs for User 18853 (<EMAIL>)
    const realUserData = {
        "user_data": {
            "user_id": "18853",
            "username": "<EMAIL>",
            "first_name": "name",
            "last_name": "last",
            "email": "<EMAIL>",
            "uuid": "3d1f6fd7-4307-11f0-99ab-0affc1153b0b",
            "lang_code": "english",
            "lang_browsercode": "en",
            "force_change": "0",
            "expiration": null,
            "email_validation_status": "1",
            "valid": "1",
            "avatar": "",
            "can_manage_subordinates": false,
            "is_saml_provision": "0",
            "language": "english",
            "newsletter_optout": "0",
            "newsletter_optout_date": null,
            "last_update": "2025-06-06 18:51:40",
            "level": "6",
            "date_format": null,
            "timezone": "Europe/Oslo",
            "manager_first_name": null,
            "manager_last_name": null,
            "manager_username": null,
            "manager_id": null,
            "cant_have_direct_manager": false,
            "spu_edit_mode": null
        },
        "additional_fields": [
            {
                "id": "14",
                "title": "Organization/Employer (select your organization or select Other)",
                "type": "dropdown",
                "mandatory": true,
                "invisible_to_user": false,
                "settings": null,
                "sequence": 5,
                "max_length": null,
                "validation_criterion": null,
                "options": [
                    {"id": "51", "label": "Austin Aspires"},
                    {"id": "55", "label": "Boston Opportunity Agenda"}
                ],
                "value": "51", // Austin Aspires
                "enabled": true
            },
            {
                "id": "23",
                "title": "Organization/Employer",
                "type": "textfield",
                "mandatory": true,
                "invisible_to_user": false,
                "settings": null,
                "sequence": 7,
                "max_length": null,
                "validation_criterion": null,
                "value": null,
                "enabled": true
            },
            {
                "id": "21",
                "title": "Does your organization serve a national, regional, or local community?",
                "type": "dropdown",
                "mandatory": true,
                "invisible_to_user": false,
                "settings": null,
                "sequence": 8,
                "max_length": null,
                "validation_criterion": null,
                "options": [
                    {"id": "148", "label": "National"},
                    {"id": "149", "label": "Regional"},
                    {"id": "150", "label": "Local"}
                ],
                "value": "148", // National
                "enabled": true
            },
            {
                "id": "20",
                "title": "Are you tied to one of these initiatives?",
                "type": "dropdown",
                "mandatory": true,
                "invisible_to_user": false,
                "settings": null,
                "sequence": 10,
                "max_length": null,
                "validation_criterion": null,
                "options": [
                    {"id": "141", "label": "ENOUGH Act"},
                    {"id": "142", "label": "No"},
                    {"id": "147", "label": "District Continuous Improvement"}
                ],
                "value": "142", // No
                "enabled": true
            },
            {
                "id": "8",
                "title": "Job Title",
                "type": "textfield",
                "mandatory": true,
                "invisible_to_user": false,
                "settings": null,
                "sequence": 14,
                "max_length": null,
                "validation_criterion": null,
                "value": "title",
                "enabled": true
            },
            {
                "id": "9",
                "title": "Role Type",
                "type": "dropdown",
                "mandatory": true,
                "invisible_to_user": false,
                "settings": null,
                "sequence": 16,
                "max_length": null,
                "validation_criterion": null,
                "options": [
                    {"id": "17", "label": "Board of Directors"},
                    {"id": "19", "label": "Communications"},
                    {"id": "21", "label": "Executive Director"}
                ],
                "value": "17", // Board of Directors
                "enabled": true
            },
            {
                "id": "12",
                "title": "Race Identity",
                "type": "dropdown",
                "mandatory": true,
                "invisible_to_user": false,
                "settings": null,
                "sequence": 18,
                "max_length": null,
                "validation_criterion": null,
                "options": [
                    {"id": "35", "label": "Black or African American"},
                    {"id": "36", "label": "Hispanic or Latine"},
                    {"id": "38", "label": "White"},
                    {"id": "41", "label": "Prefer not to respond"}
                ],
                "value": "35", // Black or African American
                "enabled": true
            },
            {
                "id": "13",
                "title": "Gender Identity",
                "type": "dropdown",
                "mandatory": true,
                "invisible_to_user": false,
                "settings": null,
                "sequence": 19,
                "max_length": null,
                "validation_criterion": null,
                "options": [
                    {"id": "42", "label": "Man"},
                    {"id": "43", "label": "Woman"},
                    {"id": "44", "label": "Non-Binary or other gender identity"},
                    {"id": "45", "label": "Prefer not to respond"}
                ],
                "value": "45", // Prefer not to respond
                "enabled": true
            },
            {
                "id": "24",
                "title": "City",
                "type": "textfield",
                "mandatory": true,
                "invisible_to_user": false,
                "settings": null,
                "sequence": 20,
                "max_length": null,
                "validation_criterion": null,
                "value": null,
                "enabled": true
            },
            {
                "id": "25",
                "title": "State",
                "type": "dropdown",
                "mandatory": true,
                "invisible_to_user": false,
                "settings": null,
                "sequence": 21,
                "max_length": null,
                "validation_criterion": null,
                "options": [
                    {"id": "166", "label": "Arkansas"},
                    {"id": "167", "label": "California"},
                    {"id": "175", "label": "Illinois"}
                ],
                "value": null,
                "enabled": true
            }
        ],
        "branches": [
            {
                "id": 5,
                "iLeft": 8,
                "iRight": 9
            }
        ],
        "saml_settings": {
            "lock_provisioned_user_fields": 0,
            "provisioned_user_fields": []
        },
        "fired_at": "",
        "expiration_date": ""
    };

    const realUserListedInfo = {
        "user_data": {
            "user_id": "18853",
            "username": "<EMAIL>",
            "first_name": "name",
            "last_name": "last",
            "email": "<EMAIL>",
            "language": "english",
            "timezone": "Europe/Oslo"
        }
    };

    console.log('\n📋 REAL USER DATA FROM LOGS:');
    console.log('=' .repeat(60));
    console.log(`User ID: ${realUserData.user_data.user_id}`);
    console.log(`Email: ${realUserData.user_data.email}`);
    console.log(`Name: ${realUserData.user_data.first_name} ${realUserData.user_data.last_name}`);
    console.log(`Language: ${realUserData.user_data.language}`);
    console.log(`Timezone: ${realUserData.user_data.timezone}`);
    console.log(`Additional Fields: ${realUserData.additional_fields.length}`);

    console.log('\n🔧 PROCESSING WITH tidyData FUNCTION:');
    console.log('=' .repeat(60));

    // Process the data exactly like the webhook does
    const processedData = tidyData(realUserData, realUserListedInfo);

    console.log('\n📊 PROCESSED DATA RESULTS:');
    console.log('=' .repeat(50));
    console.log(`Organization: "${processedData.Organization_Name__c}"`);
    console.log(`Job Title: "${processedData.Job_Title__c}"`);
    console.log(`Role Type: "${processedData.Role_Type__c}"`);
    console.log(`Race: "${processedData.Race_Identity__c}"`);
    console.log(`Gender: "${processedData.Gender_Identity__c}"`);
    console.log(`Initiative: "${processedData.Initiative__c}"`);
    console.log(`Languages: "${processedData.Languages__c}"`);
    console.log(`National/Regional/Local: "${processedData.National_Regional_or_Local__c}"`);

    console.log('\n🎯 LEAD DATA THAT WOULD BE CREATED:');
    console.log('=' .repeat(60));

    // Create the Lead data exactly like the webhook does
    const leadData = {
        LastName: processedData.Last_Name__c || "Unknown",
        FirstName: processedData.First_Name__c,
        Email: processedData.Email__c,
        Company: processedData.Organization_Name__c || "-",
        Title: processedData.Job_Title__c || "",
        Website: processedData.Organization_URL__c || "",
        Status: "Open - Not Contacted",
        Created_by_Docebo_API__c: true,
        Gender__c: processedData.Gender_Identity__c,
        Role_Type__c: processedData.Role_Type__c,
        Employment_Type__c: processedData.Employment_Type__c || "",
        Race__c: processedData.Race_Identity__c,
        Description: `Docebo user - Level: ${processedData.Level__c}, Branch: N/A`,
        Fax: processedData.Fax__c || "",
        Salutation: processedData.Salutation__c || "",
        Phone: processedData.Phone__c || "",
        Languages__c: processedData.Languages__c || "",
        MailingCity__c: processedData.MailingCity__c || "",
        MailingCountry__c: processedData.MailingCountry__c || "",
        MailingPostalCode__c: processedData.MailingPostalCode__c || "",
        MailingState__c: processedData.MailingState__c || "",
        MailingStreet__c: processedData.MailingStreet__c || "",
        Position_Role__c: processedData.Position_Role__c || "",
        TimeZone__c: realUserData.user_data.timezone || "", // This is the key field!
        AnnualRevenue: processedData.AnnualRevenue__c || 0,
        Industry: processedData.Industry__c || "",
        NumberOfEmployees: processedData.NumberOfEmployees__c || 0,
        Rating: processedData.Rating__c || "",
        LeadSource: "Docebo Platform"
    };

    console.log('Lead data with real log data:');
    Object.keys(leadData).forEach(key => {
        const value = leadData[key];
        if (value && value !== "" && value !== 0 && value !== false) {
            console.log(`   ✅ ${key}: "${value}"`);
        } else {
            console.log(`   ❌ ${key}: "${value}" (empty/null)`);
        }
    });

    console.log('\n🔍 KEY FIELD ANALYSIS:');
    console.log('=' .repeat(50));
    
    const keyFields = [
        { name: 'TimeZone__c', value: leadData.TimeZone__c, expected: 'Europe/Oslo' },
        { name: 'Company', value: leadData.Company, expected: 'Austin Aspires' },
        { name: 'Title', value: leadData.Title, expected: 'title' },
        { name: 'Role_Type__c', value: leadData.Role_Type__c, expected: 'Board of Directors' },
        { name: 'Race__c', value: leadData.Race__c, expected: 'Black or African American' },
        { name: 'Gender__c', value: leadData.Gender__c, expected: 'Prefer not to respond' },
        { name: 'Languages__c', value: leadData.Languages__c, expected: 'english' }
    ];

    let successCount = 0;
    keyFields.forEach(field => {
        const isCorrect = field.value === field.expected;
        const status = isCorrect ? '✅' : '❌';
        if (isCorrect) successCount++;
        
        console.log(`${status} ${field.name}: "${field.value}" ${isCorrect ? '(CORRECT)' : `(Expected: "${field.expected}")`}`);
    });

    console.log('\n📊 FIELD MAPPING SUCCESS RATE:');
    console.log('=' .repeat(50));
    const successRate = Math.round((successCount / keyFields.length) * 100);
    console.log(`✅ Successful mappings: ${successCount}/${keyFields.length} (${successRate}%)`);

    if (successRate >= 85) {
        console.log('🎉 EXCELLENT! Field mapping is working correctly with real data!');
    } else if (successRate >= 70) {
        console.log('✅ GOOD! Most fields are mapping correctly');
    } else {
        console.log('⚠️ Some field mappings need attention');
    }

    console.log('\n💡 EXPECTED WEBHOOK OUTCOME:');
    console.log('=' .repeat(50));
    console.log('✅ Lead should be created with TimeZone__c = "Europe/Oslo"');
    console.log('✅ Docebo_Users__c should be created (no timezone field)');
    console.log('✅ No INVALID_FIELD errors');
    console.log('✅ User 18853 should create successfully');

    return {
        processedData,
        leadData,
        successRate,
        keyFields: keyFields.map(f => ({ name: f.name, value: f.value, correct: f.value === f.expected }))
    };
}

// Execute the test
console.log('🔄 Starting test with real log data...');
const result = testWithRealLogData();

console.log('\n🎯 REAL DATA TEST SUMMARY:');
console.log('=' .repeat(60));
console.log(`📊 Field mapping accuracy: ${result.successRate}%`);
console.log(`✅ Correct fields: ${result.keyFields.filter(f => f.correct).length}`);
console.log(`❌ Incorrect fields: ${result.keyFields.filter(f => !f.correct).length}`);

console.log('\n🚀 READY FOR WEBHOOK TEST:');
console.log('The processed data shows exactly what the webhook will create.');
console.log('User 18853 should now create successfully with this field configuration!');

console.log('\n✅ Real log data test completed!');
process.exit(0);
