require('dotenv').config();
const getConnection = require('./platform/salesforce/common/getConnection');

async function checkRequiredFieldsLearningPlanCourseEnrollment() {
    try {
        console.log('🔍 CHECKING REQUIRED FIELDS FOR LEARNING PLAN COURSE ENROLLMENT');
        console.log('=' .repeat(70));
        
        const conn = await getConnection();
        if (!conn || !conn.accessToken) {
            throw new Error('Failed to connect to Salesforce');
        }

        console.log('✅ Connected to Salesforce successfully');

        // Get object metadata to see required fields
        console.log('\n📊 STEP 1: Getting Object Metadata...');
        console.log('-'.repeat(50));
        
        const metadata = await conn.sobject("Docebo_Learning_Plan_Course_Enrollment__c").describe();
        
        console.log(`Object Label: ${metadata.label}`);
        console.log(`Object Name: ${metadata.name}`);
        console.log(`Total Fields: ${metadata.fields.length}`);
        
        // Show required fields
        console.log('\n📋 REQUIRED FIELDS:');
        const requiredFields = metadata.fields.filter(field => 
            !field.nillable && !field.defaultedOnCreate && field.createable
        );
        
        requiredFields.forEach(field => {
            console.log(`   ✅ ${field.name} (${field.type}) - ${field.label}`);
        });
        
        // Show optional fields
        console.log('\n📋 OPTIONAL FIELDS:');
        const optionalFields = metadata.fields.filter(field => 
            field.nillable && field.createable && field.name.includes('__c')
        );
        
        optionalFields.forEach(field => {
            console.log(`   ⚪ ${field.name} (${field.type}) - ${field.label}`);
        });

        // Check existing records to see what fields are actually populated
        console.log('\n📊 STEP 2: Checking Existing Records...');
        console.log('-'.repeat(50));
        
        const existingRecords = await conn.query(`
            SELECT Learning_Plan_Enrollment_Id__c, Course_Enrollment_Id__c, 
                   Completed__c, Effective__c, Enrollment_Date__c, 
                   Completion_Date__c, Completion_Percentage__c
            FROM Docebo_Learning_Plan_Course_Enrollment__c 
            LIMIT 2
        `);
        
        console.log(`Found ${existingRecords.totalSize} existing records:`);
        existingRecords.records.forEach((record, index) => {
            console.log(`\n   Record ${index + 1}:`);
            Object.keys(record).forEach(key => {
                if (key !== 'attributes') {
                    console.log(`      ${key}: ${record[key]}`);
                }
            });
        });

        // Test creating a minimal record
        console.log('\n🧪 STEP 3: Testing Minimal Record Creation...');
        console.log('-'.repeat(50));
        
        console.log('Testing what minimum fields are needed...');
        
        // Try creating with just the basic fields
        const testRecord = {
            Enrollment_Date__c: new Date().toISOString(),
            Completion_Percentage__c: 0,
            Effective__c: true,
            Completed__c: false
        };
        
        console.log('Test record structure:');
        console.log(JSON.stringify(testRecord, null, 2));
        
        try {
            const testResult = await conn.sobject("Docebo_Learning_Plan_Course_Enrollment__c")
                .create(testRecord);
                
            if (testResult.success) {
                console.log(`✅ SUCCESS: Minimal record created with ID: ${testResult.id}`);
                
                // Clean up the test record
                await conn.sobject("Docebo_Learning_Plan_Course_Enrollment__c")
                    .delete(testResult.id);
                console.log(`🧹 Test record cleaned up`);
                
                return {
                    success: true,
                    canCreateMinimal: true,
                    requiredFields: requiredFields.map(f => f.name),
                    optionalFields: optionalFields.map(f => f.name)
                };
            } else {
                const errorMessage = testResult.errors?.[0]?.message || 'Unknown error';
                console.log(`❌ FAILED: ${errorMessage}`);
                
                return {
                    success: true,
                    canCreateMinimal: false,
                    error: errorMessage,
                    requiredFields: requiredFields.map(f => f.name),
                    optionalFields: optionalFields.map(f => f.name)
                };
            }
            
        } catch (createError) {
            console.log(`❌ CREATE ERROR: ${createError.message}`);
            
            return {
                success: true,
                canCreateMinimal: false,
                error: createError.message,
                requiredFields: requiredFields.map(f => f.name),
                optionalFields: optionalFields.map(f => f.name)
            };
        }

    } catch (error) {
        console.error('💥 Error checking required fields:', error);
        return {
            success: false,
            error: error.message
        };
    }
}

console.log('🔄 Starting Required Fields Check...');
checkRequiredFieldsLearningPlanCourseEnrollment()
    .then((result) => {
        console.log('\n📋 REQUIRED FIELDS CHECK SUMMARY:');
        console.log('=' .repeat(50));
        
        if (result.success) {
            console.log(`✅ Check completed successfully`);
            console.log(`📋 Required Fields: ${result.requiredFields.length}`);
            console.log(`📋 Optional Fields: ${result.optionalFields.length}`);
            
            if (result.canCreateMinimal) {
                console.log(`\n🎉 SUCCESS: Can create records with minimal fields!`);
                console.log(`💡 SOLUTION: Create Learning Plan Course Enrollments without lookup dependencies`);
            } else {
                console.log(`\n❌ ISSUE: Cannot create minimal records`);
                console.log(`⚠️ Error: ${result.error}`);
                console.log(`💡 Need to identify truly required fields`);
            }
        } else {
            console.log(`❌ Check failed: ${result.error}`);
        }
        
        console.log('\n✅ Required fields check completed');
        process.exit(0);
    })
    .catch(err => {
        console.error('💥 Required fields check failed:', err);
        process.exit(1);
    });
