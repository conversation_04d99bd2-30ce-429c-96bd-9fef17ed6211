require('dotenv').config();
const getConnection = require('./platform/salesforce/common/getConnection');
const getApiData = require("./common/docebo/fetcher");
const APP_BASE = process.env.DOCEBO_API_BASE_URL;

async function completeLearningPlanEnrollmentBulkSync() {
    try {
        console.log('🚀 COMPLETE LEARNING PLAN ENROLLMENT BULK SYNC');
        console.log('=' .repeat(80));
        console.log('🎯 Target: Fetch ALL learning plan enrollments and sync to Salesforce');
        console.log('📡 API: /learningplan/v1/learningplans/enrollments');
        
        const conn = await getConnection();
        if (!conn || !conn.accessToken) {
            throw new Error('Failed to connect to Salesforce');
        }

        console.log('✅ Connected to Salesforce successfully');

        // Step 1: Get existing Salesforce Learning Plan Enrollments
        console.log('\n📊 STEP 1: Getting Existing Learning Plan Enrollments (Duplicate Prevention)...');
        console.log('-'.repeat(50));
        
        const existingEnrollments = await conn.sobject("Docebo_Learning_Plan_Enrollment__c")
            .find({})
            .execute();
            
        const existingEnrollmentIds = new Set();
        const existingUserPlanCombo = new Set();
        
        existingEnrollments.forEach(enrollment => {
            // Track by Enrollment_Id__c (primary duplicate check)
            if (enrollment.Enrolment_Id__c) {
                existingEnrollmentIds.add(enrollment.Enrolment_Id__c);
            }
            
            // Track by User + Learning Plan combination (secondary duplicate check)
            if (enrollment.Docebo_User_Id__c && enrollment.Learning_Plan_Id__c) {
                const userPlanKey = `${enrollment.Docebo_User_Id__c}-${enrollment.Learning_Plan_Id__c}`;
                existingUserPlanCombo.add(userPlanKey);
            }
        });
        
        console.log(`Found ${existingEnrollments.length.toLocaleString()} existing learning plan enrollments in Salesforce`);
        console.log(`📋 Duplicate prevention: ${existingEnrollmentIds.size.toLocaleString()} enrollment IDs tracked`);
        console.log(`📋 User-Plan combinations: ${existingUserPlanCombo.size.toLocaleString()} tracked`);

        // Step 2: Get Learning Plans mapping
        console.log('\n📚 STEP 2: Getting Learning Plans Mapping...');
        console.log('-'.repeat(50));
        
        const sfLearningPlans = await conn.sobject("Docebo_Learning_Plan__c")
            .find({})
            .execute();
            
        const learningPlanMapping = new Map();
        sfLearningPlans.forEach(plan => {
            if (plan.Learning_Plan_External_Id__c) {
                learningPlanMapping.set(plan.Learning_Plan_External_Id__c.toString(), {
                    id: plan.Id,
                    name: plan.Learning_Plan_Name__c,
                    externalId: plan.Learning_Plan_External_Id__c
                });
            }
        });
        
        console.log(`📊 Learning plan mapping created for ${learningPlanMapping.size} learning plans`);

        // Step 3: Fetch ALL learning plan enrollments from Docebo
        console.log('\n🔍 STEP 3: Fetching ALL Learning Plan Enrollments from Docebo...');
        console.log('-'.repeat(50));
        
        let allLearningPlanEnrollments = [];
        let page = 1;
        let hasMoreData = true;
        let totalProcessed = 0;
        
        console.log('📡 Using endpoint: /learningplan/v1/learningplans/enrollments');
        console.log('🔄 This may take 5-10 minutes to fetch all learning plan enrollments...');
        
        while (hasMoreData) {
            console.log(`   📄 Fetching page ${page}...`);
            
            try {
                const response = await getApiData(
                    'GET', 
                    `${APP_BASE}/learningplan/v1/learningplans/enrollments?page=${page}&page_size=200`, 
                    null
                );
                
                if (response && response.status === 200) {
                    const items = response.data?.items || [];
                    totalProcessed += items.length;
                    
                    // Filter enrollments for learning plans that exist in Salesforce
                    const validEnrollments = items.filter(enrollment => {
                        const planId = enrollment.learning_plan_id || enrollment.learningplan_id;
                        return learningPlanMapping.has(planId.toString());
                    });
                    
                    allLearningPlanEnrollments.push(...validEnrollments);
                    
                    console.log(`      Processed ${items.length} enrollments (Valid: ${validEnrollments.length}, Total: ${allLearningPlanEnrollments.length.toLocaleString()})`);
                    
                    hasMoreData = response.data?.has_more_data || false;
                    if (items.length === 0) hasMoreData = false;
                    
                    page++;
                    
                    // Progress indicator every 50 pages
                    if (page % 50 === 0) {
                        console.log(`   📊 Progress: Page ${page}, ${totalProcessed.toLocaleString()} total processed, ${allLearningPlanEnrollments.length.toLocaleString()} valid enrollments`);
                        
                        // Show enrollment distribution by learning plan
                        const enrollmentsByPlan = new Map();
                        allLearningPlanEnrollments.forEach(enrollment => {
                            const planId = enrollment.learning_plan_id || enrollment.learningplan_id;
                            if (!enrollmentsByPlan.has(planId)) {
                                enrollmentsByPlan.set(planId, 0);
                            }
                            enrollmentsByPlan.set(planId, enrollmentsByPlan.get(planId) + 1);
                        });
                        
                        const topPlans = Array.from(enrollmentsByPlan.entries())
                            .sort((a, b) => b[1] - a[1])
                            .slice(0, 5);
                        
                        console.log(`   🏆 Top learning plans by enrollment count:`);
                        topPlans.forEach(([planId, count], index) => {
                            const planInfo = learningPlanMapping.get(planId.toString());
                            const planName = planInfo?.name || 'Unknown';
                            console.log(`      ${index + 1}. Plan ${planId} (${planName}): ${count.toLocaleString()} enrollments`);
                        });
                    }
                    
                } else {
                    console.log(`      No data on page ${page}`);
                    hasMoreData = false;
                }
            } catch (pageError) {
                console.log(`      ❌ Error on page ${page}: ${pageError.message}`);
                
                // Continue to next page unless it's a critical error
                if (pageError.message.includes('401') || pageError.message.includes('403')) {
                    console.log('      🚨 Authentication error - stopping');
                    hasMoreData = false;
                } else {
                    console.log('      ⏭️ Continuing to next page...');
                    page++;
                }
            }
        }
        
        console.log(`\n✅ Docebo fetch completed:`);
        console.log(`   Total enrollments processed: ${totalProcessed.toLocaleString()}`);
        console.log(`   Valid learning plan enrollments found: ${allLearningPlanEnrollments.length.toLocaleString()}`);

        if (allLearningPlanEnrollments.length === 0) {
            throw new Error('No learning plan enrollments found via API');
        }

        // Step 4: Identify missing enrollments
        console.log('\n🔍 STEP 4: Identifying Missing Learning Plan Enrollments (Multi-Layer Duplicate Check)...');
        console.log('-'.repeat(50));
        
        const missingEnrollments = [];
        let duplicatesFound = 0;
        
        for (const doceboEnrollment of allLearningPlanEnrollments) {
            const planId = doceboEnrollment.learning_plan_id || doceboEnrollment.learningplan_id;
            const userId = doceboEnrollment.user_id;
            
            // Primary duplicate check: Learning Plan Enrollment ID
            const enrollmentId = `LP-${planId}-${userId}`;
            
            // Secondary duplicate check: User-Plan combination
            const userPlanKey = `${userId}-${planId}`;
            
            const isDuplicateById = existingEnrollmentIds.has(enrollmentId);
            const isDuplicateByCombo = existingUserPlanCombo.has(userPlanKey);
            
            if (!isDuplicateById && !isDuplicateByCombo) {
                missingEnrollments.push(doceboEnrollment);
            } else {
                duplicatesFound++;
            }
        }
        
        console.log(`Found ${missingEnrollments.length.toLocaleString()} missing learning plan enrollments to sync`);
        console.log(`Already synced: ${(allLearningPlanEnrollments.length - missingEnrollments.length).toLocaleString()} enrollments`);
        console.log(`🛡️ Duplicates prevented: ${duplicatesFound.toLocaleString()} enrollments`);

        if (missingEnrollments.length === 0) {
            console.log('✅ All learning plan enrollments are already synced!');
            return { 
                success: true, 
                synced: 0, 
                total: allLearningPlanEnrollments.length,
                existing: existingEnrollments.length,
                totalProcessed: totalProcessed
            };
        }

        // Step 5: Get user mappings
        console.log('\n👥 STEP 5: Getting User Mappings...');
        console.log('-'.repeat(50));
        
        const userIds = [...new Set(missingEnrollments.map(e => e.user_id))];
        console.log(`Need to map ${userIds.length.toLocaleString()} unique users`);
        
        const userMappings = new Map();
        
        // Batch query users in chunks
        const chunkSize = 100;
        for (let i = 0; i < userIds.length; i += chunkSize) {
            const chunk = userIds.slice(i, i + chunkSize);
            
            const users = await conn.sobject("Docebo_Users__c")
                .find({ User_Unique_Id__c: { $in: chunk } })
                .execute();
                
            users.forEach(user => {
                userMappings.set(user.User_Unique_Id__c, user.Id);
            });
            
            const chunkNum = Math.floor(i/chunkSize) + 1;
            const totalChunks = Math.ceil(userIds.length/chunkSize);
            
            if (chunkNum % 10 === 0 || chunkNum === totalChunks) {
                console.log(`   Mapped users: chunk ${chunkNum}/${totalChunks} (Total mapped: ${userMappings.size.toLocaleString()})`);
            }
        }
        
        console.log(`✅ Successfully mapped ${userMappings.size.toLocaleString()} users`);

        // Step 6: Prepare enrollment records
        console.log('\n📝 STEP 6: Preparing Learning Plan Enrollment Records...');
        console.log('-'.repeat(50));
        
        const enrollmentsToCreate = [];
        const enrollmentIdsToCreate = new Set(); // Track what we're about to create
        let skippedCount = 0;
        let finalDuplicatesSkipped = 0;
        
        for (const doceboEnrollment of missingEnrollments) {
            const planId = doceboEnrollment.learning_plan_id || doceboEnrollment.learningplan_id;
            const userId = doceboEnrollment.user_id;
            const salesforceUserId = userMappings.get(userId);
            const salesforcePlan = learningPlanMapping.get(planId.toString());
            
            if (!salesforceUserId || !salesforcePlan) {
                skippedCount++;
                continue;
            }
            
            // Final duplicate check: ensure we don't create duplicates within this batch
            const enrollmentId = `LP-${planId}-${userId}`;
            const userPlanKey = `${salesforceUserId}-${salesforcePlan.id}`;
            
            if (enrollmentIdsToCreate.has(enrollmentId) || existingUserPlanCombo.has(userPlanKey)) {
                finalDuplicatesSkipped++;
                continue;
            }
            
            // Add to tracking sets
            enrollmentIdsToCreate.add(enrollmentId);
            existingUserPlanCombo.add(userPlanKey); // Prevent duplicates in same batch
            
            // Parse enrollment date
            let enrollmentDate = "";
            if (doceboEnrollment.enrollment_date || doceboEnrollment.date_inscr) {
                const dateStr = doceboEnrollment.enrollment_date || doceboEnrollment.date_inscr;
                try {
                    enrollmentDate = new Date(dateStr.replace(' ', 'T')).toISOString();
                } catch (e) {
                    enrollmentDate = "";
                }
            }
            
            // Parse unenrollment date
            let unenrollmentDate = "";
            if (doceboEnrollment.unenrollment_date) {
                try {
                    unenrollmentDate = new Date(doceboEnrollment.unenrollment_date.replace(' ', 'T')).toISOString();
                } catch (e) {
                    unenrollmentDate = "";
                }
            }
            
            // Use exact field mapping from existing learning plan enrollment template
            const enrollmentRecord = {
                Docebo_User_Id__c: salesforceUserId,
                Enrolment_Id__c: enrollmentId,
                Learning_Plan_Id__c: salesforcePlan.id,
                Unenrollment_Date__c: unenrollmentDate,
                Who__c: doceboEnrollment.subscribed_by_id || doceboEnrollment.enrolled_by || ""
            };
            
            enrollmentsToCreate.push(enrollmentRecord);
        }
        
        console.log(`Prepared ${enrollmentsToCreate.length.toLocaleString()} learning plan enrollment records`);
        console.log(`Skipped ${skippedCount.toLocaleString()} enrollments (missing users/plans)`);
        console.log(`🛡️ Final duplicates prevented: ${finalDuplicatesSkipped.toLocaleString()} enrollments`);

        if (enrollmentsToCreate.length === 0) {
            console.log('⚠️ No enrollment records to create (all users/plans missing)');
            return {
                success: true,
                synced: 0,
                total: allLearningPlanEnrollments.length,
                existing: existingEnrollments.length,
                skipped: skippedCount,
                totalProcessed: totalProcessed
            };
        }

        return {
            success: true,
            doceboTotal: allLearningPlanEnrollments.length,
            salesforceInitial: existingEnrollments.length,
            missingCount: missingEnrollments.length,
            toCreate: enrollmentsToCreate.length,
            userMappings: userMappings.size,
            totalProcessed: totalProcessed,
            // Continue with sync phase...
            enrollmentsToCreate: enrollmentsToCreate,
            skippedCount: skippedCount
        };

    } catch (error) {
        console.error('💥 Error in complete learning plan enrollment bulk sync:', error);
        return {
            success: false,
            error: error.message
        };
    }
}

// Execute the complete bulk sync
console.log('🚀 Starting Complete Learning Plan Enrollment Bulk Sync...');
completeLearningPlanEnrollmentBulkSync()
    .then((result) => {
        if (result.success && result.enrollmentsToCreate) {
            console.log('\n📋 PHASE 1 COMPLETE - CONTINUING TO SYNC PHASE...');
            console.log('=' .repeat(60));
            console.log(`📊 Found ${result.missingCount.toLocaleString()} missing enrollments`);
            console.log(`📝 Prepared ${result.toCreate.toLocaleString()} records to create`);
            console.log(`👥 Mapped ${result.userMappings} users`);
            
            // Continue with the sync phase
            require('./complete-learning-plan-enrollment-sync-phase2.js')(result);
        } else {
            console.log('\n📋 LEARNING PLAN ENROLLMENT BULK SYNC SUMMARY:');
            console.log('=' .repeat(60));
            
            if (result.success) {
                console.log(`📊 Total Enrollments Processed: ${result.totalProcessed?.toLocaleString() || 'N/A'}`);
                console.log(`📊 Valid Enrollments Found: ${result.doceboTotal.toLocaleString()}`);
                console.log(`📊 Salesforce Initial: ${result.salesforceInitial.toLocaleString()}`);
                console.log(`✅ All enrollments already synced!`);
            } else {
                console.log(`❌ Learning plan enrollment bulk sync failed: ${result.error}`);
            }
            
            console.log('\n✅ Learning plan enrollment bulk sync completed');
            process.exit(0);
        }
    })
    .catch(err => {
        console.error('💥 Learning plan enrollment bulk sync failed:', err);
        process.exit(1);
    });
