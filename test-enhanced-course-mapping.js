require('dotenv').config();
const { getComprehensiveCourseData } = require('./platform/salesforce/courses/mapCourseData');

async function testEnhancedCourseMapping() {
    try {
        console.log('🧪 Testing Enhanced Course Data Mapping...');
        
        // Test with the same course ID we used before
        const testCourseId = 612;
        
        console.log(`\n📋 Testing comprehensive course mapping for Course ID: ${testCourseId}`);
        
        const result = await getComprehensiveCourseData(testCourseId);
        
        if (result && result.success) {
            console.log('✅ Course data mapping successful!');
            
            const mappedData = result.mappedData;
            
            console.log('\n📊 ENHANCED COURSE FIELD MAPPING RESULTS:');
            console.log('=' .repeat(80));
            
            // Your comprehensive field list with mapping status
            const fieldMappings = [
                { field: 'Course Unique Id', sfField: 'Course_Unique_Id__c', value: mappedData.Course_Unique_Id__c },
                { field: 'Course External Id', sfField: 'Course_External_Id__c', value: mappedData.Course_External_Id__c },
                { field: 'Course Internal ID', sfField: 'Course_Internal_ID__c', value: mappedData.Course_Internal_ID__c },
                { field: 'Course Code', sfField: 'Course_Code__c', value: mappedData.Course_Code__c },
                { field: 'Course Status', sfField: 'Course_Status__c', value: mappedData.Course_Status__c },
                { field: 'Course Name', sfField: 'Course_Name__c', value: mappedData.Course_Name__c },
                { field: 'Description', sfField: 'Description__c', value: mappedData.Description__c },
                { field: 'Course Duration', sfField: 'Course_Duration__c', value: mappedData.Course_Duration__c },
                { field: 'Course Type', sfField: 'Course_Type__c', value: mappedData.Course_Type__c },
                { field: 'Type', sfField: 'Type__c', value: mappedData.Type__c },
                { field: 'Skills in course', sfField: 'Skills_in_course__c', value: mappedData.Skills_in_course__c },
                { field: 'Course Link', sfField: 'Course_Link__c', value: mappedData.Course_Link__c },
                { field: 'Language', sfField: 'Language__c', value: mappedData.Language__c },
                { field: 'User Course Level', sfField: 'User_Course_Level__c', value: mappedData.User_Course_Level__c },
                { field: 'Number of actions', sfField: 'Number_of_actions__c', value: mappedData.Number_of_actions__c },
                { field: 'Number of sessions', sfField: 'Number_of_sessions__c', value: mappedData.Number_of_sessions__c },
                { field: 'Course Creation Date', sfField: 'Course_Creation_Date__c', value: mappedData.Course_Creation_Date__c },
                { field: 'Owner', sfField: 'Owner__c', value: mappedData.Owner__c },
                { field: 'Course Category', sfField: 'Course_Category__c', value: mappedData.Course_Category__c },
                { field: 'Course Category Code', sfField: 'Course_Category_Code__c', value: mappedData.Course_Category_Code__c },
                { field: 'Course Start Date', sfField: 'Course_Start_Date__c', value: mappedData.Course_Start_Date__c },
                { field: 'Course End Date', sfField: 'Course_End_Date__c', value: mappedData.Course_End_Date__c },
                { field: 'Session Time (min)', sfField: 'Session_Time_min__c', value: mappedData.Session_Time_min__c },
                { field: 'Enrollment Date', sfField: 'Enrollment_Date__c', value: mappedData.Enrollment_Date__c },
                { field: 'Effective', sfField: 'Effective__c', value: mappedData.Effective__c },
                { field: 'Deleted', sfField: 'Deleted__c', value: mappedData.Deleted__c },
                { field: 'Deletion Date', sfField: 'Deletion_Date__c', value: mappedData.Deletion_Date__c },
                { field: 'Course has expired', sfField: 'Course_has_expired__c', value: mappedData.Course_has_expired__c },
                { field: 'Course Progress (%)', sfField: 'Course_Progress__c', value: mappedData.Course_Progress__c },
                { field: 'Created By', sfField: 'CreatedById', value: mappedData.CreatedById },
                { field: 'Last Modified By', sfField: 'LastModifiedById', value: mappedData.LastModifiedById },
                { field: 'Last Update Date', sfField: 'Last_Update_Date__c', value: mappedData.Last_Update_Date__c },
                { field: 'Score', sfField: 'Score__c', value: mappedData.Score__c },
                { field: 'Slug', sfField: 'Slug__c', value: mappedData.Slug__c },
                { field: 'Thumbnail', sfField: 'Thumbnail__c', value: mappedData.Thumbnail__c },
                { field: 'Training Material Time (sec)', sfField: 'Training_Material_Time_sec__c', value: mappedData.Training_Material_Time_sec__c }
            ];
            
            let mappedCount = 0;
            let populatedCount = 0;
            
            fieldMappings.forEach(mapping => {
                const hasValue = mapping.value !== "" && mapping.value !== 0 && mapping.value !== null && mapping.value !== undefined;
                const status = hasValue ? '✅' : '⚪';
                
                console.log(`${status} ${mapping.field}: ${mapping.sfField} = ${mapping.value || 'N/A'}`);
                
                mappedCount++;
                if (hasValue) populatedCount++;
            });
            
            console.log('\n📊 MAPPING SUMMARY:');
            console.log('=' .repeat(50));
            console.log(`📋 Total Fields Mapped: ${mappedCount}/36 (100%)`);
            console.log(`✅ Fields with Data: ${populatedCount}/36 (${Math.round(populatedCount/36*100)}%)`);
            console.log(`⚪ Fields without Data: ${mappedCount - populatedCount}/36 (${Math.round((mappedCount - populatedCount)/36*100)}%)`);
            
            console.log('\n🎯 KEY POPULATED FIELDS:');
            console.log('-'.repeat(40));
            fieldMappings
                .filter(m => m.value !== "" && m.value !== 0 && m.value !== null && m.value !== undefined)
                .forEach(m => {
                    console.log(`   ${m.field}: ${m.value}`);
                });
            
            console.log('\n💾 Complete mapped data structure:');
            console.log(JSON.stringify(mappedData, null, 2));
            
            return {
                totalFields: mappedCount,
                populatedFields: populatedCount,
                mappedData: mappedData
            };
            
        } else {
            console.log('❌ Failed to get course data');
            console.log('Error:', result?.error || 'Unknown error');
            return null;
        }
        
    } catch (error) {
        console.error('💥 Error testing enhanced course mapping:', error);
        return null;
    }
}

// Execute the test
console.log('🔄 Starting enhanced course mapping test...');
testEnhancedCourseMapping()
    .then((result) => {
        if (result) {
            console.log(`\n✅ Test completed successfully!`);
            console.log(`📊 Field Population: ${result.populatedFields}/${result.totalFields} fields have data`);
            
            if (result.populatedFields >= 15) {
                console.log('🎉 EXCELLENT! Your course mapping captures comprehensive data!');
            } else {
                console.log('⚠️ Some fields may need additional data sources or API calls');
            }
        } else {
            console.log('\n⚠️ Test completed but no results obtained');
        }
        process.exit(0);
    })
    .catch(err => {
        console.error('💥 Test failed:', err);
        process.exit(1);
    });
