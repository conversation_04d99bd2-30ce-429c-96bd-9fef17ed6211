require('dotenv').config();
const getConnection = require('./platform/salesforce/common/getConnection');
const { createCourseEnrollment, saveCourseEnrollmentsInBatch } = require('./platform/salesforce/courseEnrollment/createCourseEnrollment');

async function testCourseEnrollmentFix() {
    try {
        console.log('🧪 Testing Course Enrollment Association Fix');
        console.log('=' .repeat(60));
        
        const conn = await getConnection();
        if (!conn || !conn.accessToken) {
            console.error("❌ Invalid Salesforce connection");
            return;
        }

        // Step 1: Check current state of course enrollments
        console.log('\n📊 Checking current course enrollment state...');
        console.log('-'.repeat(50));
        
        const enrollments = await conn.sobject("Docebo_CourseEnrollment__c")
            .find({})
            .limit(10)
            .execute();
        
        console.log(`📋 Found ${enrollments.length} course enrollment records`);
        
        let withCourseAssociation = 0;
        let withUserAssociation = 0;
        
        enrollments.forEach(enrollment => {
            if (enrollment.Course__c) withCourseAssociation++;
            if (enrollment.Docebo_User__c) withUserAssociation++;
        });
        
        console.log(`✅ With Course Association: ${withCourseAssociation}/${enrollments.length}`);
        console.log(`✅ With User Association: ${withUserAssociation}/${enrollments.length}`);

        // Step 2: Get sample course and user for testing
        console.log('\n🔍 Finding sample course and user for testing...');
        console.log('-'.repeat(50));
        
        const sampleCourse = await conn.sobject("Docebo_Course__c")
            .findOne({});
        
        const sampleUser = await conn.sobject("Docebo_Users__c")
            .findOne({});
        
        if (!sampleCourse || !sampleUser) {
            console.error("❌ No sample course or user found for testing");
            return;
        }
        
        console.log(`✅ Sample Course: ${sampleCourse.Id} - ${sampleCourse.Course_Name__c}`);
        console.log(`   Course External ID: ${sampleCourse.Course_External_Id__c}`);
        console.log(`✅ Sample User: ${sampleUser.Id} - ${sampleUser.First_Name__c} ${sampleUser.Last_Name__c}`);
        console.log(`   User Unique ID: ${sampleUser.User_Unique_Id__c}`);

        // Step 3: Test single enrollment creation
        console.log('\n🧪 Testing single enrollment creation...');
        console.log('-'.repeat(50));
        
        const testEnrollmentData = {
            course_id: sampleCourse.Course_External_Id__c,
            user_id: sampleUser.User_Unique_Id__c,
            enrollment_date: "2025-01-15 10:00:00",
            completion_date: null,
            status: "Enrolled",
            score: 0
        };
        
        console.log(`📋 Test enrollment data:`);
        console.log(`   Course ID: ${testEnrollmentData.course_id}`);
        console.log(`   User ID: ${testEnrollmentData.user_id}`);
        console.log(`   Status: ${testEnrollmentData.status}`);
        
        // Clean up any existing test enrollment
        try {
            const existingEnrollment = await conn.sobject("Docebo_CourseEnrollment__c")
                .findOne({ External_Id__c: `${testEnrollmentData.course_id}-${testEnrollmentData.user_id}` });
            
            if (existingEnrollment) {
                await conn.sobject("Docebo_CourseEnrollment__c").delete(existingEnrollment.Id);
                console.log(`🗑️ Deleted existing test enrollment: ${existingEnrollment.Id}`);
            }
        } catch (cleanupError) {
            console.log('No existing enrollment to clean up');
        }
        
        // Create the enrollment
        const singleResult = await createCourseEnrollment(testEnrollmentData);
        
        if (singleResult) {
            console.log('✅ Single enrollment creation successful!');
            
            // Verify the created enrollment
            const createdEnrollment = await conn.sobject("Docebo_CourseEnrollment__c")
                .findOne({ External_Id__c: `${testEnrollmentData.course_id}-${testEnrollmentData.user_id}` });
            
            if (createdEnrollment) {
                console.log(`✅ Enrollment verified: ${createdEnrollment.Id}`);
                console.log(`   Course__c: ${createdEnrollment.Course__c || 'MISSING'}`);
                console.log(`   Docebo_User__c: ${createdEnrollment.Docebo_User__c || 'MISSING'}`);
                console.log(`   Status__c: ${createdEnrollment.Status__c}`);
                console.log(`   External_Id__c: ${createdEnrollment.External_Id__c}`);
                
                if (createdEnrollment.Course__c && createdEnrollment.Docebo_User__c) {
                    console.log('🎯 SUCCESS! Both course and user associations are working!');
                } else {
                    console.log('❌ ISSUE: Missing course or user association');
                }
            } else {
                console.log('❌ Created enrollment not found');
            }
        } else {
            console.log('❌ Single enrollment creation failed');
        }

        // Step 4: Test batch enrollment creation
        console.log('\n🧪 Testing batch enrollment creation...');
        console.log('-'.repeat(50));
        
        // Get another course for batch testing
        const courses = await conn.sobject("Docebo_Course__c")
            .find({})
            .limit(3)
            .execute();
        
        const users = await conn.sobject("Docebo_Users__c")
            .find({})
            .limit(2)
            .execute();
        
        if (courses.length >= 2 && users.length >= 2) {
            const batchEnrollments = [
                {
                    course_id: courses[1].Course_External_Id__c,
                    user_id: users[0].User_Unique_Id__c,
                    enrollment_date: "2025-01-16 10:00:00",
                    status: "Enrolled",
                    score: 0
                },
                {
                    course_id: courses[0].Course_External_Id__c,
                    user_id: users[1].User_Unique_Id__c,
                    enrollment_date: "2025-01-16 11:00:00",
                    status: "Enrolled",
                    score: 0
                }
            ];
            
            console.log(`📋 Testing batch with ${batchEnrollments.length} enrollments`);
            
            // Clean up existing batch enrollments
            for (const enrollment of batchEnrollments) {
                try {
                    const existing = await conn.sobject("Docebo_CourseEnrollment__c")
                        .findOne({ External_Id__c: `${enrollment.course_id}-${enrollment.user_id}` });
                    
                    if (existing) {
                        await conn.sobject("Docebo_CourseEnrollment__c").delete(existing.Id);
                        console.log(`🗑️ Deleted existing batch enrollment: ${existing.Id}`);
                    }
                } catch (cleanupError) {
                    // Ignore cleanup errors
                }
            }
            
            const batchResult = await saveCourseEnrollmentsInBatch(batchEnrollments);
            
            if (batchResult) {
                console.log('✅ Batch enrollment creation successful!');
                
                // Verify batch enrollments
                let batchSuccessCount = 0;
                for (const enrollment of batchEnrollments) {
                    const created = await conn.sobject("Docebo_CourseEnrollment__c")
                        .findOne({ External_Id__c: `${enrollment.course_id}-${enrollment.user_id}` });
                    
                    if (created && created.Course__c && created.Docebo_User__c) {
                        batchSuccessCount++;
                        console.log(`✅ Batch enrollment ${batchSuccessCount}: ${created.Id} - Fully associated`);
                    } else if (created) {
                        console.log(`⚠️ Batch enrollment found but missing associations: ${created.Id}`);
                    } else {
                        console.log(`❌ Batch enrollment not found: ${enrollment.course_id}-${enrollment.user_id}`);
                    }
                }
                
                console.log(`🎯 Batch Success Rate: ${batchSuccessCount}/${batchEnrollments.length} (${Math.round(batchSuccessCount/batchEnrollments.length*100)}%)`);
            } else {
                console.log('❌ Batch enrollment creation failed');
            }
        } else {
            console.log('⚠️ Not enough courses/users for batch testing');
        }

        // Step 5: Final analysis
        console.log('\n📊 Final Analysis...');
        console.log('-'.repeat(50));
        
        const finalEnrollments = await conn.sobject("Docebo_CourseEnrollment__c")
            .find({})
            .limit(20)
            .execute();
        
        let finalWithCourse = 0;
        let finalWithUser = 0;
        let finalComplete = 0;
        
        finalEnrollments.forEach(enrollment => {
            if (enrollment.Course__c) finalWithCourse++;
            if (enrollment.Docebo_User__c) finalWithUser++;
            if (enrollment.Course__c && enrollment.Docebo_User__c) finalComplete++;
        });
        
        console.log(`📊 FINAL RESULTS (${finalEnrollments.length} enrollments checked):`);
        console.log(`   ✅ With Course Association: ${finalWithCourse}/${finalEnrollments.length} (${Math.round(finalWithCourse/finalEnrollments.length*100)}%)`);
        console.log(`   ✅ With User Association: ${finalWithUser}/${finalEnrollments.length} (${Math.round(finalWithUser/finalEnrollments.length*100)}%)`);
        console.log(`   🎯 Fully Associated: ${finalComplete}/${finalEnrollments.length} (${Math.round(finalComplete/finalEnrollments.length*100)}%)`);
        
        if (finalComplete === finalEnrollments.length) {
            console.log('\n🎉 SUCCESS! All course enrollments are properly associated!');
        } else {
            console.log('\n⚠️ Some enrollments still missing associations. Check the fixes.');
        }

    } catch (error) {
        console.error('💥 Error in test:', error);
    }
}

// Execute the test
console.log('🔄 Starting course enrollment fix test...');
testCourseEnrollmentFix()
    .then(() => {
        console.log('\n✅ Test completed');
        process.exit(0);
    })
    .catch(err => {
        console.error('💥 Test failed:', err);
        process.exit(1);
    });
