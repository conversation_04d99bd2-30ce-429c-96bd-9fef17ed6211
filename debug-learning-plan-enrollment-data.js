require('dotenv').config();
const getConnection = require('./platform/salesforce/common/getConnection');
const getApiData = require("./common/docebo/fetcher");
const APP_BASE = process.env.DOCEBO_API_BASE_URL;

async function debugLearningPlanEnrollmentData() {
    try {
        console.log('🔍 DEBUG LEARNING PLAN ENROLLMENT DATA STRUCTURE');
        console.log('=' .repeat(80));
        
        const conn = await getConnection();
        if (!conn || !conn.accessToken) {
            throw new Error('Failed to connect to Salesforce');
        }

        // Step 1: Get a learning plan to test with
        console.log('📚 STEP 1: Getting a Learning Plan to test with...');
        console.log('-'.repeat(50));
        
        const sfLearningPlans = await conn.sobject("Docebo_Learning_Plan__c")
            .find({})
            .limit(5)
            .execute();
            
        if (sfLearningPlans.length === 0) {
            throw new Error('No learning plans found in Salesforce');
        }
        
        const testLp = sfLearningPlans[0];
        const lpId = testLp.Learning_Plan_External_Id__c;
        console.log(`Using Learning Plan: ${lpId} - ${testLp.Learning_Plan_Name__c}`);
        
        // Step 2: Get sample course enrollment data from Docebo
        console.log('\n🔍 STEP 2: Getting Sample Course Enrollment Data...');
        console.log('-'.repeat(50));
        
        const response = await getApiData(
            'GET', 
            `${APP_BASE}/learn/v1/enrollments?learning_plan_id=${lpId}&page=1&page_size=3`, 
            null
        );
        
        if (!response || response.status !== 200) {
            throw new Error(`Could not get enrollments for learning plan ${lpId}`);
        }
        
        const items = response.data?.items || [];
        console.log(`Found ${items.length} enrollment items`);
        
        if (items.length === 0) {
            console.log('⚠️ No enrollment items found for this learning plan');
            return;
        }
        
        // Step 3: Analyze the data structure
        console.log('\n📊 STEP 3: Analyzing Data Structure...');
        console.log('-'.repeat(50));
        
        items.forEach((item, index) => {
            console.log(`\n📋 Enrollment Item ${index + 1}:`);
            console.log(`   📊 All fields: ${Object.keys(item).join(', ')}`);
            
            // Check for course-related fields
            const courseFields = ['uidCourse', 'course_id', 'courseId', 'course_uid', 'course_name', 'name'];
            console.log(`\n   🎯 Course-related fields:`);
            courseFields.forEach(field => {
                console.log(`      ${field}: ${item[field] || 'undefined'}`);
            });
            
            // Check for user-related fields
            const userFields = ['user_id', 'userId', 'username', 'user_name', 'email'];
            console.log(`\n   👤 User-related fields:`);
            userFields.forEach(field => {
                console.log(`      ${field}: ${item[field] || 'undefined'}`);
            });
            
            // Check for enrollment-related fields
            const enrollmentFields = ['status', 'enrollment_status', 'enroll_date_of_enrollment', 'course_complete_date', 'score'];
            console.log(`\n   📈 Enrollment-related fields:`);
            enrollmentFields.forEach(field => {
                console.log(`      ${field}: ${item[field] || 'undefined'}`);
            });
            
            if (index === 0) {
                console.log(`\n   🔍 Full sample object:`);
                console.log(JSON.stringify(item, null, 2));
            }
        });
        
        // Step 4: Check Salesforce mappings
        console.log('\n📚 STEP 4: Checking Salesforce Mappings...');
        console.log('-'.repeat(50));
        
        // Get sample courses
        const sfCourses = await conn.sobject("Docebo_Course__c")
            .find({})
            .limit(5)
            .execute();
            
        console.log(`\n📊 Sample Salesforce Courses:`);
        sfCourses.slice(0, 3).forEach((course, index) => {
            console.log(`   ${index + 1}. ${course.Course_Name__c}`);
            console.log(`      Course_External_Id__c: ${course.Course_External_Id__c}`);
            console.log(`      Course_Unique_Id__c: ${course.Course_Unique_Id__c}`);
            console.log(`      Course_Internal_Id__c: ${course.Course_Internal_Id__c}`);
        });
        
        // Get sample users
        const sfUsers = await conn.sobject("Docebo_Users__c")
            .find({})
            .limit(5)
            .execute();
            
        console.log(`\n👥 Sample Salesforce Users:`);
        sfUsers.slice(0, 3).forEach((user, index) => {
            console.log(`   ${index + 1}. ${user.Email__c}`);
            console.log(`      User_Unique_Id__c: ${user.User_Unique_Id__c}`);
            console.log(`      User_External_Id__c: ${user.User_External_Id__c}`);
        });
        
        // Step 5: Try to match data
        console.log('\n🔍 STEP 5: Attempting Data Matching...');
        console.log('-'.repeat(50));
        
        if (items.length > 0) {
            const sampleItem = items[0];
            
            // Try different course field combinations
            const possibleCourseIds = [
                sampleItem.uidCourse,
                sampleItem.course_id,
                sampleItem.courseId,
                sampleItem.course_uid
            ].filter(id => id !== undefined);
            
            console.log(`Possible course IDs from Docebo: ${possibleCourseIds.join(', ')}`);
            
            // Try different user field combinations
            const possibleUserIds = [
                sampleItem.user_id,
                sampleItem.userId,
                sampleItem.username
            ].filter(id => id !== undefined);
            
            console.log(`Possible user IDs from Docebo: ${possibleUserIds.join(', ')}`);
            
            // Check if any match Salesforce data
            let courseMatches = 0;
            let userMatches = 0;
            
            possibleCourseIds.forEach(courseId => {
                const matchingCourse = sfCourses.find(course => 
                    course.Course_External_Id__c?.toString() === courseId?.toString() ||
                    course.Course_Unique_Id__c?.toString() === courseId?.toString() ||
                    course.Course_Internal_Id__c?.toString() === courseId?.toString()
                );
                if (matchingCourse) {
                    courseMatches++;
                    console.log(`✅ Course match found: ${courseId} → ${matchingCourse.Course_Name__c}`);
                }
            });
            
            possibleUserIds.forEach(userId => {
                const matchingUser = sfUsers.find(user => 
                    user.User_Unique_Id__c?.toString() === userId?.toString() ||
                    user.User_External_Id__c?.toString() === userId?.toString()
                );
                if (matchingUser) {
                    userMatches++;
                    console.log(`✅ User match found: ${userId} → ${matchingUser.Email__c}`);
                }
            });
            
            console.log(`\n📊 Matching Summary:`);
            console.log(`   Course matches: ${courseMatches}/${possibleCourseIds.length}`);
            console.log(`   User matches: ${userMatches}/${possibleUserIds.length}`);
            
            if (courseMatches === 0) {
                console.log(`⚠️ No course matches found - this explains why 0 valid enrollments`);
            }
            if (userMatches === 0) {
                console.log(`⚠️ No user matches found - this explains why 0 valid enrollments`);
            }
        }
        
        return {
            success: true,
            itemsFound: items.length,
            sampleFields: items.length > 0 ? Object.keys(items[0]) : []
        };
        
    } catch (error) {
        console.error('💥 Error debugging learning plan enrollment data:', error);
        return {
            success: false,
            error: error.message
        };
    }
}

// Execute the debug
console.log('🔄 Starting Learning Plan Enrollment Data Debug...');
debugLearningPlanEnrollmentData()
    .then((result) => {
        console.log('\n📋 DEBUG SUMMARY:');
        console.log('=' .repeat(50));
        
        if (result.success) {
            console.log(`✅ Debug completed successfully`);
            console.log(`📊 Items Found: ${result.itemsFound}`);
            console.log(`📊 Sample Fields: ${result.sampleFields.join(', ')}`);
        } else {
            console.log(`❌ Debug failed: ${result.error}`);
        }
        
        console.log('\n✅ Learning plan enrollment data debug completed');
        process.exit(0);
    })
    .catch(err => {
        console.error('💥 Learning plan enrollment data debug failed:', err);
        process.exit(1);
    });
