require('dotenv').config();
const getConnection = require('./platform/salesforce/common/getConnection');

async function checkPicklistValues() {
    try {
        console.log('🔍 Checking picklist values for Lead fields...');
        
        const conn = await getConnection();
        if (!conn || !conn.accessToken) {
            console.error("❌ Invalid Salesforce connection");
            return;
        }

        // Describe the Lead object to get field details
        const leadDescription = await conn.sobject("Lead").describe();
        
        // Find picklist fields we're interested in
        const picklistFields = ['Role_Type__c', 'Race__c', 'Gender__c', 'Status'];
        
        for (const fieldName of picklistFields) {
            const field = leadDescription.fields.find(f => f.name === fieldName);
            
            if (field && field.type === 'picklist') {
                console.log(`\n📋 ${fieldName} (${field.label}) - Valid Values:`);
                
                if (field.picklistValues && field.picklistValues.length > 0) {
                    field.picklistValues.forEach(value => {
                        const status = value.active ? '✅' : '❌';
                        console.log(`   ${status} "${value.value}" ${value.defaultValue ? '(default)' : ''}`);
                    });
                } else {
                    console.log('   No picklist values found');
                }
            } else {
                console.log(`\n❌ ${fieldName} - Field not found or not a picklist`);
            }
        }

        // Also check the Branches_Codes__c field type issue
        console.log('\n🔍 Checking Docebo_Users__c object for data type issues...');
        
        const doceboUserDescription = await conn.sobject("Docebo_Users__c").describe();
        const branchCodesField = doceboUserDescription.fields.find(f => f.name === 'Branches_Codes__c');
        
        if (branchCodesField) {
            console.log(`\n📋 Branches_Codes__c field details:`);
            console.log(`   Type: ${branchCodesField.type}`);
            console.log(`   Label: ${branchCodesField.label}`);
            console.log(`   Length: ${branchCodesField.length}`);
            console.log(`   Precision: ${branchCodesField.precision}`);
            console.log(`   Scale: ${branchCodesField.scale}`);
        }

    } catch (error) {
        console.error('💥 Error checking picklist values:', error);
    }
}

// Execute the check
console.log('🔄 Starting picklist values check...');
checkPicklistValues()
    .then(() => {
        console.log('\n✅ Picklist check completed');
        process.exit(0);
    })
    .catch(err => {
        console.error('💥 Picklist check failed:', err);
        process.exit(1);
    });
