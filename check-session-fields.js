require('dotenv').config();
const getConnection = require('./platform/salesforce/common/getConnection');

async function checkSessionFields(sessionId) {
  try {
    console.log(`Checking session fields for session ID: ${sessionId}`);
    
    const conn = await getConnection();
    if (!conn || !conn.accessToken) {
      console.error("Invalid Salesforce connection");
      return;
    }

    // First, let's get the session record with all available fields
    const sessionRecord = await conn.sobject("Docebo_Session__c")
      .findOne({ Session_External_ID__c: sessionId.toString() });
      
    if (!sessionRecord) {
      console.error(`Session not found with External ID: ${sessionId}`);
      return;
    }

    console.log(`\nSession found: ${sessionRecord.Id}`);
    console.log(`Session Name: ${sessionRecord.Session_Name__c || 'N/A'}`);
    
    // Check for instructor-related fields
    console.log('\n=== Instructor-related fields ===');
    console.log(`Instructor_Feedback__c: ${sessionRecord.Instructor_Feedback__c || 'N/A'}`);
    console.log(`Instructor__c: ${sessionRecord.Instructor__c || 'N/A'}`);
    
    // Let's also try to describe the object to see all available fields
    console.log('\n=== Describing Docebo_Session__c object ===');
    const description = await conn.sobject("Docebo_Session__c").describe();
    
    // Filter for instructor-related fields
    const instructorFields = description.fields.filter(field => 
      field.name.toLowerCase().includes('instructor')
    );
    
    if (instructorFields.length > 0) {
      console.log('\nFound instructor-related fields:');
      instructorFields.forEach(field => {
        console.log(`- ${field.name} (${field.type}) - ${field.label}`);
      });
    } else {
      console.log('\nNo instructor-related fields found in object description');
    }
    
    // Let's also check for any lookup fields that might be the instructor lookup
    const lookupFields = description.fields.filter(field => 
      field.type === 'reference'
    );
    
    if (lookupFields.length > 0) {
      console.log('\nFound lookup (reference) fields:');
      lookupFields.forEach(field => {
        console.log(`- ${field.name} (${field.type}) - ${field.label} -> ${field.referenceTo?.join(', ') || 'Unknown'}`);
      });
    }

  } catch (error) {
    console.error('Error checking session fields:', error);
  }
}

// Use the session ID from our test
const sessionId = 247;

checkSessionFields(sessionId)
  .then(() => console.log('\nDone'))
  .catch(err => console.error('Script failed:', err));
