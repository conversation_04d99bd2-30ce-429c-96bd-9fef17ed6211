require('dotenv').config();
const { updateHistoricalData } = require('./platform/salesforce/users/historicalDataUpdate');

async function runHistoricalDataUpdate() {
    try {
        console.log('🚀 STARTING HISTORICAL DATA UPDATE');
        console.log('=' .repeat(60));
        console.log('This process will:');
        console.log('1. Fetch ALL users from Docebo API');
        console.log('2. For each user, check if they exist in Salesforce');
        console.log('3. Update existing Contacts/Leads with new comprehensive fields');
        console.log('4. Create new Leads for users that don\'t exist');
        console.log('5. Update/Create Docebo_Users__c records for all users');
        console.log('=' .repeat(60));
        
        // Confirm before proceeding
        console.log('\n⚠️  WARNING: This will process ALL Docebo users and update Salesforce records.');
        console.log('Make sure you want to proceed with the historical data update.');
        
        // For safety, add a delay to allow cancellation
        console.log('\nStarting in 10 seconds... Press Ctrl+C to cancel.');
        await new Promise(resolve => setTimeout(resolve, 10000));
        
        console.log('\n🔄 Starting historical data update process...');
        
        const startTime = Date.now();
        const success = await updateHistoricalData();
        const endTime = Date.now();
        const duration = Math.round((endTime - startTime) / 1000);
        
        if (success) {
            console.log('\n🎉 HISTORICAL DATA UPDATE COMPLETED SUCCESSFULLY!');
            console.log(`⏱️  Total time: ${duration} seconds`);
            console.log('\n✅ All Docebo users have been processed:');
            console.log('   • Existing Contacts updated with comprehensive fields');
            console.log('   • Existing Leads updated with comprehensive fields');
            console.log('   • New Leads created for users not in Salesforce');
            console.log('   • All Docebo_Users__c records updated/created');
            console.log('\n🎯 Your Salesforce data is now synchronized with Docebo!');
        } else {
            console.log('\n❌ HISTORICAL DATA UPDATE FAILED');
            console.log('Check the logs above for error details.');
        }
        
    } catch (error) {
        console.error('💥 Error running historical data update:', error);
    }
}

// Handle graceful shutdown
process.on('SIGINT', () => {
    console.log('\n\n🛑 Historical data update cancelled by user.');
    process.exit(0);
});

// Run the update
console.log('🔄 Initializing historical data update...');
runHistoricalDataUpdate()
    .then(() => {
        console.log('\n✅ Historical data update process completed.');
        process.exit(0);
    })
    .catch(err => {
        console.error('💥 Historical data update process failed:', err);
        process.exit(1);
    });
