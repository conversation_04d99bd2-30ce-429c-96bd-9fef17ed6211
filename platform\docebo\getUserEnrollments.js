require('dotenv').config();
const getApiData = require("../../common/docebo/fetcher");
const doceboService = require("./services");
const APP_BASE = process.env.DOCEBO_API_BASE_URL;

// Function to get all course enrollments for a specific user
async function getUserCourseEnrollments(userId) {
    try {
        console.log(`📚 Fetching all course enrollments for User ID: ${userId}`);
        
        let page = 1;
        const pageSize = 200;
        let allEnrollments = [];
        let hasMoreData = true;

        while (hasMoreData) {
            try {
                // Use the enrollments API endpoint to get user's enrollments
                const response = await getApiData(
                    'GET',
                    `${APP_BASE}/learn/v1/enrollments?user_id=${userId}&page=${page}&page_size=${pageSize}`,
                    null
                );

                if (!response || response.status !== 200) {
                    console.error(`❌ Failed to fetch enrollments for user ${userId} on page ${page}`);
                    break;
                }

                const enrollments = response.data?.items || [];
                
                if (enrollments.length === 0) {
                    hasMoreData = false;
                    break;
                }

                console.log(`   📋 Page ${page}: Found ${enrollments.length} enrollments`);
                allEnrollments.push(...enrollments);

                // Check if there's more data
                hasMoreData = response.data?.has_more_data || enrollments.length === pageSize;
                page++;

            } catch (pageError) {
                console.error(`❌ Error fetching page ${page} for user ${userId}:`, pageError.message);
                break;
            }
        }

        console.log(`✅ Total enrollments found for user ${userId}: ${allEnrollments.length}`);
        return allEnrollments;

    } catch (error) {
        console.error(`💥 Error fetching enrollments for user ${userId}:`, error);
        return [];
    }
}

// Function to get detailed enrollment information with course data
async function getUserEnrollmentsWithCourseDetails(userId) {
    try {
        console.log(`📚 Fetching detailed enrollments with course info for User ID: ${userId}`);
        
        // Get basic enrollments
        const enrollments = await getUserCourseEnrollments(userId);
        
        if (enrollments.length === 0) {
            console.log(`⚠️ No enrollments found for user ${userId}`);
            return [];
        }

        const detailedEnrollments = [];

        // Get detailed info for each enrollment
        for (let i = 0; i < enrollments.length; i++) {
            const enrollment = enrollments[i];
            console.log(`   📋 Processing enrollment ${i + 1}/${enrollments.length}: Course ${enrollment.course_id}`);

            try {
                // Get detailed enrollment info
                const enrollmentDetails = await doceboService.getEnrolledInfo(enrollment.course_id, userId);
                
                // Get course information
                const courseInfo = await doceboService.getCourseInfo(enrollment.course_id);

                const detailedEnrollment = {
                    // Basic enrollment data
                    enrollment_id: enrollment.enrollment_id || enrollment.id,
                    course_id: enrollment.course_id,
                    user_id: userId,
                    status: enrollment.status,
                    enrollment_date: enrollment.enrollment_date || enrollment.date_inscr,
                    completion_date: enrollment.completion_date || enrollment.date_complete,
                    score: enrollment.score || enrollment.final_score,
                    progress: enrollment.progress || enrollment.completion_percentage,
                    
                    // Detailed enrollment data
                    enrollmentDetails: enrollmentDetails?.data || null,
                    
                    // Course information
                    courseInfo: courseInfo?.data || null,
                    course_name: courseInfo?.data?.name || 'Unknown Course',
                    course_type: courseInfo?.data?.type || 'Unknown',
                    course_status: courseInfo?.data?.status || 'Unknown'
                };

                detailedEnrollments.push(detailedEnrollment);

            } catch (detailError) {
                console.error(`❌ Error getting details for course ${enrollment.course_id}:`, detailError.message);
                
                // Add basic enrollment even if details fail
                detailedEnrollments.push({
                    enrollment_id: enrollment.enrollment_id || enrollment.id,
                    course_id: enrollment.course_id,
                    user_id: userId,
                    status: enrollment.status,
                    enrollment_date: enrollment.enrollment_date,
                    completion_date: enrollment.completion_date,
                    score: enrollment.score,
                    progress: enrollment.progress,
                    enrollmentDetails: null,
                    courseInfo: null,
                    course_name: 'Failed to fetch',
                    course_type: 'Unknown',
                    course_status: 'Unknown',
                    error: detailError.message
                });
            }

            // Add small delay to avoid rate limiting
            await new Promise(resolve => setTimeout(resolve, 100));
        }

        console.log(`✅ Processed ${detailedEnrollments.length} detailed enrollments for user ${userId}`);
        return detailedEnrollments;

    } catch (error) {
        console.error(`💥 Error fetching detailed enrollments for user ${userId}:`, error);
        return [];
    }
}

// Function to get user enrollments summary
async function getUserEnrollmentsSummary(userId) {
    try {
        console.log(`📊 Generating enrollment summary for User ID: ${userId}`);
        
        const enrollments = await getUserCourseEnrollments(userId);
        
        if (enrollments.length === 0) {
            return {
                user_id: userId,
                total_enrollments: 0,
                completed: 0,
                in_progress: 0,
                not_started: 0,
                enrollments: []
            };
        }

        // Analyze enrollment statuses
        const statusCounts = {
            completed: 0,
            in_progress: 0,
            not_started: 0,
            other: 0
        };

        enrollments.forEach(enrollment => {
            const status = (enrollment.status || '').toLowerCase();
            
            if (status.includes('completed') || status.includes('complete')) {
                statusCounts.completed++;
            } else if (status.includes('progress') || status.includes('started')) {
                statusCounts.in_progress++;
            } else if (status.includes('enrolled') || status.includes('not_started')) {
                statusCounts.not_started++;
            } else {
                statusCounts.other++;
            }
        });

        return {
            user_id: userId,
            total_enrollments: enrollments.length,
            completed: statusCounts.completed,
            in_progress: statusCounts.in_progress,
            not_started: statusCounts.not_started,
            other: statusCounts.other,
            enrollments: enrollments
        };

    } catch (error) {
        console.error(`💥 Error generating enrollment summary for user ${userId}:`, error);
        return {
            user_id: userId,
            total_enrollments: 0,
            completed: 0,
            in_progress: 0,
            not_started: 0,
            error: error.message
        };
    }
}

module.exports = {
    getUserCourseEnrollments,
    getUserEnrollmentsWithCourseDetails,
    getUserEnrollmentsSummary
};
