const getConnection = require("../common/getConnection");

// Helper function to ensure all Docebo_Users__c fields are properly populated
async function ensureDoceboUserFieldsComplete(doceboUserId, userInfo, userListedInfo, leadId = null, contactId = null) {
    try {
        console.log(`🔧 Ensuring complete field mapping for Docebo_Users__c: ${doceboUserId}`);
        
        const conn = await getConnection();
        if (!conn || !conn.accessToken) {
            console.error("❌ Invalid Salesforce connection");
            return false;
        }

        // Prepare the update data with all missing fields
        const updateData = {
            Id: doceboUserId
        };

        // Add missing date fields
        if (userInfo.fired_at) {
            updateData.User_Creation_Date__c = new Date(userInfo.fired_at.replace(' ', 'T')).toISOString();
        }
        
        if (userInfo.expiration_date) {
            updateData.User_Expiration_Date__c = new Date(userInfo.expiration_date.replace(' ', 'T')).toISOString();
        }
        
        if (userListedInfo && userListedInfo.last_access_date) {
            updateData.User_Last_Access_Date__c = new Date(userListedInfo.last_access_date).toISOString();
        }

        // Add suspension date if available (this might come from user status)
        if (userInfo.user_data && userInfo.user_data.valid === '0') {
            // If user is not valid, set suspension date to now
            updateData.User_Suspension_Date__c = new Date().toISOString();
        }

        // Add Lead/Contact association
        if (leadId) {
            updateData.Lead__c = leadId;
            console.log(`   🔗 Linking to Lead: ${leadId}`);
        }
        
        if (contactId) {
            updateData.Contact__c = contactId;
            console.log(`   🔗 Linking to Contact: ${contactId}`);
        }

        // Update the Docebo_Users__c record
        const result = await conn.sobject("Docebo_Users__c").update(updateData);
        
        if (result.success) {
            console.log(`✅ Successfully updated Docebo_Users__c with complete fields`);
            return true;
        } else {
            console.error(`❌ Failed to update Docebo_Users__c:`, result.errors);
            return false;
        }

    } catch (error) {
        console.error('💥 Error ensuring complete Docebo user fields:', error);
        return false;
    }
}

// Enhanced function to create Docebo_Users__c with all required fields
async function createCompleteDoceboUser(tmpUserInfo, leadId = null, contactId = null) {
    try {
        console.log(`🆕 Creating complete Docebo_Users__c record`);
        
        const conn = await getConnection();
        if (!conn || !conn.accessToken) {
            console.error("❌ Invalid Salesforce connection");
            return null;
        }

        // Ensure all required fields are present
        const completeUserData = {
            ...tmpUserInfo,
            
            // Ensure date fields are properly formatted
            User_Creation_Date__c: tmpUserInfo.User_Creation_Date__c || "",
            User_Expiration_Date__c: tmpUserInfo.User_Expiration_Date__c || "",
            User_Last_Access_Date__c: tmpUserInfo.User_Last_Access_Date__c || "",
            User_Suspension_Date__c: tmpUserInfo.User_Suspension_Date__c || "",
            
            // Add Lead/Contact associations
            Lead__c: leadId || null,
            Contact__c: contactId || null
        };

        // Remove the Id field if it exists (for creation)
        delete completeUserData.Id;

        console.log(`   📋 Creating with fields:`);
        console.log(`      User_Creation_Date__c: ${completeUserData.User_Creation_Date__c}`);
        console.log(`      User_Expiration_Date__c: ${completeUserData.User_Expiration_Date__c}`);
        console.log(`      User_Last_Access_Date__c: ${completeUserData.User_Last_Access_Date__c}`);
        console.log(`      User_Suspension_Date__c: ${completeUserData.User_Suspension_Date__c}`);
        console.log(`      Lead__c: ${completeUserData.Lead__c || 'N/A'}`);
        console.log(`      Contact__c: ${completeUserData.Contact__c || 'N/A'}`);

        const result = await conn.sobject("Docebo_Users__c").create(completeUserData);
        
        if (result.success) {
            console.log(`✅ Successfully created complete Docebo_Users__c: ${result.id}`);
            return result.id;
        } else {
            console.error(`❌ Failed to create Docebo_Users__c:`, result.errors);
            return null;
        }

    } catch (error) {
        console.error('💥 Error creating complete Docebo user:', error);
        return null;
    }
}

// Function to fix existing Docebo_Users__c records that are missing fields
async function fixExistingDoceboUsers() {
    try {
        console.log('🔧 Fixing existing Docebo_Users__c records with missing fields...');
        
        const conn = await getConnection();
        if (!conn || !conn.accessToken) {
            console.error("❌ Invalid Salesforce connection");
            return false;
        }

        // Find Docebo_Users__c records missing date fields or associations
        const incompleteUsers = await conn.sobject("Docebo_Users__c")
            .find({
                $or: [
                    { User_Creation_Date__c: null },
                    { User_Expiration_Date__c: null },
                    { User_Last_Access_Date__c: null },
                    { Lead__c: null, Contact__c: null }
                ]
            })
            .limit(100) // Process in batches
            .execute();

        console.log(`📋 Found ${incompleteUsers.length} incomplete Docebo_Users__c records`);

        let fixedCount = 0;
        
        for (const user of incompleteUsers) {
            try {
                // Try to find associated Lead or Contact by email
                let leadId = null;
                let contactId = null;

                if (user.Email__c) {
                    // Check for Contact first
                    const contacts = await conn.sobject("Contact")
                        .find({ Email: user.Email__c })
                        .execute();
                    
                    if (contacts.length > 0) {
                        contactId = contacts[0].Id;
                    } else {
                        // Check for Lead
                        const leads = await conn.sobject("Lead")
                            .find({ Email: user.Email__c })
                            .execute();
                        
                        if (leads.length > 0) {
                            leadId = leads[0].Id;
                        }
                    }
                }

                // Update with missing associations
                const updateData = { Id: user.Id };
                
                if (leadId && !user.Lead__c) {
                    updateData.Lead__c = leadId;
                }
                
                if (contactId && !user.Contact__c) {
                    updateData.Contact__c = contactId;
                }

                if (Object.keys(updateData).length > 1) { // More than just Id
                    const result = await conn.sobject("Docebo_Users__c").update(updateData);
                    if (result.success) {
                        fixedCount++;
                        console.log(`✅ Fixed Docebo_Users__c: ${user.Id} (${user.Email__c})`);
                    }
                }

            } catch (userError) {
                console.error(`❌ Error fixing user ${user.Id}:`, userError.message);
            }
        }

        console.log(`🎯 Fixed ${fixedCount} out of ${incompleteUsers.length} incomplete records`);
        return true;

    } catch (error) {
        console.error('💥 Error fixing existing Docebo users:', error);
        return false;
    }
}

module.exports = {
    ensureDoceboUserFieldsComplete,
    createCompleteDoceboUser,
    fixExistingDoceboUsers
};
