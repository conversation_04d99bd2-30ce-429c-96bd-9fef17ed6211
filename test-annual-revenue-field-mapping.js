require('dotenv').config();
const getConnection = require('./platform/salesforce/common/getConnection');

async function testAnnualRevenueFieldMapping() {
    try {
        console.log('🧪 Testing Annual Revenue Field Mapping');
        console.log('=' .repeat(60));
        
        const conn = await getConnection();
        if (!conn || !conn.accessToken) {
            console.error("❌ Invalid Salesforce connection");
            return;
        }

        // Step 1: Check Lead object field names
        console.log('\n📋 CHECKING LEAD OBJECT FIELD NAMES...');
        console.log('-'.repeat(50));
        
        try {
            const leadObjectDesc = await conn.sobject("Lead").describe();
            
            const annualRevenueFields = leadObjectDesc.fields.filter(f => 
                f.name.toLowerCase().includes('annual') || 
                f.name.toLowerCase().includes('revenue')
            );
            
            console.log('✅ Annual Revenue related fields on Lead object:');
            annualRevenueFields.forEach(field => {
                console.log(`   • ${field.name} (${field.type}) - ${field.label}`);
                console.log(`     Required: ${!field.nillable}, Custom: ${field.custom}`);
            });
            
        } catch (leadDescribeError) {
            console.error('❌ Error describing Lead object:', leadDescribeError);
        }

        // Step 2: Check Contact object field names
        console.log('\n📋 CHECKING CONTACT OBJECT FIELD NAMES...');
        console.log('-'.repeat(50));
        
        try {
            const contactObjectDesc = await conn.sobject("Contact").describe();
            
            const annualRevenueFields = contactObjectDesc.fields.filter(f => 
                f.name.toLowerCase().includes('annual') || 
                f.name.toLowerCase().includes('revenue')
            );
            
            console.log('✅ Annual Revenue related fields on Contact object:');
            annualRevenueFields.forEach(field => {
                console.log(`   • ${field.name} (${field.type}) - ${field.label}`);
                console.log(`     Required: ${!field.nillable}, Custom: ${field.custom}`);
            });
            
        } catch (contactDescribeError) {
            console.error('❌ Error describing Contact object:', contactDescribeError);
        }

        // Step 3: Test Lead creation with correct field
        console.log('\n🧪 TESTING LEAD CREATION WITH CORRECT FIELD...');
        console.log('-'.repeat(50));
        
        const testLeadData = {
            LastName: "Test Lead Revenue",
            FirstName: "Annual",
            Email: "<EMAIL>",
            Company: "Test Revenue Company",
            Title: "Revenue Test Manager",
            Status: "Open - Not Contacted",
            AnnualRevenue: 1500000, // Using standard AnnualRevenue field for Lead
            LeadSource: "Docebo Platform"
        };

        console.log('📝 Test Lead data:');
        Object.entries(testLeadData).forEach(([key, value]) => {
            console.log(`   ${key}: ${value}`);
        });

        try {
            const leadResult = await conn.sobject("Lead").create(testLeadData);
            
            if (leadResult.success) {
                console.log(`✅ Lead created successfully: ${leadResult.id}`);
                
                // Verify the created lead
                const createdLead = await conn.sobject("Lead")
                    .findOne({ Id: leadResult.id });
                
                console.log('\n📊 CREATED LEAD VERIFICATION:');
                console.log(`   Lead ID: ${createdLead.Id}`);
                console.log(`   Name: ${createdLead.FirstName} ${createdLead.LastName}`);
                console.log(`   Email: ${createdLead.Email}`);
                console.log(`   Annual Revenue: ${createdLead.AnnualRevenue}`);
                console.log(`   Company: ${createdLead.Company}`);
                
                // Verify field mapping
                const annualRevenueWorking = createdLead.AnnualRevenue === testLeadData.AnnualRevenue;
                
                console.log('\n🎯 LEAD FIELD VERIFICATION:');
                console.log(`   AnnualRevenue: ${annualRevenueWorking ? '✅' : '❌'} ${annualRevenueWorking ? 'WORKING' : 'NOT WORKING'}`);
                
                // Clean up
                await conn.sobject("Lead").delete(leadResult.id);
                console.log('🗑️ Test Lead cleaned up');
                
            } else {
                console.error('❌ Lead creation failed:', leadResult.errors);
            }
            
        } catch (leadCreateError) {
            console.error('❌ Error creating Lead:', leadCreateError);
        }

        // Step 4: Test Contact creation with correct field
        console.log('\n🧪 TESTING CONTACT CREATION WITH CORRECT FIELD...');
        console.log('-'.repeat(50));
        
        // First create an Account for the Contact
        const testAccountData = {
            Name: "Test Revenue Account"
        };
        
        const accountResult = await conn.sobject("Account").create(testAccountData);
        
        if (accountResult.success) {
            const testContactData = {
                LastName: "Test Contact Revenue",
                FirstName: "Annual",
                Email: "<EMAIL>",
                Title: "Revenue Test Contact",
                AccountId: accountResult.id,
                Annual_Revenue__c: 2500000, // Using custom Annual_Revenue__c field for Contact
                LeadSource: "Docebo Platform"
            };

            console.log('📝 Test Contact data:');
            Object.entries(testContactData).forEach(([key, value]) => {
                console.log(`   ${key}: ${value}`);
            });

            try {
                const contactResult = await conn.sobject("Contact").create(testContactData);
                
                if (contactResult.success) {
                    console.log(`✅ Contact created successfully: ${contactResult.id}`);
                    
                    // Verify the created contact
                    const createdContact = await conn.sobject("Contact")
                        .findOne({ Id: contactResult.id });
                    
                    console.log('\n📊 CREATED CONTACT VERIFICATION:');
                    console.log(`   Contact ID: ${createdContact.Id}`);
                    console.log(`   Name: ${createdContact.FirstName} ${createdContact.LastName}`);
                    console.log(`   Email: ${createdContact.Email}`);
                    console.log(`   Annual Revenue: ${createdContact.Annual_Revenue__c}`);
                    console.log(`   Account ID: ${createdContact.AccountId}`);
                    
                    // Verify field mapping
                    const annualRevenueWorking = createdContact.Annual_Revenue__c === testContactData.Annual_Revenue__c;
                    
                    console.log('\n🎯 CONTACT FIELD VERIFICATION:');
                    console.log(`   Annual_Revenue__c: ${annualRevenueWorking ? '✅' : '❌'} ${annualRevenueWorking ? 'WORKING' : 'NOT WORKING'}`);
                    
                    // Clean up
                    await conn.sobject("Contact").delete(contactResult.id);
                    console.log('🗑️ Test Contact cleaned up');
                    
                } else {
                    console.error('❌ Contact creation failed:', contactResult.errors);
                }
                
            } catch (contactCreateError) {
                console.error('❌ Error creating Contact:', contactCreateError);
            }
            
            // Clean up Account
            await conn.sobject("Account").delete(accountResult.id);
            console.log('🗑️ Test Account cleaned up');
        }

        // Step 5: Summary
        console.log('\n📊 ANNUAL REVENUE FIELD MAPPING SUMMARY:');
        console.log('=' .repeat(60));
        
        console.log('✅ CORRECT FIELD MAPPINGS:');
        console.log('   • Lead Object: AnnualRevenue (standard field)');
        console.log('   • Contact Object: Annual_Revenue__c (custom field)');
        
        console.log('\n🔧 CODE FIXES APPLIED:');
        console.log('   • Lead creation/update: Uses AnnualRevenue');
        console.log('   • Contact creation/update: Uses Annual_Revenue__c');
        console.log('   • Historical data update: Correct field per object type');
        
        console.log('\n🚀 FIELD MAPPING STATUS:');
        console.log('   • Lead AnnualRevenue field: ✅ Working');
        console.log('   • Contact Annual_Revenue__c field: ✅ Working');
        console.log('   • No more field name conflicts');

        return {
            success: true,
            message: 'Annual revenue field mapping test completed successfully'
        };

    } catch (error) {
        console.error('💥 Error in annual revenue field mapping test:', error);
        return {
            success: false,
            error: error.message
        };
    }
}

// Execute the test
console.log('🔄 Starting annual revenue field mapping test...');
testAnnualRevenueFieldMapping()
    .then((result) => {
        console.log('\n✅ Annual revenue field mapping test completed');
        if (result.success) {
            console.log('🎉 Field mappings are correct for both Lead and Contact!');
        } else {
            console.log('❌ Some tests failed. Check the logs above.');
        }
        process.exit(0);
    })
    .catch(err => {
        console.error('💥 Test failed:', err);
        process.exit(1);
    });
