require('dotenv').config();
const getConnection = require('./platform/salesforce/common/getConnection');

async function testInstructorSessionAssociation() {
    try {
        console.log('🔍 Testing Instructor-Session Association');
        console.log('=' .repeat(60));
        
        const conn = await getConnection();
        if (!conn || !conn.accessToken) {
            console.error("❌ Invalid Salesforce connection");
            return;
        }

        // Step 1: Check existing sessions and their instructor associations
        console.log('\n📊 Checking existing sessions and instructor associations...');
        console.log('-'.repeat(50));
        
        const sessions = await conn.sobject("Docebo_Session__c")
            .find({})
            .limit(20)
            .execute();
        
        console.log(`📋 Found ${sessions.length} session records`);
        
        let withInstructor = 0;
        let withoutInstructor = 0;
        let recentSessions = [];
        
        sessions.forEach(session => {
            if (session.Instructor__c) {
                withInstructor++;
            } else {
                withoutInstructor++;
            }
            
            // Collect recent sessions (last 7 days)
            const sessionDate = new Date(session.CreatedDate);
            const weekAgo = new Date();
            weekAgo.setDate(weekAgo.getDate() - 7);
            
            if (sessionDate > weekAgo) {
                recentSessions.push(session);
            }
        });
        
        console.log(`✅ With Instructor Association: ${withInstructor}/${sessions.length} (${Math.round(withInstructor/sessions.length*100)}%)`);
        console.log(`❌ Without Instructor Association: ${withoutInstructor}/${sessions.length} (${Math.round(withoutInstructor/sessions.length*100)}%)`);
        console.log(`🕐 Recent Sessions (7 days): ${recentSessions.length}`);

        // Step 2: Check recent sessions in detail
        if (recentSessions.length > 0) {
            console.log(`\n🔍 RECENT SESSION ANALYSIS:`);
            console.log('-'.repeat(50));
            
            for (let i = 0; i < Math.min(5, recentSessions.length); i++) {
                const session = recentSessions[i];
                console.log(`\n📋 Session ${i + 1}: ${session.Id}`);
                console.log(`   Session_External_ID__c: ${session.Session_External_ID__c}`);
                console.log(`   Session_Name__c: ${session.Session_Name__c || 'N/A'}`);
                console.log(`   Instructor__c: ${session.Instructor__c || 'MISSING ❌'}`);
                console.log(`   Created Date: ${session.CreatedDate}`);
                
                // If there's an instructor, get instructor details
                if (session.Instructor__c) {
                    try {
                        const instructor = await conn.sobject("Instructor__c")
                            .findOne({ Id: session.Instructor__c });
                        
                        if (instructor) {
                            console.log(`   ✅ Instructor Details:`);
                            console.log(`      Name: ${instructor.First_Name__c} ${instructor.Last_Name__c}`);
                            console.log(`      Email: ${instructor.Email__c || 'N/A'}`);
                            console.log(`      User ID: ${instructor.Name}`);
                        }
                    } catch (instructorError) {
                        console.log(`   ❌ Error getting instructor details: ${instructorError.message}`);
                    }
                }
            }
        }

        // Step 3: Check instructor records
        console.log(`\n🔍 INSTRUCTOR RECORDS ANALYSIS:`);
        console.log('-'.repeat(50));
        
        const instructors = await conn.sobject("Instructor__c")
            .find({})
            .limit(10)
            .execute();
        
        console.log(`📋 Found ${instructors.length} instructor records:`);
        
        instructors.forEach((instructor, index) => {
            console.log(`\n   Instructor ${index + 1}: ${instructor.Id}`);
            console.log(`   Name (User ID): ${instructor.Name}`);
            console.log(`   Full Name: ${instructor.First_Name__c} ${instructor.Last_Name__c}`);
            console.log(`   Email: ${instructor.Email__c || 'N/A'}`);
        });

        // Step 4: Check if instructors are linked to sessions
        console.log(`\n🔗 INSTRUCTOR-SESSION LINKAGE ANALYSIS:`);
        console.log('-'.repeat(50));
        
        for (const instructor of instructors.slice(0, 5)) {
            const linkedSessions = await conn.sobject("Docebo_Session__c")
                .find({ Instructor__c: instructor.Id })
                .execute();
            
            console.log(`\n👨‍🏫 Instructor: ${instructor.First_Name__c} ${instructor.Last_Name__c} (${instructor.Id})`);
            console.log(`   Linked to ${linkedSessions.length} session(s)`);
            
            if (linkedSessions.length > 0) {
                linkedSessions.forEach((session, index) => {
                    console.log(`   ${index + 1}. Session: ${session.Session_Name__c || session.Id}`);
                });
            }
        }

        // Step 5: Test the webhook data structure
        console.log(`\n🧪 TESTING WEBHOOK DATA STRUCTURE:`);
        console.log('-'.repeat(50));
        
        // Simulate what the webhook might receive
        const mockWebhookPayload = {
            event: "ilt.session.created",
            payload: {
                session_id: 12345,
                course_id: 67890,
                instructor_id: 98765, // This is the key field we need to check
                session_name: "Test Session",
                start_date: "2025-01-15 10:00:00",
                end_date: "2025-01-15 12:00:00"
            }
        };
        
        console.log(`📋 Mock webhook payload structure:`);
        console.log(JSON.stringify(mockWebhookPayload, null, 2));
        
        console.log(`\n🔍 Key Questions to Verify:`);
        console.log(`1. Does the webhook payload include 'instructor_id'?`);
        console.log(`2. Is the instructor_id field populated when sessions have instructors?`);
        console.log(`3. Does the createSession function receive this instructor_id?`);
        console.log(`4. Is the getInstructorData API call working correctly?`);

        // Step 6: Check Docebo API response structure
        console.log(`\n📡 DOCEBO API STRUCTURE CHECK:`);
        console.log('-'.repeat(50));
        
        // Check if we can find any session with external ID to test API
        const sessionWithExternalId = sessions.find(s => s.Session_External_ID__c);
        
        if (sessionWithExternalId) {
            console.log(`✅ Found session with External ID: ${sessionWithExternalId.Session_External_ID__c}`);
            console.log(`   Session ID: ${sessionWithExternalId.Id}`);
            console.log(`   Has Instructor: ${sessionWithExternalId.Instructor__c ? 'Yes' : 'No'}`);
            
            // This would be where we test the Docebo API call, but we'll just log what should happen
            console.log(`\n📋 To verify instructor association, check:`);
            console.log(`1. Docebo API: /learn/v1/course/session/${sessionWithExternalId.Session_External_ID__c}`);
            console.log(`2. Look for instructor_id or instructors array in response`);
            console.log(`3. Verify getInstructorData API call works with that instructor_id`);
        } else {
            console.log(`❌ No sessions found with External ID to test API calls`);
        }

        // Step 7: Recommendations
        console.log(`\n💡 RECOMMENDATIONS:`);
        console.log('=' .repeat(50));
        
        if (withoutInstructor > withInstructor) {
            console.log(`🔧 ISSUE DETECTED: ${withoutInstructor}/${sessions.length} sessions missing instructor associations`);
            console.log(`\n📋 Possible causes:`);
            console.log(`1. ❌ Webhook payload doesn't include instructor_id`);
            console.log(`2. ❌ instructor_id field is null/empty in Docebo`);
            console.log(`3. ❌ getInstructorData API call is failing`);
            console.log(`4. ❌ Session lookup by Session_External_ID__c is failing`);
            console.log(`5. ❌ Instructor creation/linking logic has bugs`);
            
            console.log(`\n🔧 Next steps:`);
            console.log(`1. 📊 Add logging to session webhook to see actual payload`);
            console.log(`2. 🧪 Test getInstructorData API call manually`);
            console.log(`3. 🔍 Check if instructor_id is included in session creation webhooks`);
            console.log(`4. 🛠️ Add error handling and logging to instructor creation process`);
        } else {
            console.log(`✅ Most sessions have instructor associations - system appears to be working`);
            console.log(`📊 Monitor recent sessions to ensure new ones get instructors assigned`);
        }

    } catch (error) {
        console.error('💥 Error in test:', error);
    }
}

// Execute the test
console.log('🔄 Starting instructor-session association test...');
testInstructorSessionAssociation()
    .then(() => {
        console.log('\n✅ Test completed');
        process.exit(0);
    })
    .catch(err => {
        console.error('💥 Test failed:', err);
        process.exit(1);
    });
