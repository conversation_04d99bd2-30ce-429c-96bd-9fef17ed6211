require('dotenv').config();
const getConnection = require('./platform/salesforce/common/getConnection');

async function investigateWelcomeCourseEnrollment() {
    try {
        console.log('🔍 INVESTIGATING "Welcome to the StriveTogether Network" COURSE ENROLLMENT');
        console.log('=' .repeat(80));
        
        const conn = await getConnection();
        if (!conn || !conn.accessToken) {
            throw new Error('Failed to connect to Salesforce');
        }

        console.log('✅ Connected to Salesforce successfully');

        // Step 1: Check if the "Welcome to the StriveTogether Network" course exists
        console.log('\n📚 STEP 1: Checking Course Existence...');
        console.log('-'.repeat(50));
        
        const welcomeCourse = await conn.sobject("Docebo_Course__c")
            .findOne({ Course_External_Id__c: 72 });
            
        if (!welcomeCourse) {
            console.log('❌ "Welcome to the StriveTogether Network" course (ID: 72) NOT FOUND in Salesforce');
            console.log('   This could be why enrollments are failing');
            
            // Let's search for similar course names
            console.log('\n🔍 Searching for similar course names...');
            const similarCourses = await conn.sobject("Docebo_Course__c")
                .find({ Course_Name__c: { $like: '%Welcome%StriveTogether%' } })
                .limit(5)
                .execute();
                
            if (similarCourses.length > 0) {
                console.log('Found similar courses:');
                similarCourses.forEach(course => {
                    console.log(`  - ID: ${course.Id}, External ID: ${course.Course_External_Id__c}, Name: ${course.Course_Name__c}`);
                });
            } else {
                console.log('No similar courses found');
            }
        } else {
            console.log('✅ "Welcome to the StriveTogether Network" course FOUND in Salesforce');
            console.log(`   Course ID: ${welcomeCourse.Id}`);
            console.log(`   Course Name: ${welcomeCourse.Course_Name__c}`);
            console.log(`   External ID: ${welcomeCourse.Course_External_Id__c}`);
        }

        // Step 2: Check recent enrollments for this course
        console.log('\n📊 STEP 2: Checking Recent Enrollments...');
        console.log('-'.repeat(50));
        
        if (welcomeCourse) {
            const recentEnrollments = await conn.sobject("Docebo_CourseEnrollment__c")
                .find({ Course__c: welcomeCourse.Id })
                .sort({ CreatedDate: -1 })
                .limit(10)
                .execute();
                
            console.log(`Found ${recentEnrollments.length} enrollments for this course`);
            
            if (recentEnrollments.length > 0) {
                console.log('\nRecent enrollments:');
                recentEnrollments.forEach((enrollment, index) => {
                    console.log(`  ${index + 1}. ID: ${enrollment.Id}, Created: ${enrollment.CreatedDate}, Status: ${enrollment.Status__c || 'N/A'}`);
                });
            }
        }

        // Step 3: Check for failed enrollments in logs (simulate log analysis)
        console.log('\n🔍 STEP 3: Analyzing Enrollment Issues...');
        console.log('-'.repeat(50));
        
        // Check for enrollments with course_id 72 that might have failed
        const allEnrollments = await conn.sobject("Docebo_CourseEnrollment__c")
            .find({ Enrollment_ID__c: { $like: '72-%' } })
            .limit(20)
            .execute();
            
        console.log(`Found ${allEnrollments.length} enrollments with course ID 72 pattern`);
        
        if (allEnrollments.length > 0) {
            console.log('\nEnrollments found:');
            allEnrollments.forEach((enrollment, index) => {
                console.log(`  ${index + 1}. Enrollment ID: ${enrollment.Enrollment_ID__c}, Course: ${enrollment.Course__c ? 'Linked' : 'NOT LINKED'}, User: ${enrollment.Docebo_User__c ? 'Linked' : 'NOT LINKED'}`);
            });
        }

        // Step 4: Check Lead creation issues
        console.log('\n🚨 STEP 4: Investigating Lead Creation Issues...');
        console.log('-'.repeat(50));
        
        // Check recent Docebo_Users__c records without Lead associations
        const usersWithoutLeads = await conn.sobject("Docebo_Users__c")
            .find({ Lead__c: null })
            .sort({ CreatedDate: -1 })
            .limit(10)
            .execute();
            
        console.log(`Found ${usersWithoutLeads.length} Docebo users without Lead associations`);
        
        if (usersWithoutLeads.length > 0) {
            console.log('\nUsers without Lead associations:');
            usersWithoutLeads.forEach((user, index) => {
                console.log(`  ${index + 1}. User ID: ${user.User_Unique_Id__c}, Name: ${user.First_Name__c} ${user.Last_Name__c}, Created: ${user.CreatedDate}`);
            });
        }

        // Step 5: Test Lead creation with sample data
        console.log('\n🧪 STEP 5: Testing Lead Creation...');
        console.log('-'.repeat(50));
        
        if (usersWithoutLeads.length > 0) {
            const testUser = usersWithoutLeads[0];
            console.log(`Testing Lead creation for user: ${testUser.User_Unique_Id__c}`);
            
            const testLeadData = {
                FirstName: testUser.First_Name__c || 'Test',
                LastName: testUser.Last_Name__c || 'User',
                Email: testUser.Email__c || '<EMAIL>',
                Company: testUser.Organization_Name__c || 'Test Company',
                LeadSource: 'Docebo API',
                Created_by_Docebo_API__c: true,
                Status: 'Open - Not Contacted'
            };
            
            try {
                console.log('Attempting to create test Lead...');
                const leadResult = await conn.sobject("Lead").create(testLeadData);
                
                if (leadResult.success) {
                    console.log('✅ Test Lead created successfully!');
                    console.log(`   Lead ID: ${leadResult.id}`);
                    
                    // Clean up test lead
                    await conn.sobject("Lead").delete(leadResult.id);
                    console.log('🗑️ Test Lead deleted');
                } else {
                    console.log('❌ Test Lead creation failed:');
                    console.log('   Errors:', JSON.stringify(leadResult.errors, null, 2));
                }
            } catch (leadError) {
                console.log('❌ Test Lead creation error:', leadError.message);
                
                if (leadError.message.includes('FIELD_FILTER_VALIDATION_EXCEPTION')) {
                    console.log('\n🚨 FIELD_FILTER_VALIDATION_EXCEPTION CONFIRMED!');
                    console.log('   This is the root cause of the lead creation issues');
                    console.log('   Lookup filters on Lead fields are preventing creation');
                }
            }
        }

        return {
            success: true,
            courseExists: !!welcomeCourse,
            courseId: welcomeCourse?.Id,
            enrollmentCount: welcomeCourse ? (await conn.sobject("Docebo_CourseEnrollment__c").find({ Course__c: welcomeCourse.Id }).execute()).length : 0,
            usersWithoutLeads: usersWithoutLeads.length
        };

    } catch (error) {
        console.error('💥 Error in investigation:', error);
        return {
            success: false,
            error: error.message
        };
    }
}

// Execute the investigation
console.log('🔄 Starting Welcome Course Enrollment Investigation...');
investigateWelcomeCourseEnrollment()
    .then((result) => {
        console.log('\n📋 INVESTIGATION SUMMARY:');
        console.log('=' .repeat(50));
        
        if (result.success) {
            console.log(`✅ Course Exists: ${result.courseExists ? 'YES' : 'NO'}`);
            if (result.courseExists) {
                console.log(`📊 Enrollment Count: ${result.enrollmentCount}`);
            }
            console.log(`👥 Users without Leads: ${result.usersWithoutLeads}`);
            
            console.log('\n🔧 RECOMMENDED ACTIONS:');
            console.log('-'.repeat(30));
            
            if (!result.courseExists) {
                console.log('1. ❗ Create the "Welcome to the StriveTogether Network" course in Salesforce');
                console.log('2. 🔄 Run course sync to import missing courses');
            }
            
            if (result.usersWithoutLeads > 0) {
                console.log('3. 🔍 Investigate Lead lookup filter restrictions');
                console.log('4. 🛠️ Fix FIELD_FILTER_VALIDATION_EXCEPTION issues');
                console.log('5. 🔄 Re-enable Lead creation in user creation process');
            }
            
            console.log('6. 📊 Monitor course enrollment webhooks for this specific course');
            console.log('7. 🧪 Test enrollment creation for course ID 72');
            
        } else {
            console.log(`❌ Investigation failed: ${result.error}`);
        }
        
        console.log('\n✅ Investigation completed');
        process.exit(0);
    })
    .catch(err => {
        console.error('💥 Investigation failed:', err);
        process.exit(1);
    });
