require('dotenv').config();
const { tidyData } = require('./platform/salesforce/users/createUser');

// Function to analyze and fix missing webhook fields
function analyzeMissingWebhookFields() {
    console.log('🔍 Analyzing Missing Webhook Fields...');
    
    // Your complete field list that should be mapped
    const requiredFields = [
        'Company',
        'Created_by_Docebo_API__c',
        'Gender Identity', 
        'Role_Type__c',
        'email',
        'EmploymentType',
        'title',
        'Race__c',
        'FirstName',
        'LastName',
        'website',
        'Description',
        'fax',
        'salutation',
        'phone',
        'languages__c',
        'mailingcity__c',
        'mailingcountry__c',
        'mailingpostalcode__c',
        'mailingstate__c',
        'mailingstreet__c',
        'position_role__c',
        'AnnualRevenue',
        'Industry',
        'LeadSource',
        'NumberOfEmployees',
        'rating',
        'TimeZone'
    ];

    // Mock Docebo webhook data with additional fields that might contain the missing data
    const mockDoceboData = {
        userInfo: {
            user_data: {
                user_id: "12345",
                first_name: "<PERSON>",
                last_name: "<PERSON><PERSON>",
                email: "<EMAIL>",
                username: "john_doe",
                level: "User",
                manager_username: "manager_user",
                email_validation_status: "1",
                valid: "1",
                // These might contain additional data
                timezone: "America/New_York",
                language: "en",
                phone: "******-123-4567"
            },
            additional_fields: [
                { id: "8", value: "Senior Manager", enabled: true }, // Job Title
                { id: "9", value: "Operations/Business Management", enabled: true }, // Role Type
                { id: "10", value: "Full-time", enabled: true }, // Employment Type
                { id: "11", value: "English, Spanish", enabled: true }, // Languages
                { id: "12", value: "White", enabled: true }, // Race
                { id: "13", value: "Male", enabled: true }, // Gender
                { id: "14", value: "Example Organization", enabled: true }, // Organization
                { id: "15", value: "Yes", enabled: true }, // Backbone Partner
                { id: "16", value: "Community Partner", enabled: true }, // Partner Type
                { id: "17", value: "2020-01-15", enabled: true }, // Employment Begin Date
                { id: "18", value: "New York", enabled: true }, // State
                { id: "19", value: "New York", enabled: true }, // City
                { id: "20", value: "Education Initiative", enabled: true }, // Initiative
                { id: "21", value: "Regional", enabled: true }, // National/Regional/Local
                { id: "22", value: "New York, NY", enabled: true }, // Organization HQ
                { id: "23", value: "www.example.org", enabled: true }, // Website
                { id: "24", value: "123 Main St", enabled: true }, // Street Address
                { id: "25", value: "10001", enabled: true }, // Postal Code
                { id: "26", value: "USA", enabled: true }, // Country
                { id: "27", value: "******-987-6543", enabled: true }, // Phone
                { id: "28", value: "******-987-6544", enabled: true }, // Fax
                { id: "29", value: "Dr.", enabled: true }, // Salutation
                { id: "30", value: "Education", enabled: true }, // Industry
                { id: "31", value: "1000000", enabled: true }, // Annual Revenue
                { id: "32", value: "500", enabled: true }, // Number of Employees
                { id: "33", value: "Hot", enabled: true }, // Rating
                { id: "34", value: "Director of Operations", enabled: true } // Position Role
            ],
            branches: [
                {
                    name: "Main Branch",
                    path: "/main/branch",
                    codes: "12345"
                }
            ],
            fired_at: "2024-01-15 09:00:00",
            expiration_date: "2025-12-31 23:59:59"
        },
        userListedInfo: {
            last_access_date: "2024-02-07T14:30:00Z"
        }
    };

    console.log('\n📋 CURRENT FIELD MAPPING ANALYSIS:');
    console.log('=' .repeat(70));

    // Test current tidyData function
    const processedData = tidyData(mockDoceboData.userInfo, mockDoceboData.userListedInfo);

    // Analyze which fields are missing or empty
    const fieldAnalysis = {
        mapped: [],
        missing: [],
        needsImprovement: []
    };

    // Check each required field
    requiredFields.forEach(field => {
        const salesforceField = mapToSalesforceField(field);
        const value = getFieldValue(processedData, salesforceField);
        
        if (value && value !== "" && value !== 0 && value !== null) {
            fieldAnalysis.mapped.push({ field, salesforceField, value });
        } else {
            fieldAnalysis.missing.push({ field, salesforceField });
        }
    });

    console.log(`✅ Mapped Fields: ${fieldAnalysis.mapped.length}/${requiredFields.length}`);
    console.log(`❌ Missing Fields: ${fieldAnalysis.missing.length}/${requiredFields.length}`);

    console.log('\n✅ CURRENTLY MAPPED FIELDS:');
    fieldAnalysis.mapped.forEach(item => {
        console.log(`   ${item.field} → ${item.salesforceField}: ${item.value}`);
    });

    console.log('\n❌ MISSING FIELDS:');
    fieldAnalysis.missing.forEach(item => {
        console.log(`   ${item.field} → ${item.salesforceField}: NOT MAPPED`);
    });

    console.log('\n💡 RECOMMENDED ADDITIONAL FIELD MAPPINGS:');
    console.log('=' .repeat(60));
    
    const recommendations = [
        { field: 'languages__c', source: 'additional_fields[11] or user_data.language', example: 'English, Spanish' },
        { field: 'TimeZone', source: 'user_data.timezone', example: 'America/New_York' },
        { field: 'phone', source: 'additional_fields[27] or user_data.phone', example: '******-123-4567' },
        { field: 'fax', source: 'additional_fields[28]', example: '******-987-6544' },
        { field: 'salutation', source: 'additional_fields[29]', example: 'Dr.' },
        { field: 'mailingstreet__c', source: 'additional_fields[24]', example: '123 Main St' },
        { field: 'mailingcity__c', source: 'additional_fields[19]', example: 'New York' },
        { field: 'mailingstate__c', source: 'additional_fields[18]', example: 'New York' },
        { field: 'mailingpostalcode__c', source: 'additional_fields[25]', example: '10001' },
        { field: 'mailingcountry__c', source: 'additional_fields[26]', example: 'USA' },
        { field: 'website', source: 'additional_fields[23] or organization_url', example: 'www.example.org' },
        { field: 'Industry', source: 'additional_fields[30]', example: 'Education' },
        { field: 'AnnualRevenue', source: 'additional_fields[31]', example: '1000000' },
        { field: 'NumberOfEmployees', source: 'additional_fields[32]', example: '500' },
        { field: 'rating', source: 'additional_fields[33]', example: 'Hot' },
        { field: 'position_role__c', source: 'additional_fields[34]', example: 'Director of Operations' }
    ];

    recommendations.forEach(rec => {
        console.log(`   ${rec.field}: Map from ${rec.source} (e.g., "${rec.example}")`);
    });

    return {
        totalFields: requiredFields.length,
        mappedCount: fieldAnalysis.mapped.length,
        missingCount: fieldAnalysis.missing.length,
        recommendations: recommendations
    };
}

// Helper function to map field names to Salesforce field names
function mapToSalesforceField(fieldName) {
    const mapping = {
        'Company': 'Organization_Name__c',
        'Created_by_Docebo_API__c': 'Created_by_Docebo_API__c',
        'Gender Identity': 'Gender_Identity__c',
        'Role_Type__c': 'Role_Type__c',
        'email': 'Email__c',
        'EmploymentType': 'Employment_Type__c',
        'title': 'Job_Title__c',
        'Race__c': 'Race_Identity__c',
        'FirstName': 'First_Name__c',
        'LastName': 'Last_Name__c',
        'website': 'Organization_URL__c',
        'Description': 'Description',
        'fax': 'Fax',
        'salutation': 'Salutation',
        'phone': 'Phone',
        'languages__c': 'Languages__c',
        'mailingcity__c': 'MailingCity__c',
        'mailingcountry__c': 'MailingCountry__c',
        'mailingpostalcode__c': 'MailingPostalCode__c',
        'mailingstate__c': 'MailingState__c',
        'mailingstreet__c': 'MailingStreet__c',
        'position_role__c': 'Position_Role__c',
        'AnnualRevenue': 'AnnualRevenue',
        'Industry': 'Industry',
        'LeadSource': 'LeadSource',
        'NumberOfEmployees': 'NumberOfEmployees',
        'rating': 'Rating',
        'TimeZone': 'TimeZone'
    };
    
    return mapping[fieldName] || fieldName;
}

// Helper function to get field value from processed data
function getFieldValue(data, fieldName) {
    return data[fieldName];
}

// Execute the analysis
console.log('🔄 Starting missing webhook fields analysis...');
const analysis = analyzeMissingWebhookFields();

console.log('\n📊 ANALYSIS SUMMARY:');
console.log('=' .repeat(50));
console.log(`📋 Total Required Fields: ${analysis.totalFields}`);
console.log(`✅ Currently Mapped: ${analysis.mappedCount} (${Math.round(analysis.mappedCount/analysis.totalFields*100)}%)`);
console.log(`❌ Missing/Empty: ${analysis.missingCount} (${Math.round(analysis.missingCount/analysis.totalFields*100)}%)`);

console.log('\n🎯 NEXT STEPS:');
console.log('1. Update tidyData() function to include missing field mappings');
console.log('2. Add additional_fields mappings for new field IDs');
console.log('3. Map user_data.timezone and user_data.language if available');
console.log('4. Test with real Docebo webhook data');
console.log('5. Update Lead creation to use the enhanced field mappings');

console.log('\n✅ Analysis completed');
process.exit(0);
