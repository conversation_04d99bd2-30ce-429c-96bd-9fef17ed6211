const getConnection = require("../common/getConnection");
const doceboService = require("../../docebo/services");
const { createNewUser } = require("../users/createUser");
const { getLearningPlanSalesForceId, createNewLearningPlanById } = require("./createLearningPlan");

const learningPlanEnrollTemplate = {
    Docebo_User_Id__c: "",
    Enrolment_Id__c: "",
    Learning_Plan_Id__c: "",
    Unenrollment_Date__c: "",
    Who__c: ""
};

async function getLearningPlanEnrollmentSalesForceId(lpEnrollmentId) {
    let conn;
    try {
        conn = await getConnection();
    } catch (err) {
        console.error("Error getting Salesforce connection:", err);
        return null;
    }
    // if no valid connection, bail out
    if (!conn || !conn.accessToken) {
        console.error("Invalid Salesforce connection in getLearningPlanEnrollmentSalesForceId");
        return null;
    }
    // query for existing enrollment
    try {
        const record = await conn.sobject("Docebo_Learning_Plan_Enrollment__c")
            .findOne({ Enrolment_Id__c: lpEnrollmentId });
        
        if (!record) {
            console.log("Learning plan enrollment doesn't exist in Salesforce");
            return null;
        }
        console.log(`Learning plan enrollment found. ID: ${record.Id}`);
        return record.Id;
    } catch (err) {
        console.error("Error finding learning plan enrollment:", err);
        return null;
    }
}

async function getSalesForceDoceboId(userId) {
    let conn;
    try {
        conn = await getConnection();
    } catch (err) {
        console.error("Error getting Salesforce connection in getSalesForceDoceboId:", err);
        return null;
    }
    if (!conn || !conn.accessToken) {
        console.error("Invalid Salesforce connection in getSalesForceDoceboId");
        return null;
    }
    
    // Check if userId is valid and can be parsed as integer
    const parsedUserId = userId ? parseInt(userId, 10) : null;
    if (isNaN(parsedUserId)) {
        console.error(`Invalid user ID: ${userId} cannot be parsed as an integer in getSalesForceDoceboId`);
        return null;
    }
    
    try {
        const record = await conn.sobject("Docebo_Users__c")
            .findOne({ User_Unique_Id__c: parsedUserId });
        
        if (!record) {
            console.log("User doesn't exist in Salesforce:", userId);
            return null;
        }
        console.log(`Record found. ID: ${record.Id}`);
        return record.Id;
    } catch (err) {
        console.error("Error finding record:", err);
        return null;
    }
}

async function learningPlanEnrollment(enrollmentInfo) {
    let saveRes = false;
    let tmpInfo = { ...learningPlanEnrollTemplate };
    
    let enrollmentId = 'lp-' + enrollmentInfo.learning_plan_id + '-' + enrollmentInfo.user_id;
    let ext = await getLearningPlanEnrollmentSalesForceId(enrollmentId);
    if (ext != null) {
        return true;
    }
    
    try {
        tmpInfo.Docebo_User_Id__c = await getSalesForceDoceboId(enrollmentInfo.user_id);
        if (tmpInfo.Docebo_User_Id__c == null) {
            console.log(`Need to create/update user ${enrollmentInfo.user_id} for learning plan enrollment`);
            
            let userInfo = await doceboService.getUserInfo(enrollmentInfo.user_id);
            if (userInfo.status == 200) {
                let userData = userInfo.data;
                userData["fired_at"] = "";
                userData["expiration_date"] = "";
                let userListedInfo = await doceboService.getUserListedInfo(enrollmentInfo.user_id);
                
                try {
                    // Create or update the user
                    let userSaveRes = await createNewUser(userData, userListedInfo);
                    if (userSaveRes) {
                        // Get the Salesforce ID of the user that was just created/updated
                        tmpInfo.Docebo_User_Id__c = await getSalesForceDoceboId(enrollmentInfo.user_id);
                        if (tmpInfo.Docebo_User_Id__c == null) {
                            console.error(`User was created/updated but ID could not be retrieved: ${enrollmentInfo.user_id}`);
                            return false;
                        }
                    } else {
                        console.error(`User creation/update failed in lp enrollment for user ${enrollmentInfo.user_id}`);
                        return false;
                    }
                } catch (err) {
                    console.error(`Error during user creation/update in lp enrollment: ${err.message}`);
                    // Continue with the enrollment if possible, otherwise fail
                    if (tmpInfo.Docebo_User_Id__c == null) {
                        return false;
                    }
                }
            } else {
                console.error(`Could not get user info from Docebo for user ${enrollmentInfo.user_id}`);
                return false;
            }
        }
    } catch (err) {
        console.error(`Error handling user in learning plan enrollment: ${err.message}`);
        return false;
    }
    
    tmpInfo.Enrolment_Id__c = enrollmentId;
    tmpInfo.Learning_Plan_Id__c = await getLearningPlanSalesForceId(enrollmentInfo.learning_plan_id);
    if (tmpInfo.Learning_Plan_Id__c == null) {
        let planSaveRes = await createNewLearningPlanById(enrollmentInfo.learning_plan_id);
        if (planSaveRes) {
            tmpInfo.Learning_Plan_Id__c = await getLearningPlanSalesForceId(enrollmentInfo.learning_plan_id);
        } else {
            console.log("Learning Plan creation failed in LP enrollment.");
            return false;
        }
    }
    
    tmpInfo.Unenrollment_Date__c = "";
    
    let conn;
    try {
        conn = await getConnection();
    } catch (err) {
        console.error("Error getting Salesforce connection:", err);
        return false;
    }
    
    if (!conn || !conn.accessToken) {
        console.error("Invalid Salesforce connection before upsert");
        return false;
    }
    
    try {
        const result = await conn.sobject("Docebo_Learning_Plan_Enrollment__c")
            .upsert(tmpInfo, "Enrolment_Id__c");
        saveRes = result.success;
    } catch (err) {
        console.error("Error upserting learning plan enrollment:", err);
        return false;
    }
    
    return saveRes;
}

async function learningPlanEnrollmentCompleted(enrollmentInfo) {
    let updateRes = false;
    let tmpInfo = { ...learningPlanEnrollTemplate };
    
    let enrollmentId = 'lp-' + enrollmentInfo.learning_plan_id + '-' + enrollmentInfo.user_id;
    let ext = await getLearningPlanEnrollmentSalesForceId(enrollmentId);
    if (ext == null) {
        console.log("Learning plan enrollment not found for completion.");
        return false;
    }
    
    tmpInfo.Docebo_User_Id__c = await getSalesForceDoceboId(enrollmentInfo.user_id);
    if (tmpInfo.Docebo_User_Id__c == null) {
        console.log("User not found in Salesforce for completion.");
        return false;
    }
    
    tmpInfo.Enrolment_Id__c = enrollmentId;
    tmpInfo.Learning_Plan_Id__c = await getLearningPlanSalesForceId(enrollmentInfo.learning_plan_id);
    if (tmpInfo.Learning_Plan_Id__c == null) {
        console.log("Learning Plan not found in Salesforce for completion.");
        return false;
    }
    
    // Set Unenrollment_Date__c to current date to mark completion
    tmpInfo.Unenrollment_Date__c = new Date().toISOString();
    
    let conn;
    try {
        conn = await getConnection();
    } catch (err) {
        console.error("Error getting Salesforce connection:", err);
        return false;
    }
    
    if (!conn || !conn.accessToken) {
        console.error("Invalid Salesforce connection before upsert for completion");
        return false;
    }
    
    try {
        const result = await conn.sobject("Docebo_Learning_Plan_Enrollment__c")
            .upsert(tmpInfo, "Enrolment_Id__c");
        updateRes = result.success;
    } catch (err) {
        console.error("Error upserting learning plan enrollment completion:", err);
        return false;
    }
    
    return updateRes;
}

module.exports = {
    learningPlanEnrollment,
    learningPlanEnrollmentCompleted
};