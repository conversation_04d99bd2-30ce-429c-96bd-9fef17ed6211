require('dotenv').config();
const axios = require('axios');

// Test webhook after creating the timezone fields in Salesforce
async function testAfterFieldCreation() {
    console.log('🧪 Testing Webhook After Creating Timezone Fields...');
    
    // Simulate a user creation webhook payload for User 18853
    const webhookPayload = {
        event: "user.created",
        fired_by_batch_action: false,
        message_id: `test-after-fields-${Date.now()}`,
        payload: {
            fired_at: new Date().toISOString(),
            user_id: 18853, // Same user that was failing
            username: "<EMAIL>",
            first_name: "name",
            last_name: "last",
            email: "<EMAIL>",
            expiration_date: null
        }
    };

    console.log('\n📤 SENDING WEBHOOK PAYLOAD (After Creating Fields):');
    console.log('=' .repeat(60));
    console.log(JSON.stringify(webhookPayload, null, 2));

    console.log('\n🛠️ REQUIRED SALESFORCE FIELDS:');
    console.log('✅ Lead.Time_Zone__c (Text, 255 chars) - "Time Zone"');
    console.log('✅ Docebo_Users__c.TimeZone__c (Text, 255 chars) - "TimeZone"');

    console.log('\n🔧 CODE CHANGES REVERTED:');
    console.log('✅ Restored Time_Zone__c field in Lead creation');
    console.log('✅ Restored TimeZone__c field in Docebo_Users__c creation');
    console.log('✅ Restored timezone logging');

    try {
        console.log('\n🔄 Sending webhook to local server...');
        
        const response = await axios.post('http://localhost:3000/docebo/user-created', webhookPayload, {
            headers: {
                'Content-Type': 'application/json'
            },
            timeout: 10000
        });

        console.log('\n✅ WEBHOOK RESPONSE:');
        console.log('=' .repeat(50));
        console.log(`Status: ${response.status}`);
        console.log(`Response:`, response.data);

        console.log('\n📋 WHAT TO EXPECT IN LOGS:');
        console.log('=' .repeat(60));
        console.log('✅ No INVALID_FIELD errors');
        console.log('✅ TimeZone__c: "Europe/Oslo" in processed data');
        console.log('✅ Time_Zone__c: "Europe/Oslo" in Lead data');
        console.log('✅ Lead created successfully message');
        console.log('✅ User created successfully message');

        console.log('\n💡 CHECK THE SERVER LOGS NOW!');
        console.log('Look for:');
        console.log('- 📥 SINGLE USER WEBHOOK DATA');
        console.log('- 🔧 PROCESSED USER DATA with TimeZone__c');
        console.log('- 🎯 CREATING LEAD with Time_Zone__c');
        console.log('- ✅ Lead created successfully');
        console.log('- ✅ User created successfully');

        return true;

    } catch (error) {
        if (error.code === 'ECONNREFUSED') {
            console.log('\n⚠️ SERVER NOT RUNNING');
            console.log('Please start the server first with: npm start');
            console.log('Then run this test again after creating the fields');
        } else {
            console.error('\n❌ WEBHOOK TEST ERROR:', error.message);
        }
        return false;
    }
}

// Execute the test
async function runAfterFieldCreationTest() {
    console.log('🚀 Testing After Field Creation...');
    console.log('=' .repeat(70));

    console.log('\n📋 FIELDS TO CREATE IN SALESFORCE:');
    console.log('=' .repeat(50));
    console.log('1. Lead Object:');
    console.log('   - Field Label: Time Zone');
    console.log('   - Field Name: Time_Zone (becomes Time_Zone__c)');
    console.log('   - Data Type: Text');
    console.log('   - Length: 255');
    console.log('');
    console.log('2. Docebo_Users__c Object:');
    console.log('   - Field Label: TimeZone');
    console.log('   - Field Name: TimeZone (becomes TimeZone__c)');
    console.log('   - Data Type: Text');
    console.log('   - Length: 255');

    console.log('\n🎯 EXPECTED TIMEZONE VALUES:');
    console.log('- Europe/Oslo');
    console.log('- Europe/Budapest');
    console.log('- America/New_York');
    console.log('- America/Los_Angeles');
    console.log('- Asia/Tokyo');
    console.log('- etc...');

    // Test the webhook
    const testSuccess = await testAfterFieldCreation();
    
    console.log('\n🎯 FIELD CREATION TEST SUMMARY:');
    console.log('=' .repeat(60));
    
    if (testSuccess) {
        console.log('✅ Webhook sent successfully');
        console.log('✅ Code is ready to use timezone fields');
        console.log('✅ User 18853 should now create successfully');
        console.log('✅ Lead should include Time_Zone__c field');
        console.log('✅ Docebo_Users__c should include TimeZone__c field');
    } else {
        console.log('⚠️ Test couldn\'t complete - check server status');
    }

    console.log('\n💡 NEXT STEPS:');
    console.log('1. Create the timezone fields in Salesforce Setup');
    console.log('2. Start the server: npm start');
    console.log('3. Run this test: node test-after-field-creation.js');
    console.log('4. Check server logs for successful creation');
    console.log('5. Verify records in Salesforce have timezone data');

    console.log('\n📊 EXPECTED OUTCOME:');
    console.log('✅ No INVALID_FIELD errors');
    console.log('✅ Lead created with Time_Zone__c = "Europe/Oslo"');
    console.log('✅ Docebo_Users__c created with TimeZone__c = "Europe/Oslo"');
    console.log('✅ Complete field mapping working (100% accuracy)');

    console.log('\n✅ Field creation test completed!');
}

// Run the test
runAfterFieldCreationTest()
    .then(() => {
        console.log('\n🎉 Field creation test completed!');
        console.log('Create the timezone fields in Salesforce, then test the webhook.');
        process.exit(0);
    })
    .catch(err => {
        console.error('\n💥 Test failed:', err);
        process.exit(1);
    });
