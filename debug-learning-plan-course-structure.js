require('dotenv').config();
const getApiData = require("./common/docebo/fetcher");
const APP_BASE = process.env.DOCEBO_API_BASE_URL;

async function debugLearningPlanCourseStructure() {
    try {
        console.log('🔍 DEBUGGING LEARNING PLAN COURSE STRUCTURE');
        console.log('=' .repeat(80));
        
        // Step 1: Get a learning plan to test with
        console.log('📚 STEP 1: Getting a Learning Plan to test with...');
        console.log('-'.repeat(50));
        
        const lpResponse = await getApiData(
            'GET', 
            `${APP_BASE}/learningplan/v1/learningplans?page=1&page_size=5`, 
            null
        );
        
        if (!lpResponse || lpResponse.status !== 200) {
            throw new Error('Could not get learning plans');
        }
        
        const learningPlans = lpResponse.data?.items || [];
        console.log(`Found ${learningPlans.length} learning plans`);
        
        if (learningPlans.length === 0) {
            throw new Error('No learning plans found');
        }
        
        const testLp = learningPlans[0];
        const lpId = testLp.learning_plan_id || testLp.id;
        console.log(`\n📋 Using Learning Plan: ${lpId} - ${testLp.title || testLp.name}`);
        console.log(`📋 Learning Plan structure: ${Object.keys(testLp).join(', ')}`);

        // Step 2: Get courses for this learning plan
        console.log('\n📚 STEP 2: Getting Courses for Learning Plan...');
        console.log('-'.repeat(50));

        const coursesResponse = await getApiData(
            'GET',
            `${APP_BASE}/learningplan/v1/learningplans/${lpId}/courses`,
            null
        );
        
        if (!coursesResponse || coursesResponse.status !== 200) {
            throw new Error(`Could not get courses for learning plan ${lpId}`);
        }
        
        const courses = coursesResponse.data?.items || [];
        console.log(`Found ${courses.length} courses in learning plan ${lpId}`);
        
        if (courses.length === 0) {
            console.log('⚠️ No courses found in this learning plan');
            return;
        }
        
        // Step 3: Examine course structure
        console.log('\n🔍 STEP 3: Examining Course Structure...');
        console.log('-'.repeat(50));
        
        courses.forEach((course, index) => {
            console.log(`\n📋 Course ${index + 1}:`);
            console.log(`   📊 All fields: ${Object.keys(course).join(', ')}`);
            console.log(`   🔍 Raw course object:`);
            console.log(JSON.stringify(course, null, 2));
            
            // Check for various ID fields
            const possibleIdFields = ['id', 'course_id', 'courseId', 'course_uid', 'uid', 'external_id', 'uidCourse'];
            console.log(`\n   🔍 Checking for ID fields:`);
            possibleIdFields.forEach(field => {
                console.log(`      ${field}: ${course[field] || 'undefined'}`);
            });
            
            if (index >= 2) { // Only show first 3 courses
                console.log(`\n   ... (showing only first 3 courses)`);
                return;
            }
        });
        
        // Step 4: Try alternative endpoints
        console.log('\n🔍 STEP 4: Trying Alternative Endpoints...');
        console.log('-'.repeat(50));
        
        const alternativeEndpoints = [
            `/learningplan/v1/learningplans/${lpId}`,
            `/learningplan/v1/learningplans/${lpId}/enrollments`,
            `/course/v1/courses?learning_plan_id=${lpId}`,
            `/learn/v1/enrollments?learning_plan_id=${lpId}`
        ];
        
        for (const endpoint of alternativeEndpoints) {
            console.log(`\n🧪 Testing: ${endpoint}`);
            
            try {
                const response = await getApiData('GET', `${APP_BASE}${endpoint}?page=1&page_size=3`, null);
                
                if (response && response.status === 200) {
                    const items = response.data?.items || response.data || [];
                    console.log(`   ✅ Success: ${Array.isArray(items) ? items.length : 'single object'} items`);
                    
                    if (Array.isArray(items) && items.length > 0) {
                        const sample = items[0];
                        console.log(`   📋 Sample structure: ${Object.keys(sample).join(', ')}`);
                        
                        // Check if this has course information
                        const courseFields = ['course_id', 'courseId', 'course_uid', 'uidCourse', 'course_name'];
                        const foundCourseFields = courseFields.filter(field => sample[field] !== undefined);
                        
                        if (foundCourseFields.length > 0) {
                            console.log(`   🎯 Found course fields: ${foundCourseFields.join(', ')}`);
                            foundCourseFields.forEach(field => {
                                console.log(`      ${field}: ${sample[field]}`);
                            });
                        }
                    } else if (!Array.isArray(items)) {
                        console.log(`   📋 Object structure: ${Object.keys(items).join(', ')}`);
                    }
                } else {
                    console.log(`   ❌ Failed or no data`);
                }
            } catch (error) {
                console.log(`   ❌ Error: ${error.message}`);
            }
        }
        
        // Step 5: Check if we can get course details directly
        console.log('\n🔍 STEP 5: Testing Direct Course API...');
        console.log('-'.repeat(50));
        
        try {
            const allCoursesResponse = await getApiData(
                'GET', 
                `${APP_BASE}/course/v1/courses?page=1&page_size=3`, 
                null
            );
            
            if (allCoursesResponse && allCoursesResponse.status === 200) {
                const allCourses = allCoursesResponse.data?.items || [];
                console.log(`Found ${allCourses.length} courses via direct API`);
                
                if (allCourses.length > 0) {
                    const sampleCourse = allCourses[0];
                    console.log(`📋 Direct course structure: ${Object.keys(sampleCourse).join(', ')}`);
                    console.log(`📋 Course ID field: ${sampleCourse.id || sampleCourse.course_id || 'not found'}`);
                }
            }
        } catch (error) {
            console.log(`❌ Could not get courses via direct API: ${error.message}`);
        }
        
        return {
            success: true,
            learningPlanId: lpId,
            coursesFound: courses.length,
            courseStructure: courses.length > 0 ? Object.keys(courses[0]) : []
        };
        
    } catch (error) {
        console.error('💥 Error debugging learning plan course structure:', error);
        return {
            success: false,
            error: error.message
        };
    }
}

// Execute the debug
console.log('🔄 Starting Learning Plan Course Structure Debug...');
debugLearningPlanCourseStructure()
    .then((result) => {
        console.log('\n📋 DEBUG SUMMARY:');
        console.log('=' .repeat(50));
        
        if (result.success) {
            console.log(`✅ Debug completed successfully`);
            console.log(`📊 Learning Plan ID: ${result.learningPlanId}`);
            console.log(`📊 Courses Found: ${result.coursesFound}`);
            console.log(`📊 Course Structure: ${result.courseStructure.join(', ')}`);
        } else {
            console.log(`❌ Debug failed: ${result.error}`);
        }
        
        console.log('\n✅ Learning plan course structure debug completed');
        process.exit(0);
    })
    .catch(err => {
        console.error('💥 Learning plan course structure debug failed:', err);
        process.exit(1);
    });
