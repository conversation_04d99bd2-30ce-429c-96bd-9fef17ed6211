require('dotenv').config();
const axios = require('axios');

async function debugSalesforceAuth() {
    try {
        console.log('🔍 DEBUGGING SALESFORCE AUTHENTICATION');
        console.log('=' .repeat(60));
        
        // Show current environment variables (masked)
        console.log('📋 Current Environment Variables:');
        console.log(`   SF_TOKEN_URL: ${process.env.SF_TOKEN_URL}`);
        console.log(`   SF_API_CLIENT_ID: ${process.env.SF_API_CLIENT_ID?.substring(0, 10)}...`);
        console.log(`   SF_API_CLIENT_SECRET: ${process.env.SF_API_CLIENT_SECRET?.substring(0, 10)}...`);
        console.log(`   SF_API_GRANT_TYPE: ${process.env.SF_API_GRANT_TYPE}`);
        console.log(`   SF_API_USER_NAME: ${process.env.SF_API_USER_NAME}`);
        console.log(`   SF_API_PASSWORD: ${process.env.SF_API_PASSWORD?.substring(0, 5)}... (length: ${process.env.SF_API_PASSWORD?.length})`);
        
        console.log('\n📡 Making Authentication Request...');
        console.log('-'.repeat(40));
        
        const requestData = {
            grant_type: process.env.SF_API_GRANT_TYPE,
            client_id: process.env.SF_API_CLIENT_ID,
            client_secret: process.env.SF_API_CLIENT_SECRET,
            username: process.env.SF_API_USER_NAME,
            password: process.env.SF_API_PASSWORD
        };
        
        console.log('Request parameters:');
        console.log(`   grant_type: ${requestData.grant_type}`);
        console.log(`   client_id: ${requestData.client_id?.substring(0, 10)}...`);
        console.log(`   client_secret: ${requestData.client_secret?.substring(0, 10)}...`);
        console.log(`   username: ${requestData.username}`);
        console.log(`   password: ${requestData.password?.substring(0, 5)}... (length: ${requestData.password?.length})`);
        
        try {
            const response = await axios.post(
                process.env.SF_TOKEN_URL,
                new URLSearchParams(requestData),
                {
                    headers: { 'Content-Type': 'application/x-www-form-urlencoded' }
                }
            );
            
            console.log('\n✅ SUCCESS! Authentication successful');
            console.log(`Access Token: ${response.data.access_token?.substring(0, 20)}...`);
            console.log(`Instance URL: ${response.data.instance_url}`);
            console.log(`Token Type: ${response.data.token_type}`);
            
        } catch (authError) {
            console.log('\n❌ AUTHENTICATION FAILED');
            console.log(`Status: ${authError.response?.status}`);
            console.log(`Error: ${JSON.stringify(authError.response?.data, null, 2)}`);
            
            if (authError.response?.data?.error === 'invalid_grant') {
                console.log('\n💡 TROUBLESHOOTING TIPS:');
                console.log('1. 🔑 Password format should be: YourPassword + SecurityToken (no space)');
                console.log('2. 🔄 Get a new security token from Salesforce:');
                console.log('   - Setup → My Personal Information → Reset My Security Token');
                console.log('3. 🔒 Check if user account is locked or has login restrictions');
                console.log('4. 🌐 Verify the correct login URL (test.salesforce.com for sandbox)');
                console.log('5. 📧 Check if IP restrictions are blocking the API calls');
                
                console.log('\n🔍 Current password analysis:');
                const password = process.env.SF_API_PASSWORD;
                if (password) {
                    console.log(`   Length: ${password.length} characters`);
                    console.log(`   Starts with: ${password.substring(0, 3)}...`);
                    console.log(`   Ends with: ...${password.substring(password.length - 3)}`);
                    console.log(`   Contains special chars: ${/[!@#$%^&*()_+\-=\[\]{};':"\\|,.<>\/?]/.test(password)}`);
                }
            }
        }
        
    } catch (error) {
        console.error('💥 Debug failed:', error.message);
    }
}

console.log('🔄 Starting Salesforce Authentication Debug...');
debugSalesforceAuth()
    .then(() => {
        console.log('\n✅ Salesforce authentication debug completed');
        process.exit(0);
    })
    .catch(err => {
        console.error('💥 Salesforce authentication debug failed:', err);
        process.exit(1);
    });
