require('dotenv').config();
const { 
    getUserCourseEnrollments, 
    getUserEnrollmentsWithCourseDetails, 
    getUserEnrollmentsSummary 
} = require('./platform/docebo/getUserEnrollments');

async function testUserEnrollments() {
    try {
        console.log('🧪 Testing User Course Enrollments API...');
        
        // Test with a known user ID (you can change this to any user ID you want to test)
        const testUserId = "17928"; // Using one from our previous tests
        
        console.log(`\n👤 Testing with User ID: ${testUserId}`);
        console.log('=' .repeat(60));

        // Test 1: Get basic enrollments
        console.log('\n📚 TEST 1: Getting basic course enrollments...');
        const basicEnrollments = await getUserCourseEnrollments(testUserId);
        
        console.log(`✅ Found ${basicEnrollments.length} enrollments`);
        
        if (basicEnrollments.length > 0) {
            console.log('\n📋 SAMPLE BASIC ENROLLMENT:');
            console.log('-'.repeat(40));
            const sample = basicEnrollments[0];
            console.log(`Course ID: ${sample.course_id}`);
            console.log(`Status: ${sample.status}`);
            console.log(`Enrollment Date: ${sample.enrollment_date || sample.date_inscr || 'N/A'}`);
            console.log(`Completion Date: ${sample.completion_date || sample.date_complete || 'N/A'}`);
            console.log(`Score: ${sample.score || sample.final_score || 'N/A'}`);
            console.log(`Progress: ${sample.progress || sample.completion_percentage || 'N/A'}`);
        }

        // Test 2: Get enrollment summary
        console.log('\n📊 TEST 2: Getting enrollment summary...');
        const summary = await getUserEnrollmentsSummary(testUserId);
        
        console.log('\n📊 ENROLLMENT SUMMARY:');
        console.log('=' .repeat(40));
        console.log(`👤 User ID: ${summary.user_id}`);
        console.log(`📚 Total Enrollments: ${summary.total_enrollments}`);
        console.log(`✅ Completed: ${summary.completed}`);
        console.log(`🔄 In Progress: ${summary.in_progress}`);
        console.log(`⏸️  Not Started: ${summary.not_started}`);
        console.log(`❓ Other Status: ${summary.other}`);

        // Test 3: Get detailed enrollments (limit to first 3 for demo)
        if (basicEnrollments.length > 0) {
            console.log('\n📋 TEST 3: Getting detailed enrollments (first 3)...');
            
            // Limit to first 3 enrollments for demo purposes
            const limitedEnrollments = basicEnrollments.slice(0, 3);
            console.log(`Processing ${limitedEnrollments.length} enrollments for detailed info...`);
            
            const detailedEnrollments = [];
            
            for (let i = 0; i < limitedEnrollments.length; i++) {
                const enrollment = limitedEnrollments[i];
                console.log(`\n📋 Processing enrollment ${i + 1}/${limitedEnrollments.length}:`);
                console.log(`   Course ID: ${enrollment.course_id}`);
                
                try {
                    // Get course info
                    const doceboService = require('./platform/docebo/services');
                    const courseInfo = await doceboService.getCourseInfo(enrollment.course_id);
                    const enrollmentDetails = await doceboService.getEnrolledInfo(enrollment.course_id, testUserId);
                    
                    const detailed = {
                        course_id: enrollment.course_id,
                        course_name: courseInfo?.data?.name || 'Unknown',
                        course_type: courseInfo?.data?.type || 'Unknown',
                        status: enrollment.status,
                        enrollment_date: enrollment.enrollment_date || enrollment.date_inscr,
                        completion_date: enrollment.completion_date || enrollment.date_complete,
                        score: enrollment.score || enrollment.final_score,
                        progress: enrollment.progress || enrollment.completion_percentage,
                        enrollmentDetails: enrollmentDetails?.data || null
                    };
                    
                    detailedEnrollments.push(detailed);
                    
                    console.log(`   ✅ Course Name: ${detailed.course_name}`);
                    console.log(`   📊 Status: ${detailed.status}`);
                    console.log(`   🎯 Score: ${detailed.score || 'N/A'}`);
                    
                } catch (detailError) {
                    console.log(`   ❌ Error getting details: ${detailError.message}`);
                }
                
                // Small delay to avoid rate limiting
                await new Promise(resolve => setTimeout(resolve, 500));
            }
            
            console.log(`\n✅ Processed ${detailedEnrollments.length} detailed enrollments`);
        }

        // Test 4: Show all enrollment data structure
        if (basicEnrollments.length > 0) {
            console.log('\n📋 COMPLETE ENROLLMENT DATA STRUCTURE:');
            console.log('=' .repeat(60));
            console.log('Sample enrollment object:');
            console.log(JSON.stringify(basicEnrollments[0], null, 2));
        }

        // Test 5: Test with different user IDs
        console.log('\n🔄 Testing with multiple users...');
        const testUserIds = ["18838", "18837", "18835"]; // Using IDs from our previous tests
        
        for (const userId of testUserIds) {
            try {
                const userSummary = await getUserEnrollmentsSummary(userId);
                console.log(`👤 User ${userId}: ${userSummary.total_enrollments} enrollments (${userSummary.completed} completed)`);
            } catch (userError) {
                console.log(`❌ User ${userId}: Error - ${userError.message}`);
            }
            
            // Small delay between users
            await new Promise(resolve => setTimeout(resolve, 200));
        }

        console.log('\n🎯 USAGE EXAMPLES:');
        console.log('=' .repeat(50));
        console.log('// Get basic enrollments for a user');
        console.log('const enrollments = await getUserCourseEnrollments(userId);');
        console.log('');
        console.log('// Get enrollment summary');
        console.log('const summary = await getUserEnrollmentsSummary(userId);');
        console.log('');
        console.log('// Get detailed enrollments with course info');
        console.log('const detailed = await getUserEnrollmentsWithCourseDetails(userId);');

        console.log('\n✅ User enrollments API testing completed!');
        
        return {
            success: true,
            totalEnrollments: basicEnrollments.length,
            summary: summary
        };

    } catch (error) {
        console.error('💥 Error testing user enrollments:', error);
        return {
            success: false,
            error: error.message
        };
    }
}

// Execute the test
console.log('🔄 Starting user enrollments API test...');
testUserEnrollments()
    .then((result) => {
        if (result && result.success) {
            console.log(`\n🎉 Test completed successfully!`);
            console.log(`📚 Found ${result.totalEnrollments} total enrollments`);
        } else {
            console.log('\n⚠️ Test completed with issues');
            if (result && result.error) {
                console.log('Error:', result.error);
            }
        }
        process.exit(0);
    })
    .catch(err => {
        console.error('💥 Test failed:', err);
        process.exit(1);
    });
