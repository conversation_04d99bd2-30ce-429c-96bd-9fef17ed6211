require('dotenv').config();
const getConnection = require('./platform/salesforce/common/getConnection');

async function checkLearningPlanCourseEnrollmentFields() {
    try {
        console.log('🔍 CHECKING LEARNING PLAN COURSE ENROLLMENT FIELDS');
        console.log('=' .repeat(70));
        
        const conn = await getConnection();
        if (!conn || !conn.accessToken) {
            throw new Error('Failed to connect to Salesforce');
        }

        console.log('✅ Connected to Salesforce successfully');

        // Step 1: Get object metadata
        console.log('\n📊 STEP 1: Getting Object Metadata...');
        console.log('-'.repeat(40));
        
        const metadata = await conn.sobject("Docebo_Learning_Plan_Course_Enrollment__c").describe();
        
        console.log(`Object Label: ${metadata.label}`);
        console.log(`Object Name: ${metadata.name}`);
        console.log(`Total Fields: ${metadata.fields.length}`);
        
        // Step 2: Show all fields
        console.log('\n📋 STEP 2: All Fields in Object...');
        console.log('-'.repeat(40));
        
        metadata.fields.forEach(field => {
            console.log(`   ${field.name} (${field.type}) - ${field.label}`);
        });

        // Step 3: Get current count with correct query
        console.log('\n📊 STEP 3: Getting Current Count...');
        console.log('-'.repeat(40));
        
        const countResult = await conn.query("SELECT COUNT() FROM Docebo_Learning_Plan_Course_Enrollment__c");
        console.log(`📊 Total Learning Plan Course Enrollments: ${countResult.totalSize.toLocaleString()}`);

        // Step 4: Get sample records with correct field names
        console.log('\n📋 STEP 4: Getting Sample Records...');
        console.log('-'.repeat(40));
        
        // Use only standard fields first
        const sampleRecords = await conn.query(`
            SELECT Id, Name, CreatedDate, LastModifiedDate
            FROM Docebo_Learning_Plan_Course_Enrollment__c 
            ORDER BY CreatedDate DESC 
            LIMIT 5
        `);
        
        console.log(`Found ${sampleRecords.totalSize} total records, showing sample:`);
        
        sampleRecords.records.forEach((record, index) => {
            console.log(`\n   ${index + 1}. ${record.Name || record.Id}`);
            console.log(`      Created: ${record.CreatedDate}`);
            console.log(`      Modified: ${record.LastModifiedDate}`);
        });

        // Step 5: Check if sync process is still running
        console.log('\n🔄 STEP 5: Checking Sync Process Status...');
        console.log('-'.repeat(40));
        
        // Check if there are any records created in the last hour
        const recentActivity = await conn.query(`
            SELECT COUNT() 
            FROM Docebo_Learning_Plan_Course_Enrollment__c 
            WHERE CreatedDate >= ${new Date(Date.now() - 60 * 60 * 1000).toISOString()}
        `);
        
        console.log(`Records created in last hour: ${recentActivity.totalSize.toLocaleString()}`);
        
        // Check if there are any records created today
        const todayActivity = await conn.query(`
            SELECT COUNT() 
            FROM Docebo_Learning_Plan_Course_Enrollment__c 
            WHERE CreatedDate = TODAY
        `);
        
        console.log(`Records created today: ${todayActivity.totalSize.toLocaleString()}`);

        return {
            success: true,
            totalRecords: countResult.totalSize,
            todayRecords: todayActivity.totalSize,
            lastHourRecords: recentActivity.totalSize,
            fields: metadata.fields.map(f => ({ name: f.name, type: f.type, label: f.label }))
        };

    } catch (error) {
        console.error('💥 Error checking learning plan course enrollment fields:', error);
        return {
            success: false,
            error: error.message
        };
    }
}

console.log('🔄 Starting Learning Plan Course Enrollment Fields Check...');
checkLearningPlanCourseEnrollmentFields()
    .then((result) => {
        console.log('\n📋 LEARNING PLAN COURSE ENROLLMENT FIELDS SUMMARY:');
        console.log('=' .repeat(60));
        
        if (result.success) {
            console.log(`📊 Total Records: ${result.totalRecords.toLocaleString()}`);
            console.log(`📅 Created Today: ${result.todayRecords.toLocaleString()}`);
            console.log(`🕐 Created Last Hour: ${result.lastHourRecords.toLocaleString()}`);
            console.log(`📋 Total Fields: ${result.fields.length}`);
            
            if (result.totalRecords <= 2) {
                console.log(`\n⚠️ ISSUE: Still only ${result.totalRecords} records - sync may not be working`);
                console.log(`🔍 Need to check if the sync process is running or has errors`);
            } else {
                console.log(`\n✅ Records found: ${result.totalRecords.toLocaleString()}`);
            }
            
            if (result.lastHourRecords > 0) {
                console.log(`🔄 Recent activity detected - sync may be running`);
            } else {
                console.log(`⚠️ No recent activity - sync may have stopped or failed`);
            }
        } else {
            console.log(`❌ Fields check failed: ${result.error}`);
        }
        
        console.log('\n✅ Learning Plan Course Enrollment fields check completed');
        process.exit(0);
    })
    .catch(err => {
        console.error('💥 Learning Plan Course Enrollment fields check failed:', err);
        process.exit(1);
    });
