require('dotenv').config();
const getConnection = require('./platform/salesforce/common/getConnection');
const doceboService = require('./platform/docebo/services');

async function testUserBatchConnection() {
    try {
        console.log('🔍 Testing User Batch Sync Prerequisites');
        console.log('=' .repeat(60));
        
        // Test 1: Salesforce Connection
        console.log('\n1️⃣ Testing Salesforce Connection...');
        const conn = await getConnection();
        if (!conn || !conn.accessToken) {
            console.error("❌ Salesforce connection failed");
            return false;
        }
        console.log('✅ Salesforce connection successful');
        
        // Test 2: Docebo API Connection
        console.log('\n2️⃣ Testing Docebo API Connection...');
        try {
            const testUsers = await doceboService.getTotalUserListedInfo(); // Get all users
            if (testUsers && Array.isArray(testUsers) && testUsers.length > 0) {
                console.log(`✅ Docebo API connection successful - Found ${testUsers.length} users`);
            } else {
                console.error("❌ Docebo API returned no users or unexpected format");
                return false;
            }
        } catch (doceboError) {
            console.error("❌ Docebo API connection failed:", doceboError.message);
            return false;
        }
        
        // Test 3: Check existing records
        console.log('\n3️⃣ Checking existing Salesforce records...');
        
        const existingUsers = await conn.sobject("Docebo_Users__c")
            .find({})
            .limit(5)
            .execute();
        console.log(`📊 Found ${existingUsers.length} existing Docebo_Users__c records`);
        
        const existingLeads = await conn.sobject("Lead")
            .find({ Created_by_Docebo_API__c: true })
            .limit(5)
            .execute();
        console.log(`📊 Found ${existingLeads.length} existing Leads created by Docebo`);
        
        const existingContacts = await conn.sobject("Contact")
            .find({ Created_by_Docebo_API__c: true })
            .limit(5)
            .execute();
        console.log(`📊 Found ${existingContacts.length} existing Contacts created by Docebo`);
        
        // Test 4: Test processing a single user
        console.log('\n4️⃣ Testing single user processing...');
        
        const { processUser } = require('./platform/salesforce/users/historicalDataUpdate');
        
        // Get one user from Docebo for testing
        const testUserList = await doceboService.getTotalUserListedInfo();
        if (testUserList.length > 0) {
            const testUser = testUserList[0];
            console.log(`🧪 Testing with user ID: ${testUser.user_id} (${testUser.email})`);
            
            // Get detailed user info
            const userInfo = await doceboService.getUserInfo(testUser.user_id);
            if (userInfo.status === 200) {
                const userData = userInfo.data;
                userData["fired_at"] = testUser.creation_date || null;
                userData["expiration_date"] = testUser.expiration_date || null;
                
                const doceboUserData = {
                    userInfo: userData,
                    userListedInfo: testUser
                };
                
                console.log('📝 Processing test user...');
                const result = await processUser(conn, doceboUserData);
                
                console.log('✅ Test user processing completed:');
                console.log(`   Contact Updated: ${result.contactUpdated ? 'Yes' : 'No'}`);
                console.log(`   Lead Updated: ${result.leadUpdated ? 'Yes' : 'No'}`);
                console.log(`   Lead Created: ${result.leadCreated ? 'Yes' : 'No'}`);
                console.log(`   Docebo User Updated: ${result.doceboUserUpdated ? 'Yes' : 'No'}`);
                console.log(`   Docebo User Created: ${result.doceboUserCreated ? 'Yes' : 'No'}`);
                
            } else {
                console.error('❌ Failed to get detailed user info');
                return false;
            }
        } else {
            console.error('❌ No test users found in Docebo');
            return false;
        }
        
        console.log('\n🎯 CONNECTION TEST SUMMARY:');
        console.log('=' .repeat(60));
        console.log('✅ Salesforce connection: Working');
        console.log('✅ Docebo API connection: Working');
        console.log('✅ Single user processing: Working');
        console.log('✅ All prerequisites met for batch sync');
        
        console.log('\n🚀 READY FOR FULL BATCH SYNC:');
        console.log('   All connections and processing tested successfully');
        console.log('   You can now proceed with the full user batch sync');
        
        return true;
        
    } catch (error) {
        console.error('💥 Error in connection test:', error);
        return false;
    }
}

// Execute the connection test
console.log('🔄 Starting user batch sync connection test...');
testUserBatchConnection()
    .then((success) => {
        if (success) {
            console.log('\n✅ Connection test passed! Ready for batch sync.');
        } else {
            console.log('\n❌ Connection test failed. Please fix issues before batch sync.');
        }
        process.exit(success ? 0 : 1);
    })
    .catch(err => {
        console.error('💥 Connection test failed:', err);
        process.exit(1);
    });
