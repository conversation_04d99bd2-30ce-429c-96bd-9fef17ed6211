require('dotenv').config();
const getConnection = require('./platform/salesforce/common/getConnection');
const doceboService = require('./platform/docebo/services');
const { mapDoceboCourseToSalesforce } = require('./platform/salesforce/courses/mapCourseData');

// Configuration
const BATCH_SIZE = 25; // Process 25 courses at a time
const DELAY_BETWEEN_BATCHES = 2000; // 2 seconds delay between batches
const MAX_RETRIES = 3; // Maximum retries for failed API calls

function delay(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
}

async function batchUpdateCoursesWithMissingFields() {
    try {
        console.log('🚀 BATCH UPDATE: Missing Course Fields Implementation');
        console.log('=' .repeat(70));
        console.log('📋 FIELDS TO UPDATE:');
        console.log('   ✅ Course_Category_Code__c - From category.code');
        console.log('   ✅ Course_Start_Date__c - From time_options.date_begin');
        console.log('   ✅ Course_End_Date__c - From time_options.date_end');
        console.log('   ✅ Session_Time_min__c - Calculated from sessions/duration');
        console.log('   ✅ Enrollment_Date__c - From catalog_options.self_enrollment.start_date');
        console.log('=' .repeat(70));

        const startTime = Date.now();
        let stats = {
            totalCourses: 0,
            processedCourses: 0,
            updatedCourses: 0,
            skippedCourses: 0,
            errorCourses: 0,
            batchesProcessed: 0
        };

        // Step 1: Get all existing courses from Salesforce
        console.log('\n🔍 FETCHING EXISTING COURSES FROM SALESFORCE...');
        console.log('-'.repeat(50));
        
        const conn = await getConnection();
        if (!conn || !conn.accessToken) {
            throw new Error('Failed to connect to Salesforce');
        }

        const existingCourses = await conn.sobject("Docebo_Course__c")
            .find({}, [
                'Id', 
                'Course_External_Id__c', 
                'Course_Name__c',
                'Course_Category_Code__c',
                'Course_Start_Date__c',
                'Course_End_Date__c',
                'Session_Time_min__c',
                'Enrollment_Date__c'
            ])
            .execute();

        stats.totalCourses = existingCourses.length;
        console.log(`✅ Found ${stats.totalCourses} courses in Salesforce`);

        if (stats.totalCourses === 0) {
            console.log('⚠️ No courses found in Salesforce to update');
            return stats;
        }

        // Step 2: Analyze which courses need updates
        console.log('\n🔍 ANALYZING COURSES THAT NEED UPDATES...');
        console.log('-'.repeat(50));

        const coursesNeedingUpdate = existingCourses.filter(course => {
            // Check if any of the missing fields are empty or null
            const needsUpdate = 
                !course.Course_Category_Code__c ||
                !course.Course_Start_Date__c ||
                !course.Course_End_Date__c ||
                !course.Session_Time_min__c ||
                !course.Enrollment_Date__c;
            
            return needsUpdate;
        });

        console.log(`📊 Analysis Results:`);
        console.log(`   Total courses: ${stats.totalCourses}`);
        console.log(`   Courses needing updates: ${coursesNeedingUpdate.length}`);
        console.log(`   Courses already complete: ${stats.totalCourses - coursesNeedingUpdate.length}`);

        if (coursesNeedingUpdate.length === 0) {
            console.log('✅ All courses already have the missing fields populated!');
            return stats;
        }

        // Step 3: Process courses in batches
        console.log('\n🔄 STARTING BATCH PROCESSING...');
        console.log('-'.repeat(50));

        const batches = [];
        for (let i = 0; i < coursesNeedingUpdate.length; i += BATCH_SIZE) {
            batches.push(coursesNeedingUpdate.slice(i, i + BATCH_SIZE));
        }

        console.log(`📦 Created ${batches.length} batches of ${BATCH_SIZE} courses each`);

        for (let batchIndex = 0; batchIndex < batches.length; batchIndex++) {
            const batch = batches[batchIndex];
            stats.batchesProcessed++;
            
            console.log(`\n📦 PROCESSING BATCH ${batchIndex + 1}/${batches.length}`);
            console.log(`   Courses in batch: ${batch.length}`);
            console.log('-'.repeat(30));

            const coursesToUpdate = [];

            for (let courseIndex = 0; courseIndex < batch.length; courseIndex++) {
                const sfCourse = batch[courseIndex];
                stats.processedCourses++;

                try {
                    console.log(`   📚 [${courseIndex + 1}/${batch.length}] ${sfCourse.Course_Name__c} (ID: ${sfCourse.Course_External_Id__c})`);

                    if (!sfCourse.Course_External_Id__c) {
                        console.log(`      ⚠️ Skipping - No external ID`);
                        stats.skippedCourses++;
                        continue;
                    }

                    // Get fresh course data from Docebo
                    let doceboCourseData = null;
                    let retryCount = 0;
                    
                    while (retryCount < MAX_RETRIES && !doceboCourseData) {
                        try {
                            const courseResponse = await doceboService.getCourseInfo(sfCourse.Course_External_Id__c);
                            if (courseResponse && courseResponse.status === 200 && courseResponse.data) {
                                doceboCourseData = courseResponse.data;
                            }
                        } catch (apiError) {
                            retryCount++;
                            console.log(`      ⚠️ API call failed (attempt ${retryCount}/${MAX_RETRIES}): ${apiError.message}`);
                            if (retryCount < MAX_RETRIES) {
                                await delay(1000 * retryCount); // Exponential backoff
                            }
                        }
                    }

                    if (!doceboCourseData) {
                        console.log(`      ❌ Could not fetch course data from Docebo after ${MAX_RETRIES} attempts`);
                        stats.errorCourses++;
                        continue;
                    }

                    // Map the course data using our enhanced mapping function
                    const mappedData = mapDoceboCourseToSalesforce(doceboCourseData, {});
                    
                    // Prepare update object with only the missing fields
                    const updateData = {
                        Id: sfCourse.Id
                    };

                    let hasUpdates = false;

                    // Only update fields that are currently empty
                    if (!sfCourse.Course_Category_Code__c && mappedData.Course_Category_Code__c) {
                        updateData.Course_Category_Code__c = mappedData.Course_Category_Code__c;
                        hasUpdates = true;
                    }

                    if (!sfCourse.Course_Start_Date__c && mappedData.Course_Start_Date__c) {
                        updateData.Course_Start_Date__c = mappedData.Course_Start_Date__c;
                        hasUpdates = true;
                    }

                    if (!sfCourse.Course_End_Date__c && mappedData.Course_End_Date__c) {
                        updateData.Course_End_Date__c = mappedData.Course_End_Date__c;
                        hasUpdates = true;
                    }

                    if (!sfCourse.Session_Time_min__c && mappedData.Session_Time_min__c) {
                        updateData.Session_Time_min__c = mappedData.Session_Time_min__c;
                        hasUpdates = true;
                    }

                    if (!sfCourse.Enrollment_Date__c && mappedData.Enrollment_Date__c) {
                        updateData.Enrollment_Date__c = mappedData.Enrollment_Date__c;
                        hasUpdates = true;
                    }

                    if (hasUpdates) {
                        coursesToUpdate.push(updateData);
                        console.log(`      ✅ Prepared for update`);
                        
                        // Log what fields will be updated
                        const fieldsToUpdate = Object.keys(updateData).filter(key => key !== 'Id');
                        console.log(`         Fields: ${fieldsToUpdate.join(', ')}`);
                    } else {
                        console.log(`      ⚠️ No updates needed - all fields already populated`);
                        stats.skippedCourses++;
                    }

                } catch (error) {
                    console.log(`      ❌ Error processing course: ${error.message}`);
                    stats.errorCourses++;
                }
            }

            // Execute batch update
            if (coursesToUpdate.length > 0) {
                console.log(`\n   🔄 Executing batch update of ${coursesToUpdate.length} courses...`);
                
                try {
                    const updateResults = await conn.sobject("Docebo_Course__c").update(coursesToUpdate);
                    const results = Array.isArray(updateResults) ? updateResults : [updateResults];
                    
                    let successCount = 0;
                    results.forEach((result, index) => {
                        if (result.success) {
                            successCount++;
                        } else {
                            console.log(`      ❌ Update failed for course ${index + 1}: ${JSON.stringify(result.errors)}`);
                        }
                    });
                    
                    stats.updatedCourses += successCount;
                    console.log(`   ✅ Successfully updated ${successCount}/${coursesToUpdate.length} courses`);
                    
                } catch (updateError) {
                    console.log(`   ❌ Batch update failed: ${updateError.message}`);
                    stats.errorCourses += coursesToUpdate.length;
                }
            } else {
                console.log(`   ⚠️ No courses in this batch needed updates`);
            }

            // Progress update
            const progressPercent = Math.round(((batchIndex + 1) / batches.length) * 100);
            console.log(`   📊 Progress: ${batchIndex + 1}/${batches.length} batches (${progressPercent}%)`);

            // Delay between batches (except for the last one)
            if (batchIndex < batches.length - 1) {
                console.log(`   ⏳ Waiting ${DELAY_BETWEEN_BATCHES/1000} seconds before next batch...`);
                await delay(DELAY_BETWEEN_BATCHES);
            }
        }

        // Step 4: Final verification
        console.log('\n🔍 FINAL VERIFICATION...');
        console.log('-'.repeat(50));

        const verificationCourses = await conn.sobject("Docebo_Course__c")
            .find({}, [
                'Course_Name__c',
                'Course_Category_Code__c',
                'Course_Start_Date__c',
                'Course_End_Date__c',
                'Session_Time_min__c',
                'Enrollment_Date__c'
            ])
            .sort({ LastModifiedDate: -1 })
            .limit(5)
            .execute();

        console.log('📋 Sample of recently updated courses:');
        verificationCourses.forEach((course, index) => {
            console.log(`   ${index + 1}. ${course.Course_Name__c}`);
            console.log(`      Category Code: ${course.Course_Category_Code__c || 'N/A'}`);
            console.log(`      Start Date: ${course.Course_Start_Date__c || 'N/A'}`);
            console.log(`      End Date: ${course.Course_End_Date__c || 'N/A'}`);
            console.log(`      Session Time: ${course.Session_Time_min__c || 'N/A'} min`);
            console.log(`      Enrollment Date: ${course.Enrollment_Date__c || 'N/A'}`);
        });

        // Final summary
        const endTime = Date.now();
        const duration = Math.round((endTime - startTime) / 1000);

        console.log('\n🎯 BATCH UPDATE COMPLETED!');
        console.log('=' .repeat(70));
        console.log(`📊 FINAL STATISTICS:`);
        console.log(`   Total courses in Salesforce: ${stats.totalCourses}`);
        console.log(`   Courses processed: ${stats.processedCourses}`);
        console.log(`   Courses updated: ${stats.updatedCourses}`);
        console.log(`   Courses skipped: ${stats.skippedCourses}`);
        console.log(`   Courses with errors: ${stats.errorCourses}`);
        console.log(`   Batches processed: ${stats.batchesProcessed}`);
        console.log(`   Total duration: ${duration} seconds`);
        console.log('=' .repeat(70));

        if (stats.updatedCourses > 0) {
            console.log('✅ SUCCESS: Missing course fields have been populated!');
        } else {
            console.log('ℹ️ INFO: No courses needed updates (all fields already populated)');
        }

        return stats;

    } catch (error) {
        console.error('❌ BATCH UPDATE FAILED:', error);
        throw error;
    }
}

// Run the batch update
if (require.main === module) {
    batchUpdateCoursesWithMissingFields()
        .then(stats => {
            console.log('\n🎉 Batch update process completed successfully!');
            process.exit(0);
        })
        .catch(error => {
            console.error('\n💥 Batch update process failed:', error);
            process.exit(1);
        });
}

module.exports = { batchUpdateCoursesWithMissingFields };
