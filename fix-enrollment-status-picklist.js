require('dotenv').config();
const getConnection = require('./platform/salesforce/common/getConnection');

async function fixEnrollmentStatusPicklist() {
    try {
        console.log('🔧 Fixing Course Enrollment Status Picklist Issue');
        console.log('=' .repeat(60));
        
        const conn = await getConnection();
        if (!conn || !conn.accessToken) {
            console.error("❌ Invalid Salesforce connection");
            return;
        }

        // Step 1: Check the Status__c field picklist values
        console.log('\n📋 CHECKING STATUS__C PICKLIST VALUES...');
        console.log('-'.repeat(50));
        
        try {
            const enrollmentObjectDesc = await conn.sobject("Docebo_CourseEnrollment__c").describe();
            
            const statusField = enrollmentObjectDesc.fields.find(f => f.name === 'Status__c');
            
            if (statusField && statusField.picklistValues) {
                console.log('✅ Valid Status__c picklist values:');
                statusField.picklistValues.forEach(value => {
                    console.log(`   • "${value.value}" - ${value.label} ${value.active ? '(Active)' : '(Inactive)'}`);
                });
                
                const validValues = statusField.picklistValues
                    .filter(v => v.active)
                    .map(v => v.value);
                
                console.log('\n📊 Active picklist values:', validValues);
                
                // Step 2: Create mapping function for Docebo status values
                console.log('\n🔧 CREATING STATUS MAPPING FUNCTION...');
                console.log('-'.repeat(50));
                
                const statusMapping = {
                    'subscribed': 'A', // Map to Active if available
                    'enrolled': 'A',
                    'active': 'A', 
                    'completed': 'C', // Map to Completed if available
                    'finished': 'C',
                    'passed': 'C',
                    'failed': 'F', // Map to Failed if available
                    'suspended': 'S', // Map to Suspended if available
                    'cancelled': 'X', // Map to Cancelled if available
                    'unenrolled': 'U', // Map to Unenrolled if available
                    'withdrawn': 'W' // Map to Withdrawn if available
                };
                
                console.log('📝 Proposed status mapping:');
                Object.entries(statusMapping).forEach(([doceboValue, salesforceValue]) => {
                    const isValid = validValues.includes(salesforceValue);
                    console.log(`   ${doceboValue} → ${salesforceValue} ${isValid ? '✅' : '❌'}`);
                });
                
                // Step 3: Find a safe default value
                const safeDefaults = ['A', 'Active', 'Enrolled', 'Open'];
                const defaultValue = safeDefaults.find(val => validValues.includes(val)) || validValues[0];
                
                console.log(`\n🛡️ Safe default value: "${defaultValue}"`);
                
                // Step 4: Generate the mapping function code
                console.log('\n📄 GENERATED MAPPING FUNCTION:');
                console.log('-'.repeat(50));
                
                const mappingFunction = `
function mapEnrollmentStatus(doceboStatus) {
    // Convert to lowercase for case-insensitive matching
    const status = (doceboStatus || "").toLowerCase().trim();
    
    // Status mapping from Docebo to Salesforce picklist values
    const statusMap = {
        'subscribed': '${defaultValue}',
        'enrolled': '${defaultValue}',
        'active': '${defaultValue}',
        'completed': '${validValues.includes('C') ? 'C' : defaultValue}',
        'finished': '${validValues.includes('C') ? 'C' : defaultValue}',
        'passed': '${validValues.includes('C') ? 'C' : defaultValue}',
        'failed': '${validValues.includes('F') ? 'F' : defaultValue}',
        'suspended': '${validValues.includes('S') ? 'S' : defaultValue}',
        'cancelled': '${validValues.includes('X') ? 'X' : defaultValue}',
        'unenrolled': '${validValues.includes('U') ? 'U' : defaultValue}',
        'withdrawn': '${validValues.includes('W') ? 'W' : defaultValue}'
    };
    
    // Return mapped value or default
    return statusMap[status] || '${defaultValue}';
}`;
                
                console.log(mappingFunction);
                
                // Step 5: Test the mapping with problematic values
                console.log('\n🧪 TESTING STATUS MAPPING...');
                console.log('-'.repeat(50));
                
                const testStatuses = ['subscribed', 'enrolled', 'completed', 'failed', 'unknown'];
                
                // Create the actual mapping function
                const mapEnrollmentStatus = (doceboStatus) => {
                    const status = (doceboStatus || "").toLowerCase().trim();
                    const statusMap = {
                        'subscribed': defaultValue,
                        'enrolled': defaultValue,
                        'active': defaultValue,
                        'completed': validValues.includes('C') ? 'C' : defaultValue,
                        'finished': validValues.includes('C') ? 'C' : defaultValue,
                        'passed': validValues.includes('C') ? 'C' : defaultValue,
                        'failed': validValues.includes('F') ? 'F' : defaultValue,
                        'suspended': validValues.includes('S') ? 'S' : defaultValue,
                        'cancelled': validValues.includes('X') ? 'X' : defaultValue,
                        'unenrolled': validValues.includes('U') ? 'U' : defaultValue,
                        'withdrawn': validValues.includes('W') ? 'W' : defaultValue
                    };
                    return statusMap[status] || defaultValue;
                };
                
                testStatuses.forEach(testStatus => {
                    const mappedValue = mapEnrollmentStatus(testStatus);
                    const isValid = validValues.includes(mappedValue);
                    console.log(`   "${testStatus}" → "${mappedValue}" ${isValid ? '✅' : '❌'}`);
                });
                
                return {
                    success: true,
                    validValues,
                    defaultValue,
                    mappingFunction: mapEnrollmentStatus
                };
                
            } else {
                console.log('❌ Status__c field not found or not a picklist');
                return { success: false, error: 'Status field not found' };
            }
            
        } catch (describeError) {
            console.error('❌ Error describing Docebo_CourseEnrollment__c object:', describeError);
            return { success: false, error: describeError.message };
        }

    } catch (error) {
        console.error('💥 Error in enrollment status picklist fix:', error);
        return {
            success: false,
            error: error.message
        };
    }
}

// Execute the fix
console.log('🔄 Starting enrollment status picklist fix...');
fixEnrollmentStatusPicklist()
    .then((result) => {
        console.log('\n✅ Enrollment status picklist analysis completed');
        if (result.success) {
            console.log('\n🎯 NEXT STEPS:');
            console.log('1. Update enrollment creation code with status mapping function');
            console.log('2. Replace hardcoded "A" with proper mapping');
            console.log('3. Handle "subscribed" and other Docebo status values');
            console.log('4. Test with actual enrollment data');
        } else {
            console.log('❌ Analysis failed. Check the logs above.');
        }
        process.exit(0);
    })
    .catch(err => {
        console.error('💥 Analysis failed:', err);
        process.exit(1);
    });
