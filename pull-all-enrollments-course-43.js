require('dotenv').config();
const getConnection = require('./platform/salesforce/common/getConnection');
const getApiData = require("./common/docebo/fetcher");
const APP_BASE = process.env.DOCEBO_API_BASE_URL;

async function pullAllEnrollmentsCourse43() {
    try {
        console.log('🔄 PULLING ALL ENROLLMENTS TO FIND COURSE 43');
        console.log('=' .repeat(80));
        console.log('🎯 Strategy: Get ALL enrollments and filter for course 43 in the data');
        
        const conn = await getConnection();
        if (!conn || !conn.accessToken) {
            throw new Error('Failed to connect to Salesforce');
        }

        console.log('✅ Connected to Salesforce successfully');

        // Step 1: Get course information
        console.log('\n📚 STEP 1: Getting Course Information...');
        console.log('-'.repeat(50));
        
        const sfCourse = await conn.sobject("Docebo_Course__c")
            .findOne({ Course_External_Id__c: 43 });
            
        if (!sfCourse) {
            throw new Error('Course 43 not found in Salesforce');
        }
        
        console.log(`✅ Course found: ${sfCourse.Course_Name__c}`);
        console.log(`   Course ID: ${sfCourse.Id}`);

        // Step 2: Get existing Salesforce enrollments
        console.log('\n📊 STEP 2: Getting Existing Salesforce Enrollments...');
        console.log('-'.repeat(50));
        
        const existingEnrollments = await conn.sobject("Docebo_CourseEnrollment__c")
            .find({ Course__c: sfCourse.Id })
            .execute();
            
        const existingEnrollmentIds = new Set();
        existingEnrollments.forEach(enrollment => {
            if (enrollment.Enrollment_ID__c) {
                existingEnrollmentIds.add(enrollment.Enrollment_ID__c);
                
                // Handle both old and new enrollment ID patterns
                if (enrollment.Enrollment_ID__c.startsWith('UE-43-')) {
                    const userId = enrollment.Enrollment_ID__c.replace('UE-43-', '');
                    existingEnrollmentIds.add(`43-${userId}`);
                } else if (enrollment.Enrollment_ID__c.startsWith('43-')) {
                    const userId = enrollment.Enrollment_ID__c.replace('43-', '');
                    existingEnrollmentIds.add(`UE-43-${userId}`);
                }
            }
        });
        
        console.log(`Found ${existingEnrollments.length} existing enrollments in Salesforce`);

        // Step 3: Pull ALL enrollments from Docebo
        console.log('\n🔍 STEP 3: Pulling ALL Enrollments from Docebo...');
        console.log('-'.repeat(50));
        
        let allEnrollments = [];
        let course43Enrollments = [];
        let page = 1;
        let hasMoreData = true;
        let totalProcessed = 0;
        
        console.log('📡 Fetching all enrollments (this may take a while)...');
        
        while (hasMoreData) {
            console.log(`   📄 Fetching page ${page}...`);
            
            try {
                const response = await getApiData(
                    'GET', 
                    `${APP_BASE}/learn/v1/enrollments?page=${page}&page_size=200`, 
                    null
                );
                
                if (response && response.status === 200) {
                    const items = response.data?.items || [];
                    allEnrollments.push(...items);
                    totalProcessed += items.length;
                    
                    // Look for course 43 enrollments in multiple ways
                    const course43Items = items.filter(item => {
                        // Check various fields that might contain course 43 identifier
                        return (
                            item.id == 43 ||
                            item.course_id == 43 ||
                            (item.url && item.url.includes('/course/43/')) ||
                            (item.uidCourse && (item.uidCourse.includes('43') || item.uidCourse === 'E-J0E815')) ||
                            (item.name && (
                                item.name.includes('Welcome to The Training Hub') ||
                                item.name === 'Welcome to The Training Hub!' ||
                                item.name.toLowerCase().includes('training hub')
                            )) ||
                            (item.slug && item.slug.includes('training-hub')) ||
                            (item.description && item.description.toLowerCase().includes('training hub'))
                        );
                    });
                    
                    if (course43Items.length > 0) {
                        course43Enrollments.push(...course43Items);
                        console.log(`      🎯 Found ${course43Items.length} course 43 enrollments! (Total C43: ${course43Enrollments.length})`);
                        
                        // Log details of first match for verification
                        if (course43Enrollments.length <= 5) {
                            const sample = course43Items[0];
                            console.log(`         Sample: "${sample.name}" (ID: ${sample.id}, URL: ${sample.url})`);
                        }
                    }
                    
                    console.log(`      Found ${items.length} total enrollments (Page total: ${totalProcessed}, Course 43: ${course43Enrollments.length})`);
                    
                    hasMoreData = response.data?.has_more_data || false;
                    if (items.length === 0) hasMoreData = false;
                    
                    // Stop if we've found a reasonable number of course 43 enrollments
                    if (course43Enrollments.length >= 5544) {
                        console.log(`   🎯 Found target number of course 43 enrollments (${course43Enrollments.length})`);
                        hasMoreData = false;
                    }
                    
                    page++;
                    
                    // Progress indicator every 10 pages
                    if (page % 10 === 0) {
                        console.log(`   📊 Progress: ${page} pages, ${totalProcessed} total enrollments, ${course43Enrollments.length} course 43 found`);
                    }
                    
                } else {
                    console.log(`      No data on page ${page}`);
                    hasMoreData = false;
                }
            } catch (pageError) {
                console.log(`      ❌ Error on page ${page}: ${pageError.message}`);
                hasMoreData = false;
            }
        }
        
        console.log(`\n✅ Enrollment fetch completed:`);
        console.log(`   Total enrollments processed: ${totalProcessed.toLocaleString()}`);
        console.log(`   Course 43 enrollments found: ${course43Enrollments.length.toLocaleString()}`);
        
        if (course43Enrollments.length === 0) {
            console.log('\n❌ No course 43 enrollments found in the entire dataset');
            
            // Show some sample course data to help debug
            console.log('\n🔍 Sample enrollment data for debugging:');
            if (allEnrollments.length > 0) {
                const samples = allEnrollments.slice(0, 3);
                samples.forEach((sample, index) => {
                    console.log(`\nSample ${index + 1}:`);
                    console.log(`   ID: ${sample.id}`);
                    console.log(`   Name: "${sample.name}"`);
                    console.log(`   URL: ${sample.url}`);
                    console.log(`   UID Course: ${sample.uidCourse}`);
                    console.log(`   Course ID: ${sample.course_id}`);
                });
            }
            
            return { success: false, error: 'No course 43 enrollments found' };
        }

        // Step 4: Identify missing enrollments
        console.log('\n🔍 STEP 4: Identifying Missing Enrollments...');
        console.log('-'.repeat(50));
        
        const missingEnrollments = [];
        
        for (const doceboEnrollment of course43Enrollments) {
            const newFormatId = `43-${doceboEnrollment.user_id}`;
            const oldFormatId = `UE-43-${doceboEnrollment.user_id}`;
            
            if (!existingEnrollmentIds.has(newFormatId) && !existingEnrollmentIds.has(oldFormatId)) {
                missingEnrollments.push(doceboEnrollment);
            }
        }
        
        console.log(`Found ${missingEnrollments.length} missing enrollments to sync`);

        if (missingEnrollments.length === 0) {
            console.log('✅ All enrollments are already synced!');
            return { 
                success: true, 
                synced: 0, 
                total: course43Enrollments.length,
                existing: existingEnrollments.length,
                totalProcessed: totalProcessed
            };
        }

        // Step 5: Get user mappings
        console.log('\n👥 STEP 5: Getting User Mappings...');
        console.log('-'.repeat(50));
        
        const userIds = [...new Set(missingEnrollments.map(e => e.user_id))];
        console.log(`Need to map ${userIds.length} unique users`);
        
        const userMappings = new Map();
        
        // Batch query users
        const chunkSize = 100;
        for (let i = 0; i < userIds.length; i += chunkSize) {
            const chunk = userIds.slice(i, i + chunkSize);
            
            const users = await conn.sobject("Docebo_Users__c")
                .find({ User_Unique_Id__c: { $in: chunk } })
                .execute();
                
            users.forEach(user => {
                userMappings.set(user.User_Unique_Id__c, user.Id);
            });
            
            console.log(`   Mapped ${users.length} users from chunk ${Math.floor(i/chunkSize) + 1}/${Math.ceil(userIds.length/chunkSize)}`);
        }
        
        console.log(`✅ Successfully mapped ${userMappings.size} users`);

        // Step 6: Prepare enrollment records
        console.log('\n📝 STEP 6: Preparing Enrollment Records...');
        console.log('-'.repeat(50));
        
        const enrollmentsToCreate = [];
        let skippedCount = 0;
        
        for (const doceboEnrollment of missingEnrollments) {
            const salesforceUserId = userMappings.get(doceboEnrollment.user_id);
            
            if (!salesforceUserId) {
                skippedCount++;
                continue;
            }
            
            const enrollmentRecord = {
                Course__c: sfCourse.Id,
                Docebo_User__c: salesforceUserId,
                Enrollment_ID__c: `43-${doceboEnrollment.user_id}`,
                Status__c: doceboEnrollment.status || 'enrolled',
                Enrollment_Date__c: doceboEnrollment.enroll_date_of_enrollment || new Date().toISOString(),
                Completion__c: doceboEnrollment.completion_percentage || 0,
                Score__c: doceboEnrollment.score || 0,
                Credits__c: doceboEnrollment.credits || 0,
                Time_in_course__c: doceboEnrollment.total_time || 0,
                First_Access__c: doceboEnrollment.date_first_access || null,
                Last_Access__c: doceboEnrollment.date_last_access || null,
                Completed_Learning_Objects__c: 0
            };
            
            enrollmentsToCreate.push(enrollmentRecord);
        }
        
        console.log(`Prepared ${enrollmentsToCreate.length} enrollment records`);
        console.log(`Skipped ${skippedCount} enrollments (users not found)`);

        // Step 7: Create enrollments in batches
        console.log('\n💾 STEP 7: Creating Enrollments in Salesforce...');
        console.log('-'.repeat(50));
        
        let successCount = 0;
        let errorCount = 0;
        const batchSize = 50;
        const totalBatches = Math.ceil(enrollmentsToCreate.length / batchSize);
        
        for (let i = 0; i < enrollmentsToCreate.length; i += batchSize) {
            const batch = enrollmentsToCreate.slice(i, i + batchSize);
            const batchNum = Math.floor(i / batchSize) + 1;
            
            console.log(`   📦 Processing batch ${batchNum}/${totalBatches} (${batch.length} records)...`);
            
            try {
                const results = await conn.sobject("Docebo_CourseEnrollment__c")
                    .create(batch);
                    
                const resultArray = Array.isArray(results) ? results : [results];
                
                let batchSuccessCount = 0;
                resultArray.forEach((result) => {
                    if (result.success) {
                        successCount++;
                        batchSuccessCount++;
                    } else {
                        errorCount++;
                    }
                });
                
                console.log(`      ✅ Batch ${batchNum}: ${batchSuccessCount} success, ${resultArray.length - batchSuccessCount} errors`);
                
                const progressPercent = ((i + batch.length) / enrollmentsToCreate.length * 100).toFixed(1);
                console.log(`      📊 Progress: ${progressPercent}% (${successCount} created)`);
                
                if (batchNum < totalBatches) {
                    await new Promise(resolve => setTimeout(resolve, 1000));
                }
                
            } catch (batchError) {
                console.error(`      ❌ Batch ${batchNum} failed:`, batchError.message);
                errorCount += batch.length;
            }
        }

        // Step 8: Final verification
        console.log('\n🔍 STEP 8: Final Verification...');
        console.log('-'.repeat(50));
        
        const finalEnrollments = await conn.sobject("Docebo_CourseEnrollment__c")
            .find({ Course__c: sfCourse.Id })
            .execute();

        return {
            success: true,
            doceboTotal: course43Enrollments.length,
            salesforceInitial: existingEnrollments.length,
            salesforceFinal: finalEnrollments.length,
            synced: successCount,
            errors: errorCount,
            skipped: skippedCount,
            totalProcessed: totalProcessed
        };

    } catch (error) {
        console.error('💥 Error in pull all enrollments:', error);
        return {
            success: false,
            error: error.message
        };
    }
}

// Execute the pull all enrollments
console.log('🔄 Starting Pull All Enrollments for Course 43...');
pullAllEnrollmentsCourse43()
    .then((result) => {
        console.log('\n📋 PULL ALL ENROLLMENTS SUMMARY:');
        console.log('=' .repeat(60));
        
        if (result.success) {
            console.log(`📊 Total API Records Processed: ${result.totalProcessed?.toLocaleString() || 'N/A'}`);
            console.log(`📊 Course 43 Enrollments Found: ${result.doceboTotal.toLocaleString()}`);
            console.log(`📊 Salesforce Initial: ${result.salesforceInitial.toLocaleString()}`);
            console.log(`📊 Salesforce Final: ${result.salesforceFinal.toLocaleString()}`);
            console.log(`✅ Successfully Synced: ${result.synced.toLocaleString()}`);
            
            if (result.errors > 0) {
                console.log(`❌ Errors: ${result.errors.toLocaleString()}`);
            }
            if (result.skipped > 0) {
                console.log(`⏭️ Skipped: ${result.skipped.toLocaleString()}`);
            }
            
            const netIncrease = result.salesforceFinal - result.salesforceInitial;
            console.log(`\n🎉 NET RESULT: ${netIncrease.toLocaleString()} new enrollments added!`);
            
            const expectedCount = 5544;
            const successPercentage = (result.salesforceFinal / expectedCount * 100).toFixed(1);
            console.log(`📊 Success Rate: ${successPercentage}% of expected ${expectedCount} enrollments`);
            
            if (result.salesforceFinal >= expectedCount * 0.9) {
                console.log(`✅ EXCELLENT: Achieved 90%+ of expected enrollments!`);
            } else if (result.doceboTotal === 0) {
                console.log(`⚠️ ISSUE: No course 43 enrollments found in API data`);
            } else {
                console.log(`⚠️ PARTIAL: Some enrollments still missing - likely user sync issues`);
            }
            
        } else {
            console.log(`❌ Pull all enrollments failed: ${result.error}`);
        }
        
        console.log('\n✅ Pull all enrollments completed');
        process.exit(0);
    })
    .catch(err => {
        console.error('💥 Pull all enrollments failed:', err);
        process.exit(1);
    });
