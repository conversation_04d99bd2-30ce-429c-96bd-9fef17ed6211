require('dotenv').config();
const getConnection = require('./platform/salesforce/common/getConnection');

async function checkStatusPicklistValues() {
    try {
        console.log('🔍 Checking Status__c Picklist Values');
        console.log('=' .repeat(50));
        
        const conn = await getConnection();
        if (!conn || !conn.accessToken) {
            console.error("❌ Invalid Salesforce connection");
            return;
        }

        // Describe the Docebo_CourseEnrollment__c object to get picklist values
        const describe = await conn.sobject("Docebo_CourseEnrollment__c").describe();
        
        // Find the Status__c field
        const statusField = describe.fields.find(field => field.name === 'Status__c');
        
        if (statusField && statusField.picklistValues) {
            console.log('📋 Valid Status__c picklist values:');
            statusField.picklistValues.forEach((value, index) => {
                console.log(`   ${index + 1}. "${value.value}" - ${value.label} ${value.active ? '✅' : '❌'}`);
            });
            
            // Check what status values are currently being used
            console.log('\n📊 Current status values in use:');
            const enrollments = await conn.sobject("Docebo_CourseEnrollment__c")
                .find({})
                .limit(100)
                .execute();
            
            const statusCounts = {};
            enrollments.forEach(enrollment => {
                const status = enrollment.Status__c || 'NULL';
                statusCounts[status] = (statusCounts[status] || 0) + 1;
            });
            
            Object.entries(statusCounts).forEach(([status, count]) => {
                console.log(`   "${status}": ${count} records`);
            });
            
            // Recommend the correct status value to use
            console.log('\n💡 RECOMMENDATION:');
            const activeValues = statusField.picklistValues.filter(v => v.active);
            if (activeValues.length > 0) {
                console.log(`Use one of these active values instead of "Enrolled":`);
                activeValues.forEach(value => {
                    console.log(`   - "${value.value}"`);
                });
            }
            
        } else {
            console.log('❌ Status__c field not found or not a picklist');
        }

    } catch (error) {
        console.error('💥 Error checking picklist values:', error);
    }
}

// Execute the check
checkStatusPicklistValues()
    .then(() => {
        console.log('\n✅ Check completed');
        process.exit(0);
    })
    .catch(err => {
        console.error('💥 Check failed:', err);
        process.exit(1);
    });
