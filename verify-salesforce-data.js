require('dotenv').config();
const getConnection = require('./platform/salesforce/common/getConnection');

async function verifySalesforceData() {
  try {
    console.log('🔍 Verifying instructor data in Salesforce...');
    
    const conn = await getConnection();
    if (!conn || !conn.accessToken) {
      console.error("Invalid Salesforce connection");
      return;
    }

    // 1. Check total instructor records
    console.log('\n📊 Checking Instructor__c records...');
    const instructorCount = await conn.sobject("Instructor__c")
      .count();
    console.log(`✅ Total Instructor__c records: ${instructorCount}`);

    // 2. Get sample instructor records
    console.log('\n👥 Sample Instructor records:');
    const sampleInstructors = await conn.sobject("Instructor__c")
      .find({}, 'Id, Name, First_Name__c, Last_Name__c, Email__c')
      .limit(10)
      .execute();
      
    sampleInstructors.forEach((instructor, index) => {
      console.log(`${index + 1}. ${instructor.Id}: ${instructor.First_Name__c} ${instructor.Last_Name__c} (UserID: ${instructor.Name})`);
      if (instructor.Email__c) {
        console.log(`   Email: ${instructor.Email__c}`);
      }
    });

    // 3. Check sessions with instructor associations
    console.log('\n📅 Checking sessions with instructor associations...');
    const sessionsWithInstructors = await conn.sobject("Docebo_Session__c")
      .find({ Instructor__c: { $ne: null } }, 'Id, Session_Name__c, Instructor__c')
      .limit(10)
      .execute();
      
    console.log(`✅ Sessions with instructor associations: ${sessionsWithInstructors.length} (showing first 10)`);
    
    for (const session of sessionsWithInstructors) {
      // Get instructor details for this session
      const instructor = await conn.sobject("Instructor__c")
        .findOne({ Id: session.Instructor__c }, 'First_Name__c, Last_Name__c, Name');
        
      console.log(`📖 Session ${session.Id}: ${session.Session_Name__c || 'Unnamed'}`);
      console.log(`   👤 Instructor: ${instructor.First_Name__c} ${instructor.Last_Name__c} (${instructor.Name})`);
      console.log(`   🔗 Lookup ID: ${session.Instructor__c}`);
      console.log('');
    }

    // 4. Check our specific test case (Course 557, Session 247)
    console.log('\n🎯 Checking our original test case (Course 557, Session 247)...');
    const testSession = await conn.sobject("Docebo_Session__c")
      .findOne({ Session_External_ID__c: '247' }, 'Id, Session_Name__c, Instructor__c');
      
    if (testSession) {
      console.log(`✅ Test session found: ${testSession.Id}`);
      console.log(`   Name: ${testSession.Session_Name__c}`);
      
      if (testSession.Instructor__c) {
        const testInstructor = await conn.sobject("Instructor__c")
          .findOne({ Id: testSession.Instructor__c }, 'First_Name__c, Last_Name__c, Name, Email__c');
          
        console.log(`   👤 Associated Instructor: ${testInstructor.First_Name__c} ${testInstructor.Last_Name__c}`);
        console.log(`   📧 Email: ${testInstructor.Email__c || 'N/A'}`);
        console.log(`   🆔 User ID: ${testInstructor.Name}`);
        console.log(`   🔗 Instructor Record ID: ${testSession.Instructor__c}`);
      } else {
        console.log('   ❌ No instructor associated');
      }
    } else {
      console.log('❌ Test session not found');
    }

    // 5. Summary statistics
    console.log('\n📈 Summary Statistics:');
    
    const totalSessions = await conn.sobject("Docebo_Session__c").count();
    const sessionsWithInstructorCount = await conn.sobject("Docebo_Session__c")
      .count({ Instructor__c: { $ne: null } });
      
    console.log(`   Total Sessions: ${totalSessions}`);
    console.log(`   Sessions with Instructors: ${sessionsWithInstructorCount}`);
    console.log(`   Total Instructors: ${instructorCount}`);
    
    const associationRate = totalSessions > 0 ? 
      ((sessionsWithInstructorCount / totalSessions) * 100).toFixed(1) : 0;
    console.log(`   Association Rate: ${associationRate}%`);

    console.log('\n🎉 Verification completed!');
    console.log('✅ Instructors and associations are successfully stored in Salesforce');

  } catch (error) {
    console.error('💥 Error verifying Salesforce data:', error);
  }
}

verifySalesforceData()
  .then(() => {
    console.log('\n✅ Verification script completed');
    process.exit(0);
  })
  .catch(err => {
    console.error('💥 Verification script failed:', err);
    process.exit(1);
  });
