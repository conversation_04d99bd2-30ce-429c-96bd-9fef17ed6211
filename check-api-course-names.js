require('dotenv').config();
const getApiData = require("./common/docebo/fetcher");
const APP_BASE = process.env.DOCEBO_API_BASE_URL;

async function checkAPICourseNames() {
    try {
        console.log('🔍 CHECKING ACTUAL COURSE NAMES FROM DOCEBO API');
        console.log('=' .repeat(60));
        
        // Get first few pages of enrollments to see what course names look like
        const courseNames = new Set();
        
        for (let page = 1; page <= 5; page++) {
            console.log(`📄 Checking page ${page}...`);
            
            try {
                const response = await getApiData(
                    'GET', 
                    `${APP_BASE}/learn/v1/enrollments?page=${page}&page_size=50`, 
                    null
                );
                
                if (response && response.status === 200) {
                    const items = response.data?.items || [];
                    
                    items.forEach(item => {
                        if (item.name) {
                            courseNames.add(item.name);
                        }
                    });
                    
                    console.log(`   Found ${items.length} enrollments`);
                } else {
                    console.log(`   No data on page ${page}`);
                    break;
                }
            } catch (pageError) {
                console.log(`   Error on page ${page}: ${pageError.message}`);
                break;
            }
        }
        
        console.log('\n📋 UNIQUE COURSE NAMES FOUND:');
        console.log('-'.repeat(50));
        
        const sortedNames = Array.from(courseNames).sort();
        sortedNames.forEach((name, index) => {
            console.log(`${index + 1}. "${name}"`);
            
            // Check if this might be our target course
            if (name.toLowerCase().includes('training hub') || 
                name.toLowerCase().includes('welcome') ||
                name.includes('43')) {
                console.log(`   🎯 POTENTIAL MATCH: "${name}"`);
            }
        });
        
        console.log(`\n📊 Total unique course names: ${courseNames.size}`);
        
        // Look specifically for variations of the course name
        console.log('\n🔍 SEARCHING FOR TRAINING HUB VARIATIONS:');
        console.log('-'.repeat(50));
        
        const trainingHubVariations = sortedNames.filter(name => 
            name.toLowerCase().includes('training') ||
            name.toLowerCase().includes('hub') ||
            name.toLowerCase().includes('welcome')
        );
        
        if (trainingHubVariations.length > 0) {
            console.log('Found potential matches:');
            trainingHubVariations.forEach((name, index) => {
                console.log(`   ${index + 1}. "${name}"`);
            });
        } else {
            console.log('❌ No courses found with "training", "hub", or "welcome" in the name');
        }
        
        // Also check if we can find course 43 by looking at the full enrollment data
        console.log('\n🔍 CHECKING ENROLLMENT DATA STRUCTURE:');
        console.log('-'.repeat(50));
        
        const response = await getApiData(
            'GET', 
            `${APP_BASE}/learn/v1/enrollments?page=1&page_size=3`, 
            null
        );
        
        if (response && response.data?.items && response.data.items.length > 0) {
            const sample = response.data.items[0];
            console.log('Sample enrollment structure:');
            Object.keys(sample).forEach(key => {
                console.log(`   ${key}: ${sample[key]}`);
            });
        }
        
        return {
            success: true,
            totalCourseNames: courseNames.size,
            trainingHubMatches: trainingHubVariations.length
        };

    } catch (error) {
        console.error('💥 Error checking course names:', error);
        return {
            success: false,
            error: error.message
        };
    }
}

// Execute the check
console.log('🔄 Starting API Course Names Check...');
checkAPICourseNames()
    .then((result) => {
        console.log('\n📋 SUMMARY:');
        console.log('=' .repeat(40));
        
        if (result.success) {
            console.log(`✅ Found ${result.totalCourseNames} unique course names`);
            console.log(`🎯 Found ${result.trainingHubMatches} potential Training Hub matches`);
            
            if (result.trainingHubMatches === 0) {
                console.log('\n💡 RECOMMENDATIONS:');
                console.log('1. The course name in the API might be different from Salesforce');
                console.log('2. Course 43 enrollments might not be in the enrollment API');
                console.log('3. Try using a different API endpoint');
                console.log('4. Check if course 43 is active/published in Docebo');
            }
        } else {
            console.log(`❌ Check failed: ${result.error}`);
        }
        
        console.log('\n✅ Check completed');
        process.exit(0);
    })
    .catch(err => {
        console.error('💥 Check failed:', err);
        process.exit(1);
    });
