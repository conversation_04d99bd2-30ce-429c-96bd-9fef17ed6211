require('dotenv').config();
const getConnection = require('./platform/salesforce/common/getConnection');
const getApiData = require("./common/docebo/fetcher");
const APP_BASE = process.env.DOCEBO_API_BASE_URL;

async function completeCourse43BulkSync() {
    try {
        console.log('🚀 COMPLETE COURSE 43 BULK SYNC - USING CORRECT API ENDPOINT');
        console.log('=' .repeat(80));
        console.log('🎯 Target: Fetch ALL course 43 enrollments and sync to Salesforce');
        console.log('📡 API: course/v1/courses/enrollments (verified working)');
        
        const conn = await getConnection();
        if (!conn || !conn.accessToken) {
            throw new Error('Failed to connect to Salesforce');
        }

        console.log('✅ Connected to Salesforce successfully');

        // Step 1: Get course information
        console.log('\n📚 STEP 1: Getting Course Information...');
        console.log('-'.repeat(50));
        
        const sfCourse = await conn.sobject("Docebo_Course__c")
            .findOne({ Course_External_Id__c: 43 });
            
        if (!sfCourse) {
            throw new Error('Course 43 not found in Salesforce');
        }
        
        console.log(`✅ Course found: ${sfCourse.Course_Name__c}`);
        console.log(`   Course ID: ${sfCourse.Id}`);

        // Step 2: Get existing Salesforce enrollments
        console.log('\n📊 STEP 2: Getting Existing Salesforce Enrollments...');
        console.log('-'.repeat(50));
        
        const existingEnrollments = await conn.sobject("Docebo_CourseEnrollment__c")
            .find({ Course__c: sfCourse.Id })
            .execute();
            
        const existingEnrollmentIds = new Set();
        existingEnrollments.forEach(enrollment => {
            if (enrollment.Enrollment_ID__c) {
                existingEnrollmentIds.add(enrollment.Enrollment_ID__c);
                
                // Handle both old and new enrollment ID patterns
                if (enrollment.Enrollment_ID__c.startsWith('UE-43-')) {
                    const userId = enrollment.Enrollment_ID__c.replace('UE-43-', '');
                    existingEnrollmentIds.add(`43-${userId}`);
                } else if (enrollment.Enrollment_ID__c.startsWith('43-')) {
                    const userId = enrollment.Enrollment_ID__c.replace('43-', '');
                    existingEnrollmentIds.add(`UE-43-${userId}`);
                }
            }
        });
        
        console.log(`Found ${existingEnrollments.length} existing enrollments in Salesforce`);

        // Step 3: Fetch ALL course 43 enrollments from Docebo
        console.log('\n🔍 STEP 3: Fetching ALL Course 43 Enrollments from Docebo...');
        console.log('-'.repeat(50));
        
        let allCourse43Enrollments = [];
        let page = 1;
        let hasMoreData = true;
        let totalProcessed = 0;
        
        console.log('📡 Using correct endpoint: course/v1/courses/enrollments');
        console.log('🔄 This may take several minutes to fetch all enrollments...');
        
        while (hasMoreData) {
            console.log(`   📄 Fetching page ${page}...`);
            
            try {
                const response = await getApiData(
                    'GET', 
                    `${APP_BASE}/course/v1/courses/enrollments?page=${page}&page_size=200`, 
                    null
                );
                
                if (response && response.status === 200) {
                    const items = response.data?.items || [];
                    totalProcessed += items.length;
                    
                    // Filter for course 43 enrollments
                    const course43Items = items.filter(item => {
                        return (
                            item.course_id == 43 ||
                            item.id_course == 43 ||
                            (item.course && item.course.id == 43) ||
                            (item.course && item.course.course_id == 43) ||
                            (item.course_name && (
                                item.course_name.includes('Welcome to The Training Hub') ||
                                item.course_name === 'Welcome to The Training Hub!' ||
                                item.course_name.toLowerCase().includes('training hub')
                            ))
                        );
                    });
                    
                    if (course43Items.length > 0) {
                        allCourse43Enrollments.push(...course43Items);
                        console.log(`      🎯 Found ${course43Items.length} course 43 enrollments (Total: ${allCourse43Enrollments.length})`);
                    }
                    
                    console.log(`      Processed ${items.length} total enrollments (Page total: ${totalProcessed})`);
                    
                    hasMoreData = response.data?.has_more_data || false;
                    if (items.length === 0) hasMoreData = false;
                    
                    page++;
                    
                    // Progress indicator every 25 pages
                    if (page % 25 === 0) {
                        console.log(`   📊 Progress: Page ${page}, ${totalProcessed.toLocaleString()} total processed, ${allCourse43Enrollments.length.toLocaleString()} course 43 found`);
                        
                        // Estimate completion
                        if (allCourse43Enrollments.length > 0) {
                            const rate = allCourse43Enrollments.length / totalProcessed;
                            const estimatedTotal = Math.round(rate * 25000); // Rough estimate
                            console.log(`   📈 Estimated total course 43 enrollments: ~${estimatedTotal.toLocaleString()}`);
                        }
                    }
                    
                } else {
                    console.log(`      No data on page ${page}`);
                    hasMoreData = false;
                }
            } catch (pageError) {
                console.log(`      ❌ Error on page ${page}: ${pageError.message}`);
                
                // Continue to next page unless it's a critical error
                if (pageError.message.includes('401') || pageError.message.includes('403')) {
                    console.log('      🚨 Authentication error - stopping');
                    hasMoreData = false;
                } else {
                    console.log('      ⏭️ Continuing to next page...');
                    page++;
                }
            }
        }
        
        console.log(`\n✅ Docebo fetch completed:`);
        console.log(`   Total API records processed: ${totalProcessed.toLocaleString()}`);
        console.log(`   Course 43 enrollments found: ${allCourse43Enrollments.length.toLocaleString()}`);
        console.log(`   Expected from Docebo UI: 5,544`);
        console.log(`   Coverage: ${(allCourse43Enrollments.length / 5544 * 100).toFixed(1)}%`);

        if (allCourse43Enrollments.length === 0) {
            throw new Error('No course 43 enrollments found via API');
        }

        // Step 4: Identify missing enrollments
        console.log('\n🔍 STEP 4: Identifying Missing Enrollments...');
        console.log('-'.repeat(50));
        
        const missingEnrollments = [];
        
        for (const doceboEnrollment of allCourse43Enrollments) {
            const newFormatId = `43-${doceboEnrollment.user_id}`;
            const oldFormatId = `UE-43-${doceboEnrollment.user_id}`;
            
            if (!existingEnrollmentIds.has(newFormatId) && !existingEnrollmentIds.has(oldFormatId)) {
                missingEnrollments.push(doceboEnrollment);
            }
        }
        
        console.log(`Found ${missingEnrollments.length.toLocaleString()} missing enrollments to sync`);
        console.log(`Already synced: ${allCourse43Enrollments.length - missingEnrollments.length} enrollments`);

        if (missingEnrollments.length === 0) {
            console.log('✅ All enrollments are already synced!');
            return { 
                success: true, 
                synced: 0, 
                total: allCourse43Enrollments.length,
                existing: existingEnrollments.length,
                totalProcessed: totalProcessed
            };
        }

        // Step 5: Get user mappings
        console.log('\n👥 STEP 5: Getting User Mappings...');
        console.log('-'.repeat(50));
        
        const userIds = [...new Set(missingEnrollments.map(e => e.user_id))];
        console.log(`Need to map ${userIds.length.toLocaleString()} unique users`);
        
        const userMappings = new Map();
        
        // Batch query users in chunks
        const chunkSize = 100;
        for (let i = 0; i < userIds.length; i += chunkSize) {
            const chunk = userIds.slice(i, i + chunkSize);
            
            const users = await conn.sobject("Docebo_Users__c")
                .find({ User_Unique_Id__c: { $in: chunk } })
                .execute();
                
            users.forEach(user => {
                userMappings.set(user.User_Unique_Id__c, user.Id);
            });
            
            const chunkNum = Math.floor(i/chunkSize) + 1;
            const totalChunks = Math.ceil(userIds.length/chunkSize);
            console.log(`   Mapped ${users.length} users from chunk ${chunkNum}/${totalChunks} (Total mapped: ${userMappings.size})`);
        }
        
        console.log(`✅ Successfully mapped ${userMappings.size.toLocaleString()} users`);

        // Step 6: Prepare enrollment records
        console.log('\n📝 STEP 6: Preparing Enrollment Records...');
        console.log('-'.repeat(50));
        
        const enrollmentsToCreate = [];
        let skippedCount = 0;
        
        for (const doceboEnrollment of missingEnrollments) {
            const salesforceUserId = userMappings.get(doceboEnrollment.user_id);
            
            if (!salesforceUserId) {
                skippedCount++;
                continue;
            }
            
            // Parse enrollment date
            let enrollmentDate = null;
            if (doceboEnrollment.enrollment_date || doceboEnrollment.date_inscr || doceboEnrollment.enroll_date_of_enrollment) {
                const dateStr = doceboEnrollment.enrollment_date || doceboEnrollment.date_inscr || doceboEnrollment.enroll_date_of_enrollment;
                try {
                    enrollmentDate = new Date(dateStr).toISOString();
                } catch (e) {
                    enrollmentDate = new Date().toISOString();
                }
            } else {
                enrollmentDate = new Date().toISOString();
            }
            
            const enrollmentRecord = {
                Course__c: sfCourse.Id,
                Docebo_User__c: salesforceUserId,
                Enrollment_ID__c: `43-${doceboEnrollment.user_id}`,
                Status__c: doceboEnrollment.status || 'enrolled',
                Enrollment_Date__c: enrollmentDate,
                Completion__c: doceboEnrollment.completion_percentage || doceboEnrollment.completion || 0,
                Score__c: doceboEnrollment.score || 0,
                Credits__c: doceboEnrollment.credits || 0,
                Time_in_course__c: doceboEnrollment.total_time || doceboEnrollment.time_spent || 0,
                First_Access__c: doceboEnrollment.date_first_access || null,
                Last_Access__c: doceboEnrollment.date_last_access || doceboEnrollment.date_last_updated || null,
                Completed_Learning_Objects__c: 0
            };
            
            enrollmentsToCreate.push(enrollmentRecord);
        }
        
        console.log(`Prepared ${enrollmentsToCreate.length.toLocaleString()} enrollment records`);
        console.log(`Skipped ${skippedCount.toLocaleString()} enrollments (users not found in Salesforce)`);

        if (enrollmentsToCreate.length === 0) {
            console.log('⚠️ No enrollments to create (all users missing from Salesforce)');
            return {
                success: true,
                synced: 0,
                total: allCourse43Enrollments.length,
                existing: existingEnrollments.length,
                skipped: skippedCount,
                totalProcessed: totalProcessed
            };
        }

        // Step 7: Create enrollments in batches
        console.log('\n💾 STEP 7: Creating Enrollments in Salesforce...');
        console.log('-'.repeat(50));
        
        let successCount = 0;
        let errorCount = 0;
        const batchSize = 50;
        const totalBatches = Math.ceil(enrollmentsToCreate.length / batchSize);
        
        console.log(`Processing ${enrollmentsToCreate.length.toLocaleString()} enrollments in ${totalBatches} batches...`);
        
        for (let i = 0; i < enrollmentsToCreate.length; i += batchSize) {
            const batch = enrollmentsToCreate.slice(i, i + batchSize);
            const batchNum = Math.floor(i / batchSize) + 1;
            
            console.log(`   📦 Processing batch ${batchNum}/${totalBatches} (${batch.length} records)...`);
            
            try {
                const results = await conn.sobject("Docebo_CourseEnrollment__c")
                    .create(batch);
                    
                const resultArray = Array.isArray(results) ? results : [results];
                
                let batchSuccessCount = 0;
                let batchErrorCount = 0;
                
                resultArray.forEach((result) => {
                    if (result.success) {
                        successCount++;
                        batchSuccessCount++;
                    } else {
                        errorCount++;
                        batchErrorCount++;
                        console.log(`      ⚠️ Error: ${result.errors?.[0]?.message || 'Unknown error'}`);
                    }
                });
                
                console.log(`      ✅ Batch ${batchNum}: ${batchSuccessCount} success, ${batchErrorCount} errors`);
                
                // Progress indicator
                const progressPercent = ((i + batch.length) / enrollmentsToCreate.length * 100).toFixed(1);
                console.log(`      📊 Progress: ${progressPercent}% (${successCount.toLocaleString()} created, ${errorCount} errors)`);
                
                // Rate limiting - pause between batches
                if (batchNum < totalBatches) {
                    await new Promise(resolve => setTimeout(resolve, 1000));
                }
                
            } catch (batchError) {
                console.error(`      ❌ Batch ${batchNum} failed:`, batchError.message);
                errorCount += batch.length;
            }
        }

        // Step 8: Final verification
        console.log('\n🔍 STEP 8: Final Verification...');
        console.log('-'.repeat(50));
        
        const finalEnrollments = await conn.sobject("Docebo_CourseEnrollment__c")
            .find({ Course__c: sfCourse.Id })
            .execute();

        return {
            success: true,
            doceboTotal: allCourse43Enrollments.length,
            salesforceInitial: existingEnrollments.length,
            salesforceFinal: finalEnrollments.length,
            synced: successCount,
            errors: errorCount,
            skipped: skippedCount,
            totalProcessed: totalProcessed
        };

    } catch (error) {
        console.error('💥 Error in complete bulk sync:', error);
        return {
            success: false,
            error: error.message
        };
    }
}

// Execute the complete bulk sync
console.log('🚀 Starting Complete Course 43 Bulk Sync...');
completeCourse43BulkSync()
    .then((result) => {
        console.log('\n📋 COMPLETE BULK SYNC SUMMARY:');
        console.log('=' .repeat(70));
        
        if (result.success) {
            console.log(`📊 Total API Records Processed: ${result.totalProcessed?.toLocaleString() || 'N/A'}`);
            console.log(`📊 Course 43 Enrollments Found: ${result.doceboTotal.toLocaleString()}`);
            console.log(`📊 Salesforce Initial: ${result.salesforceInitial.toLocaleString()}`);
            console.log(`📊 Salesforce Final: ${result.salesforceFinal.toLocaleString()}`);
            console.log(`✅ Successfully Synced: ${result.synced.toLocaleString()}`);
            
            if (result.errors > 0) {
                console.log(`❌ Errors: ${result.errors.toLocaleString()}`);
            }
            if (result.skipped > 0) {
                console.log(`⏭️ Skipped: ${result.skipped.toLocaleString()}`);
            }
            
            const netIncrease = result.salesforceFinal - result.salesforceInitial;
            console.log(`\n🎉 NET RESULT: ${netIncrease.toLocaleString()} new enrollments added!`);
            
            const expectedCount = 5544;
            const successPercentage = (result.salesforceFinal / expectedCount * 100).toFixed(1);
            console.log(`📊 Success Rate: ${successPercentage}% of expected ${expectedCount} enrollments`);
            
            if (result.salesforceFinal >= expectedCount * 0.8) {
                console.log(`🎉 EXCELLENT: Achieved 80%+ of expected enrollments!`);
            } else if (result.salesforceFinal >= expectedCount * 0.5) {
                console.log(`✅ GOOD: Achieved 50%+ of expected enrollments!`);
            } else {
                console.log(`⚠️ PARTIAL: Some enrollments still missing`);
            }
            
            console.log('\n💡 NEXT STEPS:');
            console.log('1. ✅ Verify enrollment data in Salesforce');
            console.log('2. 🔄 Ensure webhooks are working for future enrollments');
            console.log('3. 📊 Monitor for any missing historical data');
            
        } else {
            console.log(`❌ Complete bulk sync failed: ${result.error}`);
        }
        
        console.log('\n✅ Complete bulk sync finished');
        process.exit(0);
    })
    .catch(err => {
        console.error('💥 Complete bulk sync failed:', err);
        process.exit(1);
    });
