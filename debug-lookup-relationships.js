require('dotenv').config();
const getConnection = require('./platform/salesforce/common/getConnection');
const getApiData = require("./common/docebo/fetcher");
const APP_BASE = process.env.DOCEBO_API_BASE_URL;

async function debugLookupRelationships() {
    try {
        console.log('🔍 DEBUG LOOKUP RELATIONSHIPS');
        console.log('=' .repeat(60));
        
        const conn = await getConnection();
        if (!conn || !conn.accessToken) {
            throw new Error('Failed to connect to Salesforce');
        }

        console.log('✅ Connected to Salesforce successfully');

        // Step 1: Get the test data we know works
        console.log('\n📊 STEP 1: Getting Test Data...');
        console.log('-'.repeat(40));
        
        // Use Learning Plan External ID 9 and get one enrollment
        const testLearningPlanId = "9";
        const response = await getApiData(
            'GET', 
            `${APP_BASE}/learn/v1/enrollments?learning_plan_id=${testLearningPlanId}&page=1&page_size=1`, 
            null
        );
        
        if (!response || response.status !== 200 || !response.data?.items?.length) {
            throw new Error('Could not get test enrollment data');
        }
        
        const testEnrollment = response.data.items[0];
        console.log(`📋 Test Enrollment:`);
        console.log(`   User ID: ${testEnrollment.user_id}`);
        console.log(`   Course UID: ${testEnrollment.uidCourse}`);
        console.log(`   Learning Plan ID: ${testLearningPlanId}`);

        // Step 2: Debug Learning Plan lookup
        console.log('\n🔍 STEP 2: Debug Learning Plan Lookup...');
        console.log('-'.repeat(40));
        
        const sfLearningPlan = await conn.sobject("Docebo_Learning_Plan__c")
            .findOne({ Learning_Plan_External_Id__c: testLearningPlanId });
            
        if (sfLearningPlan) {
            console.log(`✅ Found Learning Plan: ${sfLearningPlan.Learning_Plan_Name__c}`);
            console.log(`   SF ID: ${sfLearningPlan.Id}`);
            console.log(`   External ID: ${sfLearningPlan.Learning_Plan_External_Id__c}`);
        } else {
            console.log(`❌ Learning Plan not found for External ID: ${testLearningPlanId}`);
            return;
        }

        // Step 3: Debug User lookup
        console.log('\n👤 STEP 3: Debug User Lookup...');
        console.log('-'.repeat(40));
        
        const sfUser = await conn.sobject("Docebo_Users__c")
            .findOne({ User_Unique_Id__c: testEnrollment.user_id });
            
        if (sfUser) {
            console.log(`✅ Found User: ${sfUser.Email__c}`);
            console.log(`   SF ID: ${sfUser.Id}`);
            console.log(`   Unique ID: ${sfUser.User_Unique_Id__c}`);
        } else {
            console.log(`❌ User not found for ID: ${testEnrollment.user_id}`);
            return;
        }

        // Step 4: Debug Course lookup
        console.log('\n📚 STEP 4: Debug Course Lookup...');
        console.log('-'.repeat(40));
        
        // Get course from Docebo API to find numeric ID
        const courseResponse = await getApiData(
            'GET', 
            `${APP_BASE}/course/v1/courses?page=1&page_size=50`, 
            null
        );
        
        const doceboCourse = courseResponse.data?.items?.find(course => course.uidCourse === testEnrollment.uidCourse);
        
        if (doceboCourse) {
            console.log(`✅ Found Docebo Course: ${doceboCourse.uidCourse}`);
            console.log(`   Numeric ID: ${doceboCourse.id}`);
            console.log(`   UID Course: ${doceboCourse.uidCourse}`);
            
            // Find corresponding Salesforce course
            const sfCourse = await conn.sobject("Docebo_Course__c")
                .findOne({ Course_External_Id__c: doceboCourse.id.toString() });
                
            if (sfCourse) {
                console.log(`✅ Found SF Course: ${sfCourse.Course_Name__c}`);
                console.log(`   SF ID: ${sfCourse.Id}`);
                console.log(`   External ID: ${sfCourse.Course_External_Id__c}`);
            } else {
                console.log(`❌ SF Course not found for External ID: ${doceboCourse.id}`);
                return;
            }
        } else {
            console.log(`❌ Docebo course not found for UID: ${testEnrollment.uidCourse}`);
            return;
        }

        // Step 5: Debug Learning Plan Enrollment lookup
        console.log('\n🔗 STEP 5: Debug Learning Plan Enrollment Lookup...');
        console.log('-'.repeat(40));
        
        console.log(`Looking for Learning Plan Enrollment:`);
        console.log(`   Learning_Plan_Id__c: ${sfLearningPlan.Id}`);
        console.log(`   Docebo_User_Id__c: ${sfUser.Id}`);
        
        const lpEnrollment = await conn.sobject("Docebo_Learning_Plan_Enrollment__c")
            .findOne({ 
                Learning_Plan_Id__c: sfLearningPlan.Id,
                Docebo_User_Id__c: sfUser.Id
            });
            
        if (lpEnrollment) {
            console.log(`✅ Found Learning Plan Enrollment: ${lpEnrollment.Name}`);
            console.log(`   SF ID: ${lpEnrollment.Id}`);
        } else {
            console.log(`❌ Learning Plan Enrollment not found`);
            
            // Check what Learning Plan Enrollments exist for this user
            const userLpEnrollments = await conn.sobject("Docebo_Learning_Plan_Enrollment__c")
                .find({ Docebo_User_Id__c: sfUser.Id })
                .limit(5)
                .execute();
                
            console.log(`   User has ${userLpEnrollments.length} Learning Plan Enrollments:`);
            userLpEnrollments.forEach((enrollment, index) => {
                console.log(`      ${index + 1}. LP: ${enrollment.Learning_Plan_Id__c}, Name: ${enrollment.Name}`);
            });
            
            // Check what Learning Plan Enrollments exist for this LP
            const lpEnrollments = await conn.sobject("Docebo_Learning_Plan_Enrollment__c")
                .find({ Learning_Plan_Id__c: sfLearningPlan.Id })
                .limit(5)
                .execute();
                
            console.log(`   Learning Plan has ${lpEnrollments.length} enrollments:`);
            lpEnrollments.forEach((enrollment, index) => {
                console.log(`      ${index + 1}. User: ${enrollment.Docebo_User_Id__c}, Name: ${enrollment.Name}`);
            });
        }

        // Step 6: Debug Course Enrollment lookup
        console.log('\n📖 STEP 6: Debug Course Enrollment Lookup...');
        console.log('-'.repeat(40));
        
        console.log(`Looking for Course Enrollment:`);
        console.log(`   Course__c: ${sfCourse.Id}`);
        console.log(`   Docebo_User__c: ${sfUser.Id}`);
        
        const courseEnrollment = await conn.sobject("Docebo_CourseEnrollment__c")
            .findOne({ 
                Course__c: sfCourse.Id,
                Docebo_User__c: sfUser.Id
            });
            
        if (courseEnrollment) {
            console.log(`✅ Found Course Enrollment: ${courseEnrollment.Name}`);
            console.log(`   SF ID: ${courseEnrollment.Id}`);
        } else {
            console.log(`❌ Course Enrollment not found`);
            
            // Check what Course Enrollments exist for this user
            const userCourseEnrollments = await conn.sobject("Docebo_CourseEnrollment__c")
                .find({ Docebo_User__c: sfUser.Id })
                .limit(5)
                .execute();
                
            console.log(`   User has ${userCourseEnrollments.length} Course Enrollments:`);
            userCourseEnrollments.forEach((enrollment, index) => {
                console.log(`      ${index + 1}. Course: ${enrollment.Course__c}, Name: ${enrollment.Name}`);
            });
            
            // Check what Course Enrollments exist for this course
            const courseEnrollments = await conn.sobject("Docebo_CourseEnrollment__c")
                .find({ Course__c: sfCourse.Id })
                .limit(5)
                .execute();
                
            console.log(`   Course has ${courseEnrollments.length} enrollments:`);
            courseEnrollments.forEach((enrollment, index) => {
                console.log(`      ${index + 1}. User: ${enrollment.Docebo_User__c}, Name: ${enrollment.Name}`);
            });
        }

        // Step 7: Summary and next steps
        console.log('\n🎯 STEP 7: Summary and Next Steps...');
        console.log('-'.repeat(40));
        
        const hasLpEnrollment = !!lpEnrollment;
        const hasCourseEnrollment = !!courseEnrollment;
        
        console.log(`📊 Lookup Results:`);
        console.log(`   Learning Plan: ✅ Found`);
        console.log(`   User: ✅ Found`);
        console.log(`   Course: ✅ Found`);
        console.log(`   Learning Plan Enrollment: ${hasLpEnrollment ? '✅ Found' : '❌ Missing'}`);
        console.log(`   Course Enrollment: ${hasCourseEnrollment ? '✅ Found' : '❌ Missing'}`);
        
        if (hasLpEnrollment && hasCourseEnrollment) {
            console.log(`\n🎉 SUCCESS: All lookups found! Ready to create Learning Plan Course Enrollment`);
            
            // Test creating a Learning Plan Course Enrollment
            const testRecord = {
                Learning_Plan_Enrollment_Id__c: lpEnrollment.Id,
                Course_Enrollment_Id__c: courseEnrollment.Id,
                Enrollment_Date__c: new Date().toISOString(),
                Completion_Percentage__c: 0,
                Effective__c: true,
                Completed__c: false
            };
            
            console.log(`\n🧪 Test Record Structure:`);
            console.log(JSON.stringify(testRecord, null, 2));
            
            return {
                success: true,
                readyToSync: true,
                testRecord: testRecord
            };
        } else {
            console.log(`\n⚠️ BLOCKING ISSUES:`);
            if (!hasLpEnrollment) {
                console.log(`   - Missing Learning Plan Enrollment for this user/LP combination`);
            }
            if (!hasCourseEnrollment) {
                console.log(`   - Missing Course Enrollment for this user/course combination`);
            }
            
            return {
                success: true,
                readyToSync: false,
                missingLpEnrollment: !hasLpEnrollment,
                missingCourseEnrollment: !hasCourseEnrollment
            };
        }

    } catch (error) {
        console.error('💥 Error debugging lookup relationships:', error);
        return {
            success: false,
            error: error.message
        };
    }
}

console.log('🔄 Starting Lookup Relationships Debug...');
debugLookupRelationships()
    .then((result) => {
        console.log('\n📋 LOOKUP RELATIONSHIPS DEBUG SUMMARY:');
        console.log('=' .repeat(50));
        
        if (result.success) {
            console.log(`✅ Debug completed successfully`);
            
            if (result.readyToSync) {
                console.log(`🎉 READY TO SYNC: All lookup relationships found!`);
                console.log(`🚀 Next step: Run full Learning Plan Course Enrollment sync`);
            } else {
                console.log(`⚠️ NOT READY: Missing lookup relationships`);
                if (result.missingLpEnrollment) {
                    console.log(`   - Need to create/sync Learning Plan Enrollments first`);
                }
                if (result.missingCourseEnrollment) {
                    console.log(`   - Need to create/sync Course Enrollments first`);
                }
            }
        } else {
            console.log(`❌ Debug failed: ${result.error}`);
        }
        
        console.log('\n✅ Lookup relationships debug completed');
        process.exit(0);
    })
    .catch(err => {
        console.error('💥 Lookup relationships debug failed:', err);
        process.exit(1);
    });
