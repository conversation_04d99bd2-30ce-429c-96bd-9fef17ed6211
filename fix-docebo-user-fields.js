require('dotenv').config();
const { fixExistingDoceboUsers } = require('./platform/salesforce/users/fixDoceboUserFields');
const getConnection = require('./platform/salesforce/common/getConnection');

async function testAndFixDoceboUserFields() {
    try {
        console.log('🔧 Testing and Fixing Docebo_Users__c Field Issues...');
        
        const conn = await getConnection();
        if (!conn || !conn.accessToken) {
            console.error("❌ Invalid Salesforce connection");
            return;
        }

        // Step 1: Check current state of Docebo_Users__c records
        console.log('\n📊 Analyzing current Docebo_Users__c records...');
        
        const allUsers = await conn.sobject("Docebo_Users__c")
            .find({})
            .limit(10) // Check first 10 for analysis
            .execute();

        console.log(`📋 Found ${allUsers.length} Docebo_Users__c records to analyze`);

        let missingCreationDate = 0;
        let missingExpirationDate = 0;
        let missingLastAccessDate = 0;
        let missingSuspensionDate = 0;
        let missingLeadContact = 0;

        allUsers.forEach(user => {
            if (!user.User_Creation_Date__c) missingCreationDate++;
            if (!user.User_Expiration_Date__c) missingExpirationDate++;
            if (!user.User_Last_Access_Date__c) missingLastAccessDate++;
            if (!user.User_Suspension_Date__c) missingSuspensionDate++;
            if (!user.Lead__c && !user.Contact__c) missingLeadContact++;
        });

        console.log('\n📊 FIELD ANALYSIS RESULTS:');
        console.log('=' .repeat(60));
        console.log(`❌ Missing User_Creation_Date__c: ${missingCreationDate}/${allUsers.length}`);
        console.log(`❌ Missing User_Expiration_Date__c: ${missingExpirationDate}/${allUsers.length}`);
        console.log(`❌ Missing User_Last_Access_Date__c: ${missingLastAccessDate}/${allUsers.length}`);
        console.log(`❌ Missing User_Suspension_Date__c: ${missingSuspensionDate}/${allUsers.length}`);
        console.log(`❌ Missing Lead/Contact Association: ${missingLeadContact}/${allUsers.length}`);

        // Step 2: Show sample record details
        if (allUsers.length > 0) {
            const sampleUser = allUsers[0];
            console.log('\n📋 SAMPLE RECORD DETAILS:');
            console.log('-'.repeat(50));
            console.log(`Record ID: ${sampleUser.Id}`);
            console.log(`Email: ${sampleUser.Email__c || 'N/A'}`);
            console.log(`User_Creation_Date__c: ${sampleUser.User_Creation_Date__c || 'MISSING'}`);
            console.log(`User_Expiration_Date__c: ${sampleUser.User_Expiration_Date__c || 'MISSING'}`);
            console.log(`User_Last_Access_Date__c: ${sampleUser.User_Last_Access_Date__c || 'MISSING'}`);
            console.log(`User_Suspension_Date__c: ${sampleUser.User_Suspension_Date__c || 'MISSING'}`);
            console.log(`Lead__c: ${sampleUser.Lead__c || 'MISSING'}`);
            console.log(`Contact__c: ${sampleUser.Contact__c || 'MISSING'}`);
            console.log(`🔗 Record URL: https://strivetogether--full.sandbox.my.salesforce.com/${sampleUser.Id}`);
        }

        // Step 3: Fix the issues
        console.log('\n🔧 Starting fix process...');
        const fixResult = await fixExistingDoceboUsers();
        
        if (fixResult) {
            console.log('✅ Fix process completed successfully');
            
            // Step 4: Verify fixes
            console.log('\n🔍 Verifying fixes...');
            const updatedUsers = await conn.sobject("Docebo_Users__c")
                .find({})
                .limit(10)
                .execute();

            let fixedLeadContact = 0;
            updatedUsers.forEach(user => {
                if (user.Lead__c || user.Contact__c) fixedLeadContact++;
            });

            console.log(`✅ Records with Lead/Contact association: ${fixedLeadContact}/${updatedUsers.length}`);
            
            if (updatedUsers.length > 0) {
                const updatedSample = updatedUsers[0];
                console.log('\n📋 UPDATED SAMPLE RECORD:');
                console.log('-'.repeat(50));
                console.log(`Record ID: ${updatedSample.Id}`);
                console.log(`Email: ${updatedSample.Email__c || 'N/A'}`);
                console.log(`Lead__c: ${updatedSample.Lead__c || 'Still Missing'}`);
                console.log(`Contact__c: ${updatedSample.Contact__c || 'Still Missing'}`);
                console.log(`🔗 Record URL: https://strivetogether--full.sandbox.my.salesforce.com/${updatedSample.Id}`);
            }
            
        } else {
            console.log('❌ Fix process failed');
        }

        // Step 5: Provide recommendations
        console.log('\n💡 RECOMMENDATIONS:');
        console.log('=' .repeat(60));
        
        if (missingCreationDate > 0 || missingExpirationDate > 0 || missingLastAccessDate > 0) {
            console.log('📅 DATE FIELDS: These need to be populated from Docebo webhook data');
            console.log('   - Ensure webhook includes fired_at, expiration_date, last_access_date');
            console.log('   - Update webhook processing to map these fields correctly');
        }
        
        if (missingLeadContact > 0) {
            console.log('🔗 ASSOCIATIONS: Lead/Contact linking needs improvement');
            console.log('   - Ensure Lead ID is captured when creating new leads');
            console.log('   - Ensure Contact ID is captured when updating existing contacts');
            console.log('   - Run the fix script to associate existing records');
        }

        console.log('\n🎯 NEXT STEPS:');
        console.log('1. Update webhook processing to include all date fields');
        console.log('2. Ensure Lead/Contact IDs are properly stored during creation');
        console.log('3. Test with new webhook data to verify fixes');

    } catch (error) {
        console.error('💥 Error in test and fix process:', error);
    }
}

// Execute the test and fix
console.log('🔄 Starting Docebo_Users__c field analysis and fix...');
testAndFixDoceboUserFields()
    .then(() => {
        console.log('\n✅ Analysis and fix process completed');
        process.exit(0);
    })
    .catch(err => {
        console.error('💥 Process failed:', err);
        process.exit(1);
    });
