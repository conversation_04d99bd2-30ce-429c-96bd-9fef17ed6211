require('dotenv').config();
const getConnection = require('./platform/salesforce/common/getConnection');

async function checkUser18854InSalesforce() {
    try {
        console.log('🔍 Checking if User 18854 (<EMAIL>) exists in Salesforce...');
        
        const conn = await getConnection();
        
        // Check for Docebo_Users__c record
        console.log('\n📋 SEARCHING FOR DOCEBO_USERS__C RECORD:');
        console.log('=' .repeat(60));
        
        const doceboUserQuery = `
            SELECT Id, Name, User_Unique_Id__c, Username__c, Email__c, 
                   First_Name__c, Last_Name__c, Organization_Name__c, 
                   Job_Title__c, Role_Type__c, Race_Identity__c, 
                   Gender_Identity__c, Languages__c, CreatedDate
            FROM Docebo_Users__c 
            WHERE User_Unique_Id__c = 18854 
            OR Username__c = '<EMAIL>'
            OR Email__c = '<EMAIL>'
        `;
        
        const doceboUserResult = await conn.query(doceboUserQuery);
        
        if (doceboUserResult.totalSize > 0) {
            console.log(`✅ FOUND ${doceboUserResult.totalSize} Docebo_Users__c record(s):`);
            doceboUserResult.records.forEach((record, index) => {
                console.log(`\n📋 Record ${index + 1}:`);
                console.log(`   ID: ${record.Id}`);
                console.log(`   Name: ${record.Name}`);
                console.log(`   User_Unique_Id__c: ${record.User_Unique_Id__c}`);
                console.log(`   Username__c: ${record.Username__c}`);
                console.log(`   Email__c: ${record.Email__c}`);
                console.log(`   First_Name__c: ${record.First_Name__c}`);
                console.log(`   Last_Name__c: ${record.Last_Name__c}`);
                console.log(`   Organization_Name__c: ${record.Organization_Name__c}`);
                console.log(`   Job_Title__c: ${record.Job_Title__c}`);
                console.log(`   Role_Type__c: ${record.Role_Type__c}`);
                console.log(`   Race_Identity__c: ${record.Race_Identity__c}`);
                console.log(`   Gender_Identity__c: ${record.Gender_Identity__c}`);
                console.log(`   Languages__c: ${record.Languages__c}`);
                console.log(`   CreatedDate: ${record.CreatedDate}`);
                
                // Generate Salesforce link
                const salesforceLink = `https://strivetogether--full.sandbox.my.salesforce.com/${record.Id}`;
                console.log(`   🔗 Salesforce Link: ${salesforceLink}`);
            });
        } else {
            console.log('❌ NO Docebo_Users__c record found for User 18854');
        }
        
        // Check for Lead record
        console.log('\n📋 SEARCHING FOR LEAD RECORD:');
        console.log('=' .repeat(50));
        
        const leadQuery = `
            SELECT Id, Name, FirstName, LastName, Email, Company, Title,
                   Role_Type__c, Race__c, Gender__c, Languages__c, 
                   TimeZone__c, Created_by_Docebo_API__c, CreatedDate
            FROM Lead 
            WHERE Email = '<EMAIL>'
            OR (FirstName = 'Aa123' AND LastName = 'Bb123')
        `;
        
        const leadResult = await conn.query(leadQuery);
        
        if (leadResult.totalSize > 0) {
            console.log(`✅ FOUND ${leadResult.totalSize} Lead record(s):`);
            leadResult.records.forEach((record, index) => {
                console.log(`\n📋 Lead ${index + 1}:`);
                console.log(`   ID: ${record.Id}`);
                console.log(`   Name: ${record.Name}`);
                console.log(`   FirstName: ${record.FirstName}`);
                console.log(`   LastName: ${record.LastName}`);
                console.log(`   Email: ${record.Email}`);
                console.log(`   Company: ${record.Company}`);
                console.log(`   Title: ${record.Title}`);
                console.log(`   Role_Type__c: ${record.Role_Type__c}`);
                console.log(`   Race__c: ${record.Race__c}`);
                console.log(`   Gender__c: ${record.Gender__c}`);
                console.log(`   Languages__c: ${record.Languages__c}`);
                console.log(`   TimeZone__c: ${record.TimeZone__c}`);
                console.log(`   Created_by_Docebo_API__c: ${record.Created_by_Docebo_API__c}`);
                console.log(`   CreatedDate: ${record.CreatedDate}`);
                
                // Generate Salesforce link
                const salesforceLink = `https://strivetogether--full.sandbox.my.salesforce.com/${record.Id}`;
                console.log(`   🔗 Salesforce Link: ${salesforceLink}`);
            });
        } else {
            console.log('❌ NO Lead record <NAME_EMAIL>');
        }
        
        // Summary
        console.log('\n📊 SUMMARY FOR USER 18854:');
        console.log('=' .repeat(50));
        console.log(`Docebo_Users__c records: ${doceboUserResult.totalSize}`);
        console.log(`Lead records: ${leadResult.totalSize}`);
        
        if (doceboUserResult.totalSize === 0 && leadResult.totalSize === 0) {
            console.log('\n❌ CONCLUSION: User 18854 was NOT created in Salesforce');
            console.log('💡 This confirms the INVALID_FIELD errors prevented creation');
            console.log('💡 The field fix should resolve this for future attempts');
        } else {
            console.log('\n✅ CONCLUSION: User 18854 records found in Salesforce');
            console.log('🎯 Use the Salesforce links above to view the records');
        }
        
        return {
            doceboUserCount: doceboUserResult.totalSize,
            leadCount: leadResult.totalSize,
            doceboUsers: doceboUserResult.records,
            leads: leadResult.records
        };
        
    } catch (error) {
        console.error('💥 Error checking User 18854 in Salesforce:', error);
        return null;
    }
}

// Execute the check
console.log('🔄 Starting User 18854 Salesforce check...');
checkUser18854InSalesforce()
    .then((result) => {
        if (result) {
            console.log('\n✅ User 18854 Salesforce check completed!');
            
            if (result.doceboUserCount > 0 || result.leadCount > 0) {
                console.log('\n🎯 SALESFORCE LINKS:');
                result.doceboUsers.forEach((user, index) => {
                    console.log(`Docebo_Users__c ${index + 1}: https://strivetogether--full.sandbox.my.salesforce.com/${user.Id}`);
                });
                result.leads.forEach((lead, index) => {
                    console.log(`Lead ${index + 1}: https://strivetogether--full.sandbox.my.salesforce.com/${lead.Id}`);
                });
            } else {
                console.log('\n💡 NO RECORDS FOUND - User 18854 was not created due to field errors');
                console.log('🔧 The field fix should resolve this for future webhook attempts');
            }
        } else {
            console.log('\n⚠️ Check failed');
        }
        process.exit(0);
    })
    .catch(err => {
        console.error('💥 Check failed:', err);
        process.exit(1);
    });
