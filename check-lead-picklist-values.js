require('dotenv').config();
const getConnection = require('./platform/salesforce/common/getConnection');

async function checkLeadPicklistValues() {
    try {
        console.log('🔍 Checking valid picklist values for Lead fields...');
        
        const conn = await getConnection();
        if (!conn || !conn.accessToken) {
            console.error("❌ Invalid Salesforce connection");
            return;
        }

        // Get Lead object description
        const leadDescription = await conn.sobject("Lead").describe();
        
        // Find picklist fields we're using
        const picklistFields = ['Contact_Type__c', 'Role_Type__c', 'Race__c', 'Gender__c', 'Status'];
        
        console.log('\n📋 Valid Picklist Values:');
        console.log('=' .repeat(60));
        
        for (const fieldName of picklistFields) {
            const field = leadDescription.fields.find(f => f.name === fieldName);
            
            if (field && field.type === 'picklist') {
                console.log(`\n🔧 ${fieldName}:`);
                
                if (field.picklistValues && field.picklistValues.length > 0) {
                    field.picklistValues.forEach((value, index) => {
                        const status = value.active ? '✅' : '❌';
                        const defaultFlag = value.defaultValue ? ' (default)' : '';
                        console.log(`   ${index + 1}. "${value.value}"${defaultFlag} ${status}`);
                    });
                } else {
                    console.log('   No picklist values found');
                }
            } else {
                console.log(`\n❌ ${fieldName} - Field not found or not a picklist`);
            }
        }

    } catch (error) {
        console.error('💥 Error checking picklist values:', error);
    }
}

// Execute the check
console.log('🔄 Starting picklist values check...');
checkLeadPicklistValues()
    .then(() => {
        console.log('\n✅ Picklist check completed');
        process.exit(0);
    })
    .catch(err => {
        console.error('💥 Picklist check failed:', err);
        process.exit(1);
    });
