require('dotenv').config();
const doceboServices = require('./platform/docebo/services');
const { createInstructor } = require('./platform/salesforce/instructors/createInstructor');
const { getCourseSalesForceId } = require('./platform/salesforce/courses/createCourse');
const { getIltSessionSalesForceId } = require('./platform/salesforce/session/createSession');

// Configuration
const BATCH_SIZE = 10; // Process 10 courses at a time
const DELAY_BETWEEN_BATCHES = 2000; // 2 seconds between batches
const DELAY_BETWEEN_COURSES = 500; // 0.5 seconds between courses
const DELAY_BETWEEN_SESSIONS = 200; // 0.2 seconds between sessions
const DELAY_BETWEEN_INSTRUCTORS = 100; // 0.1 seconds between instructors

function delay(ms) {
  return new Promise(resolve => setTimeout(resolve, ms));
}

async function processCoursesBatch(courses, batchNumber, totalBatches) {
  console.log(`\n🔄 Processing batch ${batchNumber}/${totalBatches} (${courses.length} courses)`);
  
  let batchStats = {
    processed: 0,
    sessions: 0,
    instructors: 0,
    successful: 0,
    errors: 0
  };
  
  for (let i = 0; i < courses.length; i++) {
    const course = courses[i];
    console.log(`\n📖 [Batch ${batchNumber}] Course ${i + 1}/${courses.length}: ${course.name} (ID: ${course.id})`);
    
    try {
      // Check if course exists in Salesforce
      const courseSalesforceId = await getCourseSalesForceId(course.id);
      if (!courseSalesforceId) {
        console.log(`   ⚠️  Course not found in Salesforce, skipping...`);
        continue;
      }
      
      // Get sessions for this course
      const sessionsResult = await doceboServices.getSessionListedInfo(course.id);
      
      if (!sessionsResult || sessionsResult.length === 0) {
        console.log(`   ℹ️  No sessions found`);
        continue;
      }
      
      console.log(`   📅 Found ${sessionsResult.length} sessions`);
      batchStats.sessions += sessionsResult.length;
      
      // Process each session
      for (const session of sessionsResult) {
        try {
          // Check if session exists in Salesforce
          const sessionSalesforceId = await getIltSessionSalesForceId(session.id);
          if (!sessionSalesforceId) {
            console.log(`      ⚠️  Session ${session.id} not found in Salesforce, skipping...`);
            continue;
          }
          
          // Get detailed session info
          const sessionInfo = await doceboServices.getCourseSessionInfo(session.id);
          
          if (!sessionInfo || sessionInfo.status !== 200 || !sessionInfo.data) {
            console.log(`      ❌ Failed to get session ${session.id} data`);
            batchStats.errors++;
            continue;
          }
          
          const sessionData = sessionInfo.data;
          
          if (!sessionData.instructors || sessionData.instructors.length === 0) {
            console.log(`      ℹ️  No instructors in session ${session.id}`);
            continue;
          }
          
          console.log(`      👥 Processing ${sessionData.instructors.length} instructors in session ${session.id}`);
          batchStats.instructors += sessionData.instructors.length;
          
          // Process each instructor
          for (const instructor of sessionData.instructors) {
            try {
              const instructorResponse = await doceboServices.getInstructorData(
                instructor.user_id,
                course.id,
                session.id
              );
              
              if (!instructorResponse || instructorResponse.status !== 200 || !instructorResponse.data) {
                console.log(`         ❌ Failed to get instructor ${instructor.user_id} data`);
                batchStats.errors++;
                continue;
              }
              
              const result = await createInstructor(
                instructorResponse.data,
                instructor.user_id,
                course.id,
                session.id,
                instructor
              );
              
              if (result) {
                console.log(`         ✅ ${instructor.firstname} ${instructor.lastname} associated`);
                batchStats.successful++;
              } else {
                console.log(`         ❌ Failed to associate ${instructor.firstname} ${instructor.lastname}`);
                batchStats.errors++;
              }
              
              await delay(DELAY_BETWEEN_INSTRUCTORS);
              
            } catch (instructorError) {
              console.error(`         ❌ Error with instructor ${instructor.user_id}:`, instructorError.message);
              batchStats.errors++;
            }
          }
          
          await delay(DELAY_BETWEEN_SESSIONS);
          
        } catch (sessionError) {
          console.error(`      ❌ Error with session ${session.id}:`, sessionError.message);
          batchStats.errors++;
        }
      }
      
      batchStats.processed++;
      await delay(DELAY_BETWEEN_COURSES);
      
    } catch (courseError) {
      console.error(`❌ Error with course ${course.id}:`, courseError.message);
      batchStats.errors++;
    }
  }
  
  console.log(`\n📊 Batch ${batchNumber} completed:`);
  console.log(`   Courses: ${batchStats.processed}/${courses.length}`);
  console.log(`   Sessions: ${batchStats.sessions}`);
  console.log(`   Instructors: ${batchStats.instructors}`);
  console.log(`   Successful: ${batchStats.successful}`);
  console.log(`   Errors: ${batchStats.errors}`);
  
  return batchStats;
}

async function processAllInstructorAssociationsBatch() {
  try {
    console.log('🚀 Starting batch instructor association process...');
    console.log(`📋 Configuration: ${BATCH_SIZE} courses per batch`);
    
    // Get all courses
    console.log('\n📚 Fetching all courses from Docebo...');
    const allCourses = await doceboServices.getTotalCourseListedInfo();
    
    if (!allCourses || allCourses.length === 0) {
      console.log('❌ No courses found in Docebo');
      return;
    }
    
    console.log(`✅ Found ${allCourses.length} courses in Docebo`);
    
    // Split courses into batches
    const batches = [];
    for (let i = 0; i < allCourses.length; i += BATCH_SIZE) {
      batches.push(allCourses.slice(i, i + BATCH_SIZE));
    }
    
    console.log(`📦 Split into ${batches.length} batches of up to ${BATCH_SIZE} courses each`);
    
    let totalStats = {
      processed: 0,
      sessions: 0,
      instructors: 0,
      successful: 0,
      errors: 0
    };
    
    // Process each batch
    for (let i = 0; i < batches.length; i++) {
      const batchStats = await processCoursesBatch(batches[i], i + 1, batches.length);
      
      // Accumulate stats
      totalStats.processed += batchStats.processed;
      totalStats.sessions += batchStats.sessions;
      totalStats.instructors += batchStats.instructors;
      totalStats.successful += batchStats.successful;
      totalStats.errors += batchStats.errors;
      
      // Delay between batches (except for the last one)
      if (i < batches.length - 1) {
        console.log(`\n⏳ Waiting ${DELAY_BETWEEN_BATCHES/1000} seconds before next batch...`);
        await delay(DELAY_BETWEEN_BATCHES);
      }
    }
    
    // Final summary
    console.log('\n🎉 Batch instructor association process completed!');
    console.log('📊 Final Summary:');
    console.log(`   Total courses processed: ${totalStats.processed}/${allCourses.length}`);
    console.log(`   Total sessions: ${totalStats.sessions}`);
    console.log(`   Total instructors: ${totalStats.instructors}`);
    console.log(`   Successful associations: ${totalStats.successful}`);
    console.log(`   Total errors: ${totalStats.errors}`);
    
    const successRate = totalStats.instructors > 0 ? 
      ((totalStats.successful / totalStats.instructors) * 100).toFixed(1) : 0;
    console.log(`   Success rate: ${successRate}%`);
    
  } catch (error) {
    console.error('💥 Fatal error in batch instructor association process:', error);
  }
}

// Parse command line arguments
const args = process.argv.slice(2);
const startFromBatch = args[0] ? parseInt(args[0]) : 1;

if (startFromBatch > 1) {
  console.log(`🔄 Starting from batch ${startFromBatch}...`);
}

processAllInstructorAssociationsBatch()
  .then(() => {
    console.log('\n✅ Batch script completed successfully');
    process.exit(0);
  })
  .catch(err => {
    console.error('💥 Batch script failed:', err);
    process.exit(1);
  });
