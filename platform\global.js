const doceboService = require("./docebo/services");
const { getTotalUserList, saveUsersInBatch } = require("./salesforce/users/createUser");
const { getTotalCourseList, createNewCourse} = require("./salesforce/courses/createCourse");
const { createNewLearningPlanById } = require("./salesforce/LearningPlan/createLearningPlan");
const { learningPlanEnrollment } = require("./salesforce/LearningPlan/createLearningPlanEnrollment");
const { deleteAllCourses }  = require("./salesforce/courses/deleteCourse");
const { deleteUser } = require("./salesforce/users/deleteUser");
const { getLastSyncedId, updateSyncState } = require("./sync/syncState");

const {createNewSession} = require("./salesforce/session/createSession");

async function getTotalUserListInfo() {
    // Get the last synced user ID
    const lastSyncedUserId = getLastSyncedId('users');
    console.log(`Starting user sync from last ID: ${lastSyncedUserId}`);
    
    const userListedInfo = await doceboService.getTotalUserListedInfo();
    const salesforceUserIds = await getTotalUserList();
    let tmpIds = [...new Set(salesforceUserIds)];
    
    console.log(tmpIds.length)
    if (userListedInfo.length > 0) {
        console.log(`Found ${userListedInfo.length} users in Docebo.`);
        console.log(`Found ${salesforceUserIds.length} users in Salesforce.`);
        
        // Filter users that are newer than the last synced ID
        const usersToSave = userListedInfo.filter(user =>
            Number(user.user_id) > lastSyncedUserId && 
            !salesforceUserIds.includes(Number(user.user_id))
        );
        
        console.log(`Filtered down to ${usersToSave.length} users to save.`);
        const usersForBatch = [];
        let maxUserId = lastSyncedUserId;
        
        for (const user of usersToSave) {
            const userInfo = await doceboService.getUserInfo(user.user_id);
            if(usersForBatch.length > 100) break;
            console.log(`User ID: ${user.user_id}, Status: ${userInfo.status}`);
            
            // Track the highest user ID we've processed
            if (Number(user.user_id) > maxUserId) {
                maxUserId = Number(user.user_id);
            }
            
            if (userInfo.status === 200) {
                let userData = userInfo.data;
                userData["fired_at"] = user.creation_date || null;
                userData["expiration_date"] = user.expiration_date || null;
                usersForBatch.push({
                    userInfo: userData,
                    userListedInfo: user
                });
            } else {
                console.log(`Failed to fetch user info for User ID: ${user.user_id}`);
            }
        }
        
        if (usersForBatch.length > 0) {
            console.log(`Saving ${usersForBatch.length} users in batches.`);
            await saveUsersInBatch(usersForBatch);
            
            // Update the sync state with the highest user ID we've processed
            updateSyncState('users', maxUserId);
            console.log(`Updated last synced user ID to: ${maxUserId}`);
        } else {
            console.log("No new users to save.");
        }
    } else {
        console.log("No users found in Docebo.");
    }
    return null;
}

async function getTotalCourseListInfo() {
    // Get the last synced course ID
    const lastSyncedCourseId = getLastSyncedId('courses');
    console.log(`Starting course sync from last ID: ${lastSyncedCourseId}`);
    
    const courseListedInfo = await doceboService.getTotalCourseListedInfo();
    const salesforceCourseIds = await getTotalCourseList();
    console.log(courseListedInfo, salesforceCourseIds);
    
    if (courseListedInfo.length > 0) {
        console.log(`Found ${courseListedInfo.length} courses in Docebo.`);
        console.log(`Found ${salesforceCourseIds.length} courses in Salesforce.`);
        
        // Filter courses that are newer than the last synced ID
        const coursesToSave = courseListedInfo.filter(course =>
            Number(course.id) > lastSyncedCourseId &&
            !salesforceCourseIds.includes(Number(course.id))
        );
        
        console.log(`Filtered down to ${coursesToSave.length} courses to save.`);
        let maxCourseId = lastSyncedCourseId;
        
        for (const course of coursesToSave) {
            const courseInfo = await doceboService.getCourseInfo(course.id);
            console.log(`Course ID: ${course.id}, Status: ${courseInfo.status}`);
            
            // Track the highest course ID we've processed
            if (Number(course.id) > maxCourseId) {
                maxCourseId = Number(course.id);
            }
            
            if (courseInfo.status === 200) {
                let courseData = courseInfo.data;
                let courseSaveRes = await createNewCourse(courseData);
                console.log(`Course ID: ${course.id}, Save Result: ${courseSaveRes}`);
            } else {
                console.log(`Failed to fetch course info for Course ID: ${course.id}`);
            }
        }
        
        // Update the sync state with the highest course ID we've processed
        if (maxCourseId > lastSyncedCourseId) {
            updateSyncState('courses', maxCourseId);
            console.log(`Updated last synced course ID to: ${maxCourseId}`);
        }
    } else {
        console.log("No courses found in Docebo.");
    }
    return null;
}

async function getTotalSessionListInfo() {
    // Get the last synced session ID
    const lastSyncedSessionId = getLastSyncedId('sessions');
    console.log(`Starting session sync from last ID: ${lastSyncedSessionId}`);
    
    const courseListedInfo = await doceboService.getTotalCourseListedInfo();
    let maxSessionId = lastSyncedSessionId;
    
    for (var i = 0; i < courseListedInfo.length; i++) {
        const courseId = courseListedInfo[i].id;
        const sessionListedInfo = await doceboService.getSessionListedInfo(courseId);
        console.log(`Course ID: ${courseId}, Session Count: ${sessionListedInfo.length}`);
        
        // Filter sessions that are newer than the last synced ID
        const sessionsToProcess = sessionListedInfo.filter(session => 
            Number(session.id) > lastSyncedSessionId
        );
        
        for (var j = 0; j < sessionsToProcess.length; j++) {
            let courseInfo = await doceboService.getCourseInfo(courseListedInfo[i].id);
            const sessionId = sessionsToProcess[j].id;
            
            // Track the highest session ID we've processed
            if (Number(sessionId) > maxSessionId) {
                maxSessionId = Number(sessionId);
            }
            
            const sessionInfo = await doceboService.getCourseSessionInfo(sessionId);
            console.log(`Started saving for Course ID: ${courseListedInfo[i].id}, Session ID: ${sessionId}`);
            let saveRes = await createNewSession(sessionInfo.data, courseInfo.data);
            console.log(`Finished result: ${saveRes} of saving for Course ID: ${courseListedInfo[i].id}, Session ID: ${sessionId}`);
        }
    }
    
    // Update the sync state with the highest session ID we've processed
    if (maxSessionId > lastSyncedSessionId) {
        updateSyncState('sessions', maxSessionId);
        console.log(`Updated last synced session ID to: ${maxSessionId}`);
    }
}

async function getTotalLearningPlanListInfo() {
    // Get the last synced learning plan ID
    const lastSyncedLpId = getLastSyncedId('learningPlans');
    console.log(`Starting learning plan sync from last ID: ${lastSyncedLpId}`);
    
    const learningPlanListedInfo = await doceboService.getTotalLearningPlanListedInfo();
    let maxLpId = lastSyncedLpId;
    
    if (learningPlanListedInfo.length > 0) {
        console.log(`Found ${learningPlanListedInfo.length} learning plans in Docebo.`);
        
        // Filter learning plans that are newer than the last synced ID
        const lpsToProcess = learningPlanListedInfo.filter(lp => 
            Number(lp.learning_plan_id) > lastSyncedLpId
        );
        
        console.log(`Filtered down to ${lpsToProcess.length} learning plans to process.`);
        
        for (const lp of lpsToProcess) {
            if (lp.learning_plan_id) {
                // Track the highest learning plan ID we've processed
                if (Number(lp.learning_plan_id) > maxLpId) {
                    maxLpId = Number(lp.learning_plan_id);
                }
                
                let lpSaveRes = await createNewLearningPlanById(lp.learning_plan_id);
                console.log(`Learning Plan ID: ${lp.learning_plan_id}, Save Result: ${lpSaveRes}`);
            } else {
                console.log(`Failed to fetch learning plan info for Learning Plan ID: ${lp.learning_plan_id}`);
            }
        }
        
        // Update the sync state with the highest learning plan ID we've processed
        if (maxLpId > lastSyncedLpId) {
            updateSyncState('learningPlans', maxLpId);
            console.log(`Updated last synced learning plan ID to: ${maxLpId}`);
        }
    } else {
        console.log("No learning plans found in Docebo.");
    }
    return null;
}

async function getTotalLearningPlanEnrollmentListedInfo() {
    // For enrollments, we'll use a timestamp-based approach since they don't have sequential IDs
    const lastSyncedEnrollmentId = getLastSyncedId('learningPlanEnrollments');
    console.log(`Starting learning plan enrollment sync from last ID: ${lastSyncedEnrollmentId}`);
    
    const learningPlanEnrollmentListedInfo = await doceboService.getTotalLearningPlanEnrollmentListedInfo();
    let processedCount = 0;
    
    if (learningPlanEnrollmentListedInfo.length > 0) {
        console.log(`Found ${learningPlanEnrollmentListedInfo.length} learning plan enrollments in Docebo.`);
        
        for (const lp of learningPlanEnrollmentListedInfo) {
            // For enrollments, we'll use a counter-based approach
            processedCount++;
            
            if (lp.learning_plan_id && lp.user_id) {
                let lpEnrollSaveRes = await learningPlanEnrollment(lp);
                console.log(`Learning Plan ID: ${lp.learning_plan_id}, User ID: ${lp.user_id}, Save Result: ${lpEnrollSaveRes}`);
            } else {
                console.log(`Failed to fetch learning plan enrollment info for Learning Plan ID: ${lp.learning_plan_id}, User ID: ${lp.user_id}`);
            }
        }
        
        // Update the sync state with a counter for enrollments
        // This is a simple approach - in a real system you might want to use timestamps or other identifiers
        const newEnrollmentId = lastSyncedEnrollmentId + processedCount;
        updateSyncState('learningPlanEnrollments', newEnrollmentId);
        console.log(`Updated learning plan enrollment sync counter to: ${newEnrollmentId}`);
    } else {
        console.log("No learning plan enrollments found in Docebo.");
    }
    return null;
}

async function deleteSalesForceUser() {
    console.log("==== Start deleting empty user ====");
    let delRest = await deleteUser.deleteEmptyUser();
    console.log("==== Here is delete result ====", delRest);
}

async function deleteAllSalesForceCourses (){
    deleteAllCourses()
}


async function initCronJob() {
    console.log("==== Start cron job ====");
    // await getTotalLearningPlanEnrollmentListedInfo(); // DISABLED: Comment out to stop learning plan enrollment sync
    // await getTotalLearningPlanListInfo();
    // await getTotalSessionListInfo();
    // await getTotalUserListInfo();
    // await getTotalCourseListInfo();
    // await deleteSalesForceUser();
    // await deleteAllSalesForceCourses();
}

module.exports = {
    initCronJob
}
