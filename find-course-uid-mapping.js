require('dotenv').config();
const getConnection = require('./platform/salesforce/common/getConnection');
const getApiData = require("./common/docebo/fetcher");
const APP_BASE = process.env.DOCEBO_API_BASE_URL;

async function findCourseUidMapping() {
    try {
        console.log('🔍 FINDING COURSE UID MAPPING');
        console.log('=' .repeat(60));
        
        const conn = await getConnection();
        if (!conn || !conn.accessToken) {
            throw new Error('Failed to connect to Salesforce');
        }

        // Step 1: Get a few courses from Docebo API to see their structure
        console.log('📚 STEP 1: Getting Courses from Docebo API...');
        console.log('-'.repeat(50));
        
        const doceboCoursesResponse = await getApiData(
            'GET', 
            `${APP_BASE}/course/v1/courses?page=1&page_size=10`, 
            null
        );
        
        if (!doceboCoursesResponse || doceboCoursesResponse.status !== 200) {
            throw new Error('Could not get courses from Docebo');
        }
        
        const doceboCourses = doceboCoursesResponse.data?.items || [];
        console.log(`Found ${doceboCourses.length} courses from Docebo API`);
        
        if (doceboCourses.length > 0) {
            console.log('\n📋 Sample Docebo Course Structure:');
            const sampleCourse = doceboCourses[0];
            console.log(`   Course Name: ${sampleCourse.name}`);
            console.log(`   Course ID (numeric): ${sampleCourse.id}`);
            console.log(`   Course UID (alphanumeric): ${sampleCourse.uid}`);
            console.log(`   All fields: ${Object.keys(sampleCourse).join(', ')}`);
        }
        
        // Step 2: Check if any Salesforce courses have the uid stored somewhere
        console.log('\n📊 STEP 2: Checking Salesforce Courses for UID Storage...');
        console.log('-'.repeat(50));
        
        // Get all courses with all fields
        const sfCourses = await conn.query(`
            SELECT Id, Name, Course_Name__c, Course_External_Id__c, Course_Internal_ID__c, 
                   Course_Code__c, Slug__c, Course_Link__c
            FROM Docebo_Course__c 
            LIMIT 10
        `);
        
        console.log(`Found ${sfCourses.totalSize} courses in Salesforce`);
        
        // Step 3: Try to match courses by name and see if we can find the UID pattern
        console.log('\n🔍 STEP 3: Attempting to Match Courses by Name...');
        console.log('-'.repeat(50));
        
        const matches = [];
        
        for (const doceboCourse of doceboCourses.slice(0, 5)) {
            console.log(`\n🔍 Looking for Docebo course: "${doceboCourse.name}"`);
            console.log(`   Docebo ID: ${doceboCourse.id}, UID: ${doceboCourse.uid}`);
            
            // Try to find matching Salesforce course by name
            const matchingSfCourse = sfCourses.records.find(sfCourse => 
                sfCourse.Course_Name__c === doceboCourse.name
            );
            
            if (matchingSfCourse) {
                console.log(`   ✅ Found match in Salesforce:`);
                console.log(`      SF Course_External_Id__c: ${matchingSfCourse.Course_External_Id__c}`);
                console.log(`      SF Course_Internal_ID__c: ${matchingSfCourse.Course_Internal_ID__c}`);
                console.log(`      SF Course_Code__c: ${matchingSfCourse.Course_Code__c}`);
                console.log(`      SF Slug__c: ${matchingSfCourse.Slug__c}`);
                
                matches.push({
                    doceboId: doceboCourse.id,
                    doceboUid: doceboCourse.uid,
                    doceboName: doceboCourse.name,
                    sfExternalId: matchingSfCourse.Course_External_Id__c,
                    sfInternalId: matchingSfCourse.Course_Internal_ID__c,
                    sfId: matchingSfCourse.Id
                });
                
                // Check if the External ID matches either the numeric ID or the UID
                if (matchingSfCourse.Course_External_Id__c == doceboCourse.id) {
                    console.log(`      🎯 SF External ID matches Docebo numeric ID!`);
                } else if (matchingSfCourse.Course_External_Id__c == doceboCourse.uid) {
                    console.log(`      🎯 SF External ID matches Docebo UID!`);
                } else {
                    console.log(`      ⚠️ SF External ID doesn't match either Docebo ID or UID`);
                }
            } else {
                console.log(`   ❌ No matching Salesforce course found`);
            }
        }
        
        // Step 4: Create a mapping strategy
        console.log('\n🎯 STEP 4: Creating Mapping Strategy...');
        console.log('-'.repeat(50));
        
        if (matches.length > 0) {
            console.log(`Found ${matches.length} course matches`);
            
            // Check if we can create a mapping from UID to Salesforce ID
            const uidToSfIdMap = new Map();
            const numericIdToUidMap = new Map();
            
            matches.forEach(match => {
                uidToSfIdMap.set(match.doceboUid, match.sfId);
                numericIdToUidMap.set(match.doceboId.toString(), match.doceboUid);
                
                console.log(`   Mapping: ${match.doceboUid} → SF ID: ${match.sfId}`);
                console.log(`   Reverse: ${match.doceboId} → UID: ${match.doceboUid}`);
            });
            
            // Step 5: Test the mapping with our learning plan enrollment data
            console.log('\n🧪 STEP 5: Testing Mapping with Learning Plan Enrollment Data...');
            console.log('-'.repeat(50));
            
            // Get the sample enrollment data we found earlier
            const testLpId = 50; // From our earlier debug
            const enrollmentResponse = await getApiData(
                'GET', 
                `${APP_BASE}/learn/v1/enrollments?learning_plan_id=${testLpId}&page=1&page_size=3`, 
                null
            );
            
            if (enrollmentResponse && enrollmentResponse.status === 200) {
                const enrollments = enrollmentResponse.data?.items || [];
                
                enrollments.forEach((enrollment, index) => {
                    console.log(`\n   Enrollment ${index + 1}:`);
                    console.log(`      uidCourse: ${enrollment.uidCourse}`);
                    
                    if (uidToSfIdMap.has(enrollment.uidCourse)) {
                        const sfId = uidToSfIdMap.get(enrollment.uidCourse);
                        console.log(`      ✅ Can map to Salesforce Course ID: ${sfId}`);
                    } else {
                        console.log(`      ❌ Cannot map uidCourse to Salesforce`);
                        
                        // Try to find if there's a numeric ID we can use
                        console.log(`      🔍 Need to find numeric ID for uidCourse: ${enrollment.uidCourse}`);
                    }
                });
            }
            
            return {
                success: true,
                matches: matches.length,
                uidToSfIdMap: Object.fromEntries(uidToSfIdMap),
                strategy: matches.length > 0 ? 'direct_uid_mapping' : 'need_numeric_lookup'
            };
        } else {
            console.log('❌ No course matches found - need alternative strategy');
            
            return {
                success: true,
                matches: 0,
                strategy: 'need_alternative_approach'
            };
        }
        
    } catch (error) {
        console.error('💥 Error finding course UID mapping:', error);
        return {
            success: false,
            error: error.message
        };
    }
}

console.log('🔄 Starting course UID mapping search...');
findCourseUidMapping()
    .then((result) => {
        console.log('\n📋 COURSE UID MAPPING SUMMARY:');
        console.log('=' .repeat(50));
        
        if (result.success) {
            console.log(`✅ Search completed successfully`);
            console.log(`📊 Course Matches Found: ${result.matches}`);
            console.log(`🎯 Strategy: ${result.strategy}`);
            
            if (result.uidToSfIdMap) {
                console.log(`📋 UID to Salesforce ID Mapping:`);
                Object.entries(result.uidToSfIdMap).forEach(([uid, sfId]) => {
                    console.log(`   ${uid} → ${sfId}`);
                });
            }
            
            if (result.strategy === 'direct_uid_mapping') {
                console.log('\n💡 SOLUTION: Can use direct UID mapping for Learning Plan Course Enrollments!');
            } else if (result.strategy === 'need_numeric_lookup') {
                console.log('\n💡 SOLUTION: Need to lookup numeric course IDs first, then map to UIDs');
            } else {
                console.log('\n💡 NEXT STEPS: Need to investigate alternative mapping approaches');
            }
        } else {
            console.log(`❌ Search failed: ${result.error}`);
        }
        
        console.log('\n✅ Course UID mapping search completed');
        process.exit(0);
    })
    .catch(err => {
        console.error('💥 Course UID mapping search failed:', err);
        process.exit(1);
    });
