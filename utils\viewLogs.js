const fs = require('fs');
const path = require('path');

/**
 * Reads and prints logs from a specific date
 * 
 * Usage: 
 * - node utils/viewLogs.js            # shows today's logs
 * - node utils/viewLogs.js 2025-05-15 # shows logs for the specified date
 */

// Get the date from command line arguments or use today's date
const requestedDate = process.argv[2] || new Date().toISOString().split('T')[0];

// Define the log file path
const logsDir = path.join(__dirname, '../logs');
const logFilePath = path.join(logsDir, `${requestedDate}.log`);

// Check if the log file exists
if (!fs.existsSync(logFilePath)) {
    console.error(`No logs found for date: ${requestedDate}`);
    console.error(`Log file does not exist: ${logFilePath}`);
    process.exit(1);
}

// Read and display the log file
const logContent = fs.readFileSync(logFilePath, 'utf8');
console.log(`=== Logs for ${requestedDate} ===\n`);
console.log(logContent);