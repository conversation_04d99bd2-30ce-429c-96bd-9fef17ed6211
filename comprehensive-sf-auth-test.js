require('dotenv').config();
const axios = require('axios');
const jsforce = require('jsforce');

async function comprehensiveSalesforceAuthTest() {
    try {
        console.log('🔍 COMPREHENSIVE SALESFORCE AUTHENTICATION TEST');
        console.log('=' .repeat(70));
        
        // Test 1: Current credentials
        console.log('📋 TEST 1: Current Environment Variables');
        console.log('-'.repeat(40));
        console.log(`   SF_TOKEN_URL: ${process.env.SF_TOKEN_URL}`);
        console.log(`   SF_API_USER_NAME: ${process.env.SF_API_USER_NAME}`);
        console.log(`   SF_API_PASSWORD: ${process.env.SF_API_PASSWORD?.substring(0, 8)}... (length: ${process.env.SF_API_PASSWORD?.length})`);
        console.log(`   SF_API_CLIENT_ID: ${process.env.SF_API_CLIENT_ID?.substring(0, 15)}...`);
        
        // Test 2: Try authentication with current credentials
        console.log('\n📡 TEST 2: Authentication with Current Credentials');
        console.log('-'.repeat(40));
        
        try {
            const response = await axios.post(
                process.env.SF_TOKEN_URL,
                new URLSearchParams({
                    grant_type: process.env.SF_API_GRANT_TYPE,
                    client_id: process.env.SF_API_CLIENT_ID,
                    client_secret: process.env.SF_API_CLIENT_SECRET,
                    username: process.env.SF_API_USER_NAME,
                    password: process.env.SF_API_PASSWORD
                }),
                {
                    headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
                    timeout: 10000
                }
            );
            
            console.log('✅ SUCCESS! Authentication successful');
            console.log(`   Access Token: ${response.data.access_token?.substring(0, 20)}...`);
            console.log(`   Instance URL: ${response.data.instance_url}`);
            
            // Test 3: Try a simple query with the new token
            console.log('\n📊 TEST 3: Testing Query with New Token');
            console.log('-'.repeat(40));
            
            const conn = new jsforce.Connection({
                instanceUrl: response.data.instance_url,
                accessToken: response.data.access_token,
                version: '60.0'
            });
            
            try {
                const queryResult = await conn.query("SELECT COUNT() FROM User LIMIT 1");
                console.log(`✅ Query successful: ${queryResult.totalSize} users found`);
                
                // Test Docebo objects
                try {
                    const lpResult = await conn.query("SELECT COUNT() FROM Docebo_Learning_Plan_Course_Enrollment__c");
                    console.log(`📊 Learning Plan Course Enrollments: ${lpResult.totalSize}`);
                    
                    console.log('\n🎉 ALL TESTS PASSED! Salesforce connection is working!');
                    return { success: true, token: response.data.access_token };
                    
                } catch (doceboError) {
                    console.log(`⚠️ Docebo query failed: ${doceboError.message}`);
                    return { success: true, token: response.data.access_token, warning: 'Basic auth works but Docebo objects may have issues' };
                }
                
            } catch (queryError) {
                console.log(`❌ Query failed: ${queryError.message}`);
                return { success: false, error: 'Authentication succeeded but queries fail', token: response.data.access_token };
            }
            
        } catch (authError) {
            console.log('❌ Authentication failed');
            console.log(`   Status: ${authError.response?.status}`);
            console.log(`   Error: ${JSON.stringify(authError.response?.data, null, 2)}`);
            
            // Test 4: Try alternative approaches
            console.log('\n🔧 TEST 4: Troubleshooting Steps');
            console.log('-'.repeat(40));
            
            if (authError.response?.data?.error === 'invalid_grant') {
                console.log('🔍 Invalid Grant Error - Possible causes:');
                console.log('   1. 🔑 Incorrect password + security token combination');
                console.log('   2. 🔒 User account is locked or frozen');
                console.log('   3. 🌐 IP restrictions are blocking the request');
                console.log('   4. 📧 User login policies require additional verification');
                console.log('   5. 🔄 Security token has expired or been reset');
                
                console.log('\n💡 RECOMMENDED ACTIONS:');
                console.log('   1. Log into Salesforce manually to verify account status');
                console.log('   2. Reset security token: Setup → My Personal Information → Reset My Security Token');
                console.log('   3. Check Setup → Login History for any blocked attempts');
                console.log('   4. Verify Setup → Network Access for IP restrictions');
                console.log('   5. Check if the Connected App needs to be refreshed');
                
                // Test 5: Try with JSForce direct login (alternative method)
                console.log('\n🔄 TEST 5: Trying JSForce Direct Login');
                console.log('-'.repeat(40));
                
                try {
                    const directConn = new jsforce.Connection({
                        loginUrl: 'https://test.salesforce.com'
                    });
                    
                    const userInfo = await directConn.login(
                        process.env.SF_API_USER_NAME,
                        process.env.SF_API_PASSWORD
                    );
                    
                    console.log('✅ JSForce direct login successful!');
                    console.log(`   User ID: ${userInfo.id}`);
                    console.log(`   Org ID: ${userInfo.organizationId}`);
                    
                    // Test query with direct connection
                    const directResult = await directConn.query("SELECT COUNT() FROM User LIMIT 1");
                    console.log(`✅ Direct query successful: ${directResult.totalSize} users found`);
                    
                    return { success: true, method: 'jsforce_direct', connection: directConn };
                    
                } catch (directError) {
                    console.log(`❌ JSForce direct login also failed: ${directError.message}`);
                }
            }
            
            return { success: false, error: authError.response?.data || authError.message };
        }
        
    } catch (error) {
        console.error('💥 Comprehensive test failed:', error.message);
        return { success: false, error: error.message };
    }
}

console.log('🔄 Starting comprehensive Salesforce authentication test...');
comprehensiveSalesforceAuthTest()
    .then((result) => {
        console.log('\n📋 COMPREHENSIVE TEST SUMMARY:');
        console.log('=' .repeat(50));
        
        if (result.success) {
            console.log('✅ Salesforce authentication is working!');
            if (result.method) {
                console.log(`   Method: ${result.method}`);
            }
            if (result.warning) {
                console.log(`⚠️ Warning: ${result.warning}`);
            }
            console.log('\n🚀 Ready to run Learning Plan Course Enrollment sync!');
        } else {
            console.log('❌ Salesforce authentication failed');
            console.log(`   Error: ${result.error}`);
            console.log('\n🔧 Please follow the troubleshooting steps above');
        }
        
        console.log('\n✅ Comprehensive authentication test completed');
        process.exit(0);
    })
    .catch(err => {
        console.error('💥 Comprehensive authentication test failed:', err);
        process.exit(1);
    });
