require('dotenv').config();
const { createCourseEnrollment } = require('./platform/salesforce/courseEnrollment/createCourseEnrollment');
const getConnection = require('./platform/salesforce/common/getConnection');

async function testStatusTextField() {
    try {
        console.log('🧪 Testing Status__c Text Field with Docebo Values');
        console.log('=' .repeat(60));
        
        const conn = await getConnection();
        if (!conn || !conn.accessToken) {
            console.error("❌ Invalid Salesforce connection");
            return;
        }

        // Get sample user and course for testing
        const sampleUser = await conn.sobject("Docebo_Users__c")
            .findOne({}, ['Id', 'User_Unique_Id__c', 'First_Name__c', 'Last_Name__c']);
        
        const sampleCourse = await conn.sobject("Docebo_Course__c")
            .findOne({}, ['Id', 'Course_External_Id__c', 'Course_Name__c']);
        
        if (!sampleUser || !sampleCourse) {
            console.log('⚠️ No sample user or course found for testing');
            return;
        }
        
        console.log(`📚 Using sample course: ${sampleCourse.Course_Name__c} (ID: ${sampleCourse.Course_External_Id__c})`);
        console.log(`👤 Using sample user: ${sampleUser.First_Name__c} ${sampleUser.Last_Name__c} (ID: ${sampleUser.User_Unique_Id__c})`);

        // Test different Docebo status values
        const testStatuses = [
            'subscribed',
            'enrolled', 
            'active',
            'completed',
            'failed',
            'suspended',
            'cancelled',
            'withdrawn'
        ];

        console.log('\n🧪 TESTING DIFFERENT STATUS VALUES...');
        console.log('-'.repeat(60));

        const testResults = [];

        for (let i = 0; i < testStatuses.length; i++) {
            const status = testStatuses[i];
            console.log(`\n📋 Testing status: "${status}"`);
            
            const testEnrollmentData = {
                course_id: sampleCourse.Course_External_Id__c,
                user_id: sampleUser.User_Unique_Id__c,
                enrollment_date: new Date().toISOString().replace('T', ' ').slice(0, 19),
                completion_date: null,
                status: status, // Test the specific status
                score: 75 + i * 5, // Vary the score
                total_time: 3600 + i * 600, // Vary the time
                time_in_course: 3600 + i * 600,
                completed_learning_objects: 5 + i,
                completion: 70 + i * 5,
                credits: 2,
                unenrollment_date: null
            };

            try {
                const result = await createCourseEnrollment(testEnrollmentData);
                
                if (result) {
                    console.log(`✅ Enrollment created successfully with status: "${status}"`);
                    
                    // Verify the created enrollment
                    const enrollmentId = `${testEnrollmentData.course_id}-${testEnrollmentData.user_id}`.substring(0, 16);
                    const createdEnrollment = await conn.sobject("Docebo_CourseEnrollment__c")
                        .findOne({ Enrollment_ID__c: enrollmentId });
                    
                    if (createdEnrollment) {
                        console.log(`   📊 Stored Status: "${createdEnrollment.Status__c}"`);
                        console.log(`   📊 Score: ${createdEnrollment.Score__c}`);
                        console.log(`   📊 Time: ${createdEnrollment.Time_in_course__c} seconds`);
                        
                        // Verify status matches exactly
                        const statusMatches = createdEnrollment.Status__c === status;
                        console.log(`   🎯 Status Match: ${statusMatches ? '✅' : '❌'} ${statusMatches ? 'PERFECT' : 'MISMATCH'}`);
                        
                        testResults.push({
                            status,
                            success: true,
                            storedStatus: createdEnrollment.Status__c,
                            matches: statusMatches,
                            enrollmentId: createdEnrollment.Id
                        });
                        
                        // Clean up
                        await conn.sobject("Docebo_CourseEnrollment__c").delete(createdEnrollment.Id);
                        console.log(`   🗑️ Test enrollment cleaned up`);
                        
                    } else {
                        console.log(`   ❌ Created enrollment not found for verification`);
                        testResults.push({
                            status,
                            success: false,
                            error: 'Enrollment not found after creation'
                        });
                    }
                    
                } else {
                    console.log(`❌ Enrollment creation failed for status: "${status}"`);
                    testResults.push({
                        status,
                        success: false,
                        error: 'Creation failed'
                    });
                }
                
            } catch (createError) {
                console.error(`❌ Error creating enrollment with status "${status}":`, createError.message);
                testResults.push({
                    status,
                    success: false,
                    error: createError.message
                });
            }
            
            // Small delay between tests
            await new Promise(resolve => setTimeout(resolve, 1000));
        }

        // Summary
        console.log('\n📊 STATUS TEXT FIELD TEST SUMMARY:');
        console.log('=' .repeat(60));
        
        const successfulTests = testResults.filter(r => r.success);
        const failedTests = testResults.filter(r => !r.success);
        const perfectMatches = testResults.filter(r => r.success && r.matches);
        
        console.log(`✅ Successful tests: ${successfulTests.length}/${testResults.length}`);
        console.log(`🎯 Perfect status matches: ${perfectMatches.length}/${testResults.length}`);
        console.log(`❌ Failed tests: ${failedTests.length}/${testResults.length}`);
        
        if (successfulTests.length > 0) {
            console.log('\n✅ SUCCESSFUL STATUS VALUES:');
            successfulTests.forEach(result => {
                const matchIcon = result.matches ? '✅' : '⚠️';
                console.log(`   ${matchIcon} "${result.status}" → "${result.storedStatus}"`);
            });
        }
        
        if (failedTests.length > 0) {
            console.log('\n❌ FAILED STATUS VALUES:');
            failedTests.forEach(result => {
                console.log(`   ❌ "${result.status}" - ${result.error}`);
            });
        }
        
        console.log('\n🎉 STATUS FIELD BENEFITS:');
        console.log('   • No more picklist restrictions');
        console.log('   • Stores exact Docebo status values');
        console.log('   • Supports any future status values');
        console.log('   • Better data integrity and reporting');
        
        console.log('\n🚀 READY FOR PRODUCTION:');
        console.log('   • Status__c field accepts all Docebo values');
        console.log('   • No mapping or conversion needed');
        console.log('   • Webhook will work with any status');

        return {
            success: true,
            totalTests: testResults.length,
            successfulTests: successfulTests.length,
            perfectMatches: perfectMatches.length,
            results: testResults
        };

    } catch (error) {
        console.error('💥 Error in status text field test:', error);
        return {
            success: false,
            error: error.message
        };
    }
}

// Execute the test
console.log('🔄 Starting status text field test...');
testStatusTextField()
    .then((result) => {
        console.log('\n✅ Status text field test completed');
        if (result.success) {
            console.log(`🎉 ${result.perfectMatches}/${result.totalTests} status values working perfectly!`);
        } else {
            console.log('❌ Test failed. Check the logs above.');
        }
        process.exit(0);
    })
    .catch(err => {
        console.error('💥 Test failed:', err);
        process.exit(1);
    });
