# 🔧 Docebo Webhook Configuration Guide

## 📋 Overview

This guide provides step-by-step instructions for configuring Docebo webhooks to integrate with the Salesforce synchronization tool.

## 🎯 Prerequisites

- Docebo Super Admin access
- Integration tool deployed and running
- Public URL or ngrok tunnel for webhook endpoints
- SSL certificate (recommended for production)

## 🔗 Webhook Endpoints Configuration

### Base URL Structure
```
https://your-domain.com/webhook/docebo/[endpoint]
```

For local development with ngrok:
```
https://abc123.ngrok.io/webhook/docebo/[endpoint]
```

## 👥 User Management Webhooks

### Configuration in Docebo Admin Panel

1. **Navigate to**: Admin Menu → Advanced Settings → Webhooks
2. **Click**: "Add New Webhook"
3. **Configure**:

**Webhook Name**: `Salesforce User Sync`
**Endpoint URL**: `https://your-domain.com/webhook/docebo/user/manage`
**HTTP Method**: `POST`
**Content Type**: `application/json`

**Events to Subscribe**:
- ✅ `user.created` - New user registration
- ✅ `user.updated` - User profile changes
- ✅ `user.deleted` - User deactivation
- ✅ `user.selfregistrationrequest.approved` - Self-registration approval

**Headers** (Optional):
```
Authorization: Bearer your-webhook-secret
X-Webhook-Source: Docebo-User-Management
```

**Payload Example**:
```json
{
  "message_id": "{{message_id}}",
  "event": "{{event}}",
  "payloads": [
    {
      "user_id": "{{user_id}}",
      "fired_at": "{{fired_at}}",
      "expiration_date": "{{expiration_date}}"
    }
  ]
}
```

## 📚 Course Management Webhooks

### Configuration Steps

**Webhook Name**: `Salesforce Course Sync`
**Endpoint URL**: `https://your-domain.com/webhook/docebo/course/manage`
**HTTP Method**: `POST`
**Content Type**: `application/json`

**Events to Subscribe**:
- ✅ `course.created` - New course creation
- ✅ `course.updated` - Course modifications
- ✅ `course.properties_changed` - Course property updates
- ✅ `course.deleted` - Course deletion

**Required Fields in Payload**:
- `course_id` - Unique course identifier
- `fired_at` - Event timestamp
- `event` - Event type

## 🎓 Course Enrollment Webhooks

### Enrollment Creation Webhook

**Webhook Name**: `Salesforce Enrollment Creation`
**Endpoint URL**: `https://your-domain.com/webhook/docebo/user/enrollment/created`
**HTTP Method**: `POST`

**Events to Subscribe**:
- ✅ `course.enrollment.created`

**Required Payload Fields**:
```json
{
  "message_id": "{{message_id}}",
  "payloads": [
    {
      "user_id": "{{user_id}}",
      "course_id": "{{course_id}}",
      "enrollment_date": "{{enrollment_date}}",
      "status": "{{status}}",
      "score": "{{score}}",
      "total_time": "{{total_time}}"
    }
  ]
}
```

### Enrollment Completion Webhook

**Webhook Name**: `Salesforce Enrollment Completion`
**Endpoint URL**: `https://your-domain.com/webhook/docebo/user/enrollment/completed`
**HTTP Method**: `POST`

**Events to Subscribe**:
- ✅ `course.enrollment.completed`

### Enrollment Deletion Webhook

**Webhook Name**: `Salesforce Enrollment Deletion`
**Endpoint URL**: `https://your-domain.com/webhook/docebo/user/enrollment/deleted`
**HTTP Method**: `POST`

**Events to Subscribe**:
- ✅ `course.enrollment.deleted`

## 🏫 Session Management Webhooks

### ILT Session Webhook

**Webhook Name**: `Salesforce Session Management`
**Endpoint URL**: `https://your-domain.com/webhook/docebo/session/manage`
**HTTP Method**: `POST`

**Events to Subscribe**:
- ✅ `ilt.session.created` - New ILT session
- ✅ `ilt.session.updated` - Session modifications
- ✅ `ilt.session.enrollment.created` - Session enrollment
- ✅ `ilt.session.enrollment.updated` - Enrollment updates
- ✅ `ilt.session.enrollment.deleted` - Enrollment removal

**Required Fields**:
- `session_id` - Session identifier
- `course_id` - Associated course ID
- `user_id` - User ID (for enrollment events)

### Instructor Assignment Webhook

**Webhook Name**: `Salesforce Instructor Assignment`
**Endpoint URL**: `https://your-domain.com/webhook/docebo/session/instructor/assigned`
**HTTP Method**: `POST`

**Events to Subscribe**:
- ✅ `ilt.session.instructor.assigned`

## 📖 Learning Plan Webhooks

### Learning Plan Management

**Webhook Name**: `Salesforce Learning Plan Sync`
**Endpoint URL**: `https://your-domain.com/webhook/docebo/lp/manage`
**HTTP Method**: `POST`

**Events to Subscribe**:
- ✅ `learningplan.enrollment.created` - LP enrollment
- ✅ `learningplan.enrollment.deleted` - LP unenrollment
- ✅ `learningplan.enrollment.completed` - LP completion
- ✅ `badge.earned` - Badge awarded

## 🔒 Security Configuration

### Authentication Options

#### Option 1: Webhook Secret
Add to webhook headers:
```
Authorization: Bearer your-secret-token
```

Validate in your application:
```javascript
const webhookSecret = req.headers.authorization?.replace('Bearer ', '');
if (webhookSecret !== process.env.WEBHOOK_SECRET) {
    return res.status(401).json({ error: 'Unauthorized' });
}
```

#### Option 2: IP Whitelisting
Configure your firewall to only allow requests from Docebo's IP ranges.

#### Option 3: HTTPS Only
Always use HTTPS endpoints in production:
```
https://your-domain.com/webhook/docebo/*
```

### Payload Validation
```javascript
// Validate required fields
const requiredFields = ['message_id', 'event'];
for (const field of requiredFields) {
    if (!req.body[field]) {
        return res.status(400).json({ 
            error: `Missing required field: ${field}` 
        });
    }
}
```

## 🧪 Testing Webhooks

### Using Docebo Test Feature

1. **Navigate to**: Webhook configuration page
2. **Click**: "Test Webhook" button
3. **Select**: Event type to test
4. **Review**: Response status and payload

### Manual Testing with cURL

```bash
# Test user creation webhook
curl -X POST https://your-domain.com/webhook/docebo/user/manage \
  -H "Content-Type: application/json" \
  -d '{
    "message_id": "test-123",
    "event": "user.created",
    "payload": {
      "user_id": 12345,
      "fired_at": "2024-01-01T10:00:00Z"
    }
  }'
```

### Using Postman Collection

Create a Postman collection with all webhook endpoints:

```json
{
  "info": {
    "name": "Docebo Webhooks",
    "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"
  },
  "item": [
    {
      "name": "User Management",
      "request": {
        "method": "POST",
        "header": [
          {
            "key": "Content-Type",
            "value": "application/json"
          }
        ],
        "body": {
          "mode": "raw",
          "raw": "{\n  \"message_id\": \"test-user-123\",\n  \"event\": \"user.created\",\n  \"payload\": {\n    \"user_id\": 12345,\n    \"fired_at\": \"2024-01-01T10:00:00Z\"\n  }\n}"
        },
        "url": {
          "raw": "{{base_url}}/webhook/docebo/user/manage",
          "host": ["{{base_url}}"],
          "path": ["webhook", "docebo", "user", "manage"]
        }
      }
    }
  ]
}
```

## 📊 Monitoring and Troubleshooting

### Webhook Delivery Status

Monitor webhook delivery in Docebo:
1. **Navigate to**: Admin Menu → Advanced Settings → Webhooks
2. **Click**: Webhook name
3. **View**: "Delivery History" tab
4. **Check**: Status codes and response times

### Common Issues and Solutions

#### Issue: 404 Not Found
**Cause**: Incorrect endpoint URL
**Solution**: Verify the endpoint URL matches your application routes

#### Issue: 500 Internal Server Error
**Cause**: Application error processing webhook
**Solution**: Check application logs for detailed error information

#### Issue: Timeout Errors
**Cause**: Webhook processing takes too long
**Solution**: Implement background processing queues (already implemented)

#### Issue: Duplicate Processing
**Cause**: Webhook retries due to slow responses
**Solution**: Use message_id for deduplication (already implemented)

### Debugging Tips

1. **Enable Detailed Logging**:
```javascript
console.log('📥 Webhook received:', JSON.stringify(req.body, null, 2));
```

2. **Check Message Queue**:
```javascript
console.log('Current message queue length:', messageQue.length);
```

3. **Monitor Processing Queues**:
```javascript
console.log('Session queue length:', sessionManagementQueue.length);
console.log('Enrollment queue length:', courseEnrollmentQueue.length);
```

## 🚀 Production Deployment

### Checklist

- [ ] SSL certificate configured
- [ ] Webhook endpoints accessible from internet
- [ ] Authentication/authorization implemented
- [ ] Error handling and logging configured
- [ ] Monitoring and alerting set up
- [ ] Backup and recovery procedures documented
- [ ] Load balancing configured (if needed)
- [ ] Rate limiting implemented (if needed)

### Performance Considerations

- **Queue Processing**: Background queues prevent webhook timeouts
- **Batch Processing**: Multiple items processed together for efficiency
- **Connection Pooling**: Reuse database connections
- **Caching**: Cache frequently accessed data
- **Monitoring**: Track response times and error rates
