require('dotenv').config();
const getConnection = require('./platform/salesforce/common/getConnection');

async function checkTimezoneFieldNames() {
    try {
        console.log('🔍 Checking TimeZone field names on Lead object...');
        
        const conn = await getConnection();
        
        console.log('\n📋 CHECKING LEAD OBJECT FIELDS:');
        console.log('=' .repeat(60));
        
        // Get Lead object metadata
        const leadMetadata = await conn.sobject('Lead').describe();
        
        // Look for all timezone-related field variations
        const timezoneVariations = [
            'TimeZone',      // Standard field without __c
            'TimeZone__c',   // Custom field with __c
            'Timezone',      // Alternative spelling
            'Timezone__c',   // Alternative spelling with __c
            'Time_Zone',     // With underscore
            'Time_Zone__c',  // With underscore and __c
            'User_Timezone', // User prefix
            'User_Timezone__c',
            'User_TimeZone',
            'User_TimeZone__c'
        ];
        
        console.log('Searching for timezone field variations:');
        
        let foundFields = [];
        
        timezoneVariations.forEach(fieldName => {
            const field = leadMetadata.fields.find(f => f.name === fieldName);
            if (field) {
                foundFields.push(field);
                console.log(`   ✅ FOUND: ${field.name} (${field.type}) - "${field.label}"`);
                console.log(`      Updateable: ${field.updateable}, Createable: ${field.createable}`);
            } else {
                console.log(`   ❌ NOT FOUND: ${fieldName}`);
            }
        });
        
        // Also search for any field containing "time" or "zone"
        console.log('\n🔍 ALL FIELDS CONTAINING "TIME" OR "ZONE":');
        console.log('=' .repeat(50));
        
        const timeZoneFields = leadMetadata.fields.filter(field => 
            field.name.toLowerCase().includes('time') || 
            field.name.toLowerCase().includes('zone') ||
            field.label.toLowerCase().includes('time') ||
            field.label.toLowerCase().includes('zone')
        );
        
        if (timeZoneFields.length > 0) {
            timeZoneFields.forEach(field => {
                console.log(`   📋 ${field.name} (${field.type}) - "${field.label}"`);
                console.log(`      Updateable: ${field.updateable}, Createable: ${field.createable}`);
            });
        } else {
            console.log('   ❌ No fields containing "time" or "zone" found');
        }
        
        // Test creating a Lead with different field name variations
        console.log('\n🧪 TESTING LEAD CREATION WITH DIFFERENT FIELD NAMES:');
        console.log('=' .repeat(60));
        
        const testLeadData = {
            LastName: "TestTimeZone",
            FirstName: "Field",
            Email: "<EMAIL>",
            Company: "Test Company",
            Status: "Open - Not Contacted"
        };
        
        // Test each found timezone field
        for (const field of foundFields) {
            console.log(`\n🧪 Testing field: ${field.name}`);
            
            const testData = {
                ...testLeadData,
                [field.name]: "Europe/Oslo"
            };
            
            try {
                const result = await conn.sobject("Lead").create(testData);
                if (result.success) {
                    console.log(`   ✅ SUCCESS: Lead created with ${field.name} = "Europe/Oslo"`);
                    console.log(`   🔗 Lead ID: ${result.id}`);
                    console.log(`   🔗 Salesforce Link: https://strivetogether--full.sandbox.my.salesforce.com/${result.id}`);
                    
                    // Clean up - delete the test lead
                    await conn.sobject("Lead").delete(result.id);
                    console.log(`   🗑️ Test lead deleted`);
                } else {
                    console.log(`   ❌ FAILED: ${JSON.stringify(result.errors)}`);
                }
            } catch (error) {
                console.log(`   ❌ ERROR: ${error.message}`);
            }
        }
        
        // Recommendations
        console.log('\n💡 RECOMMENDATIONS:');
        console.log('=' .repeat(50));
        
        if (foundFields.length > 0) {
            console.log('✅ Found timezone fields on Lead object:');
            foundFields.forEach(field => {
                console.log(`   - Use "${field.name}" for timezone data`);
            });
            
            const preferredField = foundFields.find(f => f.name === 'TimeZone') || 
                                 foundFields.find(f => f.name === 'TimeZone__c') ||
                                 foundFields[0];
            
            console.log(`\n🎯 RECOMMENDED FIELD: ${preferredField.name}`);
            console.log(`   Type: ${preferredField.type}`);
            console.log(`   Label: ${preferredField.label}`);
            
        } else {
            console.log('❌ No timezone fields found on Lead object');
            console.log('💡 You may need to create a timezone field:');
            console.log('   - Field Name: TimeZone (becomes TimeZone__c)');
            console.log('   - Data Type: Text');
            console.log('   - Length: 255');
        }
        
        return {
            foundFields: foundFields,
            allTimeZoneFields: timeZoneFields,
            recommendedField: foundFields.length > 0 ? foundFields[0].name : null
        };
        
    } catch (error) {
        console.error('💥 Error checking timezone field names:', error);
        return null;
    }
}

// Execute the check
console.log('🔄 Starting timezone field name check...');
checkTimezoneFieldNames()
    .then((result) => {
        if (result) {
            console.log('\n✅ Timezone field check completed!');
            
            if (result.recommendedField) {
                console.log(`\n🎯 USE THIS FIELD NAME: ${result.recommendedField}`);
                console.log('Update your code to use this field name for timezone data.');
            } else {
                console.log('\n⚠️ No timezone field found - create one in Salesforce Setup');
            }
        } else {
            console.log('\n⚠️ Field check failed');
        }
        process.exit(0);
    })
    .catch(err => {
        console.error('💥 Field check failed:', err);
        process.exit(1);
    });
