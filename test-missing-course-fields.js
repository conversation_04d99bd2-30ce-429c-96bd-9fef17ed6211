require('dotenv').config();
const { createNewCourse } = require('./platform/salesforce/courses/createCourse');
const { mapDoceboCourseToSalesforce } = require('./platform/salesforce/courses/mapCourseData');
const doceboService = require('./platform/docebo/services');
const getConnection = require('./platform/salesforce/common/getConnection');

async function testMissingCourseFields() {
    try {
        console.log('🧪 Testing Missing Course Fields Implementation...');
        console.log('=' .repeat(60));

        // Test data with all the fields that were missing
        const mockCourseData = {
            id: 999,
            uid: "TEST-COURSE-999",
            code: "TEST999",
            name: "Test Course for Missing Fields",
            description: "This course tests the missing field implementation",
            status: "published",
            type: "elearning",
            created_on: "2024-01-01 10:00:00",
            updated_on: "2024-01-15 14:30:00",
            credits: 3,
            language: {
                name: "English"
            },
            category: {
                name: "Leadership Development",
                code: "LEAD-001"
            },
            time_options: {
                date_begin: "2024-02-01 09:00:00",
                date_end: "2024-02-28 17:00:00",
                duration: {
                    days: 5
                }
            },
            catalog_options: {
                self_enrollment: {
                    start_date: "2024-01-15 12:00:00",
                    end_date: "2024-03-01 23:59:59"
                }
            },
            sessions: [
                {
                    id: 1,
                    date_start: "2024-02-01 09:00:00",
                    date_end: "2024-02-01 12:00:00"
                },
                {
                    id: 2,
                    date_start: "2024-02-02 13:00:00",
                    date_end: "2024-02-02 17:00:00"
                }
            ],
            average_completion_time: 7200, // 2 hours in seconds
            created_by: {
                id: 123,
                fullname: "Test Admin"
            }
        };

        const mockCourseListedInfo = {
            is_deleted: false,
            removed_at: null,
            actions: ["view", "edit", "delete"],
            sessions_count: 2,
            category: {
                name: "Leadership Development",
                code: "LEAD-001"
            }
        };

        console.log('\n📋 TESTING FIELD MAPPING...');
        console.log('-'.repeat(40));

        // Test the mapping function
        const mappedData = mapDoceboCourseToSalesforce(mockCourseData, {});
        
        console.log('\n🎯 MISSING FIELDS TEST RESULTS:');
        console.log('=' .repeat(50));
        
        // Test Course Category Code
        console.log(`✅ Course_Category_Code__c: "${mappedData.Course_Category_Code__c}"`);
        console.log(`   Expected: "LEAD-001", Got: "${mappedData.Course_Category_Code__c}"`);
        console.log(`   Status: ${mappedData.Course_Category_Code__c === "LEAD-001" ? "✅ PASS" : "❌ FAIL"}`);
        
        // Test Course Start Date
        console.log(`\n✅ Course_Start_Date__c: "${mappedData.Course_Start_Date__c}"`);
        console.log(`   Expected: "2024-02-01", Got: "${mappedData.Course_Start_Date__c}"`);
        console.log(`   Status: ${mappedData.Course_Start_Date__c === "2024-02-01" ? "✅ PASS" : "❌ FAIL"}`);
        
        // Test Course End Date
        console.log(`\n✅ Course_End_Date__c: "${mappedData.Course_End_Date__c}"`);
        console.log(`   Expected: "2024-02-28", Got: "${mappedData.Course_End_Date__c}"`);
        console.log(`   Status: ${mappedData.Course_End_Date__c === "2024-02-28" ? "✅ PASS" : "❌ FAIL"}`);
        
        // Test Session Time (min)
        console.log(`\n✅ Session_Time_min__c: ${mappedData.Session_Time_min__c}`);
        console.log(`   Expected: 420 minutes (7 hours total), Got: ${mappedData.Session_Time_min__c}`);
        console.log(`   Status: ${mappedData.Session_Time_min__c === 420 ? "✅ PASS" : "❌ FAIL"}`);
        
        // Test Enrollment Date
        console.log(`\n✅ Enrollment_Date__c: "${mappedData.Enrollment_Date__c}"`);
        console.log(`   Expected: "2024-01-15", Got: "${mappedData.Enrollment_Date__c}"`);
        console.log(`   Status: ${mappedData.Enrollment_Date__c === "2024-01-15" ? "✅ PASS" : "❌ FAIL"}`);

        console.log('\n📊 ADDITIONAL FIELD VERIFICATION:');
        console.log('-'.repeat(40));
        console.log(`Course_Category__c: "${mappedData.Course_Category__c}"`);
        console.log(`Course_Duration__c: ${mappedData.Course_Duration__c} days`);
        console.log(`Credits_CEUs__c: ${mappedData.Credits_CEUs__c}`);
        console.log(`Course_Status__c: "${mappedData.Course_Status__c}"`);
        console.log(`Effective__c: ${mappedData.Effective__c}`);

        console.log('\n🧪 TESTING COURSE CREATION WITH MISSING FIELDS...');
        console.log('-'.repeat(50));

        // Test the actual course creation function
        try {
            // Mock the getCourseListedInfo function to return our test data
            const originalGetCourseListedInfo = doceboService.getCourseListedInfo;
            doceboService.getCourseListedInfo = async () => mockCourseListedInfo;

            const courseId = await createNewCourse(mockCourseData);

            // Restore the original function
            doceboService.getCourseListedInfo = originalGetCourseListedInfo;
            
            if (courseId) {
                console.log(`✅ Course created successfully with ID: ${courseId}`);
                
                // Verify the course was created with all fields
                const conn = await getConnection();
                const createdCourse = await conn.sobject("Docebo_Course__c")
                    .findOne({ Course_External_Id__c: mockCourseData.id });
                
                if (createdCourse) {
                    console.log('\n🔍 VERIFYING CREATED COURSE FIELDS:');
                    console.log('-'.repeat(40));
                    console.log(`Course_Category_Code__c: "${createdCourse.Course_Category_Code__c}"`);
                    console.log(`Course_Start_Date__c: "${createdCourse.Course_Start_Date__c}"`);
                    console.log(`Course_End_Date__c: "${createdCourse.Course_End_Date__c}"`);
                    console.log(`Session_Time_min__c: ${createdCourse.Session_Time_min__c}`);
                    console.log(`Enrollment_Date__c: "${createdCourse.Enrollment_Date__c}"`);
                    
                    // Clean up - delete the test course
                    await conn.sobject("Docebo_Course__c").delete(courseId);
                    console.log('\n🧹 Test course cleaned up successfully');
                } else {
                    console.log('❌ Could not retrieve created course for verification');
                }
            } else {
                console.log('❌ Course creation failed');
            }
        } catch (createError) {
            console.error('❌ Error during course creation test:', createError);
        }

        console.log('\n🎯 TESTING DIFFERENT SESSION TIME SCENARIOS...');
        console.log('-'.repeat(50));

        // Test with enrollment data (should use total_time)
        const enrollmentData = { total_time: 3600 }; // 1 hour in seconds
        const mappedWithEnrollment = mapDoceboCourseToSalesforce(mockCourseData, enrollmentData);
        console.log(`With enrollment total_time: ${mappedWithEnrollment.Session_Time_min__c} minutes (expected: 60)`);

        // Test with no sessions (should use course duration)
        const courseWithoutSessions = { ...mockCourseData };
        delete courseWithoutSessions.sessions;
        const mappedWithoutSessions = mapDoceboCourseToSalesforce(courseWithoutSessions, {});
        console.log(`Without sessions: ${mappedWithoutSessions.Session_Time_min__c} minutes (expected: 2400 = 5 days * 8 hours * 60 min)`);

        // Test with only average completion time
        const courseWithAvgTime = {
            ...mockCourseData,
            time_options: { duration: { days: 0 } },
            average_completion_time: 5400 // 1.5 hours in seconds
        };
        delete courseWithAvgTime.sessions;
        const mappedWithAvgTime = mapDoceboCourseToSalesforce(courseWithAvgTime, {});
        console.log(`With average completion time: ${mappedWithAvgTime.Session_Time_min__c} minutes (expected: 90)`);

        console.log('\n✅ MISSING COURSE FIELDS TEST COMPLETED!');
        console.log('=' .repeat(60));
        console.log('All missing fields have been implemented:');
        console.log('✅ Course Category Code - Mapped from category.code');
        console.log('✅ Course Start Date - Mapped from time_options.date_begin');
        console.log('✅ Course End Date - Mapped from time_options.date_end');
        console.log('✅ Session Time (min) - Calculated from sessions or course duration');
        console.log('✅ Enrollment Date - Mapped from catalog_options.self_enrollment.start_date');

    } catch (error) {
        console.error('❌ Error during missing fields test:', error);
    }
}

// Run the test
if (require.main === module) {
    testMissingCourseFields();
}

module.exports = { testMissingCourseFields };
