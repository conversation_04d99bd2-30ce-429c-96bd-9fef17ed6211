require('dotenv').config();
const getConnection = require('./platform/salesforce/common/getConnection');
const getApiData = require("./common/docebo/fetcher");
const APP_BASE = process.env.DOCEBO_API_BASE_URL;

async function diagnoseWelcomeCourseEnrollments() {
    try {
        console.log('🔍 DIAGNOSING "Welcome to the StriveTogether Network" COURSE ENROLLMENT ISSUES');
        console.log('=' .repeat(80));
        
        const conn = await getConnection();
        if (!conn || !conn.accessToken) {
            throw new Error('Failed to connect to Salesforce');
        }

        console.log('✅ Connected to Salesforce successfully');

        // Step 1: Verify course exists in both systems
        console.log('\n📚 STEP 1: Verifying Course in Both Systems...');
        console.log('-'.repeat(60));
        
        // Check Salesforce
        const sfCourse = await conn.sobject("Docebo_Course__c")
            .findOne({ Course_External_Id__c: 72 });
            
        if (!sfCourse) {
            console.log('❌ Course not found in Salesforce');
            return { success: false, error: 'Course not found in Salesforce' };
        }
        
        console.log('✅ Salesforce Course Found:');
        console.log(`   Course ID: ${sfCourse.Id}`);
        console.log(`   Course Name: ${sfCourse.Course_Name__c}`);
        console.log(`   External ID: ${sfCourse.Course_External_Id__c}`);

        // Check Docebo
        let doceboCourse = null;
        try {
            const doceboResponse = await getApiData('GET', `${APP_BASE}/course/v1/courses/72`, null);
            if (doceboResponse && doceboResponse.status === 200) {
                doceboCourse = doceboResponse.data;
                console.log('✅ Docebo Course Found:');
                console.log(`   Course ID: ${doceboCourse.id}`);
                console.log(`   Course Name: ${doceboCourse.name}`);
                console.log(`   Course Status: ${doceboCourse.status}`);
            }
        } catch (doceboError) {
            console.log('❌ Error fetching course from Docebo:', doceboError.message);
        }

        // Step 2: Check for enrollments in Docebo API directly
        console.log('\n🔍 STEP 2: Checking Enrollments in Docebo API...');
        console.log('-'.repeat(60));
        
        let doceboEnrollments = [];
        try {
            let page = 1;
            let hasMoreData = true;
            
            while (hasMoreData && page <= 10) { // Limit to 10 pages for safety
                console.log(`   📄 Checking Docebo API page ${page}...`);
                
                const enrollmentResponse = await getApiData(
                    'GET', 
                    `${APP_BASE}/learn/v1/enrollments?course_id=72&page=${page}&page_size=200`, 
                    null
                );
                
                if (enrollmentResponse && enrollmentResponse.status === 200) {
                    const items = enrollmentResponse.data?.items || [];
                    doceboEnrollments.push(...items);
                    
                    console.log(`      Found ${items.length} enrollments on page ${page}`);
                    
                    hasMoreData = enrollmentResponse.data?.has_more_data || false;
                    if (items.length === 0) hasMoreData = false;
                    page++;
                } else {
                    console.log(`      No data on page ${page}`);
                    hasMoreData = false;
                }
            }
            
            console.log(`✅ Total Docebo Enrollments Found: ${doceboEnrollments.length}`);
            
            if (doceboEnrollments.length > 0) {
                console.log('\n📋 Sample Docebo Enrollments:');
                doceboEnrollments.slice(0, 5).forEach((enrollment, index) => {
                    console.log(`   ${index + 1}. User ${enrollment.user_id}: Status=${enrollment.status}, Progress=${enrollment.completion_percentage}%`);
                });
            }
            
        } catch (enrollmentError) {
            console.log('❌ Error fetching enrollments from Docebo:', enrollmentError.message);
        }

        // Step 3: Check Salesforce enrollments
        console.log('\n📊 STEP 3: Checking Salesforce Course Enrollments...');
        console.log('-'.repeat(60));
        
        const sfEnrollments = await conn.sobject("Docebo_CourseEnrollment__c")
            .find({ Course__c: sfCourse.Id })
            .execute();
            
        console.log(`Salesforce Enrollments Found: ${sfEnrollments.length}`);
        
        if (sfEnrollments.length > 0) {
            console.log('\n📋 Sample Salesforce Enrollments:');
            sfEnrollments.slice(0, 5).forEach((enrollment, index) => {
                console.log(`   ${index + 1}. ID: ${enrollment.Id}, Status: ${enrollment.Status__c}, User: ${enrollment.Docebo_User__c}`);
            });
        }

        // Step 4: Check for enrollments with different course ID patterns
        console.log('\n🔍 STEP 4: Checking for Alternative Course ID Patterns...');
        console.log('-'.repeat(60));
        
        // Check for enrollments with course_id 72 in enrollment ID
        const enrollmentsWithCourse72 = await conn.sobject("Docebo_CourseEnrollment__c")
            .find({ Enrollment_ID__c: { $like: '72-%' } })
            .limit(20)
            .execute();
            
        console.log(`Enrollments with "72-" pattern: ${enrollmentsWithCourse72.length}`);
        
        if (enrollmentsWithCourse72.length > 0) {
            console.log('\n📋 Enrollments with Course 72 Pattern:');
            enrollmentsWithCourse72.forEach((enrollment, index) => {
                console.log(`   ${index + 1}. Enrollment ID: ${enrollment.Enrollment_ID__c}, Course Link: ${enrollment.Course__c ? 'YES' : 'NO'}`);
            });
        }

        // Step 5: Check webhook logs for course 72
        console.log('\n📝 STEP 5: Checking Recent Webhook Activity...');
        console.log('-'.repeat(60));
        
        // Check recent logs for course 72 activity
        const fs = require('fs');
        const path = require('path');
        
        try {
            const logPath = path.join(__dirname, 'logs', '2025-06-16.log');
            if (fs.existsSync(logPath)) {
                const logContent = fs.readFileSync(logPath, 'utf8');
                const course72Lines = logContent.split('\n').filter(line => 
                    line.includes('course_id":72') || 
                    line.includes('course_id": 72') ||
                    line.includes('"72"') ||
                    line.includes('Welcome to the StriveTogether Network')
                );
                
                console.log(`Found ${course72Lines.length} log entries mentioning course 72`);
                
                if (course72Lines.length > 0) {
                    console.log('\n📋 Recent Course 72 Log Entries:');
                    course72Lines.slice(-5).forEach((line, index) => {
                        const timestamp = line.substring(0, 19);
                        const message = line.substring(20);
                        console.log(`   ${index + 1}. ${timestamp}: ${message.substring(0, 100)}...`);
                    });
                }
            } else {
                console.log('⚠️ Log file not found');
            }
        } catch (logError) {
            console.log('❌ Error reading log file:', logError.message);
        }

        // Step 6: Check for course enrollment webhook processing issues
        console.log('\n🔧 STEP 6: Analyzing Potential Issues...');
        console.log('-'.repeat(60));
        
        const issues = [];
        const recommendations = [];
        
        if (doceboEnrollments.length > 0 && sfEnrollments.length === 0) {
            issues.push('Enrollments exist in Docebo but not in Salesforce');
            recommendations.push('Check webhook processing for course enrollment events');
            recommendations.push('Verify course enrollment webhook is configured for course 72');
        }
        
        if (doceboEnrollments.length === 0) {
            issues.push('No enrollments found in Docebo API for course 72');
            recommendations.push('Verify course ID 72 is correct');
            recommendations.push('Check if course enrollments are in learning plans instead');
        }
        
        if (enrollmentsWithCourse72.length > 0 && sfEnrollments.length === 0) {
            issues.push('Enrollment records exist but not linked to course');
            recommendations.push('Check course linking logic in enrollment creation');
            recommendations.push('Verify course external ID matching');
        }

        // Step 7: Test enrollment creation manually
        console.log('\n🧪 STEP 7: Testing Manual Enrollment Creation...');
        console.log('-'.repeat(60));
        
        if (doceboEnrollments.length > 0) {
            const testEnrollment = doceboEnrollments[0];
            console.log(`Testing with Docebo enrollment: User ${testEnrollment.user_id}`);
            
            // Find the user in Salesforce
            const sfUser = await conn.sobject("Docebo_Users__c")
                .findOne({ User_Unique_Id__c: testEnrollment.user_id });
                
            if (sfUser) {
                console.log(`✅ Found Salesforce user: ${sfUser.Id}`);
                
                const testEnrollmentData = {
                    Course__c: sfCourse.Id,
                    Docebo_User__c: sfUser.Id,
                    Enrollment_ID__c: `72-${testEnrollment.user_id}`,
                    Status__c: testEnrollment.status || 'enrolled',
                    Enrollment_Date__c: new Date().toISOString(),
                    Completion__c: testEnrollment.completion_percentage || 0
                };
                
                try {
                    const testResult = await conn.sobject("Docebo_CourseEnrollment__c")
                        .create(testEnrollmentData);
                        
                    if (testResult.success) {
                        console.log(`✅ Test enrollment created successfully: ${testResult.id}`);
                        
                        // Clean up test enrollment
                        await conn.sobject("Docebo_CourseEnrollment__c").delete(testResult.id);
                        console.log('🗑️ Test enrollment cleaned up');
                        
                        recommendations.push('Manual enrollment creation works - check webhook processing');
                    } else {
                        console.log('❌ Test enrollment failed:', testResult.errors);
                        issues.push('Manual enrollment creation failed');
                    }
                } catch (testError) {
                    console.log('❌ Test enrollment error:', testError.message);
                    issues.push(`Manual enrollment creation error: ${testError.message}`);
                }
            } else {
                console.log('❌ Salesforce user not found for test');
                issues.push('User sync issues may be preventing enrollment creation');
            }
        }

        return {
            success: true,
            doceboEnrollments: doceboEnrollments.length,
            salesforceEnrollments: sfEnrollments.length,
            enrollmentsWithPattern: enrollmentsWithCourse72.length,
            issues: issues,
            recommendations: recommendations
        };

    } catch (error) {
        console.error('💥 Error in diagnosis:', error);
        return {
            success: false,
            error: error.message
        };
    }
}

// Execute the diagnosis
console.log('🔄 Starting Welcome Course Enrollment Diagnosis...');
diagnoseWelcomeCourseEnrollments()
    .then((result) => {
        console.log('\n📋 DIAGNOSIS SUMMARY:');
        console.log('=' .repeat(60));
        
        if (result.success) {
            console.log(`📊 Docebo Enrollments: ${result.doceboEnrollments}`);
            console.log(`📊 Salesforce Enrollments: ${result.salesforceEnrollments}`);
            console.log(`📊 Enrollments with Course 72 Pattern: ${result.enrollmentsWithPattern}`);
            
            if (result.issues.length > 0) {
                console.log('\n🚨 ISSUES IDENTIFIED:');
                result.issues.forEach((issue, index) => {
                    console.log(`   ${index + 1}. ${issue}`);
                });
            }
            
            if (result.recommendations.length > 0) {
                console.log('\n💡 RECOMMENDATIONS:');
                result.recommendations.forEach((rec, index) => {
                    console.log(`   ${index + 1}. ${rec}`);
                });
            }
            
        } else {
            console.log(`❌ Diagnosis failed: ${result.error}`);
        }
        
        console.log('\n✅ Diagnosis completed');
        process.exit(0);
    })
    .catch(err => {
        console.error('💥 Diagnosis failed:', err);
        process.exit(1);
    });
