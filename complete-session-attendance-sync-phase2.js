const getConnection = require('./platform/salesforce/common/getConnection');

module.exports = async function completeSessionAttendanceSyncPhase2(phase1Result) {
    try {
        console.log('🚀 PHASE 2: SYNCING SESSION ATTENDANCE TO SALESFORCE');
        console.log('=' .repeat(80));
        
        const conn = await getConnection();
        if (!conn || !conn.accessToken) {
            throw new Error('Failed to connect to Salesforce in Phase 2');
        }

        const {
            missingAttendance,
            sessionMapping,
            courseMapping,
            userMappings,
            existingUserSessionCombo,
            existingAttendanceIds
        } = phase1Result;

        // Step 7: Prepare attendance records with final duplicate check
        console.log('\n📝 STEP 7: Preparing Session Attendance Records (Final Duplicate Check)...');
        console.log('-'.repeat(50));
        
        const attendanceToCreate = [];
        const attendanceIdsToCreate = new Set(); // Track what we're about to create
        let skippedCount = 0;
        let finalDuplicatesSkipped = 0;
        const sessionStats = new Map();
        
        for (const doceboAttendance of missingAttendance) {
            const sessionId = doceboAttendance.session_id;
            const courseId = doceboAttendance.course_id;
            const userId = doceboAttendance.user_id;
            const salesforceUserId = userMappings.get(userId);
            const salesforceSession = sessionMapping.get(sessionId.toString());
            const salesforceCourse = courseMapping.get(courseId.toString());
            
            if (!salesforceUserId || !salesforceCourse) {
                skippedCount++;
                continue;
            }
            
            // Final duplicate check: ensure we don't create duplicates within this batch
            const sessionAttendanceId = `S-${sessionId}-${userId}`;
            const userCourseKey = `${salesforceUserId}-${salesforceCourse.id}`;
            
            if (attendanceIdsToCreate.has(sessionAttendanceId) || existingUserSessionCombo.has(userCourseKey)) {
                finalDuplicatesSkipped++;
                continue;
            }
            
            // Add to tracking sets
            attendanceIdsToCreate.add(sessionAttendanceId);
            existingUserSessionCombo.add(userCourseKey); // Prevent duplicates in same batch
            
            // Track stats by session
            if (!sessionStats.has(sessionId)) {
                sessionStats.set(sessionId, { 
                    name: salesforceSession?.name || doceboAttendance.session_name || 'Unknown', 
                    count: 0 
                });
            }
            sessionStats.get(sessionId).count++;
            
            // Parse enrollment/attendance date
            let attendanceDate = "";
            if (doceboAttendance.enrollment_date || doceboAttendance.date_inscr || doceboAttendance.session_enrollment_date) {
                const dateStr = doceboAttendance.enrollment_date || doceboAttendance.date_inscr || doceboAttendance.session_enrollment_date;
                try {
                    // Handle both space and T separators like the webhook does
                    attendanceDate = new Date(dateStr.replace(' ', 'T')).toISOString();
                } catch (e) {
                    attendanceDate = ""; // Use empty string like webhook template
                }
            }
            
            // Parse completion date
            let completionDate = "";
            if (doceboAttendance.completion_date) {
                try {
                    completionDate = new Date(doceboAttendance.completion_date.replace(' ', 'T')).toISOString();
                } catch (e) {
                    completionDate = "";
                }
            }
            
            // Use exact field mapping from working webhook implementation for session attendance
            const attendanceRecord = {
                Course__c: salesforceCourse.id,
                Docebo_User__c: salesforceUserId,
                Enrollment_ID__c: sessionAttendanceId.substring(0, 16), // Limit to 16 chars as per webhook
                Status__c: doceboAttendance.status || 'subscribed', // Use 'subscribed' as default like webhook
                Enrollment_Date__c: attendanceDate,
                Completion__c: doceboAttendance.completion_percentage || doceboAttendance.completion || 0,
                Completion_Date__c: completionDate,
                Score__c: doceboAttendance.score || doceboAttendance.score_given || 0,
                Credits__c: doceboAttendance.credits || 0,
                Time_in_course__c: doceboAttendance.total_time || doceboAttendance.time_in_course || 0,
                Completed_Learning_Objects__c: doceboAttendance.completed_learning_objects || 0,
                Unenrollment_Date__c: doceboAttendance.unenrollment_date ? 
                    new Date(doceboAttendance.unenrollment_date.replace(' ', 'T')).toISOString() : ""
            };
            
            attendanceToCreate.push(attendanceRecord);
        }
        
        console.log(`Prepared ${attendanceToCreate.length.toLocaleString()} session attendance records`);
        console.log(`Skipped ${skippedCount.toLocaleString()} attendance records (missing users/courses)`);
        console.log(`🛡️ Final duplicates prevented: ${finalDuplicatesSkipped.toLocaleString()} attendance records`);
        
        // Show top sessions to be synced
        const topSessionsToSync = Array.from(sessionStats.entries())
            .sort((a, b) => b[1].count - a[1].count)
            .slice(0, 10);
        
        console.log(`\n📊 Top 10 sessions to sync attendance:`);
        topSessionsToSync.forEach(([sessionId, stats], index) => {
            console.log(`   ${index + 1}. Session ${sessionId} (${stats.name}): ${stats.count.toLocaleString()} attendance records`);
        });

        if (attendanceToCreate.length === 0) {
            console.log('⚠️ No attendance records to create (all users/courses missing)');
            return {
                success: true,
                synced: 0,
                total: phase1Result.doceboTotal,
                existing: phase1Result.salesforceInitial,
                skipped: skippedCount,
                totalProcessed: phase1Result.totalProcessed,
                sessionsProcessed: phase1Result.sessionsProcessed
            };
        }

        // Step 8: Create attendance records in batches with duplicate-safe processing
        console.log('\n💾 STEP 8: Creating Session Attendance in Salesforce (Duplicate-Safe)...');
        console.log('-'.repeat(50));
        
        let successCount = 0;
        let errorCount = 0;
        let duplicateErrorCount = 0;
        const batchSize = 50;
        const totalBatches = Math.ceil(attendanceToCreate.length / batchSize);
        
        console.log(`Processing ${attendanceToCreate.length.toLocaleString()} attendance records in ${totalBatches.toLocaleString()} batches...`);
        console.log(`🛡️ Duplicate prevention: Multi-layer checking enabled`);
        
        for (let i = 0; i < attendanceToCreate.length; i += batchSize) {
            const batch = attendanceToCreate.slice(i, i + batchSize);
            const batchNum = Math.floor(i / batchSize) + 1;
            
            console.log(`   📦 Processing batch ${batchNum.toLocaleString()}/${totalBatches.toLocaleString()} (${batch.length} records)...`);
            
            try {
                const results = await conn.sobject("Docebo_CourseEnrollment__c")
                    .create(batch);
                    
                const resultArray = Array.isArray(results) ? results : [results];
                
                let batchSuccessCount = 0;
                let batchErrorCount = 0;
                
                resultArray.forEach((result) => {
                    if (result.success) {
                        successCount++;
                        batchSuccessCount++;
                    } else {
                        errorCount++;
                        batchErrorCount++;
                        
                        const errorMessage = result.errors?.[0]?.message || 'Unknown error';
                        
                        // Track duplicate errors separately
                        if (errorMessage.toLowerCase().includes('duplicate') || 
                            errorMessage.toLowerCase().includes('unique') ||
                            errorMessage.includes('DUPLICATE_VALUE')) {
                            duplicateErrorCount++;
                        }
                        
                        if (batchErrorCount <= 3) { // Only show first 3 errors per batch
                            console.log(`      ⚠️ Error: ${errorMessage}`);
                        }
                    }
                });
                
                console.log(`      ✅ Batch ${batchNum}: ${batchSuccessCount} success, ${batchErrorCount} errors`);
                
                // Progress indicator every 50 batches
                if (batchNum % 50 === 0 || batchNum === totalBatches) {
                    const progressPercent = ((i + batch.length) / attendanceToCreate.length * 100).toFixed(1);
                    console.log(`      📊 Progress: ${progressPercent}% (${successCount.toLocaleString()} created, ${errorCount.toLocaleString()} errors)`);
                }
                
                // Rate limiting - pause between batches
                if (batchNum < totalBatches) {
                    await new Promise(resolve => setTimeout(resolve, 500));
                }
                
            } catch (batchError) {
                console.error(`      ❌ Batch ${batchNum} failed:`, batchError.message);
                errorCount += batch.length;
            }
        }

        // Step 9: Final verification
        console.log('\n🔍 STEP 9: Final Verification...');
        console.log('-'.repeat(50));
        
        const finalAttendance = await conn.sobject("Docebo_CourseEnrollment__c")
            .find({})
            .execute();

        return {
            success: true,
            doceboTotal: phase1Result.doceboTotal,
            salesforceInitial: phase1Result.salesforceInitial,
            salesforceFinal: finalAttendance.length,
            synced: successCount,
            errors: errorCount,
            duplicateErrors: duplicateErrorCount,
            skipped: skippedCount,
            totalProcessed: phase1Result.totalProcessed,
            sessionsProcessed: phase1Result.sessionsProcessed
        };

    } catch (error) {
        console.error('💥 Error in session attendance sync phase 2:', error);
        return {
            success: false,
            error: error.message
        };
    }
};

// If this file is run directly, show completion message
if (require.main === module) {
    console.log('📋 SESSION ATTENDANCE BULK SYNC PHASE 2 SUMMARY:');
    console.log('=' .repeat(70));
    
    const result = process.argv[2] ? JSON.parse(process.argv[2]) : {};
    
    if (result.success) {
        console.log(`📊 Total Sessions Processed: ${result.totalProcessed?.toLocaleString() || 'N/A'}`);
        console.log(`📊 Session Attendance Found: ${result.doceboTotal.toLocaleString()}`);
        console.log(`📊 Sessions Processed: ${result.sessionsProcessed || 'N/A'}`);
        console.log(`📊 Salesforce Initial: ${result.salesforceInitial.toLocaleString()}`);
        console.log(`📊 Salesforce Final: ${result.salesforceFinal.toLocaleString()}`);
        console.log(`✅ Successfully Synced: ${result.synced.toLocaleString()}`);
        
        if (result.errors > 0) {
            console.log(`❌ Total Errors: ${result.errors.toLocaleString()}`);
            if (result.duplicateErrors > 0) {
                console.log(`🛡️ Duplicate Errors (Expected): ${result.duplicateErrors.toLocaleString()}`);
                console.log(`⚠️ Other Errors: ${(result.errors - result.duplicateErrors).toLocaleString()}`);
            }
        }
        if (result.skipped > 0) {
            console.log(`⏭️ Skipped: ${result.skipped.toLocaleString()}`);
        }
        
        const netIncrease = result.salesforceFinal - result.salesforceInitial;
        console.log(`\n🎉 NET RESULT: ${netIncrease.toLocaleString()} new session attendance records added!`);
        
        const successRate = result.synced / (result.synced + result.errors) * 100;
        console.log(`📊 Success Rate: ${successRate.toFixed(1)}%`);
        
        if (successRate >= 95) {
            console.log(`🎉 EXCELLENT: 95%+ success rate!`);
        } else if (successRate >= 80) {
            console.log(`✅ GOOD: 80%+ success rate!`);
        } else {
            console.log(`⚠️ REVIEW NEEDED: Success rate below 80%`);
        }
        
        console.log('\n🛡️ DUPLICATE PREVENTION SUMMARY:');
        console.log('✅ Multi-layer duplicate checking implemented:');
        console.log('   1. Session Attendance ID pattern matching (S-sessionId-userId)');
        console.log('   2. User-Course combination tracking');
        console.log('   3. Batch-level duplicate prevention');
        console.log('   4. Salesforce-level duplicate error handling');
        
        console.log('\n💡 NEXT STEPS:');
        console.log('1. ✅ Verify session attendance data in Salesforce');
        console.log('2. 🔄 Ensure session webhooks are working for future attendance');
        console.log('3. 📊 Set up monitoring for ongoing session sync health');
        console.log('4. 🎯 Consider scheduling regular bulk syncs for session data integrity');
        console.log('5. 🛡️ Monitor duplicate prevention effectiveness');
        
    } else {
        console.log(`❌ Session attendance bulk sync failed: ${result.error}`);
    }
    
    console.log('\n✅ Complete session attendance bulk sync finished');
    process.exit(0);
}
