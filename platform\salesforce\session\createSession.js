const getConnection = require("../common/getConnection");
const { createInstructor } = require("../instructors/createInstructor");
const { createNewCourse } = require("../courses/createCourse");
const doceboService = require("../../docebo/services");

const sessionTemplate = {
    Attendance__c: "",
    Archive_Date__c: "",
    Archived_Enrollment__c: "",
    Calendar_Sync_url__c: "",
    Completion_date__c: "",
    CourseId__c: "",
    CreatedById: "",
    Date_End__c: "",
    Date_Start__c: "",
    Deleted__c: false,
    Deletion_Date__c: "",
    Enrollment_Date__c: "",
    Evaluation_Status__c: "",
    Event_Attendance_Hours__c: 0,
    Event_Attendance_Status__c: "",
    Instructor_Feedback__c: "",
    LastModifiedById: "",
    Learner_Evaluation__c: "",
    Session_Attendance_Type__c: "",
    Session_Code__c: "",
    Session_Completion_Date__c: "",
    Session_Enrollment_Date__c: "",
    Session_External_ID__c: "",
    Session_Maximum_Enrollments__c: "",
    Session_Minimum_Enrollments__c: "",
    Session_Name__c: "",
    Name: "",
    Session_URL__c: "",
    Time_in_Session__c: "",
    User_Course_Level__c: "",
    Webinar_Tool__c: "",
    Webinar_Tool_Time_in_Session__c: 0
}

function adjustSessionData(courseSalesForceId, sessionData) {
    let tmpSession = { ...sessionTemplate };
    const { Attendance__c, CreatedById, LastModifiedById, Name, ...rest } = tmpSession;
    tmpSession = rest;
    tmpSession.CourseId__c = courseSalesForceId;
    // tmpSession.Course_External_Id__c = courseSalesForceId;
    // tmpSession.Archive_Date__c = sessionData.lastEdit.dateTime == null ? "" : new Date(sessionData.lastEdit.dateTime).toISOString();
    tmpSession.Archive_Date__c = sessionData.lastEdit.dateTime == null ? "" : new Date(sessionData.lastEdit.dateTime.replace(' ', 'T')).toISOString();
    tmpSession.Archived_Enrollment__c = false;
    // tmpSession.Date_Start__c = sessionData.date_start == "" ? "" : new Date(sessionData.date_start).toISOString();
    tmpSession.Date_Start__c = sessionData.date_start == "" ? "" : new Date(sessionData.date_start.replace(' ', 'T')).toISOString();
    // tmpSession.Date_End__c = sessionData.date_end == "" ? "" : new Date(sessionData.date_end).toISOString();
    tmpSession.Date_End__c = sessionData.date_end == "" ? "" : new Date(sessionData.date_end.replace(' ', 'T')).toISOString();
    tmpSession.Enrollment_Date__c = "";
    // tmpSession.Evaluation_Status__c = sessionData.evaluation_type;
    tmpSession.Evaluation_Status__c = "";

    tmpSession.Event_Attendance_Hours__c = sessionData.min_attended_dates_for_completion * 24;
    tmpSession.Session_Code__c = 0; // sessionData.code is given as string, but in salesforce it is number;
    tmpSession.Session_Completion_Date__c = "";
    tmpSession.Session_Enrollment_Date__c = "";
    tmpSession.Session_External_ID__c = sessionData.session_id;
    tmpSession.Session_Maximum_Enrollments__c = sessionData.max_enroll;
    tmpSession.Session_Minimum_Enrollments__c = sessionData.min_enroll;
    tmpSession.Session_Name__c = sessionData.name;
    tmpSession.Time_in_Session__c = handleTimeDifference(
        sessionData.date_start,
        sessionData.date_end
    );
    tmpSession.Webinar_Tool_Time_in_Session__c = 0;

    return tmpSession;
}

function handleTimeDifference(startDate, endDate) {
    const dateStart = new Date(startDate);
    const dateEnd = new Date(endDate);

    const diffMs = dateEnd - dateStart;
    const diffHours = Math.floor((diffMs / (1000 * 60 * 60)) % 24);

    return diffHours;
}


async function getIltSessionSalesForceId(sessionCode) {
    let conn;
    try {
        conn = await getConnection();
    } catch (err) {
        console.error("Error getting Salesforce connection:", err);
        return null;
    }
    
    if (!conn || !conn.accessToken) {
        console.error("Invalid Salesforce connection in getIltSessionSalesForceId");
        return null;
    }
    
    try {
        const record = await conn.sobject("Docebo_Session__c")
            .findOne({ Session_External_ID__c: sessionCode.toString() });
            
        if (!record) {
            console.log("Session doesn't exist in Salesforce");
            return null;
        }
        
        console.log(`Session found. ID: ${record.Id}`);
        return record.Id;
    } catch (err) {
        console.error("Error finding session record:", err);
        return null;
    }
}

async function getCourseSalesForceId(courseId) {
    let conn;
    try {
        conn = await getConnection();
    } catch (err) {
        console.error("Error getting Salesforce connection:", err);
        return null;
    }
    
    if (!conn || !conn.accessToken) {
        console.error("Invalid Salesforce connection in getCourseSalesForceId");
        return null;
    }
    
    try {
        const record = await conn.sobject("Docebo_Course__c")
            .findOne({ Course_Internal_ID__c: courseId });
            
        if (!record) {
            console.log("Course doesn't exist in Salesforce");
            return null;
        }
        
        console.log(`Course found. ID: ${record.Id}`);
        return record.Id;
    } catch (err) {
        console.error("Error finding course record:", err);
        return null;
    }
}


async function createNewSession(sessionData, courseData) {
    let saveRes = false;
    
    // Check if session already exists
    let sessionSalesForceId = await getIltSessionSalesForceId(sessionData.session_id);
    if (sessionSalesForceId != null) {
        console.log("Session already exists in Salesforce");
        return true;
    }
    
    // Get Salesforce connection
    let conn;
    try {
        conn = await getConnection();
    } catch (err) {
        console.error("Error getting Salesforce connection:", err);
        return false;
    }
    
    if (!conn || !conn.accessToken) {
        console.error("Invalid Salesforce connection in createNewSession");
        return false;
    }
    
    // Get or create course
    let courseSalesForceId = await getCourseSalesForceId(courseData.id);
    if (courseSalesForceId == null) {
        const newCourseId = await createNewCourse(courseData);
        if (newCourseId != null) {
            courseSalesForceId = newCourseId;
        } else {
            console.log("Session creation failed - could not create course");
            return false;
        }
    }
    
    // Create session
    let tmpSession = adjustSessionData(courseSalesForceId, sessionData);
    try {
        const result = await conn.sobject("Docebo_Session__c").create(tmpSession);
        if (result.success) {
            saveRes = true;
            
            // After successful session creation, get instructor data if available
            console.log(`🔍 INSTRUCTOR PROCESSING for Session ${sessionData.session_id}:`);
            console.log(`   Session Data Keys: ${Object.keys(sessionData).join(', ')}`);
            console.log(`   instructor_id: ${sessionData.instructor_id || 'NOT FOUND'}`);
            console.log(`   instructors: ${sessionData.instructors ? JSON.stringify(sessionData.instructors) : 'NOT FOUND'}`);

            if (sessionData.instructor_id) {
                try {
                    console.log(`📡 Calling getInstructorData for instructor ${sessionData.instructor_id}`);
                    const instructorResponse = await doceboService.getInstructorData(
                        sessionData.instructor_id,
                        courseData.id,
                        sessionData.session_id
                    );

                    console.log(`📡 Instructor API Response Status: ${instructorResponse?.status}`);
                    console.log(`📡 Instructor API Response Data: ${instructorResponse?.data ? 'Present' : 'Missing'}`);

                    if (instructorResponse.status === 200 && instructorResponse.data) {
                        console.log(`🔧 Creating instructor for session ${sessionData.session_id}`);
                        const instructorResult = await createInstructor(
                            instructorResponse.data,
                            sessionData.instructor_id,
                            courseData.id,
                            sessionData.session_id
                        );
                        console.log(`🔧 Instructor creation result: ${instructorResult ? 'SUCCESS' : 'FAILED'}`);
                    } else {
                        console.log(`❌ Invalid instructor API response for instructor ${sessionData.instructor_id}`);
                    }
                } catch (err) {
                    console.error("❌ Error processing instructor data:", err);
                    // Don't fail the session creation if instructor processing fails
                }
            } else {
                console.log(`⚠️ No instructor_id found in session data - session will be created without instructor`);
            }
        }
    } catch (err) {
        console.error("Error creating session:", err);
        return false;
    }
    
    return saveRes;
}

module.exports = {
    createNewSession,
    getIltSessionSalesForceId
}