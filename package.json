{"name": "strive", "version": "1.0.0", "main": "index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1", "start": "node app.js", "dev": "nodemon app.js", "logs": "node utils/viewLogs.js", "logs:today": "node utils/viewLogs.js", "logs:date": "node utils/viewLogs.js"}, "author": "", "license": "ISC", "description": "", "dependencies": {"@hubspot/api-client": "^12.0.1", "axios": "^1.7.9", "cors": "^2.8.5", "dotenv": "^16.4.7", "express": "^4.21.2", "fabric": "^6.6.2", "fs": "^0.0.1-security", "html-to-text": "^9.0.5", "jsforce": "^3.6.3", "ngrok": "^5.0.0-beta.2", "nodemon": "^3.1.9", "uuid": "^11.0.5"}}