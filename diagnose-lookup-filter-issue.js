require('dotenv').config();
const getConnection = require('./platform/salesforce/common/getConnection');

async function diagnoseLookupFilterIssue() {
    try {
        console.log('🔍 Diagnosing Lead__c Lookup Filter Issue');
        console.log('=' .repeat(70));
        console.log('🎯 INVESTIGATING:');
        console.log('   Error: "Value does not exist or does not match filter criteria"');
        console.log('   Field: Lead__c on Docebo_Users__c object');
        console.log('   Email: <EMAIL>');
        console.log('=' .repeat(70));
        
        const conn = await getConnection();
        if (!conn || !conn.accessToken) {
            console.error("❌ Invalid Salesforce connection");
            return;
        }

        // Step 1: Analyze the Lead__c field configuration
        console.log('\n🔍 Step 1: Analyzing Lead__c field configuration...');
        
        try {
            const doceboUserDescription = await conn.sobject("Docebo_Users__c").describe();
            const leadField = doceboUserDescription.fields.find(f => f.name === 'Lead__c');
            
            if (leadField) {
                console.log('\n📋 Lead__c Field Details:');
                console.log(`   Type: ${leadField.type}`);
                console.log(`   Required: ${leadField.nillable ? 'No' : 'Yes'}`);
                console.log(`   Updateable: ${leadField.updateable}`);
                console.log(`   Has Lookup Filter: ${leadField.filteredLookupInfo ? 'Yes' : 'No'}`);
                
                if (leadField.filteredLookupInfo) {
                    console.log('\n🚨 LOOKUP FILTER DETECTED:');
                    console.log(`   Controlling Fields: ${JSON.stringify(leadField.filteredLookupInfo.controllingFields)}`);
                    console.log(`   Dependent: ${leadField.filteredLookupInfo.dependent}`);
                    console.log(`   Optional Filter: ${leadField.filteredLookupInfo.optionalFilter}`);
                    console.log(`   Filter Active: ${leadField.filteredLookupInfo.active}`);
                } else {
                    console.log('✅ No lookup filter detected on Lead__c field');
                }
            } else {
                console.log('❌ Lead__c field not found');
            }
        } catch (describeError) {
            console.log(`❌ Error describing field: ${describeError.message}`);
        }

        // Step 2: Find the specific Lead that's failing
        console.log('\n🔍 Step 2: Finding <NAME_EMAIL>...');
        
        const leads = await conn.sobject("Lead")
            .find({ Email: '<EMAIL>' })
            .execute();
        
        if (leads.length > 0) {
            console.log(`\n📋 Found ${leads.length} Lead(s) <NAME_EMAIL>:`);
            
            for (let i = 0; i < leads.length; i++) {
                const lead = leads[i];
                console.log(`\n📊 Lead ${i + 1}: ${lead.Id}`);
                console.log(`   Name: ${lead.FirstName} ${lead.LastName}`);
                console.log(`   Email: ${lead.Email}`);
                console.log(`   Status: ${lead.Status}`);
                console.log(`   Owner: ${lead.OwnerId}`);
                console.log(`   Lead Source: ${lead.LeadSource}`);
                console.log(`   Created by Docebo API: ${lead.Created_by_Docebo_API__c}`);
                console.log(`   Is Converted: ${lead.IsConverted}`);
                console.log(`   Created Date: ${lead.CreatedDate}`);
                console.log(`   Company: ${lead.Company}`);
                
                // Test if this Lead can be associated
                console.log(`\n🧪 Testing association with Lead ${lead.Id}...`);
                
                // Find a test Docebo_Users__c record to try association
                const testDoceboUser = await conn.sobject("Docebo_Users__c")
                    .findOne({ Email__c: '<EMAIL>' });
                
                if (testDoceboUser) {
                    console.log(`   Using test Docebo_Users__c: ${testDoceboUser.Id}`);
                    
                    try {
                        const testResult = await conn.sobject("Docebo_Users__c").update({
                            Id: testDoceboUser.Id,
                            Lead__c: lead.Id
                        });
                        
                        if (testResult.success) {
                            console.log(`   ✅ SUCCESS: Lead ${lead.Id} can be associated`);
                            
                            // Reset the association for next test
                            await conn.sobject("Docebo_Users__c").update({
                                Id: testDoceboUser.Id,
                                Lead__c: null
                            });
                        } else {
                            console.log(`   ❌ FAILED: ${JSON.stringify(testResult.errors)}`);
                        }
                    } catch (testError) {
                        console.log(`   ❌ ERROR: ${testError.message}`);
                        console.log(`   Error Code: ${testError.errorCode}`);
                        
                        if (testError.errorCode === 'FIELD_FILTER_VALIDATION_EXCEPTION') {
                            console.log('\n🚨 FIELD_FILTER_VALIDATION_EXCEPTION CONFIRMED!');
                            console.log('   This Lead fails the lookup filter criteria');
                        }
                    }
                } else {
                    console.log('   ⚠️ No Docebo_Users__c record found for testing');
                }
            }
        } else {
            console.log('❌ No Leads found <NAME_EMAIL>');
        }

        // Step 3: Find Leads that DO work with the filter
        console.log('\n🔍 Step 3: Finding Leads that pass the lookup filter...');
        
        // Find a Docebo_Users__c record that has a successful Lead association
        const successfulAssociations = await conn.sobject("Docebo_Users__c")
            .find({ Lead__c: { $ne: null } })
            .limit(5)
            .execute();
        
        if (successfulAssociations.length > 0) {
            console.log(`\n📋 Found ${successfulAssociations.length} successful Lead associations:`);
            
            for (const doceboUser of successfulAssociations) {
                const associatedLead = await conn.sobject("Lead")
                    .findOne({ Id: doceboUser.Lead__c });
                
                if (associatedLead) {
                    console.log(`\n✅ Successful Association Example:`);
                    console.log(`   Docebo_Users__c: ${doceboUser.Id}`);
                    console.log(`   Lead: ${associatedLead.Id}`);
                    console.log(`   Lead Status: ${associatedLead.Status}`);
                    console.log(`   Lead Owner: ${associatedLead.OwnerId}`);
                    console.log(`   Lead Source: ${associatedLead.LeadSource}`);
                    console.log(`   Created by Docebo API: ${associatedLead.Created_by_Docebo_API__c}`);
                    console.log(`   Is Converted: ${associatedLead.IsConverted}`);
                }
            }
        } else {
            console.log('⚠️ No successful Lead associations found');
        }

        // Step 4: Provide recommendations
        console.log('\n📊 DIAGNOSIS SUMMARY:');
        console.log('=' .repeat(70));
        
        console.log('\n🔧 LIKELY CAUSES:');
        console.log('   1. Lead Status filter (e.g., only "Open - Not Contacted" allowed)');
        console.log('   2. Lead Owner filter (e.g., only specific owners allowed)');
        console.log('   3. Lead Source filter (e.g., only "Docebo Platform" allowed)');
        console.log('   4. Created_by_Docebo_API__c filter (e.g., only true values allowed)');
        console.log('   5. IsConverted filter (e.g., only unconverted leads allowed)');
        
        console.log('\n💡 RECOMMENDED SOLUTIONS:');
        console.log('   1. IMMEDIATE: Modify Lead creation to match filter criteria');
        console.log('   2. LONG-TERM: Remove or adjust the lookup filter in Salesforce');
        console.log('   3. ALTERNATIVE: Use Contact associations instead of Lead');
        console.log('   4. WORKAROUND: Create new Leads that match filter criteria');
        
        console.log('\n🔧 CODE FIXES TO IMPLEMENT:');
        console.log('   • Ensure Lead Status = "Open - Not Contacted"');
        console.log('   • Ensure Lead Source = "Docebo Platform"');
        console.log('   • Ensure Created_by_Docebo_API__c = true');
        console.log('   • Ensure IsConverted = false');
        console.log('   • Set appropriate Lead Owner');
        
        console.log('\n⚠️ ADMIN ACTION REQUIRED:');
        console.log('   Contact Salesforce Admin to review Lead__c lookup filter');
        console.log('   Consider relaxing filter criteria for Docebo integration');

        return {
            success: true,
            hasLookupFilter: true,
            leadsFound: leads.length,
            message: 'Lookup filter diagnosis completed'
        };

    } catch (error) {
        console.error('💥 Error in lookup filter diagnosis:', error);
        return {
            success: false,
            error: error.message
        };
    }
}

// Execute the diagnosis
console.log('🔄 Starting lookup filter diagnosis...');
diagnoseLookupFilterIssue()
    .then((result) => {
        console.log('\n✅ Lookup filter diagnosis completed');
        if (result.success) {
            console.log('🎯 Root cause identified - lookup filter issue');
        } else {
            console.log('❌ Diagnosis failed. Check the logs above.');
        }
        process.exit(0);
    })
    .catch(err => {
        console.error('💥 Diagnosis failed:', err);
        process.exit(1);
    });
