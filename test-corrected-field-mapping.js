require('dotenv').config();
const { tidyData } = require('./platform/salesforce/users/createUser');

// Test the corrected field mapping with real User 18851 data
function testCorrectedFieldMapping() {
    console.log('🧪 Testing Corrected Field Mapping with Real User 18851 Data...');
    
    // Real Docebo data from User 18851
    const realDoceboData = {
        userInfo: {
            user_data: {
                user_id: "18851",
                username: "<EMAIL>",
                first_name: "Fname",
                last_name: "lN<PERSON>",
                email: "<EMAIL>",
                language: "english",
                timezone: "Europe/Budapest",
                valid: "1",
                level: "6"
            },
            additional_fields: [
                {
                    id: "14",
                    title: "Organization/Employer",
                    value: "55", // Boston Opportunity Agenda
                    enabled: true,
                    options: [
                        { id: "55", label: "Boston Opportunity Agenda" }
                    ]
                },
                {
                    id: "23",
                    title: "Organization/Employer",
                    value: null,
                    enabled: true
                },
                {
                    id: "21",
                    title: "Does your organization serve a national, regional, or local community?",
                    value: null,
                    enabled: true
                },
                {
                    id: "20",
                    title: "Are you tied to one of these initiatives?",
                    value: "141", // ENOUGH Act
                    enabled: true,
                    options: [
                        { id: "141", label: "ENOUGH Act" }
                    ]
                },
                {
                    id: "8",
                    title: "Job Title",
                    value: "JTitle",
                    enabled: true
                },
                {
                    id: "9",
                    title: "Role Type",
                    value: "19", // Communications
                    enabled: true,
                    options: [
                        { id: "19", label: "Communications" }
                    ]
                },
                {
                    id: "12",
                    title: "Race Identity",
                    value: "36", // Hispanic or Latine
                    enabled: true,
                    options: [
                        { id: "36", label: "Hispanic or Latine" }
                    ]
                },
                {
                    id: "13",
                    title: "Gender Identity",
                    value: "45", // Prefer not to respond
                    enabled: true,
                    options: [
                        { id: "45", label: "Prefer not to respond" }
                    ]
                },
                {
                    id: "24",
                    title: "City",
                    value: "City",
                    enabled: true
                },
                {
                    id: "25",
                    title: "State",
                    value: "166", // Arkansas
                    enabled: true,
                    options: [
                        { id: "166", label: "Arkansas" }
                    ]
                }
            ],
            fired_at: "2025-06-06 18:31:13",
            expiration_date: ""
        },
        userListedInfo: null
    };

    console.log('\n🔧 TESTING CORRECTED FIELD MAPPING:');
    console.log('=' .repeat(70));

    // Test the corrected tidyData function
    const processedData = tidyData(realDoceboData.userInfo, realDoceboData.userListedInfo);

    console.log('\n📊 CORRECTED FIELD MAPPING RESULTS:');
    console.log('=' .repeat(60));

    // Check the corrected fields
    const correctedFields = [
        { field: 'Languages', expected: 'english', actual: processedData.Languages__c },
        { field: 'TimeZone', expected: 'Europe/Budapest', actual: processedData.TimeZone__c },
        { field: 'City', expected: 'City', actual: processedData.MailingCity__c },
        { field: 'State', expected: 'Arkansas', actual: processedData.MailingState__c },
        { field: 'Organization', expected: 'Boston Opportunity Agenda', actual: processedData.Organization_Name__c },
        { field: 'Job Title', expected: 'JTitle', actual: processedData.Job_Title__c },
        { field: 'Role Type', expected: 'Communications', actual: processedData.Role_Type__c },
        { field: 'Race', expected: 'Hispanic or Latine', actual: processedData.Race_Identity__c },
        { field: 'Gender', expected: 'Prefer not to respond', actual: processedData.Gender_Identity__c },
        { field: 'Initiative', expected: 'ENOUGH Act', actual: processedData.Initiative__c }
    ];

    let correctCount = 0;
    let totalCount = correctedFields.length;

    correctedFields.forEach(field => {
        const isCorrect = field.actual === field.expected;
        const status = isCorrect ? '✅' : '❌';
        
        if (isCorrect) correctCount++;
        
        console.log(`${status} ${field.field}: "${field.actual}" ${isCorrect ? '(CORRECT)' : `(Expected: "${field.expected}")`}`);
    });

    console.log('\n📊 CORRECTED MAPPING SUMMARY:');
    console.log('=' .repeat(50));
    console.log(`✅ Correctly Mapped: ${correctCount}/${totalCount} (${Math.round(correctCount/totalCount*100)}%)`);
    console.log(`❌ Still Incorrect: ${totalCount - correctCount}/${totalCount} (${Math.round((totalCount - correctCount)/totalCount*100)}%)`);

    // Test Lead data creation with corrected mapping
    console.log('\n🎯 LEAD DATA WITH CORRECTED MAPPING:');
    console.log('=' .repeat(50));
    
    const leadData = {
        LastName: processedData.Last_Name__c || "Unknown",
        FirstName: processedData.First_Name__c,
        Email: processedData.Email__c,
        Company: processedData.Organization_Name__c || "-",
        Title: processedData.Job_Title__c || "",
        Status: "Open - Not Contacted",
        Created_by_Docebo_API__c: true,
        Gender__c: processedData.Gender_Identity__c,
        Role_Type__c: processedData.Role_Type__c,
        Race__c: processedData.Race_Identity__c,
        Languages__c: processedData.Languages__c || "",
        MailingCity__c: processedData.MailingCity__c || "",
        MailingState__c: processedData.MailingState__c || "",
        Time_Zone__c: processedData.TimeZone__c || "",
        Position_Role__c: processedData.Position_Role__c || "",
        LeadSource: "Docebo Platform"
    };

    console.log('Enhanced Lead data that would be created:');
    Object.keys(leadData).forEach(key => {
        const value = leadData[key];
        if (value && value !== "" && value !== 0) {
            console.log(`   ${key}: ${value}`);
        }
    });

    // Show complete processed data
    console.log('\n📋 COMPLETE CORRECTED PROCESSED DATA:');
    console.log('=' .repeat(60));
    console.log(JSON.stringify(processedData, null, 2));

    console.log('\n💡 IMPROVEMENT ANALYSIS:');
    console.log('=' .repeat(50));
    
    if (correctCount >= 8) {
        console.log('🎉 EXCELLENT! Field mapping is now working correctly!');
        console.log('✅ The webhook will now create comprehensive Lead and Docebo_Users__c records');
    } else if (correctCount >= 6) {
        console.log('✅ GOOD! Significant improvement in field mapping');
        console.log('🔧 A few more fields may need adjustment');
    } else {
        console.log('⚠️ Some fields still need correction');
    }

    return {
        totalFields: totalCount,
        correctFields: correctCount,
        improvementPercentage: Math.round(correctCount/totalCount*100)
    };
}

// Execute the test
console.log('🔄 Starting corrected field mapping test...');
const result = testCorrectedFieldMapping();

console.log('\n🎯 CORRECTED FIELD MAPPING RESULTS:');
console.log('=' .repeat(60));
console.log(`📈 Field Accuracy: ${result.improvementPercentage}%`);
console.log(`✅ Correct Fields: ${result.correctFields}/${result.totalFields}`);

if (result.improvementPercentage >= 80) {
    console.log('🎉 EXCELLENT! Webhook field mapping is now highly accurate!');
    console.log('✅ Ready for production use with comprehensive data capture');
} else {
    console.log('🔧 Field mapping improved but may need further refinement');
}

console.log('\n✅ Corrected field mapping test completed');
process.exit(0);
