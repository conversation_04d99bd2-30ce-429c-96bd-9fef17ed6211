// Initialize logger first so all logs are captured
const logger = require('./utils/logger');

const express = require('express');
const cors = require('cors');
const bodyParser = require('body-parser');
const globalService = require("./platform/global");

const PORT = process.env.PORT || 5000;
const app = express();
app.use(cors());
app.use(bodyParser.json());
app.use(bodyParser.urlencoded({extended: true}));

const doceboRoute = require("./platform/docebo/router")
// const salesforceRoute = require("./platform/salesforce/router")

app.use("/webhook/docebo", doceboRoute);
app.listen(PORT, ()=> {
    console.log("Server is running on http://localhost:5000");
    console.log("Logs are being saved to daily log files in the logs directory");
    globalService.initCronJob();
})
