require('dotenv').config();
const axios = require('axios');

// Test webhook after fixing the field name errors
async function testFieldFix() {
    console.log('🧪 Testing Webhook After Field Name Fix...');
    
    // Simulate a user creation webhook payload for User 18853
    const webhookPayload = {
        event: "user.created",
        fired_by_batch_action: false,
        message_id: `test-field-fix-${Date.now()}`,
        payload: {
            fired_at: new Date().toISOString(),
            user_id: 18853, // Same user that was failing
            username: "<EMAIL>",
            first_name: "name",
            last_name: "last",
            email: "<EMAIL>",
            expiration_date: null
        }
    };

    console.log('\n📤 SENDING WEBHOOK PAYLOAD (After Field Fix):');
    console.log('=' .repeat(60));
    console.log(JSON.stringify(webhookPayload, null, 2));

    console.log('\n🔧 FIELD FIXES APPLIED:');
    console.log('✅ Removed Time_Zone__c from Lead creation (field doesn\'t exist)');
    console.log('✅ Removed TimeZone__c from Docebo_Users__c creation (field doesn\'t exist)');
    console.log('✅ Updated logging to exclude non-existent fields');

    try {
        console.log('\n🔄 Sending webhook to local server...');
        
        const response = await axios.post('http://localhost:3000/docebo/user-created', webhookPayload, {
            headers: {
                'Content-Type': 'application/json'
            },
            timeout: 10000
        });

        console.log('\n✅ WEBHOOK RESPONSE:');
        console.log('=' .repeat(50));
        console.log(`Status: ${response.status}`);
        console.log(`Response:`, response.data);

        console.log('\n📋 WHAT TO EXPECT IN LOGS:');
        console.log('=' .repeat(60));
        console.log('✅ No more "INVALID_FIELD" errors');
        console.log('✅ Lead created successfully message');
        console.log('✅ User created successfully message');
        console.log('✅ Enhanced logging showing processed data');

        console.log('\n💡 CHECK THE SERVER LOGS NOW!');
        console.log('Look for:');
        console.log('- 📥 SINGLE USER WEBHOOK DATA');
        console.log('- 🔧 PROCESSED USER DATA');
        console.log('- 🎯 CREATING LEAD');
        console.log('- ✅ Lead created successfully');
        console.log('- ✅ User created successfully');
        console.log('- NO ERROR messages about invalid fields');

        return true;

    } catch (error) {
        if (error.code === 'ECONNREFUSED') {
            console.log('\n⚠️ SERVER NOT RUNNING');
            console.log('Please start the server first with: npm start');
            console.log('Then run this test again to verify the field fix');
        } else {
            console.error('\n❌ WEBHOOK TEST ERROR:', error.message);
        }
        return false;
    }
}

// Execute the test
async function runFieldFixTest() {
    console.log('🚀 Starting Field Fix Test...');
    console.log('=' .repeat(70));

    console.log('\n🔍 ISSUE IDENTIFIED:');
    console.log('❌ User 18853 (<EMAIL>) failed to create because:');
    console.log('   - Time_Zone__c field doesn\'t exist on Lead object');
    console.log('   - TimeZone__c field doesn\'t exist on Docebo_Users__c object');

    console.log('\n🛠️ FIXES APPLIED:');
    console.log('✅ Commented out Time_Zone__c field in Lead creation');
    console.log('✅ Commented out TimeZone__c field in Docebo_Users__c creation');
    console.log('✅ Updated logging to exclude non-existent fields');

    // Test the fix
    const testSuccess = await testFieldFix();
    
    console.log('\n🎯 FIELD FIX TEST SUMMARY:');
    console.log('=' .repeat(60));
    
    if (testSuccess) {
        console.log('✅ Webhook sent successfully');
        console.log('✅ Field name errors should be resolved');
        console.log('✅ User 18853 should now create successfully');
        console.log('✅ Lead should now create successfully');
    } else {
        console.log('⚠️ Test couldn\'t complete - check server status');
    }

    console.log('\n💡 NEXT STEPS:');
    console.log('1. Check server logs for successful creation messages');
    console.log('2. Verify Lead and Docebo_Users__c records are created');
    console.log('3. Check Salesforce for the new records');
    console.log('4. If successful, the webhook system is now working correctly');

    console.log('\n📊 EXPECTED OUTCOME:');
    console.log('✅ No more INVALID_FIELD errors');
    console.log('✅ Lead created with comprehensive data (minus timezone)');
    console.log('✅ Docebo_Users__c created with comprehensive data (minus timezone)');
    console.log('✅ All other field mappings working correctly');

    console.log('\n✅ Field fix test completed!');
}

// Run the test
runFieldFixTest()
    .then(() => {
        console.log('\n🎉 Field fix test completed!');
        console.log('The webhook should now work correctly for User 18853 and future users.');
        process.exit(0);
    })
    .catch(err => {
        console.error('\n💥 Test failed:', err);
        process.exit(1);
    });
