require('dotenv').config();
const getConnection = require('./platform/salesforce/common/getConnection');
const doceboServices = require('./platform/docebo/services');

async function testCourseFieldMapping() {
    try {
        console.log('🧪 Testing Course Field Mapping from Docebo to Salesforce');
        console.log('=' .repeat(70));
        
        const conn = await getConnection();
        if (!conn || !conn.accessToken) {
            console.error("❌ Invalid Salesforce connection");
            return;
        }

        // Step 1: Check Salesforce Course object structure
        console.log('\n🔍 SALESFORCE COURSE OBJECT ANALYSIS:');
        console.log('-'.repeat(50));
        
        try {
            const courseObjectDesc = await conn.sobject("Docebo_Course__c").describe();
            const courseFields = courseObjectDesc.fields;
            
            console.log(`📋 Docebo_Course__c object has ${courseFields.length} fields:`);
            
            const customFields = courseFields.filter(f => f.name.endsWith('__c') || f.name === 'Name');
            console.log(`\n📋 Custom fields in Salesforce Course object:`);
            customFields.forEach(field => {
                console.log(`   ${field.name}: ${field.label} (${field.type})`);
            });
            
        } catch (describeError) {
            console.log(`❌ Error describing Docebo_Course__c object: ${describeError.message}`);
        }

        // Step 2: Get a sample course from Salesforce to test with
        console.log('\n🔍 SAMPLE COURSE ANALYSIS:');
        console.log('-'.repeat(50));
        
        const sampleCourse = await conn.sobject("Docebo_Course__c")
            .findOne({});
        
        if (!sampleCourse) {
            console.error("❌ No sample course found in Salesforce");
            return;
        }
        
        console.log(`📋 Sample Course: ${sampleCourse.Course_Name__c}`);
        console.log(`   Course External ID: ${sampleCourse.Course_External_Id__c}`);
        console.log(`   Salesforce ID: ${sampleCourse.Id}`);

        // Step 3: Test Docebo course API
        console.log('\n🔍 DOCEBO COURSE API ANALYSIS:');
        console.log('-'.repeat(50));
        
        try {
            const courseApiResponse = await doceboServices.getCourseInfo(sampleCourse.Course_External_Id__c);
            
            console.log(`📡 Docebo API Response:`);
            console.log(`   Status: ${courseApiResponse?.status || 'N/A'}`);
            
            if (courseApiResponse && courseApiResponse.data) {
                console.log(`\n📋 Available Course Fields from Docebo API:`);
                
                function logObjectFields(obj, prefix = '') {
                    Object.keys(obj).forEach(key => {
                        const value = obj[key];
                        if (value && typeof value === 'object' && !Array.isArray(value)) {
                            console.log(`   ${prefix}${key}: [Object]`);
                            logObjectFields(value, `${prefix}${key}.`);
                        } else {
                            console.log(`   ${prefix}${key}: ${Array.isArray(value) ? `[Array of ${value.length}]` : value} (${typeof value})`);
                        }
                    });
                }
                
                logObjectFields(courseApiResponse.data);
                
                // Also get course listed info
                const courseListedResponse = await doceboServices.getCourseListedInfo(sampleCourse.Course_External_Id__c);
                
                if (courseListedResponse) {
                    console.log(`\n📋 Additional Course Listed Info:`);
                    logObjectFields(courseListedResponse, 'listed.');
                }
                
            } else {
                console.log(`❌ Invalid API response structure`);
            }
            
        } catch (apiError) {
            console.log(`❌ Error calling Docebo course API: ${apiError.message}`);
        }

        // Step 4: Compare with required fields
        console.log('\n📊 REQUIRED vs CURRENT FIELD MAPPING:');
        console.log('=' .repeat(50));
        
        const requiredFields = [
            'Course Unique Id',
            'Course External Id', 
            'Course Internal ID',
            'Course Code',
            'Course Status',
            'Course Name',
            'Description',
            'Course Duration',
            'Course Type',
            'Type',
            'Skills in course',
            'Course Link',
            'Language',
            'User Course Level',
            'Number of actions',
            'Number of sessions',
            'Course Creation Date',
            'Owner',
            'Course Category',
            'Course Category Code',
            'Course Start Date',
            'Course End Date',
            'Session Time (min)',
            'Enrollment Date',
            'Effective',
            'Deleted',
            'Deletion Date',
            'Course has expired',
            'Course Progress (%)',
            'Created By',
            'Last Modified By',
            'Last Update Date',
            'Score',
            'Slug',
            'Thumbnail',
            'Training Material Time (sec)'
        ];
        
        const currentSalesforceFields = {
            'Course Unique Id': 'Course_External_Id__c', // ✅ Mapped
            'Course External Id': 'Course_External_Id__c', // ✅ Mapped
            'Course Internal ID': 'Course_Internal_ID__c', // ✅ Mapped
            'Course Code': 'Course_Code__c', // ✅ Mapped
            'Course Status': 'Course_Status__c', // ✅ Mapped
            'Course Name': 'Course_Name__c', // ✅ Mapped
            'Description': 'Description__c', // ✅ Mapped
            'Course Duration': 'Course_Duration__c', // ✅ Mapped
            'Course Type': 'Course_Type__c', // ✅ Mapped
            'Type': 'Type__c', // ✅ Mapped
            'Skills in course': 'Skills_in_course__c', // ✅ Mapped
            'Course Link': 'Course_Link__c', // ✅ Mapped
            'Language': 'Language__c', // ✅ Mapped
            'User Course Level': 'User_Course_Level__c', // ✅ Mapped
            'Number of actions': 'Number_of_actions__c', // ✅ Mapped
            'Number of sessions': 'Number_of_sessions__c', // ✅ Mapped
            'Course Creation Date': 'Course_Creation_Date__c', // ✅ Mapped
            'Owner': 'OwnerId', // ✅ Mapped
            'Course Category': 'Course_Category__c', // ✅ Mapped
            'Course Category Code': 'Course_Category_Code__c', // ✅ Mapped
            'Course Start Date': 'Course_Start_Date__c', // ✅ Mapped
            'Course End Date': 'Course_End_Date__c', // ✅ Mapped
            'Session Time (min)': 'Session_Time_min__c', // ✅ Mapped
            'Enrollment Date': 'Enrollment_Date__c', // ✅ Mapped
            'Effective': 'Effective__c', // ✅ Mapped
            'Deleted': 'Deleted__c', // ✅ Mapped
            'Deletion Date': 'Deletion_Date__c', // ✅ Mapped
            'Course has expired': 'Course_has_expired__c', // ✅ Mapped
            'Course Progress (%)': 'Course_Progress__c', // ✅ Mapped
            'Created By': 'CreatedById', // ✅ System field
            'Last Modified By': 'LastModifiedById', // ✅ System field
            'Last Update Date': 'Last_Update_Date__c', // ✅ Mapped
            'Score': 'Score__c', // ✅ Mapped
            'Slug': 'Slug__c', // ✅ Mapped
            'Thumbnail': 'Thumbnail__c', // ✅ Mapped
            'Training Material Time (sec)': 'Training_Material_Time_sec__c' // ✅ Mapped
        };
        
        console.log(`\n📋 FIELD MAPPING STATUS:`);
        requiredFields.forEach(field => {
            const salesforceField = currentSalesforceFields[field];
            const status = salesforceField ? '✅ MAPPED' : '❌ MISSING';
            console.log(`   ${field}: ${status}${salesforceField ? ` → ${salesforceField}` : ''}`);
        });

        // Step 5: Check current data population
        console.log('\n🔍 CURRENT DATA POPULATION CHECK:');
        console.log('-'.repeat(50));
        
        console.log(`📋 Sample course field values:`);
        Object.entries(currentSalesforceFields).forEach(([reqField, sfField]) => {
            const value = sampleCourse[sfField];
            const hasValue = value !== null && value !== undefined && value !== '';
            console.log(`   ${reqField}: ${hasValue ? '✅' : '❌'} "${value}"`);
        });

        // Step 6: Summary and recommendations
        console.log('\n📊 COURSE FIELD MAPPING SUMMARY:');
        console.log('=' .repeat(50));
        
        const mappedCount = Object.keys(currentSalesforceFields).length;
        const totalRequired = requiredFields.length;
        const mappingPercentage = Math.round((mappedCount / totalRequired) * 100);
        
        console.log(`\n🎯 MAPPING COVERAGE: ${mappedCount}/${totalRequired} (${mappingPercentage}%)`);
        
        console.log(`\n✅ WELL MAPPED FIELDS:`);
        console.log(`   • Basic Info: Name, Code, Status, Type, Description`);
        console.log(`   • Dates: Creation, Start, End, Last Update`);
        console.log(`   • Structure: Duration, Sessions, Actions`);
        console.log(`   • Content: Skills, Language, Thumbnail, Link`);
        console.log(`   • Status: Effective, Deleted, Expired`);
        console.log(`   • Metrics: Progress, Score, Training Time`);
        
        console.log(`\n💡 RECOMMENDATIONS:`);
        if (mappingPercentage >= 90) {
            console.log(`   🎉 Excellent coverage! All required fields are mapped.`);
        } else {
            console.log(`   🔧 Consider adding missing field mappings`);
        }
        console.log(`   📊 Verify data quality and API response completeness`);
        console.log(`   🔍 Check if additional Docebo fields should be captured`);

    } catch (error) {
        console.error('💥 Error in course field mapping test:', error);
    }
}

// Execute the test
console.log('🔄 Starting course field mapping analysis...');
testCourseFieldMapping()
    .then(() => {
        console.log('\n✅ Analysis completed');
        process.exit(0);
    })
    .catch(err => {
        console.error('💥 Analysis failed:', err);
        process.exit(1);
    });
