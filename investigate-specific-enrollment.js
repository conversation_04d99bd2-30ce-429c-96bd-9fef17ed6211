require('dotenv').config();
const getConnection = require('./platform/salesforce/common/getConnection');

async function investigateSpecificEnrollment() {
    try {
        console.log('🔍 Investigating Specific Enrollment: UE-21-17928');
        console.log('=' .repeat(60));
        
        const conn = await getConnection();
        if (!conn || !conn.accessToken) {
            console.error("❌ Invalid Salesforce connection");
            return;
        }

        const enrollmentExternalId = "UE-21-17928";
        
        // Step 1: Find the enrollment record
        console.log(`\n📋 Looking for enrollment with External_Id__c: ${enrollmentExternalId}`);
        console.log('-'.repeat(50));
        
        const enrollment = await conn.sobject("Docebo_CourseEnrollment__c")
            .findOne({ External_Id__c: enrollmentExternalId });
        
        if (!enrollment) {
            console.log(`❌ Enrollment not found with External_Id__c: ${enrollmentExternalId}`);
            
            // Try alternative search methods
            console.log('\n🔍 Trying alternative search methods...');
            
            // Search by partial match
            const partialMatches = await conn.sobject("Docebo_CourseEnrollment__c")
                .find({ External_Id__c: { $like: `%${enrollmentExternalId}%` } })
                .execute();
            
            if (partialMatches.length > 0) {
                console.log(`✅ Found ${partialMatches.length} partial matches:`);
                partialMatches.forEach((match, index) => {
                    console.log(`   ${index + 1}. ${match.Id} - External_Id__c: "${match.External_Id__c}"`);
                });
            } else {
                console.log('❌ No partial matches found either');
            }
            
            return;
        }
        
        console.log(`✅ Found enrollment record: ${enrollment.Id}`);
        console.log(`   External_Id__c: ${enrollment.External_Id__c}`);
        console.log(`   Course__c: ${enrollment.Course__c || 'MISSING ❌'}`);
        console.log(`   Docebo_User__c: ${enrollment.Docebo_User__c || 'MISSING ❌'}`);
        console.log(`   Status__c: ${enrollment.Status__c}`);
        console.log(`   Enrollment_Date__c: ${enrollment.Enrollment_Date__c}`);
        console.log(`   Completion_Date__c: ${enrollment.Completion_Date__c || 'N/A'}`);
        console.log(`   Score__c: ${enrollment.Score__c}`);
        console.log(`   Created Date: ${enrollment.CreatedDate}`);
        
        // Step 2: Parse the External_Id to understand the structure
        console.log(`\n🔍 Analyzing External_Id structure...`);
        console.log('-'.repeat(50));
        
        const externalIdParts = enrollment.External_Id__c.split('-');
        console.log(`📋 External_Id parts: ${JSON.stringify(externalIdParts)}`);
        
        if (externalIdParts.length >= 2) {
            const courseId = externalIdParts[0]; // Should be "UE"
            const actualCourseId = externalIdParts[1]; // Should be "21"
            const userId = externalIdParts[2]; // Should be "17928"
            
            console.log(`   Course ID (part 1): "${courseId}"`);
            console.log(`   Actual Course ID (part 2): "${actualCourseId}"`);
            console.log(`   User ID (part 3): "${userId}"`);
            
            // The structure seems to be UE-courseId-userId, so course ID is "21"
            const parsedCourseId = actualCourseId;
            const parsedUserId = userId;
            
            console.log(`\n🔍 Parsed IDs:`);
            console.log(`   Course ID to lookup: "${parsedCourseId}"`);
            console.log(`   User ID to lookup: "${parsedUserId}"`);
            
            // Step 3: Look for the course
            console.log(`\n🔍 Looking for course with ID: ${parsedCourseId}`);
            console.log('-'.repeat(50));
            
            // Try different course lookup methods
            const courseLookupMethods = [
                { field: 'Course_External_Id__c', value: parseInt(parsedCourseId), type: 'int' },
                { field: 'Course_External_Id__c', value: parsedCourseId, type: 'string' },
                { field: 'Course_Unique_Id__c', value: parsedCourseId, type: 'string' },
                { field: 'Course_Internal_ID__c', value: parseInt(parsedCourseId), type: 'int' },
                { field: 'Id', value: parsedCourseId, type: 'string' }
            ];
            
            let foundCourse = null;
            
            for (const method of courseLookupMethods) {
                try {
                    console.log(`   Trying ${method.field} = ${method.value} (${method.type})`);
                    const query = {};
                    query[method.field] = method.value;
                    
                    const course = await conn.sobject("Docebo_Course__c")
                        .findOne(query);
                    
                    if (course) {
                        console.log(`   ✅ FOUND! Course: ${course.Id} - ${course.Course_Name__c}`);
                        console.log(`      Course_External_Id__c: ${course.Course_External_Id__c}`);
                        console.log(`      Course_Unique_Id__c: ${course.Course_Unique_Id__c}`);
                        console.log(`      Course_Internal_ID__c: ${course.Course_Internal_ID__c}`);
                        foundCourse = course;
                        break;
                    } else {
                        console.log(`   ❌ Not found`);
                    }
                } catch (error) {
                    console.log(`   ❌ Error: ${error.message}`);
                }
            }
            
            // Step 4: Look for the user
            console.log(`\n🔍 Looking for user with ID: ${parsedUserId}`);
            console.log('-'.repeat(50));
            
            const userLookupMethods = [
                { field: 'User_Unique_Id__c', value: parseInt(parsedUserId), type: 'int' },
                { field: 'User_Unique_Id__c', value: parsedUserId, type: 'string' },
                { field: 'Id', value: parsedUserId, type: 'string' }
            ];
            
            let foundUser = null;
            
            for (const method of userLookupMethods) {
                try {
                    console.log(`   Trying ${method.field} = ${method.value} (${method.type})`);
                    const query = {};
                    query[method.field] = method.value;
                    
                    const user = await conn.sobject("Docebo_Users__c")
                        .findOne(query);
                    
                    if (user) {
                        console.log(`   ✅ FOUND! User: ${user.Id} - ${user.First_Name__c} ${user.Last_Name__c}`);
                        console.log(`      User_Unique_Id__c: ${user.User_Unique_Id__c}`);
                        console.log(`      Email: ${user.Email__c}`);
                        foundUser = user;
                        break;
                    } else {
                        console.log(`   ❌ Not found`);
                    }
                } catch (error) {
                    console.log(`   ❌ Error: ${error.message}`);
                }
            }
            
            // Step 5: Try to fix the association if both course and user are found
            if (foundCourse && foundUser) {
                console.log(`\n🔧 Attempting to fix the association...`);
                console.log('-'.repeat(50));
                
                try {
                    const updateResult = await conn.sobject("Docebo_CourseEnrollment__c").update({
                        Id: enrollment.Id,
                        Course__c: foundCourse.Id,
                        Docebo_User__c: foundUser.Id
                    });
                    
                    if (updateResult.success) {
                        console.log(`✅ SUCCESS! Enrollment ${enrollment.Id} updated with associations:`);
                        console.log(`   Course__c: ${foundCourse.Id} (${foundCourse.Course_Name__c})`);
                        console.log(`   Docebo_User__c: ${foundUser.Id} (${foundUser.First_Name__c} ${foundUser.Last_Name__c})`);
                        
                        // Verify the update
                        const updatedEnrollment = await conn.sobject("Docebo_CourseEnrollment__c")
                            .findOne({ Id: enrollment.Id });
                        
                        console.log(`\n✅ Verification:`);
                        console.log(`   Course__c: ${updatedEnrollment.Course__c}`);
                        console.log(`   Docebo_User__c: ${updatedEnrollment.Docebo_User__c}`);
                        
                    } else {
                        console.log(`❌ Update failed:`, updateResult.errors);
                    }
                } catch (updateError) {
                    console.log(`❌ Update error:`, updateError.message);
                }
            } else {
                console.log(`\n❌ Cannot fix association:`);
                console.log(`   Course found: ${foundCourse ? 'Yes' : 'No'}`);
                console.log(`   User found: ${foundUser ? 'Yes' : 'No'}`);
                
                if (!foundCourse) {
                    console.log(`\n💡 Course lookup issue:`);
                    console.log(`   The course with ID "${parsedCourseId}" was not found in Salesforce`);
                    console.log(`   This could mean:`);
                    console.log(`   1. The course hasn't been created in Salesforce yet`);
                    console.log(`   2. The course ID field mapping is incorrect`);
                    console.log(`   3. The External_Id parsing logic is wrong`);
                }
                
                if (!foundUser) {
                    console.log(`\n💡 User lookup issue:`);
                    console.log(`   The user with ID "${parsedUserId}" was not found in Salesforce`);
                    console.log(`   This could mean:`);
                    console.log(`   1. The user hasn't been created in Salesforce yet`);
                    console.log(`   2. The user ID field mapping is incorrect`);
                    console.log(`   3. The External_Id parsing logic is wrong`);
                }
            }
        } else {
            console.log(`❌ Unexpected External_Id format: ${enrollment.External_Id__c}`);
            console.log(`   Expected format: courseId-userId or prefix-courseId-userId`);
        }
        
        // Step 6: Check for similar enrollment patterns
        console.log(`\n🔍 Checking for similar enrollment patterns...`);
        console.log('-'.repeat(50));
        
        const similarEnrollments = await conn.sobject("Docebo_CourseEnrollment__c")
            .find({ External_Id__c: { $like: 'UE-%' } })
            .limit(5)
            .execute();
        
        console.log(`📋 Found ${similarEnrollments.length} enrollments with UE- prefix:`);
        
        let associatedCount = 0;
        let notAssociatedCount = 0;
        
        similarEnrollments.forEach((enr, index) => {
            const hasAssociations = enr.Course__c && enr.Docebo_User__c;
            if (hasAssociations) associatedCount++;
            else notAssociatedCount++;
            
            console.log(`   ${index + 1}. ${enr.External_Id__c} - ${hasAssociations ? '✅ Associated' : '❌ Not Associated'}`);
        });
        
        console.log(`\n📊 Pattern Analysis:`);
        console.log(`   ✅ Associated: ${associatedCount}/${similarEnrollments.length}`);
        console.log(`   ❌ Not Associated: ${notAssociatedCount}/${similarEnrollments.length}`);
        
        if (notAssociatedCount > 0) {
            console.log(`\n💡 RECOMMENDATION:`);
            console.log(`   There are ${notAssociatedCount} enrollments with similar issues.`);
            console.log(`   This suggests a systematic problem with the enrollment creation logic.`);
            console.log(`   Check the course enrollment creation code for proper field mapping.`);
        }

    } catch (error) {
        console.error('💥 Error in investigation:', error);
    }
}

// Execute the investigation
console.log('🔄 Starting specific enrollment investigation...');
investigateSpecificEnrollment()
    .then(() => {
        console.log('\n✅ Investigation completed');
        process.exit(0);
    })
    .catch(err => {
        console.error('💥 Investigation failed:', err);
        process.exit(1);
    });
