require('dotenv').config();
const getConnection = require('./platform/salesforce/common/getConnection');

// Test with the standard field names you provided
async function testStandardFieldNames() {
    try {
        console.log('🧪 Testing with standard field names...');
        
        const conn = await getConnection();
        if (!conn || !conn.accessToken) {
            console.error("❌ Invalid Salesforce connection");
            return;
        }

        // Clean up existing test records
        console.log('\n🧹 Cleaning up existing test records...');
        const testEmail = "<EMAIL>";
        
        try {
            const existingLeads = await conn.sobject("Lead")
                .find({ Email: testEmail });
            
            for (const lead of existingLeads) {
                await conn.sobject("Lead").delete(lead.Id);
                console.log(`   Deleted existing Lead: ${lead.Id}`);
            }
        } catch (cleanupError) {
            console.log('   No existing records to clean up');
        }

        // Create account first
        console.log('\n🏢 Creating account...');
        const accountData = {
            Name: "Test Standard Fields Account",
            Website: "https://www.example.com",
        };
        const accountResult = await conn.sobject("Account").create(accountData);
        
        if (accountResult.success) {
            console.log(`✅ Account created: ${accountResult.id}`);
            
            // Test the standard field names you provided
            console.log('\n🔍 Testing standard field names...');
            
            const standardFieldsToTest = {
                // Standard fields with your provided names
                Fax: "(*************",
                AnnualRevenue: 5000000,
                Industry: "Healthcare",
                NumberOfEmployees: 15000,
                Rating: "Hot"
            };

            // Base lead data (fields we know work)
            const baseLeadData = {
                Company: "Test Standard Fields Company",
                Email: testEmail,
                Title: "Test Director",
                FirstName: "Test",
                LastName: "Standard Fields",
                Website: "https://www.example.com",
                Status: "Open - Not Contacted",
                Description: "Testing standard field names functionality",
                Salutation: "Ms.",
                Phone: "(*************",
                LeadSource: "Docebo Platform",
                OwnerId: "005O400000BxnnxIAB",
                Created_by_Docebo_API__c: true,
                Gender__c: "Female",
                Role_Type__c: "Operations/Business Management",
                Employment_Type__c: "Full-time",
                Race__c: "Asian"
            };

            // Test each standard field individually
            console.log('\n🔬 Testing each standard field individually:');
            console.log('=' .repeat(60));

            const successfulFields = [];
            const failedFields = [];

            for (const [fieldName, fieldValue] of Object.entries(standardFieldsToTest)) {
                console.log(`\n🧪 Testing field: ${fieldName}`);
                
                // Create test lead data with base + current field
                const testLeadData = {
                    ...baseLeadData,
                    Email: `${fieldName.toLowerCase()}.<EMAIL>`,
                    [fieldName]: fieldValue
                };

                try {
                    const testResult = await conn.sobject("Lead").create(testLeadData);
                    
                    if (testResult.success) {
                        console.log(`   ✅ ${fieldName}: SUCCESS - Field exists and accepts value`);
                        successfulFields.push({ field: fieldName, value: fieldValue, leadId: testResult.id });
                        
                        // Clean up the test lead
                        await conn.sobject("Lead").delete(testResult.id);
                    } else {
                        console.log(`   ❌ ${fieldName}: FAILED - ${testResult.errors?.[0]?.message || 'Unknown error'}`);
                        failedFields.push({ field: fieldName, error: testResult.errors?.[0]?.message || 'Unknown error' });
                    }
                } catch (error) {
                    console.log(`   ❌ ${fieldName}: ERROR - ${error.message}`);
                    failedFields.push({ field: fieldName, error: error.message });
                }
            }

            // Summary
            console.log('\n📊 STANDARD FIELDS TEST RESULTS:');
            console.log('=' .repeat(50));
            
            if (successfulFields.length > 0) {
                console.log(`\n✅ SUCCESSFUL FIELDS (${successfulFields.length}):`);
                successfulFields.forEach((item, index) => {
                    console.log(`   ${index + 1}. ${item.field} - Value: ${item.value}`);
                });
            }
            
            if (failedFields.length > 0) {
                console.log(`\n❌ FAILED FIELDS (${failedFields.length}):`);
                failedFields.forEach((item, index) => {
                    console.log(`   ${index + 1}. ${item.field} - Error: ${item.error}`);
                });
            }

            // Now create a comprehensive lead with ALL working fields
            console.log('\n🚀 Creating comprehensive lead with ALL working fields...');
            
            const comprehensiveLeadData = { ...baseLeadData };
            
            // Add all successful standard fields
            successfulFields.forEach(item => {
                comprehensiveLeadData[item.field] = item.value;
            });
            
            // Update email for final test
            comprehensiveLeadData.Email = "<EMAIL>";
            comprehensiveLeadData.LastName = "Comprehensive Standard";
            
            try {
                const finalResult = await conn.sobject("Lead").create(comprehensiveLeadData);
                
                if (finalResult.success) {
                    console.log(`✅ Comprehensive lead created: ${finalResult.id}`);
                    console.log(`🔗 Lead URL: https://strivetogether--full.sandbox.my.salesforce.com/${finalResult.id}`);
                    
                    // Verify the created lead
                    const createdLead = await conn.sobject("Lead")
                        .findOne({ Id: finalResult.id });
                    
                    if (createdLead) {
                        console.log('\n📋 COMPREHENSIVE LEAD WITH STANDARD FIELDS:');
                        console.log('=' .repeat(70));
                        
                        console.log('\n👤 CONTACT INFORMATION:');
                        console.log(`   Full Name: ${createdLead.Salutation || ''} ${createdLead.FirstName} ${createdLead.LastName}`);
                        console.log(`   Email: ${createdLead.Email}`);
                        console.log(`   Phone: ${createdLead.Phone || 'N/A'}`);
                        console.log(`   Fax: ${createdLead.Fax || 'N/A'}`);
                        
                        console.log('\n🏢 ORGANIZATION DETAILS:');
                        console.log(`   Company: ${createdLead.Company}`);
                        console.log(`   Title: ${createdLead.Title}`);
                        console.log(`   Website: ${createdLead.Website || 'N/A'}`);
                        console.log(`   Industry: ${createdLead.Industry || 'N/A'}`);
                        console.log(`   Annual Revenue: $${(createdLead.AnnualRevenue || 0).toLocaleString()}`);
                        console.log(`   Employees: ${(createdLead.NumberOfEmployees || 0).toLocaleString()}`);
                        console.log(`   Rating: ${createdLead.Rating || 'N/A'}`);
                        
                        console.log('\n👥 DEMOGRAPHICS & ROLE:');
                        console.log(`   Gender: ${createdLead.Gender__c || 'N/A'}`);
                        console.log(`   Race/Ethnicity: ${createdLead.Race__c || 'N/A'}`);
                        console.log(`   Role Type: ${createdLead.Role_Type__c || 'N/A'}`);
                        console.log(`   Employment Type: ${createdLead.Employment_Type__c || 'N/A'}`);
                        
                        console.log('\n⚙️ SYSTEM INFORMATION:');
                        console.log(`   Lead Source: ${createdLead.LeadSource || 'N/A'}`);
                        console.log(`   Status: ${createdLead.Status || 'N/A'}`);
                        console.log(`   Created by Docebo API: ${createdLead.Created_by_Docebo_API__c || 'N/A'}`);
                        
                        console.log('\n🎯 SUCCESS! Lead created with standard field names!');
                    }
                    
                    console.log('\n📊 FINAL SUMMARY:');
                    console.log(`   ✅ Base fields: ${Object.keys(baseLeadData).length}`);
                    console.log(`   ✅ Standard fields working: ${successfulFields.length}`);
                    console.log(`   📈 Total fields: ${Object.keys(baseLeadData).length + successfulFields.length}`);
                    
                    return finalResult.id;
                }
            } catch (finalError) {
                console.error('💥 Error creating comprehensive lead:', finalError.message);
            }

            console.log('\n📋 SUMMARY:');
            console.log(`   ✅ Working standard fields: ${successfulFields.length}/${Object.keys(standardFieldsToTest).length}`);
            console.log(`   ❌ Failed standard fields: ${failedFields.length}/${Object.keys(standardFieldsToTest).length}`);
        }

    } catch (error) {
        console.error('💥 Error in standard fields test:', error);
    }
}

// Execute the test
console.log('🔄 Starting standard field names test...');
testStandardFieldNames()
    .then((leadId) => {
        if (leadId) {
            console.log(`\n✅ Test completed successfully! Lead ID: ${leadId}`);
            console.log(`🔗 Direct Link: https://strivetogether--full.sandbox.my.salesforce.com/${leadId}`);
        } else {
            console.log('\n⚠️ Test completed - check results above');
        }
        process.exit(0);
    })
    .catch(err => {
        console.error('💥 Test failed:', err);
        process.exit(1);
    });
