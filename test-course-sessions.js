require('dotenv').config();
const doceboServices = require('./platform/docebo/services');

async function testCourseSessionsData(courseId) {
  try {
    console.log(`Fetching sessions for course ID: ${courseId}`);
    
    // Get list of sessions for this course
    const sessionsResult = await doceboServices.getSessionListedInfo(courseId);
    
    if (!sessionsResult || sessionsResult.length === 0) {
      console.log(`No sessions found for course ID: ${courseId}`);
      return;
    }
    
    console.log(`Found ${sessionsResult.length} sessions for course ID: ${courseId}`);
    console.log('Session IDs:', sessionsResult.map(s => s.id).join(', '));
    
    // Save sessions list to file
    const fs = require('fs');
    fs.writeFileSync('course-sessions-list.json', JSON.stringify(sessionsResult, null, 2));
    
    // Get detailed info for each session
    console.log('\nFetching detailed information for each session:');
    const detailedSessions = [];
    
    for (const session of sessionsResult) {
      console.log(`\nFetching details for session ID: ${session.id}`);
      const sessionInfo = await doceboServices.getCourseSessionInfo(session.id);
      
      if (sessionInfo && sessionInfo.status === 200 && sessionInfo.data) {
        console.log(`Session data retrieved successfully for ID: ${session.id}`);
        detailedSessions.push(sessionInfo.data);
      } else {
        console.log(`Failed to retrieve data for session ID: ${session.id}`);
      }
    }
    
    // Save detailed session data to file
    fs.writeFileSync('course-sessions-detailed.json', JSON.stringify(detailedSessions, null, 2));
    console.log('\nAll session data saved to course-sessions-detailed.json');
    
  } catch (error) {
    console.error('Error fetching course sessions data:', error);
  }
}

// Parse command line arguments or use default
const courseId = process.argv[2] || 557;

// Execute
testCourseSessionsData(courseId)
  .then(() => console.log('Done'))
  .catch(err => console.error('Script failed:', err));