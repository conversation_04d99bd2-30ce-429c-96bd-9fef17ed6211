require('dotenv').config();
const doceboServices = require('./platform/docebo/services');

/**
 * Test script for calling the Docebo instructor data endpoint
 * Endpoint: /learn/v1/instructor/getInstructorData
 * Required parameters:
 * - user_id (User_Unique_ID__c from Docebo User object)
 * - course_id (Course_Internal_ID__c from Docebo Session object)
 */
async function testInstructorData(userId, courseId, sessionId = null) {
  try {
    console.log(`Testing instructor data API with:`);
    console.log(`- user_id: ${userId}`);
    console.log(`- course_id: ${courseId}`);
    if (sessionId) console.log(`- session_id: ${sessionId}`);
    
    // Call the existing getInstructorData function from docebo services
    const result = await doceboServices.getInstructorData(userId, courseId, sessionId);
    
    // Display the raw result
    console.log('\nFull API Response:');
    console.log(JSON.stringify(result, null, 2));
    
    // Check if we got instructor data
    if (result && result.data && result.data.instructors && result.data.instructors.length > 0) {
      console.log('\nSuccess! Instructor data found.');
      
      // Check if there are any top-level properties in the response besides "instructors"
      const responseKeys = Object.keys(result.data).filter(key => key !== 'instructors');
      if (responseKeys.length > 0) {
        console.log('\nResponse includes these additional properties:');
        responseKeys.forEach(key => {
          console.log(`- ${key}: ${JSON.stringify(result.data[key])}`);
        });
      }
      
      // Display information about each instructor
      console.log(`\nFound ${result.data.instructors.length} instructor(s):`);
      
      result.data.instructors.forEach((instructor, index) => {
        console.log(`\n--- INSTRUCTOR #${index + 1} ---`);
        
        // Show all fields for this instructor
        console.log('\nAll available fields:');
        Object.keys(instructor).forEach(field => {
          const value = instructor[field];
          if (value === null) {
            console.log(`- ${field}: null`);
          } else if (value === "") {
            console.log(`- ${field}: ""`);
          } else if (typeof value === 'object') {
            console.log(`- ${field}: ${JSON.stringify(value)}`);
          } else {
            console.log(`- ${field}: ${value}`);
          }
        });
        
        // Group common fields for better readability
        console.log('\nCommon instructor fields:');
        if (instructor.idst) console.log(`- ID: ${instructor.idst}`);
        if (instructor.userid) console.log(`- User ID: ${instructor.userid}`);
        if (instructor.firstname || instructor.lastname) {
          console.log(`- Name: ${instructor.firstname || ''} ${instructor.lastname || ''}`);
        }
        if (instructor.email) console.log(`- Email: ${instructor.email}`);
        
        // Additional known fields that might be present
        console.log('\nAdditional fields (if available):');
        if (instructor.avatar !== undefined) console.log(`- avatar: ${instructor.avatar}`);
        if (instructor.urlAvatar !== undefined) console.log(`- urlAvatar: ${instructor.urlAvatar}`);
        if (instructor.level !== undefined) console.log(`- level: ${instructor.level}`);
        if (instructor.role !== undefined) console.log(`- role: ${instructor.role}`);
        if (instructor.type !== undefined) console.log(`- type: ${instructor.type}`);
        if (instructor.status !== undefined) console.log(`- status: ${instructor.status}`);
      });
      
      // Save this output to a file for reference
      const fs = require('fs');
      fs.writeFileSync('instructor-data-output.json', JSON.stringify(result.data, null, 2));
      console.log('\nOutput also saved to instructor-data-output.json for reference');
      
    } else {
      console.log('\nNo instructor data found or empty response.');
      console.log('This could be because:');
      console.log('1. The user ID is not valid or not an instructor');
      console.log('2. The course ID is not valid');
      console.log('3. The user is not assigned as an instructor for this course');
      
      if (result && result.status !== 200) {
        console.log(`\nAPI returned status: ${result.status}`);
      }
    }
    
    return result;
  } catch (error) {
    console.error('Error testing instructor data API:', error.message);
    console.error(error);
  }
}

// Parse command line arguments
function parseArgs() {
  // Default values
  const defaultUserId = '123';
  const defaultCourseId = '557';
  
  // Check for command line arguments
  const args = process.argv.slice(2);
  const userId = args[0] || defaultUserId;
  const courseId = args[1] || defaultCourseId;
  const sessionId = args[2] || null;
  
  return { userId, courseId, sessionId };
}

// Main execution
if (require.main === module) {
  const { userId, courseId, sessionId } = parseArgs();
  
  testInstructorData(userId, courseId, sessionId)
    .then(() => console.log('\nTest completed'))
    .catch(error => console.error('Test failed:', error));
}

// Export the function for use in other files
module.exports = {
  testInstructorData
};