const axios = require('axios');
const getAccessToken = require("./access-token");

module.exports = async function getApiData(method, url, payload) {
    let tokenInfo = await getAccessToken();
    if(tokenInfo.status == 200) {
        try {
            const response = await axios({
                method: method,
                url: url,
                data: payload,
                headers: {
                    'Authorization': `Bearer ${tokenInfo.accessToken}`,
                    'Content-Type': 'application/json'
                }
            })
            return {
                status: 200,
                data: response.data.data
            } 
        } catch (error) {
            return {
                status: '505',
                data: error.response?.data || error.message
            }
        }
    }else if(tokenInfo.status == 203){
        return {
            status: '505',
            data: "No access token was provided"
        }
    }else if(tokenInfo.status == 505){
        return {
            status: '505',
            data: "Error occurred in fetching access token"
        }
    }
}