require('dotenv').config();
const getConnection = require('./platform/salesforce/common/getConnection');
const getApiData = require("./common/docebo/fetcher");
const APP_BASE = process.env.DOCEBO_API_BASE_URL;

async function simplifiedLearningPlanCourseEnrollmentSync() {
    try {
        console.log('🚀 SIMPLIFIED LEARNING PLAN COURSE ENROLLMENT SYNC');
        console.log('=' .repeat(80));
        console.log('🎯 Target: Docebo_Learning_Plan_Course_Enrollment__c object');
        console.log('📡 Strategy: Only require Learning Plan Enrollment lookup (Course Enrollment is optional)');
        
        const conn = await getConnection();
        if (!conn || !conn.accessToken) {
            throw new Error('Failed to connect to Salesforce');
        }

        console.log('✅ Connected to Salesforce successfully');

        // Step 1: Create the uidCourse to Salesforce mapping
        console.log('\n🗺️ STEP 1: Creating uidCourse to Salesforce Mapping...');
        console.log('-'.repeat(50));
        
        // Get all courses from Docebo to create ID to uidCourse mapping
        let allDoceboCourses = [];
        let page = 1;
        let hasMoreData = true;
        
        while (hasMoreData && page <= 10) {
            const response = await getApiData(
                'GET', 
                `${APP_BASE}/course/v1/courses?page=${page}&page_size=200`, 
                null
            );
            
            if (response && response.status === 200) {
                const items = response.data?.items || [];
                allDoceboCourses.push(...items);
                
                hasMoreData = response.data?.has_more_data || false;
                if (items.length === 0) hasMoreData = false;
                page++;
            } else {
                hasMoreData = false;
            }
        }
        
        console.log(`Found ${allDoceboCourses.length} courses from Docebo`);
        
        // Create numeric ID to uidCourse mapping
        const numericIdToUidMap = new Map();
        allDoceboCourses.forEach(course => {
            if (course.id && course.uidCourse) {
                numericIdToUidMap.set(course.id.toString(), course.uidCourse);
            }
        });
        
        // Get Salesforce courses and create uidCourse to Salesforce ID mapping
        const sfCourses = await conn.sobject("Docebo_Course__c")
            .find({})
            .execute();
            
        const uidToSalesforceIdMap = new Map();
        sfCourses.forEach(sfCourse => {
            const numericId = sfCourse.Course_External_Id__c?.toString();
            if (numericId && numericIdToUidMap.has(numericId)) {
                const uidCourse = numericIdToUidMap.get(numericId);
                uidToSalesforceIdMap.set(uidCourse, {
                    id: sfCourse.Id,
                    name: sfCourse.Course_Name__c,
                    externalId: sfCourse.Course_External_Id__c
                });
            }
        });
        
        console.log(`✅ Created mapping for ${uidToSalesforceIdMap.size} courses`);

        // Step 2: Get reference data
        console.log('\n📚 STEP 2: Getting Reference Data...');
        console.log('-'.repeat(50));
        
        // Learning Plans
        const sfLearningPlans = await conn.sobject("Docebo_Learning_Plan__c")
            .find({})
            .execute();
            
        const learningPlanMapping = new Map();
        sfLearningPlans.forEach(plan => {
            if (plan.Learning_Plan_External_Id__c) {
                learningPlanMapping.set(plan.Learning_Plan_External_Id__c.toString(), {
                    id: plan.Id,
                    name: plan.Learning_Plan_Name__c,
                    externalId: plan.Learning_Plan_External_Id__c
                });
            }
        });
        
        // Users
        const sfUsers = await conn.sobject("Docebo_Users__c")
            .find({})
            .execute();
            
        const userMapping = new Map();
        sfUsers.forEach(user => {
            if (user.User_Unique_Id__c) {
                userMapping.set(user.User_Unique_Id__c.toString(), {
                    id: user.Id,
                    email: user.Email__c,
                    uniqueId: user.User_Unique_Id__c
                });
            }
        });
        
        // Learning Plan Enrollments (REQUIRED lookup)
        const sfLearningPlanEnrollments = await conn.sobject("Docebo_Learning_Plan_Enrollment__c")
            .find({})
            .execute();
            
        const learningPlanEnrollmentMapping = new Map();
        sfLearningPlanEnrollments.forEach(enrollment => {
            if (enrollment.Learning_Plan_Id__c && enrollment.Docebo_User_Id__c) {
                const key = `${enrollment.Learning_Plan_Id__c}-${enrollment.Docebo_User_Id__c}`;
                learningPlanEnrollmentMapping.set(key, enrollment.Id);
            }
        });
        
        console.log(`📊 Learning Plans: ${learningPlanMapping.size}`);
        console.log(`📊 Courses: ${uidToSalesforceIdMap.size}`);
        console.log(`📊 Users: ${userMapping.size}`);
        console.log(`📊 Learning Plan Enrollments: ${learningPlanEnrollmentMapping.size}`);

        // Step 3: Get existing Learning Plan Course Enrollments
        console.log('\n📊 STEP 3: Getting Existing Learning Plan Course Enrollments...');
        console.log('-'.repeat(50));
        
        const existingEnrollments = await conn.sobject("Docebo_Learning_Plan_Course_Enrollment__c")
            .find({})
            .execute();
            
        console.log(`Found ${existingEnrollments.length} existing learning plan course enrollments`);

        // Step 4: Process ALL learning plans (not just one)
        console.log('\n🔍 STEP 4: Fetching Learning Plan Course Enrollments from ALL Learning Plans...');
        console.log('-'.repeat(50));
        
        let allLearningPlanCourseEnrollments = [];
        const learningPlanIds = Array.from(learningPlanMapping.keys());
        
        console.log(`📊 Processing ${learningPlanIds.length} learning plans...`);
        
        for (let i = 0; i < learningPlanIds.length; i++) {
            const learningPlanId = learningPlanIds[i];
            const planInfo = learningPlanMapping.get(learningPlanId);
            
            console.log(`   📄 Processing LP ${i + 1}/${learningPlanIds.length}: ${learningPlanId} (${planInfo.name})...`);
            
            try {
                // Get first page of enrollments for this learning plan
                const response = await getApiData(
                    'GET', 
                    `${APP_BASE}/learn/v1/enrollments?learning_plan_id=${learningPlanId}&page=1&page_size=100`, 
                    null
                );
                
                if (response && response.status === 200) {
                    const items = response.data?.items || [];
                    
                    // Filter for valid enrollments
                    const validItems = items.filter(item => {
                        const hasUidCourse = item.uidCourse && uidToSalesforceIdMap.has(item.uidCourse);
                        const hasUser = item.user_id && userMapping.has(item.user_id.toString());
                        return hasUidCourse && hasUser;
                    });
                    
                    validItems.forEach(enrollment => {
                        enrollment.learning_plan_id = learningPlanId;
                        enrollment.learning_plan_name = planInfo.name;
                    });
                    
                    allLearningPlanCourseEnrollments.push(...validItems);
                    console.log(`      ✅ Found ${validItems.length} valid course enrollments`);
                    
                } else {
                    console.log(`      ⚪ No enrollments found for LP ${learningPlanId}`);
                }
                
            } catch (planError) {
                console.log(`      ❌ Error processing LP ${learningPlanId}: ${planError.message}`);
                continue;
            }
            
            // Progress indicator
            if ((i + 1) % 10 === 0) {
                console.log(`   📊 Progress: ${i + 1}/${learningPlanIds.length} LPs processed, ${allLearningPlanCourseEnrollments.length.toLocaleString()} total course enrollments found`);
            }
        }
        
        console.log(`\n✅ Docebo fetch completed: ${allLearningPlanCourseEnrollments.length.toLocaleString()} course enrollments found`);

        if (allLearningPlanCourseEnrollments.length === 0) {
            console.log('⚠️ No learning plan course enrollments found');
            return {
                success: true,
                doceboTotal: 0,
                salesforceInitial: existingEnrollments.length,
                message: 'No data found'
            };
        }

        // Step 5: Prepare records (only require Learning Plan Enrollment lookup)
        console.log('\n🔍 STEP 5: Preparing Records (Only LP Enrollment Required)...');
        console.log('-'.repeat(50));
        
        const enrollmentsToCreate = [];
        let skippedCount = 0;
        let missingLpEnrollments = 0;
        let duplicatesSkipped = 0;
        
        // Track existing enrollments to prevent duplicates
        const existingKeys = new Set();
        existingEnrollments.forEach(enrollment => {
            if (enrollment.Learning_Plan_Enrollment_Id__c) {
                // Create key based on LP Enrollment + Course UID (since we don't have Course Enrollment)
                const key = `${enrollment.Learning_Plan_Enrollment_Id__c}`;
                existingKeys.add(key);
            }
        });
        
        for (const doceboEnrollment of allLearningPlanCourseEnrollments) {
            const learningPlanId = doceboEnrollment.learning_plan_id;
            const courseUid = doceboEnrollment.uidCourse;
            const userId = doceboEnrollment.user_id;
            
            const salesforceLearningPlan = learningPlanMapping.get(learningPlanId.toString());
            const salesforceCourse = uidToSalesforceIdMap.get(courseUid);
            const salesforceUser = userMapping.get(userId.toString());
            
            if (!salesforceLearningPlan || !salesforceCourse || !salesforceUser) {
                skippedCount++;
                continue;
            }
            
            // Find Learning Plan Enrollment (REQUIRED)
            const lpEnrollmentKey = `${salesforceLearningPlan.id}-${salesforceUser.id}`;
            const learningPlanEnrollmentId = learningPlanEnrollmentMapping.get(lpEnrollmentKey);
            
            if (!learningPlanEnrollmentId) {
                missingLpEnrollments++;
                continue;
            }
            
            // Check for duplicates (simplified - just based on LP Enrollment)
            const uniqueKey = `${learningPlanEnrollmentId}-${courseUid}`;
            if (existingKeys.has(uniqueKey)) {
                duplicatesSkipped++;
                continue;
            }
            existingKeys.add(uniqueKey);
            
            // Parse dates
            let enrollmentDate = "";
            if (doceboEnrollment.enroll_date_of_enrollment) {
                try {
                    enrollmentDate = new Date(doceboEnrollment.enroll_date_of_enrollment.replace(' ', 'T')).toISOString();
                } catch (e) {
                    enrollmentDate = "";
                }
            }
            
            let completionDate = "";
            if (doceboEnrollment.course_complete_date) {
                try {
                    completionDate = new Date(doceboEnrollment.course_complete_date.replace(' ', 'T')).toISOString();
                } catch (e) {
                    completionDate = "";
                }
            }
            
            // Create record with ONLY required Learning Plan Enrollment lookup
            const enrollmentRecord = {
                Learning_Plan_Enrollment_Id__c: learningPlanEnrollmentId,
                // Course_Enrollment_Id__c: SKIP - it's optional
                Enrollment_Date__c: enrollmentDate,
                Completion_Date__c: completionDate,
                Completion_Percentage__c: 0,
                Effective__c: true,
                Completed__c: doceboEnrollment.course_complete_date ? true : false
            };
            
            enrollmentsToCreate.push(enrollmentRecord);
        }
        
        console.log(`Prepared ${enrollmentsToCreate.length.toLocaleString()} learning plan course enrollment records`);
        console.log(`Skipped ${skippedCount.toLocaleString()} enrollments (missing references)`);
        console.log(`Missing LP Enrollments: ${missingLpEnrollments.toLocaleString()}`);
        console.log(`🛡️ Duplicates prevented: ${duplicatesSkipped.toLocaleString()}`);

        if (enrollmentsToCreate.length === 0) {
            console.log('⚠️ No records to create - missing required Learning Plan Enrollments');
            return {
                success: true,
                doceboTotal: allLearningPlanCourseEnrollments.length,
                salesforceInitial: existingEnrollments.length,
                synced: 0,
                skipped: skippedCount,
                missingLpEnrollments: missingLpEnrollments,
                duplicates: duplicatesSkipped
            };
        }

        // Step 6: Create records in batches
        console.log('\n💾 STEP 6: Creating Learning Plan Course Enrollments...');
        console.log('-'.repeat(50));
        
        console.log(`🔍 Sample record structure:`);
        console.log(JSON.stringify(enrollmentsToCreate[0], null, 2));
        
        let successCount = 0;
        let errorCount = 0;
        const batchSize = 50;
        const totalBatches = Math.ceil(enrollmentsToCreate.length / batchSize);
        
        console.log(`\nProcessing ${enrollmentsToCreate.length.toLocaleString()} records in ${totalBatches.toLocaleString()} batches...`);
        
        for (let i = 0; i < enrollmentsToCreate.length; i += batchSize) {
            const batch = enrollmentsToCreate.slice(i, i + batchSize);
            const batchNum = Math.floor(i / batchSize) + 1;
            
            console.log(`   📦 Processing batch ${batchNum.toLocaleString()}/${totalBatches.toLocaleString()} (${batch.length} records)...`);
            
            try {
                const results = await conn.sobject("Docebo_Learning_Plan_Course_Enrollment__c")
                    .create(batch);
                    
                const resultArray = Array.isArray(results) ? results : [results];
                
                let batchSuccessCount = 0;
                let batchErrorCount = 0;
                
                resultArray.forEach((result, index) => {
                    if (result.success) {
                        successCount++;
                        batchSuccessCount++;
                    } else {
                        errorCount++;
                        batchErrorCount++;
                        
                        if (batchErrorCount <= 3) {
                            const errorMessage = result.errors?.[0]?.message || 'Unknown error';
                            console.log(`      ⚠️ Record ${index + 1}: ${errorMessage}`);
                        }
                    }
                });
                
                console.log(`      ✅ Batch ${batchNum}: ${batchSuccessCount} success, ${batchErrorCount} errors`);
                
                // Progress indicator
                if (batchNum % 20 === 0 || batchNum === totalBatches) {
                    const progressPercent = ((i + batch.length) / enrollmentsToCreate.length * 100).toFixed(1);
                    console.log(`      📊 Progress: ${progressPercent}% (${successCount.toLocaleString()} created, ${errorCount.toLocaleString()} errors)`);
                }
                
                // Rate limiting
                if (batchNum < totalBatches) {
                    await new Promise(resolve => setTimeout(resolve, 500));
                }
                
            } catch (batchError) {
                console.error(`      ❌ Batch ${batchNum} failed:`, batchError.message);
                errorCount += batch.length;
            }
        }

        // Step 7: Final verification
        console.log('\n🔍 STEP 7: Final Verification...');
        console.log('-'.repeat(50));
        
        const finalEnrollments = await conn.sobject("Docebo_Learning_Plan_Course_Enrollment__c")
            .find({})
            .execute();

        return {
            success: true,
            doceboTotal: allLearningPlanCourseEnrollments.length,
            salesforceInitial: existingEnrollments.length,
            salesforceFinal: finalEnrollments.length,
            synced: successCount,
            errors: errorCount,
            skipped: skippedCount,
            missingLpEnrollments: missingLpEnrollments,
            duplicates: duplicatesSkipped
        };

    } catch (error) {
        console.error('💥 Error in simplified learning plan course enrollment sync:', error);
        return {
            success: false,
            error: error.message
        };
    }
}

// Execute the simplified sync
console.log('🔄 Starting Simplified Learning Plan Course Enrollment Sync...');
simplifiedLearningPlanCourseEnrollmentSync()
    .then((result) => {
        console.log('\n📋 SIMPLIFIED SYNC SUMMARY:');
        console.log('=' .repeat(60));
        
        if (result.success) {
            if (result.salesforceFinal !== undefined) {
                console.log(`📊 Total Course Enrollments Found: ${result.doceboTotal.toLocaleString()}`);
                console.log(`📊 Salesforce Initial: ${result.salesforceInitial.toLocaleString()}`);
                console.log(`📊 Salesforce Final: ${result.salesforceFinal.toLocaleString()}`);
                console.log(`✅ Successfully Synced: ${result.synced.toLocaleString()}`);
                console.log(`❌ Errors: ${result.errors.toLocaleString()}`);
                console.log(`⏭️ Skipped: ${result.skipped.toLocaleString()}`);
                console.log(`⚠️ Missing LP Enrollments: ${result.missingLpEnrollments.toLocaleString()}`);
                console.log(`🛡️ Duplicates Prevented: ${result.duplicates.toLocaleString()}`);
                
                const netIncrease = result.salesforceFinal - result.salesforceInitial;
                console.log(`\n🎉 NET RESULT: ${netIncrease.toLocaleString()} new learning plan course enrollments added!`);
                
                if (netIncrease > 0) {
                    console.log(`🎉 SUCCESS: Learning Plan Course Enrollment sync is working!`);
                    console.log(`📊 From ${result.salesforceInitial} to ${result.salesforceFinal} records - MASSIVE improvement!`);
                } else if (result.errors === 0 && result.synced === 0) {
                    console.log(`⚠️ No records created - all may already exist or missing LP Enrollments`);
                } else {
                    console.log(`⚠️ Issues detected - review errors and missing LP Enrollments`);
                }
            } else {
                console.log(`📋 Result: ${result.message}`);
            }
        } else {
            console.log(`❌ Sync failed: ${result.error}`);
        }
        
        console.log('\n✅ Simplified learning plan course enrollment sync completed');
        process.exit(0);
    })
    .catch(err => {
        console.error('💥 Simplified learning plan course enrollment sync failed:', err);
        process.exit(1);
    });
