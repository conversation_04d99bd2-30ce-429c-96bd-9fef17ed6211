require('dotenv').config();
const getConnection = require('./platform/salesforce/common/getConnection');

async function checkEnrollmentFields() {
    try {
        console.log('🔍 Checking Docebo_CourseEnrollment__c Object Fields');
        console.log('=' .repeat(60));
        
        const conn = await getConnection();
        if (!conn || !conn.accessToken) {
            console.error("❌ Invalid Salesforce connection");
            return;
        }

        // Describe the object to see all available fields
        console.log('\n📋 Describing Docebo_CourseEnrollment__c object...');
        console.log('-'.repeat(50));
        
        const describe = await conn.sobject("Docebo_CourseEnrollment__c").describe();
        
        console.log(`✅ Object: ${describe.name} (${describe.label})`);
        console.log(`   Fields: ${describe.fields.length}`);
        console.log(`   Createable: ${describe.createable}`);
        console.log(`   Updateable: ${describe.updateable}`);
        
        console.log(`\n📋 All Fields:`);
        console.log('-'.repeat(50));
        
        describe.fields.forEach((field, index) => {
            const isCustom = field.custom ? '🔧' : '📋';
            const isRequired = !field.nillable ? '⚠️' : '';
            const isUnique = field.unique ? '🔑' : '';
            const isExternalId = field.externalId ? '🆔' : '';
            
            console.log(`${index + 1}. ${isCustom} ${field.name} (${field.type}) ${isRequired}${isUnique}${isExternalId}`);
            console.log(`   Label: ${field.label}`);
            
            if (field.name.toLowerCase().includes('external') || 
                field.name.toLowerCase().includes('unique') ||
                field.name.toLowerCase().includes('id')) {
                console.log(`   ⭐ POTENTIAL KEY FIELD`);
            }
            
            if (field.name.toLowerCase().includes('course') || 
                field.name.toLowerCase().includes('user')) {
                console.log(`   🔗 RELATIONSHIP FIELD`);
            }
            
            console.log('');
        });
        
        // Check for existing records to understand the data structure
        console.log(`\n📊 Sample Records:`);
        console.log('-'.repeat(50));
        
        const sampleRecords = await conn.sobject("Docebo_CourseEnrollment__c")
            .find({})
            .limit(3)
            .execute();
        
        if (sampleRecords.length > 0) {
            console.log(`✅ Found ${sampleRecords.length} sample records:`);
            
            sampleRecords.forEach((record, index) => {
                console.log(`\n📋 Record ${index + 1}: ${record.Id}`);
                
                // Show all non-null fields
                Object.keys(record).forEach(key => {
                    if (record[key] !== null && record[key] !== undefined && key !== 'attributes') {
                        console.log(`   ${key}: ${record[key]}`);
                    }
                });
            });
            
            // Analyze the pattern
            console.log(`\n🔍 Field Analysis:`);
            console.log('-'.repeat(30));
            
            const allFields = new Set();
            sampleRecords.forEach(record => {
                Object.keys(record).forEach(key => {
                    if (key !== 'attributes') allFields.add(key);
                });
            });
            
            console.log(`📋 Fields found in sample data:`);
            Array.from(allFields).sort().forEach(field => {
                const hasData = sampleRecords.some(record => record[field]);
                console.log(`   ${field}: ${hasData ? '✅ Has Data' : '❌ Empty'}`);
            });
            
        } else {
            console.log(`❌ No records found in Docebo_CourseEnrollment__c`);
        }
        
        // Look for the specific enrollment ID in any text field
        console.log(`\n🔍 Searching for enrollment ID "UE-21-17928" in all text fields...`);
        console.log('-'.repeat(50));
        
        const textFields = describe.fields.filter(field => 
            field.type === 'string' || field.type === 'textarea' || field.type === 'id'
        );
        
        for (const field of textFields) {
            try {
                console.log(`   Searching in ${field.name}...`);
                
                const query = {};
                query[field.name] = { $like: '%UE-21-17928%' };
                
                const matches = await conn.sobject("Docebo_CourseEnrollment__c")
                    .find(query)
                    .limit(1)
                    .execute();
                
                if (matches.length > 0) {
                    console.log(`   ✅ FOUND in ${field.name}!`);
                    console.log(`      Record ID: ${matches[0].Id}`);
                    console.log(`      Value: ${matches[0][field.name]}`);
                    
                    // Show the full record
                    console.log(`\n📋 Full Record Details:`);
                    Object.keys(matches[0]).forEach(key => {
                        if (matches[0][key] !== null && matches[0][key] !== undefined && key !== 'attributes') {
                            console.log(`      ${key}: ${matches[0][key]}`);
                        }
                    });
                    
                    break;
                }
            } catch (searchError) {
                // Ignore search errors for fields that don't support LIKE
            }
        }
        
        console.log(`\n💡 RECOMMENDATIONS:`);
        console.log('=' .repeat(50));
        console.log(`1. 🔧 Update the code to use the correct field names`);
        console.log(`2. 🔍 Identify which field stores the enrollment external ID`);
        console.log(`3. 📊 Check if External_Id__c field needs to be created`);
        console.log(`4. 🔗 Verify Course__c and Docebo_User__c field mappings`);

    } catch (error) {
        console.error('💥 Error in check:', error);
    }
}

// Execute the check
console.log('🔄 Starting enrollment fields check...');
checkEnrollmentFields()
    .then(() => {
        console.log('\n✅ Check completed');
        process.exit(0);
    })
    .catch(err => {
        console.error('💥 Check failed:', err);
        process.exit(1);
    });
