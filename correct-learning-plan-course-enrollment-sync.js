require('dotenv').config();
const getConnection = require('./platform/salesforce/common/getConnection');
const getApiData = require("./common/docebo/fetcher");
const APP_BASE = process.env.DOCEBO_API_BASE_URL;

async function correctLearningPlanCourseEnrollmentSync() {
    try {
        console.log('🚀 CORRECT LEARNING PLAN COURSE ENROLLMENT SYNC');
        console.log('=' .repeat(80));
        console.log('🎯 Target: Docebo_Learning_Plan_Course_Enrollment__c object');
        console.log('📡 Strategy: Use /learn/v1/enrollments?learning_plan_id=X for each LP');
        
        const conn = await getConnection();
        if (!conn || !conn.accessToken) {
            throw new Error('Failed to connect to Salesforce');
        }

        console.log('✅ Connected to Salesforce successfully');

        // Step 1: Get existing Learning Plan Course Enrollments from Salesforce
        console.log('\n📊 STEP 1: Getting Existing Learning Plan Course Enrollments...');
        console.log('-'.repeat(50));
        
        const existingEnrollments = await conn.sobject("Docebo_Learning_Plan_Course_Enrollment__c")
            .find({})
            .execute();
            
        console.log(`Found ${existingEnrollments.length} existing learning plan course enrollments in Salesforce`);

        // Step 2: Get reference data from Salesforce
        console.log('\n📚 STEP 2: Getting Reference Data from Salesforce...');
        console.log('-'.repeat(50));
        
        // Get Learning Plans
        const sfLearningPlans = await conn.sobject("Docebo_Learning_Plan__c")
            .find({})
            .execute();
            
        const learningPlanMapping = new Map();
        sfLearningPlans.forEach(plan => {
            if (plan.Learning_Plan_External_Id__c) {
                learningPlanMapping.set(plan.Learning_Plan_External_Id__c.toString(), {
                    id: plan.Id,
                    name: plan.Learning_Plan_Name__c
                });
            }
        });
        
        console.log(`📊 Learning Plans: ${learningPlanMapping.size}`);

        // Get Courses (using uidCourse field)
        const sfCourses = await conn.sobject("Docebo_Course__c")
            .find({})
            .execute();
            
        const courseMapping = new Map();
        sfCourses.forEach(course => {
            // Try both Course_External_Id__c and Course_Unique_Id__c
            const courseKey = course.Course_Unique_Id__c || course.Course_External_Id__c;
            if (courseKey) {
                courseMapping.set(courseKey.toString(), {
                    id: course.Id,
                    name: course.Course_Name__c,
                    uniqueId: courseKey
                });
            }
        });
        
        console.log(`📊 Courses: ${courseMapping.size}`);

        // Get Users
        const sfUsers = await conn.sobject("Docebo_Users__c")
            .find({})
            .execute();
            
        const userMapping = new Map();
        sfUsers.forEach(user => {
            if (user.User_Unique_Id__c) {
                userMapping.set(user.User_Unique_Id__c.toString(), {
                    id: user.Id,
                    email: user.Email__c
                });
            }
        });
        
        console.log(`📊 Users: ${userMapping.size}`);

        // Step 3: Get Learning Plan Course Enrollments from Docebo
        console.log('\n🔍 STEP 3: Fetching Learning Plan Course Enrollments from Docebo...');
        console.log('-'.repeat(50));
        
        let allLearningPlanCourseEnrollments = [];
        const learningPlanIds = Array.from(learningPlanMapping.keys());
        
        console.log(`📊 Processing ${learningPlanIds.length} learning plans...`);
        console.log('📡 Using endpoint: /learn/v1/enrollments?learning_plan_id=X');
        
        for (let i = 0; i < learningPlanIds.length; i++) {
            const learningPlanId = learningPlanIds[i];
            const planInfo = learningPlanMapping.get(learningPlanId);
            
            console.log(`   📄 Processing LP ${i + 1}/${learningPlanIds.length}: ${learningPlanId} (${planInfo.name})...`);
            
            try {
                let page = 1;
                let hasMoreData = true;
                let planEnrollments = [];
                
                while (hasMoreData && page <= 10) { // Limit to 10 pages per LP
                    const response = await getApiData(
                        'GET', 
                        `${APP_BASE}/learn/v1/enrollments?learning_plan_id=${learningPlanId}&page=${page}&page_size=200`, 
                        null
                    );
                    
                    if (response && response.status === 200) {
                        const items = response.data?.items || [];
                        
                        // Filter for items that have both uidCourse and user_id
                        const validItems = items.filter(item => 
                            item.uidCourse && item.user_id && 
                            courseMapping.has(item.uidCourse.toString()) &&
                            userMapping.has(item.user_id.toString())
                        );
                        
                        planEnrollments.push(...validItems);
                        
                        console.log(`      📄 Page ${page}: ${items.length} items, ${validItems.length} valid course enrollments`);
                        
                        hasMoreData = response.data?.has_more_data || false;
                        if (items.length === 0) hasMoreData = false;
                        page++;
                    } else {
                        hasMoreData = false;
                    }
                }
                
                if (planEnrollments.length > 0) {
                    // Add learning plan context to each enrollment
                    planEnrollments.forEach(enrollment => {
                        enrollment.learning_plan_id = learningPlanId;
                        enrollment.learning_plan_name = planInfo.name;
                    });
                    
                    allLearningPlanCourseEnrollments.push(...planEnrollments);
                    console.log(`      ✅ Added ${planEnrollments.length} course enrollments for LP ${learningPlanId}`);
                } else {
                    console.log(`      ⚪ No course enrollments found for LP ${learningPlanId}`);
                }
                
            } catch (planError) {
                console.log(`      ❌ Error processing LP ${learningPlanId}: ${planError.message}`);
                continue;
            }
            
            // Progress indicator every 10 learning plans
            if ((i + 1) % 10 === 0) {
                console.log(`   📊 Progress: ${i + 1}/${learningPlanIds.length} LPs processed, ${allLearningPlanCourseEnrollments.length.toLocaleString()} total course enrollments found`);
            }
        }
        
        console.log(`\n✅ Docebo fetch completed:`);
        console.log(`   Learning plans processed: ${learningPlanIds.length}`);
        console.log(`   Total learning plan course enrollments found: ${allLearningPlanCourseEnrollments.length.toLocaleString()}`);

        if (allLearningPlanCourseEnrollments.length === 0) {
            console.log('⚠️ No learning plan course enrollments found via API');
            return {
                success: true,
                doceboTotal: 0,
                salesforceInitial: existingEnrollments.length,
                message: 'No data found in Docebo'
            };
        }

        // Step 4: Prepare Learning Plan Course Enrollment Records
        console.log('\n🔍 STEP 4: Preparing Learning Plan Course Enrollment Records...');
        console.log('-'.repeat(50));
        
        const enrollmentsToCreate = [];
        const existingKeys = new Set();
        
        // Track existing enrollments by unique key
        existingEnrollments.forEach(enrollment => {
            if (enrollment.Learning_Plan__c && enrollment.Course__c && enrollment.User__c) {
                const key = `${enrollment.Learning_Plan__c}-${enrollment.Course__c}-${enrollment.User__c}`;
                existingKeys.add(key);
            }
        });
        
        let skippedCount = 0;
        let duplicatesSkipped = 0;
        
        for (const doceboEnrollment of allLearningPlanCourseEnrollments) {
            const learningPlanId = doceboEnrollment.learning_plan_id;
            const courseUid = doceboEnrollment.uidCourse;
            const userId = doceboEnrollment.user_id;
            
            const salesforceLearningPlan = learningPlanMapping.get(learningPlanId.toString());
            const salesforceCourse = courseMapping.get(courseUid.toString());
            const salesforceUser = userMapping.get(userId.toString());
            
            if (!salesforceLearningPlan || !salesforceCourse || !salesforceUser) {
                skippedCount++;
                continue;
            }
            
            // Check for duplicates
            const uniqueKey = `${salesforceLearningPlan.id}-${salesforceCourse.id}-${salesforceUser.id}`;
            if (existingKeys.has(uniqueKey)) {
                duplicatesSkipped++;
                continue;
            }
            
            // Add to tracking set
            existingKeys.add(uniqueKey);
            
            // Parse enrollment date
            let enrollmentDate = "";
            if (doceboEnrollment.enroll_date_of_enrollment) {
                try {
                    enrollmentDate = new Date(doceboEnrollment.enroll_date_of_enrollment.replace(' ', 'T')).toISOString();
                } catch (e) {
                    enrollmentDate = "";
                }
            }
            
            // Parse completion date
            let completionDate = "";
            if (doceboEnrollment.course_complete_date) {
                try {
                    completionDate = new Date(doceboEnrollment.course_complete_date.replace(' ', 'T')).toISOString();
                } catch (e) {
                    completionDate = "";
                }
            }
            
            // Create learning plan course enrollment record
            const enrollmentRecord = {
                Learning_Plan__c: salesforceLearningPlan.id,
                Course__c: salesforceCourse.id,
                User__c: salesforceUser.id,
                Status__c: doceboEnrollment.status || 'enrolled',
                Enrollment_Date__c: enrollmentDate,
                Completion_Date__c: completionDate,
                Score__c: doceboEnrollment.score || 0,
                Progress__c: 0, // Will be calculated from completion status
                Time_Spent__c: doceboEnrollment.total_time || 0
            };
            
            enrollmentsToCreate.push(enrollmentRecord);
        }
        
        console.log(`Prepared ${enrollmentsToCreate.length.toLocaleString()} learning plan course enrollment records`);
        console.log(`Skipped ${skippedCount.toLocaleString()} enrollments (missing references)`);
        console.log(`🛡️ Duplicates prevented: ${duplicatesSkipped.toLocaleString()} enrollments`);

        if (enrollmentsToCreate.length === 0) {
            console.log('✅ All learning plan course enrollments are already synced!');
            return {
                success: true,
                doceboTotal: allLearningPlanCourseEnrollments.length,
                salesforceInitial: existingEnrollments.length,
                synced: 0,
                skipped: skippedCount,
                duplicates: duplicatesSkipped
            };
        }

        // Step 5: Create records in batches
        console.log('\n💾 STEP 5: Creating Learning Plan Course Enrollments...');
        console.log('-'.repeat(50));
        
        let successCount = 0;
        let errorCount = 0;
        let duplicateErrorCount = 0;
        const batchSize = 50;
        const totalBatches = Math.ceil(enrollmentsToCreate.length / batchSize);
        
        console.log(`Processing ${enrollmentsToCreate.length.toLocaleString()} learning plan course enrollments in ${totalBatches.toLocaleString()} batches...`);
        
        for (let i = 0; i < enrollmentsToCreate.length; i += batchSize) {
            const batch = enrollmentsToCreate.slice(i, i + batchSize);
            const batchNum = Math.floor(i / batchSize) + 1;
            
            console.log(`   📦 Processing batch ${batchNum.toLocaleString()}/${totalBatches.toLocaleString()} (${batch.length} records)...`);
            
            try {
                const results = await conn.sobject("Docebo_Learning_Plan_Course_Enrollment__c")
                    .create(batch);
                    
                const resultArray = Array.isArray(results) ? results : [results];
                
                let batchSuccessCount = 0;
                let batchErrorCount = 0;
                
                resultArray.forEach((result) => {
                    if (result.success) {
                        successCount++;
                        batchSuccessCount++;
                    } else {
                        errorCount++;
                        batchErrorCount++;
                        
                        const errorMessage = result.errors?.[0]?.message || 'Unknown error';
                        
                        // Track duplicate errors separately
                        if (errorMessage.toLowerCase().includes('duplicate') || 
                            errorMessage.toLowerCase().includes('unique') ||
                            errorMessage.includes('DUPLICATE_VALUE')) {
                            duplicateErrorCount++;
                        }
                        
                        if (batchErrorCount <= 3) { // Only show first 3 errors per batch
                            console.log(`      ⚠️ Error: ${errorMessage}`);
                        }
                    }
                });
                
                console.log(`      ✅ Batch ${batchNum}: ${batchSuccessCount} success, ${batchErrorCount} errors`);
                
                // Progress indicator every 20 batches
                if (batchNum % 20 === 0 || batchNum === totalBatches) {
                    const progressPercent = ((i + batch.length) / enrollmentsToCreate.length * 100).toFixed(1);
                    console.log(`      📊 Progress: ${progressPercent}% (${successCount.toLocaleString()} created, ${errorCount.toLocaleString()} errors)`);
                }
                
                // Rate limiting - pause between batches
                if (batchNum < totalBatches) {
                    await new Promise(resolve => setTimeout(resolve, 500));
                }
                
            } catch (batchError) {
                console.error(`      ❌ Batch ${batchNum} failed:`, batchError.message);
                errorCount += batch.length;
            }
        }

        // Step 6: Final verification
        console.log('\n🔍 STEP 6: Final Verification...');
        console.log('-'.repeat(50));
        
        const finalEnrollments = await conn.sobject("Docebo_Learning_Plan_Course_Enrollment__c")
            .find({})
            .execute();

        return {
            success: true,
            doceboTotal: allLearningPlanCourseEnrollments.length,
            salesforceInitial: existingEnrollments.length,
            salesforceFinal: finalEnrollments.length,
            synced: successCount,
            errors: errorCount,
            duplicateErrors: duplicateErrorCount,
            skipped: skippedCount,
            duplicates: duplicatesSkipped
        };

    } catch (error) {
        console.error('💥 Error in correct learning plan course enrollment sync:', error);
        return {
            success: false,
            error: error.message
        };
    }
}

// Execute the correct sync
console.log('🔄 Starting Correct Learning Plan Course Enrollment Sync...');
correctLearningPlanCourseEnrollmentSync()
    .then((result) => {
        console.log('\n📋 LEARNING PLAN COURSE ENROLLMENT SYNC SUMMARY:');
        console.log('=' .repeat(70));
        
        if (result.success) {
            if (result.salesforceFinal !== undefined) {
                console.log(`📊 Total Course Enrollments Found: ${result.doceboTotal.toLocaleString()}`);
                console.log(`📊 Salesforce Initial: ${result.salesforceInitial.toLocaleString()}`);
                console.log(`📊 Salesforce Final: ${result.salesforceFinal.toLocaleString()}`);
                console.log(`✅ Successfully Synced: ${result.synced.toLocaleString()}`);
                
                if (result.errors > 0) {
                    console.log(`❌ Total Errors: ${result.errors.toLocaleString()}`);
                    if (result.duplicateErrors > 0) {
                        console.log(`🛡️ Duplicate Errors (Expected): ${result.duplicateErrors.toLocaleString()}`);
                        console.log(`⚠️ Other Errors: ${(result.errors - result.duplicateErrors).toLocaleString()}`);
                    }
                }
                if (result.skipped > 0) {
                    console.log(`⏭️ Skipped: ${result.skipped.toLocaleString()}`);
                }
                if (result.duplicates > 0) {
                    console.log(`🛡️ Duplicates Prevented: ${result.duplicates.toLocaleString()}`);
                }
                
                const netIncrease = result.salesforceFinal - result.salesforceInitial;
                console.log(`\n🎉 NET RESULT: ${netIncrease.toLocaleString()} new learning plan course enrollments added!`);
                
                if (netIncrease > 0) {
                    console.log(`🎉 SUCCESS: Learning plan course enrollments are now synced!`);
                } else {
                    console.log(`✅ All learning plan course enrollments were already synced.`);
                }
            } else {
                console.log(`📋 Result: ${result.message}`);
                if (result.doceboTotal !== undefined) {
                    console.log(`📊 Docebo Total: ${result.doceboTotal.toLocaleString()}`);
                }
                if (result.skipped) {
                    console.log(`⏭️ Skipped: ${result.skipped.toLocaleString()}`);
                }
                if (result.duplicates) {
                    console.log(`🛡️ Duplicates: ${result.duplicates.toLocaleString()}`);
                }
            }
        } else {
            console.log(`❌ Sync failed: ${result.error}`);
        }
        
        console.log('\n✅ Correct learning plan course enrollment sync completed');
        process.exit(0);
    })
    .catch(err => {
        console.error('💥 Correct learning plan course enrollment sync failed:', err);
        process.exit(1);
    });
