require('dotenv').config();
const getConnection = require('./platform/salesforce/common/getConnection');
const getApiData = require("./common/docebo/fetcher");
const APP_BASE = process.env.DOCEBO_API_BASE_URL;

async function createCourseUidMapping() {
    try {
        console.log('🔍 CREATING COURSE UID MAPPING');
        console.log('=' .repeat(60));
        
        const conn = await getConnection();
        if (!conn || !conn.accessToken) {
            throw new Error('Failed to connect to Salesforce');
        }

        // Step 1: Get all courses from Docebo API
        console.log('📚 STEP 1: Getting All Courses from Docebo API...');
        console.log('-'.repeat(50));
        
        let allDoceboCourses = [];
        let page = 1;
        let hasMoreData = true;
        
        while (hasMoreData && page <= 10) { // Limit to 10 pages for safety
            const response = await getApiData(
                'GET', 
                `${APP_BASE}/course/v1/courses?page=${page}&page_size=200`, 
                null
            );
            
            if (response && response.status === 200) {
                const items = response.data?.items || [];
                allDoceboCourses.push(...items);
                
                console.log(`   📄 Page ${page}: ${items.length} courses (Total: ${allDoceboCourses.length})`);
                
                hasMoreData = response.data?.has_more_data || false;
                if (items.length === 0) hasMoreData = false;
                page++;
            } else {
                hasMoreData = false;
            }
        }
        
        console.log(`✅ Found ${allDoceboCourses.length} courses from Docebo`);
        
        // Step 2: Create mapping from numeric ID to uidCourse
        console.log('\n🗺️ STEP 2: Creating ID to uidCourse Mapping...');
        console.log('-'.repeat(50));
        
        const numericIdToUidMap = new Map();
        const uidToNumericIdMap = new Map();
        
        allDoceboCourses.forEach(course => {
            if (course.id && course.uidCourse) {
                numericIdToUidMap.set(course.id.toString(), course.uidCourse);
                uidToNumericIdMap.set(course.uidCourse, course.id.toString());
            }
        });
        
        console.log(`Created mapping for ${numericIdToUidMap.size} courses`);
        
        // Show sample mappings
        const sampleMappings = Array.from(numericIdToUidMap.entries()).slice(0, 5);
        console.log('\n📋 Sample ID to uidCourse mappings:');
        sampleMappings.forEach(([id, uid]) => {
            console.log(`   ${id} → ${uid}`);
        });
        
        // Step 3: Get Salesforce courses and create final mapping
        console.log('\n📊 STEP 3: Creating Final Salesforce Mapping...');
        console.log('-'.repeat(50));
        
        const sfCourses = await conn.sobject("Docebo_Course__c")
            .find({})
            .execute();
            
        console.log(`Found ${sfCourses.length} courses in Salesforce`);
        
        // Create mapping from uidCourse to Salesforce ID
        const uidToSalesforceIdMap = new Map();
        let mappedCount = 0;
        let unmappedCount = 0;
        
        sfCourses.forEach(sfCourse => {
            const numericId = sfCourse.Course_External_Id__c?.toString();
            
            if (numericId && numericIdToUidMap.has(numericId)) {
                const uidCourse = numericIdToUidMap.get(numericId);
                uidToSalesforceIdMap.set(uidCourse, {
                    id: sfCourse.Id,
                    name: sfCourse.Course_Name__c,
                    externalId: sfCourse.Course_External_Id__c
                });
                mappedCount++;
            } else {
                unmappedCount++;
            }
        });
        
        console.log(`✅ Successfully mapped ${mappedCount} courses`);
        console.log(`⚠️ Could not map ${unmappedCount} courses`);
        
        // Show sample final mappings
        const sampleFinalMappings = Array.from(uidToSalesforceIdMap.entries()).slice(0, 5);
        console.log('\n📋 Sample uidCourse to Salesforce mappings:');
        sampleFinalMappings.forEach(([uid, sfData]) => {
            console.log(`   ${uid} → ${sfData.id} (${sfData.name})`);
        });
        
        // Step 4: Test with learning plan enrollment data
        console.log('\n🧪 STEP 4: Testing with Learning Plan Enrollment Data...');
        console.log('-'.repeat(50));
        
        // Get sample enrollment data
        const testLpId = 50;
        const enrollmentResponse = await getApiData(
            'GET', 
            `${APP_BASE}/learn/v1/enrollments?learning_plan_id=${testLpId}&page=1&page_size=5`, 
            null
        );
        
        if (enrollmentResponse && enrollmentResponse.status === 200) {
            const enrollments = enrollmentResponse.data?.items || [];
            console.log(`Testing with ${enrollments.length} sample enrollments`);
            
            let successfulMappings = 0;
            let failedMappings = 0;
            
            enrollments.forEach((enrollment, index) => {
                console.log(`\n   Enrollment ${index + 1}:`);
                console.log(`      uidCourse: ${enrollment.uidCourse}`);
                console.log(`      User ID: ${enrollment.user_id}`);
                console.log(`      Course Name: ${enrollment.name}`);
                
                if (uidToSalesforceIdMap.has(enrollment.uidCourse)) {
                    const sfCourse = uidToSalesforceIdMap.get(enrollment.uidCourse);
                    console.log(`      ✅ SUCCESS: Maps to SF Course: ${sfCourse.name} (${sfCourse.id})`);
                    successfulMappings++;
                } else {
                    console.log(`      ❌ FAILED: Cannot map uidCourse to Salesforce`);
                    failedMappings++;
                }
            });
            
            console.log(`\n📊 Mapping Test Results:`);
            console.log(`   ✅ Successful mappings: ${successfulMappings}/${enrollments.length}`);
            console.log(`   ❌ Failed mappings: ${failedMappings}/${enrollments.length}`);
            
            if (successfulMappings > 0) {
                console.log(`\n🎉 SUCCESS! We can map Learning Plan Course Enrollments!`);
                
                return {
                    success: true,
                    totalDoceboCourses: allDoceboCourses.length,
                    totalSalesforceCourses: sfCourses.length,
                    mappedCourses: mappedCount,
                    unmappedCourses: unmappedCount,
                    testEnrollments: enrollments.length,
                    successfulMappings: successfulMappings,
                    failedMappings: failedMappings,
                    uidToSalesforceIdMap: Object.fromEntries(uidToSalesforceIdMap),
                    ready: true
                };
            } else {
                console.log(`\n⚠️ No successful mappings found in test data`);
                
                return {
                    success: true,
                    ready: false,
                    issue: 'No successful test mappings'
                };
            }
        } else {
            console.log('⚠️ Could not get test enrollment data');
            
            return {
                success: true,
                totalDoceboCourses: allDoceboCourses.length,
                totalSalesforceCourses: sfCourses.length,
                mappedCourses: mappedCount,
                unmappedCourses: unmappedCount,
                uidToSalesforceIdMap: Object.fromEntries(uidToSalesforceIdMap),
                ready: mappedCount > 0
            };
        }
        
    } catch (error) {
        console.error('💥 Error creating course UID mapping:', error);
        return {
            success: false,
            error: error.message
        };
    }
}

console.log('🔄 Starting course UID mapping creation...');
createCourseUidMapping()
    .then((result) => {
        console.log('\n📋 COURSE UID MAPPING CREATION SUMMARY:');
        console.log('=' .repeat(60));
        
        if (result.success) {
            console.log(`✅ Mapping creation completed successfully`);
            
            if (result.totalDoceboCourses) {
                console.log(`📊 Total Docebo Courses: ${result.totalDoceboCourses}`);
            }
            if (result.totalSalesforceCourses) {
                console.log(`📊 Total Salesforce Courses: ${result.totalSalesforceCourses}`);
            }
            if (result.mappedCourses !== undefined) {
                console.log(`✅ Successfully Mapped: ${result.mappedCourses}`);
                console.log(`⚠️ Could Not Map: ${result.unmappedCourses}`);
            }
            
            if (result.testEnrollments) {
                console.log(`🧪 Test Enrollments: ${result.testEnrollments}`);
                console.log(`✅ Successful Test Mappings: ${result.successfulMappings}`);
                console.log(`❌ Failed Test Mappings: ${result.failedMappings}`);
            }
            
            if (result.ready) {
                console.log(`\n🎉 READY TO SYNC LEARNING PLAN COURSE ENROLLMENTS!`);
                console.log(`💡 The mapping is working and we can proceed with the sync.`);
            } else {
                console.log(`\n⚠️ NOT READY: ${result.issue || 'Mapping issues detected'}`);
            }
        } else {
            console.log(`❌ Mapping creation failed: ${result.error}`);
        }
        
        console.log('\n✅ Course UID mapping creation completed');
        process.exit(0);
    })
    .catch(err => {
        console.error('💥 Course UID mapping creation failed:', err);
        process.exit(1);
    });
