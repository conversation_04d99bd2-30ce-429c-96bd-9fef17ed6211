require('dotenv').config();
const getConnection = require('./platform/salesforce/common/getConnection');
const getApiData = require("./common/docebo/fetcher");
const APP_BASE = process.env.DOCEBO_API_BASE_URL;

async function bulkSyncCourse43Enrollments() {
    try {
        console.log('🔄 BULK SYNCING COURSE 43 "Welcome to The Training Hub!" ENROLLMENTS');
        console.log('=' .repeat(80));
        
        const conn = await getConnection();
        if (!conn || !conn.accessToken) {
            throw new Error('Failed to connect to Salesforce');
        }

        console.log('✅ Connected to Salesforce successfully');

        // Step 1: Get course information
        console.log('\n📚 STEP 1: Getting Course Information...');
        console.log('-'.repeat(50));
        
        const sfCourse = await conn.sobject("Docebo_Course__c")
            .findOne({ Course_External_Id__c: 43 });
            
        if (!sfCourse) {
            throw new Error('Course 43 not found in Salesforce');
        }
        
        console.log(`✅ Course found: ${sfCourse.Course_Name__c}`);
        console.log(`   Course ID: ${sfCourse.Id}`);

        // Step 2: Get existing Salesforce enrollments
        console.log('\n📊 STEP 2: Getting Existing Salesforce Enrollments...');
        console.log('-'.repeat(50));
        
        const existingEnrollments = await conn.sobject("Docebo_CourseEnrollment__c")
            .find({ Course__c: sfCourse.Id })
            .execute();
            
        const existingEnrollmentIds = new Set();
        
        // Handle both old and new enrollment ID patterns
        existingEnrollments.forEach(enrollment => {
            if (enrollment.Enrollment_ID__c) {
                existingEnrollmentIds.add(enrollment.Enrollment_ID__c);
                
                // Also add the user ID part for matching
                if (enrollment.Enrollment_ID__c.startsWith('UE-43-')) {
                    const userId = enrollment.Enrollment_ID__c.replace('UE-43-', '');
                    existingEnrollmentIds.add(`43-${userId}`);
                } else if (enrollment.Enrollment_ID__c.startsWith('43-')) {
                    const userId = enrollment.Enrollment_ID__c.replace('43-', '');
                    existingEnrollmentIds.add(`UE-43-${userId}`);
                }
            }
        });
        
        console.log(`Found ${existingEnrollments.length} existing enrollments in Salesforce`);
        console.log(`Tracking ${existingEnrollmentIds.size} unique enrollment patterns`);

        // Step 3: Fetch all Docebo enrollments
        console.log('\n🔍 STEP 3: Fetching All Docebo Enrollments...');
        console.log('-'.repeat(50));
        
        let doceboEnrollments = [];
        let page = 1;
        let hasMoreData = true;
        
        while (hasMoreData) {
            console.log(`   📄 Fetching Docebo page ${page}...`);
            
            const response = await getApiData(
                'GET', 
                `${APP_BASE}/learn/v1/enrollments?course_id=43&page=${page}&page_size=200`, 
                null
            );
            
            if (response && response.status === 200) {
                const items = response.data?.items || [];
                doceboEnrollments.push(...items);
                
                console.log(`      Found ${items.length} enrollments (Total: ${doceboEnrollments.length})`);
                
                hasMoreData = response.data?.has_more_data || false;
                if (items.length === 0) hasMoreData = false;
                page++;
            } else {
                console.log(`      No data on page ${page}`);
                hasMoreData = false;
            }
        }
        
        console.log(`✅ Total Docebo enrollments: ${doceboEnrollments.length}`);

        // Step 4: Identify missing enrollments
        console.log('\n🔍 STEP 4: Identifying Missing Enrollments...');
        console.log('-'.repeat(50));
        
        const missingEnrollments = [];
        
        for (const doceboEnrollment of doceboEnrollments) {
            const newFormatId = `43-${doceboEnrollment.user_id}`;
            const oldFormatId = `UE-43-${doceboEnrollment.user_id}`;
            
            // Check if enrollment exists in either format
            if (!existingEnrollmentIds.has(newFormatId) && !existingEnrollmentIds.has(oldFormatId)) {
                missingEnrollments.push(doceboEnrollment);
            }
        }
        
        console.log(`Found ${missingEnrollments.length} missing enrollments to sync`);

        if (missingEnrollments.length === 0) {
            console.log('✅ All enrollments are already synced!');
            return { 
                success: true, 
                synced: 0, 
                total: doceboEnrollments.length,
                existing: existingEnrollments.length 
            };
        }

        // Step 5: Get user mappings for missing enrollments
        console.log('\n👥 STEP 5: Getting User Mappings...');
        console.log('-'.repeat(50));
        
        const userIds = [...new Set(missingEnrollments.map(e => e.user_id))];
        console.log(`Need to map ${userIds.length} unique users`);
        
        const userMappings = new Map();
        
        // Batch query users in chunks of 100
        const chunkSize = 100;
        for (let i = 0; i < userIds.length; i += chunkSize) {
            const chunk = userIds.slice(i, i + chunkSize);
            
            const users = await conn.sobject("Docebo_Users__c")
                .find({ User_Unique_Id__c: { $in: chunk } })
                .execute();
                
            users.forEach(user => {
                userMappings.set(user.User_Unique_Id__c, user.Id);
            });
            
            console.log(`   Mapped ${users.length} users from chunk ${Math.floor(i/chunkSize) + 1}/${Math.ceil(userIds.length/chunkSize)}`);
        }
        
        console.log(`✅ Successfully mapped ${userMappings.size} users`);

        // Step 6: Prepare enrollment records
        console.log('\n📝 STEP 6: Preparing Enrollment Records...');
        console.log('-'.repeat(50));
        
        const enrollmentsToCreate = [];
        let skippedCount = 0;
        
        for (const doceboEnrollment of missingEnrollments) {
            const salesforceUserId = userMappings.get(doceboEnrollment.user_id);
            
            if (!salesforceUserId) {
                skippedCount++;
                continue;
            }
            
            // Use new format for enrollment ID
            const enrollmentRecord = {
                Course__c: sfCourse.Id,
                Docebo_User__c: salesforceUserId,
                Enrollment_ID__c: `43-${doceboEnrollment.user_id}`,
                Status__c: doceboEnrollment.status || 'enrolled',
                Enrollment_Date__c: doceboEnrollment.date_inscr || new Date().toISOString(),
                Completion__c: doceboEnrollment.completion_percentage || 0,
                Score__c: doceboEnrollment.score_given || 0,
                Credits__c: doceboEnrollment.credits || 0,
                Time_in_course__c: doceboEnrollment.total_time || 0,
                First_Access__c: doceboEnrollment.date_first_access || null,
                Last_Access__c: doceboEnrollment.date_last_access || null,
                Completed_Learning_Objects__c: 0
            };
            
            enrollmentsToCreate.push(enrollmentRecord);
        }
        
        console.log(`Prepared ${enrollmentsToCreate.length} enrollment records`);
        console.log(`Skipped ${skippedCount} enrollments (users not found in Salesforce)`);

        // Step 7: Bulk create enrollments in batches
        console.log('\n💾 STEP 7: Creating Enrollments in Salesforce...');
        console.log('-'.repeat(50));
        
        let successCount = 0;
        let errorCount = 0;
        const batchSize = 50; // Conservative batch size
        const totalBatches = Math.ceil(enrollmentsToCreate.length / batchSize);
        
        console.log(`Processing ${enrollmentsToCreate.length} enrollments in ${totalBatches} batches...`);
        
        for (let i = 0; i < enrollmentsToCreate.length; i += batchSize) {
            const batch = enrollmentsToCreate.slice(i, i + batchSize);
            const batchNum = Math.floor(i / batchSize) + 1;
            
            console.log(`   📦 Processing batch ${batchNum}/${totalBatches} (${batch.length} records)...`);
            
            try {
                const results = await conn.sobject("Docebo_CourseEnrollment__c")
                    .create(batch);
                    
                const resultArray = Array.isArray(results) ? results : [results];
                
                let batchSuccessCount = 0;
                let batchErrorCount = 0;
                
                resultArray.forEach((result, index) => {
                    if (result.success) {
                        successCount++;
                        batchSuccessCount++;
                    } else {
                        errorCount++;
                        batchErrorCount++;
                        console.log(`      ❌ Error in batch ${batchNum}, record ${index + 1}:`, result.errors);
                    }
                });
                
                console.log(`      ✅ Batch ${batchNum} completed: ${batchSuccessCount} success, ${batchErrorCount} errors`);
                
                // Progress indicator
                const progressPercent = ((i + batch.length) / enrollmentsToCreate.length * 100).toFixed(1);
                console.log(`      📊 Overall progress: ${progressPercent}% (${successCount} created, ${errorCount} errors)`);
                
                // Small delay between batches to avoid rate limits
                if (batchNum < totalBatches) {
                    await new Promise(resolve => setTimeout(resolve, 1000));
                }
                
            } catch (batchError) {
                console.error(`      ❌ Batch ${batchNum} failed:`, batchError.message);
                errorCount += batch.length;
            }
        }

        // Step 8: Verify final results
        console.log('\n🔍 STEP 8: Verifying Final Results...');
        console.log('-'.repeat(50));
        
        const finalEnrollments = await conn.sobject("Docebo_CourseEnrollment__c")
            .find({ Course__c: sfCourse.Id })
            .execute();
            
        console.log(`Final enrollment count in Salesforce: ${finalEnrollments.length}`);

        return {
            success: true,
            doceboTotal: doceboEnrollments.length,
            salesforceInitial: existingEnrollments.length,
            salesforceFinal: finalEnrollments.length,
            synced: successCount,
            errors: errorCount,
            skipped: skippedCount,
            missingIdentified: missingEnrollments.length
        };

    } catch (error) {
        console.error('💥 Error in bulk sync:', error);
        return {
            success: false,
            error: error.message
        };
    }
}

// Execute the bulk sync
console.log('🔄 Starting Course 43 Bulk Enrollment Sync...');
bulkSyncCourse43Enrollments()
    .then((result) => {
        console.log('\n📋 BULK SYNC SUMMARY:');
        console.log('=' .repeat(60));
        
        if (result.success) {
            console.log(`📊 Docebo Total Enrollments: ${result.doceboTotal.toLocaleString()}`);
            console.log(`📊 Salesforce Initial: ${result.salesforceInitial.toLocaleString()}`);
            console.log(`📊 Salesforce Final: ${result.salesforceFinal.toLocaleString()}`);
            console.log(`✅ Successfully Synced: ${result.synced.toLocaleString()}`);
            
            if (result.errors > 0) {
                console.log(`❌ Errors: ${result.errors.toLocaleString()}`);
            }
            if (result.skipped > 0) {
                console.log(`⏭️ Skipped (users not found): ${result.skipped.toLocaleString()}`);
            }
            
            const netIncrease = result.salesforceFinal - result.salesforceInitial;
            console.log(`\n🎉 NET RESULT: ${netIncrease.toLocaleString()} new enrollments added to Salesforce!`);
            
            const syncPercentage = result.doceboTotal > 0 ? ((result.salesforceFinal / result.doceboTotal) * 100).toFixed(1) : 0;
            console.log(`📊 Sync Completion: ${syncPercentage}% of Docebo enrollments now in Salesforce`);
            
            if (result.salesforceFinal < result.doceboTotal) {
                const remaining = result.doceboTotal - result.salesforceFinal;
                console.log(`\n⚠️ ${remaining.toLocaleString()} enrollments still missing - likely due to user sync issues`);
            } else {
                console.log(`\n✅ All historical enrollments successfully synced!`);
            }
            
        } else {
            console.log(`❌ Bulk sync failed: ${result.error}`);
        }
        
        console.log('\n✅ Bulk sync process completed');
        process.exit(0);
    })
    .catch(err => {
        console.error('💥 Bulk sync process failed:', err);
        process.exit(1);
    });
