require('dotenv').config();
const getConnection = require('./platform/salesforce/common/getConnection');
const { fetchAllDoceboUsers, processUser } = require('./platform/salesforce/users/historicalDataUpdate');

async function testFirst10UsersUpdate() {
    try {
        console.log('🧪 Testing Historical Data Update for First 10 Users...');
        
        const conn = await getConnection();
        if (!conn || !conn.accessToken) {
            console.error("❌ Invalid Salesforce connection");
            return;
        }

        // Step 1: Fetch all users from Docebo
        console.log('\n📥 Fetching users from Docebo API...');
        const allDoceboUsers = await fetchAllDoceboUsers();
        
        if (!allDoceboUsers || allDoceboUsers.length === 0) {
            console.log('⚠️ No users found in Docebo API');
            return;
        }

        console.log(`✅ Found ${allDoceboUsers.length} total users in Docebo`);
        
        // Step 2: Take only first 10 users
        const first10Users = allDoceboUsers.slice(0, 10);
        console.log(`🎯 Processing first ${first10Users.length} users for testing...`);

        // Step 3: Process each user and collect results
        const results = [];
        let processedCount = 0;
        let updatedContacts = 0;
        let updatedLeads = 0;
        let createdLeads = 0;
        let updatedDoceboUsers = 0;
        let createdDoceboUsers = 0;

        for (const doceboUser of first10Users) {
            try {
                console.log(`\n📋 Processing user ${processedCount + 1}/10: ${doceboUser.userInfo?.user_data?.email || 'No email'}`);
                
                const result = await processUser(conn, doceboUser);
                
                // Track results
                if (result.contactUpdated) updatedContacts++;
                if (result.leadUpdated) updatedLeads++;
                if (result.leadCreated) createdLeads++;
                if (result.doceboUserUpdated) updatedDoceboUsers++;
                if (result.doceboUserCreated) createdDoceboUsers++;
                
                // Store user info for link generation
                const userEmail = doceboUser.userInfo?.user_data?.email;
                const userId = doceboUser.userInfo?.user_data?.user_id;
                
                if (userEmail) {
                    results.push({
                        email: userEmail,
                        userId: userId,
                        result: result
                    });
                }
                
                processedCount++;
                
            } catch (userError) {
                console.error(`❌ Error processing user ${doceboUser.userInfo?.user_data?.user_id}:`, userError.message);
            }
        }

        // Step 4: Get Salesforce record IDs for links
        console.log('\n🔍 Collecting Salesforce record links...');
        const recordLinks = [];

        for (const userResult of results) {
            try {
                // Check for Contact
                const contacts = await conn.sobject("Contact")
                    .find({ Email: userResult.email })
                    .execute();
                
                if (contacts.length > 0) {
                    recordLinks.push({
                        email: userResult.email,
                        userId: userResult.userId,
                        type: 'Contact',
                        recordId: contacts[0].Id,
                        url: `https://strivetogether--full.sandbox.my.salesforce.com/${contacts[0].Id}`,
                        action: userResult.result.contactUpdated ? 'Updated' : 'Existing'
                    });
                } else {
                    // Check for Lead
                    const leads = await conn.sobject("Lead")
                        .find({ Email: userResult.email })
                        .execute();
                    
                    if (leads.length > 0) {
                        recordLinks.push({
                            email: userResult.email,
                            userId: userResult.userId,
                            type: 'Lead',
                            recordId: leads[0].Id,
                            url: `https://strivetogether--full.sandbox.my.salesforce.com/${leads[0].Id}`,
                            action: userResult.result.leadUpdated ? 'Updated' : userResult.result.leadCreated ? 'Created' : 'Existing'
                        });
                    }
                }

                // Check for Docebo_Users__c
                const doceboUsers = await conn.sobject("Docebo_Users__c")
                    .find({ User_Unique_Id__c: Number(userResult.userId) })
                    .execute();
                
                if (doceboUsers.length > 0) {
                    recordLinks.push({
                        email: userResult.email,
                        userId: userResult.userId,
                        type: 'Docebo_Users__c',
                        recordId: doceboUsers[0].Id,
                        url: `https://strivetogether--full.sandbox.my.salesforce.com/${doceboUsers[0].Id}`,
                        action: userResult.result.doceboUserUpdated ? 'Updated' : userResult.result.doceboUserCreated ? 'Created' : 'Existing'
                    });
                }

            } catch (linkError) {
                console.error(`Error getting links for ${userResult.email}:`, linkError.message);
            }
        }

        // Step 5: Display results and links
        console.log('\n📊 FIRST 10 USERS UPDATE SUMMARY:');
        console.log('=' .repeat(80));
        console.log(`📥 Total Users Processed: ${processedCount}`);
        console.log(`👥 Contacts Updated: ${updatedContacts}`);
        console.log(`🎯 Leads Updated: ${updatedLeads}`);
        console.log(`🆕 Leads Created: ${createdLeads}`);
        console.log(`📋 Docebo_Users__c Updated: ${updatedDoceboUsers}`);
        console.log(`🆕 Docebo_Users__c Created: ${createdDoceboUsers}`);

        console.log('\n🔗 SALESFORCE RECORD LINKS:');
        console.log('=' .repeat(80));

        // Group by user
        const userGroups = {};
        recordLinks.forEach(link => {
            if (!userGroups[link.email]) {
                userGroups[link.email] = [];
            }
            userGroups[link.email].push(link);
        });

        Object.keys(userGroups).forEach((email, index) => {
            console.log(`\n👤 User ${index + 1}: ${email}`);
            console.log('-'.repeat(60));
            
            userGroups[email].forEach(link => {
                console.log(`   ${link.type} (${link.action}): ${link.url}`);
            });
        });

        console.log('\n🎯 SUCCESS! First 10 users processed and links generated!');
        console.log('\nYou can now click on any of the links above to view the updated/created records in Salesforce.');

        return recordLinks;

    } catch (error) {
        console.error('💥 Error in first 10 users update test:', error);
    }
}

// Execute the test
console.log('🔄 Starting first 10 users historical data update test...');
testFirst10UsersUpdate()
    .then((links) => {
        if (links && links.length > 0) {
            console.log(`\n✅ Test completed successfully! Generated ${links.length} record links.`);
        } else {
            console.log('\n⚠️ Test completed but no links generated');
        }
        process.exit(0);
    })
    .catch(err => {
        console.error('💥 Test failed:', err);
        process.exit(1);
    });
