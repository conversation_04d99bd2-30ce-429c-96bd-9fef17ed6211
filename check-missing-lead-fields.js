require('dotenv').config();
const getConnection = require('./platform/salesforce/common/getConnection');

async function checkMissingLeadFields() {
    try {
        console.log('🔍 Checking which fields need to be created on Lead object...');
        
        const conn = await getConnection();
        if (!conn || !conn.accessToken) {
            console.error("❌ Invalid Salesforce connection");
            return;
        }

        // Get all fields that currently exist on Lead object
        const leadDescription = await conn.sobject("Lead").describe();
        const existingFields = leadDescription.fields.map(field => field.name.toLowerCase());
        
        console.log(`📋 Lead object currently has ${leadDescription.fields.length} total fields`);

        // List of all fields we want to use (from your mapping)
        const requiredFields = [
            // Standard fields
            'Company',
            'Email', 
            'Title',
            'FirstName',
            'LastName',
            'Website',
            'Status',
            'Description',
            'Fax',
            'Salutation', 
            'Phone',
            'LeadSource',
            'OwnerId',
            
            // Custom fields we want to create
            'Created_by_Docebo_API__c',
            'Gender__c',
            'Role_Type__c', 
            'Employment_Type__c',
            'Race__c',
            'Contact_Type__c',
            'Languages__c',
            'MailingCity__c',
            'MailingCountry__c', 
            'MailingPostalCode__c',
            'MailingState__c',
            'MailingStreet__c',
            'Position_Role__c',
            'Annual_Revenue__c',
            'Industry__c',
            'NumberOfEmployees__c',
            'Rating__c',
            'Time_Zone__c',
            'accountid',
            'Active_Portal_User__c',
            'FTE__c',
            'Gateway__c',
            'Inactive_Contact__c',
            'Legacy_Id__c',
            'No_Longer_Leadership__c',
            'No_Longer_Staff__c',
            'Number_of_Years_in_the_Partnership__c',
            'Type__c'
        ];

        console.log('\n📊 Field Analysis:');
        console.log('=' .repeat(80));

        const existingFieldsList = [];
        const missingFieldsList = [];

        requiredFields.forEach(fieldName => {
            const fieldExists = existingFields.includes(fieldName.toLowerCase());
            
            if (fieldExists) {
                existingFieldsList.push(fieldName);
                console.log(`✅ ${fieldName} - EXISTS`);
            } else {
                missingFieldsList.push(fieldName);
                console.log(`❌ ${fieldName} - MISSING`);
            }
        });

        console.log('\n📈 SUMMARY:');
        console.log('=' .repeat(50));
        console.log(`✅ Existing Fields: ${existingFieldsList.length}/${requiredFields.length}`);
        console.log(`❌ Missing Fields: ${missingFieldsList.length}/${requiredFields.length}`);

        if (missingFieldsList.length > 0) {
            console.log('\n🔧 FIELDS TO CREATE ON LEAD OBJECT:');
            console.log('=' .repeat(60));
            
            // Categorize missing fields
            const standardFields = missingFieldsList.filter(field => !field.includes('__c'));
            const customFields = missingFieldsList.filter(field => field.includes('__c'));
            
            if (standardFields.length > 0) {
                console.log('\n📝 Standard Fields (may need to be enabled):');
                standardFields.forEach((field, index) => {
                    console.log(`   ${index + 1}. ${field}`);
                });
            }
            
            if (customFields.length > 0) {
                console.log('\n🔧 Custom Fields (need to be created):');
                customFields.forEach((field, index) => {
                    console.log(`   ${index + 1}. ${field}`);
                });
                
                console.log('\n💡 CUSTOM FIELD CREATION GUIDE:');
                console.log('-'.repeat(40));
                console.log('For each custom field above, create in Salesforce with these settings:');
                console.log('• Object: Lead');
                console.log('• Field Type: Based on field name (see recommendations below)');
                console.log('• Field Name: Remove __c suffix for the label');
                console.log('• API Name: Exactly as shown above');
                
                console.log('\n🎯 RECOMMENDED FIELD TYPES:');
                console.log('-'.repeat(30));
                
                const fieldTypeRecommendations = {
                    'Created_by_Docebo_API__c': 'Checkbox',
                    'Gender__c': 'Picklist (Male, Female, Non-Binary, Prefer Not To Say)',
                    'Role_Type__c': 'Picklist (copy from existing Contact object)',
                    'Employment_Type__c': 'Text (255)',
                    'Race__c': 'Picklist (copy from existing Contact object)', 
                    'Contact_Type__c': 'Text (255)',
                    'Languages__c': 'Text (255)',
                    'MailingCity__c': 'Text (255)',
                    'MailingCountry__c': 'Text (255)',
                    'MailingPostalCode__c': 'Text (20)',
                    'MailingState__c': 'Text (255)',
                    'MailingStreet__c': 'Text Area (255)',
                    'Position_Role__c': 'Text (255)',
                    'Annual_Revenue__c': 'Number (18, 0)',
                    'Industry__c': 'Text (255)',
                    'NumberOfEmployees__c': 'Number (18, 0)',
                    'Rating__c': 'Text (255)',
                    'Time_Zone__c': 'Text (255)',
                    'accountid': 'Text (18) - for Account ID reference',
                    'Active_Portal_User__c': 'Checkbox',
                    'FTE__c': 'Number (3, 2)',
                    'Gateway__c': 'Text (255)',
                    'Inactive_Contact__c': 'Checkbox',
                    'Legacy_Id__c': 'Text (255)',
                    'No_Longer_Leadership__c': 'Checkbox',
                    'No_Longer_Staff__c': 'Checkbox',
                    'Number_of_Years_in_the_Partnership__c': 'Number (18, 0)',
                    'Type__c': 'Text (255)'
                };
                
                customFields.forEach(field => {
                    const recommendation = fieldTypeRecommendations[field] || 'Text (255)';
                    console.log(`   ${field}: ${recommendation}`);
                });
            }
            
            console.log('\n📋 COMPLETE LIST OF MISSING FIELDS:');
            console.log('-'.repeat(40));
            missingFieldsList.forEach((field, index) => {
                console.log(`${index + 1}. ${field}`);
            });
            
        } else {
            console.log('\n🎉 ALL FIELDS EXIST! Your webhook is ready to create comprehensive leads!');
        }

    } catch (error) {
        console.error('💥 Error checking Lead fields:', error);
    }
}

// Execute the check
console.log('🔄 Starting Lead field validation...');
checkMissingLeadFields()
    .then(() => {
        console.log('\n✅ Field check completed');
        process.exit(0);
    })
    .catch(err => {
        console.error('💥 Field check failed:', err);
        process.exit(1);
    });
