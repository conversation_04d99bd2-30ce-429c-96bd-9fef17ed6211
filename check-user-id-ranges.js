require('dotenv').config();
const getConnection = require('./platform/salesforce/common/getConnection');

async function checkUserIdRanges() {
    try {
        console.log('🔍 CHECKING USER ID RANGES');
        console.log('=' .repeat(50));
        
        const conn = await getConnection();
        if (!conn || !conn.accessToken) {
            throw new Error('Failed to connect to Salesforce');
        }

        console.log('✅ Connected to Salesforce successfully');

        // Step 1: Get user ID ranges in Salesforce
        console.log('\n📊 STEP 1: Getting User ID Ranges in Salesforce...');
        console.log('-'.repeat(50));
        
        const userStats = await conn.query(`
            SELECT MIN(User_Unique_Id__c) minId, MAX(User_Unique_Id__c) maxId, COUNT(Id) totalUsers
            FROM Docebo_Users__c 
            WHERE User_Unique_Id__c != null
        `);
        
        if (userStats.records.length > 0) {
            const stats = userStats.records[0];
            console.log(`📊 Salesforce User ID Range:`);
            console.log(`   Min ID: ${stats.minId}`);
            console.log(`   Max ID: ${stats.maxId}`);
            console.log(`   Total Users: ${stats.totalUsers}`);
        }

        // Step 2: Get sample user IDs
        console.log('\n📋 STEP 2: Sample User IDs in Salesforce...');
        console.log('-'.repeat(50));
        
        const sampleUsers = await conn.query(`
            SELECT User_Unique_Id__c, Email__c
            FROM Docebo_Users__c 
            WHERE User_Unique_Id__c != null
            ORDER BY User_Unique_Id__c ASC
            LIMIT 10
        `);
        
        console.log('First 10 users by ID:');
        sampleUsers.records.forEach((user, index) => {
            console.log(`   ${index + 1}. ID: ${user.User_Unique_Id__c} - ${user.Email__c}`);
        });

        // Step 3: Check if specific user IDs from Docebo exist
        console.log('\n🔍 STEP 3: Checking Specific Docebo User IDs...');
        console.log('-'.repeat(50));
        
        const doceboUserIds = [13023, 13056, 13209, 13396, 13700]; // From our test
        
        for (const userId of doceboUserIds) {
            const userCheck = await conn.query(`
                SELECT Id, Email__c, User_Unique_Id__c
                FROM Docebo_Users__c 
                WHERE User_Unique_Id__c = ${userId}
            `);
            
            if (userCheck.records.length > 0) {
                const user = userCheck.records[0];
                console.log(`   ✅ User ${userId}: Found - ${user.Email__c}`);
            } else {
                console.log(`   ❌ User ${userId}: Not found in Salesforce`);
            }
        }

        // Step 4: Check user ID distribution
        console.log('\n📈 STEP 4: User ID Distribution...');
        console.log('-'.repeat(50));
        
        const distribution = await conn.query(`
            SELECT 
                CASE 
                    WHEN User_Unique_Id__c < 10000 THEN 'Under 10k'
                    WHEN User_Unique_Id__c < 12000 THEN '10k-12k'
                    WHEN User_Unique_Id__c < 14000 THEN '12k-14k'
                    WHEN User_Unique_Id__c < 16000 THEN '14k-16k'
                    ELSE 'Over 16k'
                END range,
                COUNT(Id) count
            FROM Docebo_Users__c 
            WHERE User_Unique_Id__c != null
            GROUP BY 
                CASE 
                    WHEN User_Unique_Id__c < 10000 THEN 'Under 10k'
                    WHEN User_Unique_Id__c < 12000 THEN '10k-12k'
                    WHEN User_Unique_Id__c < 14000 THEN '12k-14k'
                    WHEN User_Unique_Id__c < 16000 THEN '14k-16k'
                    ELSE 'Over 16k'
                END
            ORDER BY MIN(User_Unique_Id__c)
        `);
        
        console.log('User ID distribution:');
        distribution.records.forEach(record => {
            console.log(`   ${record.range}: ${record.count} users`);
        });

        return {
            success: true,
            userStats: userStats.records[0] || {},
            sampleUsers: sampleUsers.records,
            distribution: distribution.records
        };

    } catch (error) {
        console.error('💥 Error checking user ID ranges:', error);
        return {
            success: false,
            error: error.message
        };
    }
}

console.log('🔄 Starting User ID Ranges Check...');
checkUserIdRanges()
    .then((result) => {
        console.log('\n📋 USER ID RANGES CHECK SUMMARY:');
        console.log('=' .repeat(50));
        
        if (result.success) {
            console.log(`✅ Check completed successfully`);
            
            if (result.userStats.totalUsers) {
                console.log(`📊 Total Users: ${result.userStats.totalUsers}`);
                console.log(`📊 ID Range: ${result.userStats.minId} - ${result.userStats.maxId}`);
            }
            
            console.log(`📋 Sample Users: ${result.sampleUsers.length}`);
            console.log(`📈 Distribution Groups: ${result.distribution.length}`);
            
            // Analysis
            if (result.userStats.maxId && result.userStats.maxId < 13000) {
                console.log(`\n⚠️ ISSUE: Salesforce users max ID (${result.userStats.maxId}) is lower than Docebo user IDs (13k+)`);
                console.log(`💡 SOLUTION: Need to sync newer users from Docebo to Salesforce first`);
            } else {
                console.log(`\n✅ User ID ranges appear compatible`);
            }
        } else {
            console.log(`❌ Check failed: ${result.error}`);
        }
        
        console.log('\n✅ User ID ranges check completed');
        process.exit(0);
    })
    .catch(err => {
        console.error('💥 User ID ranges check failed:', err);
        process.exit(1);
    });
