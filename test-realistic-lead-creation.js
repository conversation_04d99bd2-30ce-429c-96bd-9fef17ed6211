require('dotenv').config();
const getConnection = require('./platform/salesforce/common/getConnection');
const { createNewUser } = require('./platform/salesforce/users/createUser');

// Mock user data with realistic fields that should work
const mockUserInfo = {
    user_data: {
        user_id: "88888",
        first_name: "<PERSON>",
        last_name: "Realistic Lead",
        email: "<EMAIL>",
        username: "jane_realistic_lead",
        level: "User",
        manager_username: "jane_manager",
        email_validation_status: "1",
        valid: "1"
    },
    additional_fields: [
        { id: "8", value: "Program Manager", enabled: true }, // Job Title
        { id: "9", value: "Management", enabled: true }, // Role Type
        { id: "10", value: "Full-time", enabled: true }, // Employment Type
        { id: "12", value: "Asian", enabled: true }, // Race Identity
        { id: "13", value: "Female", enabled: true }, // Gender Identity
        { id: "14", value: "StriveTogether Partner Org", enabled: true }, // Organization Name
        { id: "15", value: "No", enabled: true }, // Backbone Partner
        { id: "16", value: "Community Partner", enabled: true }, // Back Partner Type
        { id: "17", value: "2022-03-01", enabled: true }, // Employment Begin Date
        { id: "20", value: "Education Equity", enabled: true }, // Initiative
        { id: "21", value: "Local", enabled: true }, // National/Regional/Local
        { id: "22", value: "Cincinnati, OH", enabled: true } // Organization Headquarters
    ],
    branches: [
        {
            name: "Education Branch",
            path: "/education/programs",
            codes: "EDU001"
        }
    ],
    fired_at: "2022-03-01 09:00:00",
    expiration_date: "2025-12-31 23:59:59"
};

const mockUserListedInfo = {
    last_access_date: "2024-01-20T16:45:00Z"
};

async function testRealisticLeadCreation() {
    try {
        console.log('🧪 Testing realistic lead creation with available fields only...');
        
        const conn = await getConnection();
        if (!conn || !conn.accessToken) {
            console.error("❌ Invalid Salesforce connection");
            return;
        }

        // Clean up any existing test records
        console.log('\n🧹 Cleaning up any existing test records...');
        
        try {
            // Delete existing records
            const existingUsers = await conn.sobject("Docebo_Users__c")
                .find({ Email__c: mockUserInfo.user_data.email });
            
            for (const user of existingUsers) {
                await conn.sobject("Docebo_Users__c").delete(user.Id);
                console.log(`   Deleted existing Docebo_Users__c: ${user.Id}`);
            }

            const existingLeads = await conn.sobject("Lead")
                .find({ Email: mockUserInfo.user_data.email });
            
            for (const lead of existingLeads) {
                await conn.sobject("Lead").delete(lead.Id);
                console.log(`   Deleted existing Lead: ${lead.Id}`);
            }

            const existingContacts = await conn.sobject("Contact")
                .find({ Email: mockUserInfo.user_data.email });
            
            for (const contact of existingContacts) {
                await conn.sobject("Contact").delete(contact.Id);
                console.log(`   Deleted existing Contact: ${contact.Id}`);
            }

        } catch (cleanupError) {
            console.log('   No existing records to clean up or cleanup failed:', cleanupError.message);
        }

        console.log('\n🚀 Creating new user with realistic lead data...');
        
        // Create the user (which should trigger lead creation)
        const result = await createNewUser(mockUserInfo, mockUserListedInfo);
        
        if (result) {
            console.log('✅ User creation completed successfully');
            
            // Verify what was created
            console.log('\n🔍 Verifying created records...');
            
            // Check Docebo_Users__c record
            const createdUser = await conn.sobject("Docebo_Users__c")
                .findOne({ Email__c: mockUserInfo.user_data.email });
            
            if (createdUser) {
                console.log(`✅ Docebo_Users__c created: ${createdUser.Id}`);
                console.log(`   Name: ${createdUser.First_Name__c} ${createdUser.Last_Name__c}`);
                console.log(`   Organization: ${createdUser.Organization_Name__c}`);
                console.log(`   Job Title: ${createdUser.Job_Title__c}`);
                console.log(`   Role Type: ${createdUser.Role_Type__c}`);
                console.log(`   Employment Type: ${createdUser.Employment_Type__c}`);
            }

            // Check Lead record with all available fields
            const createdLead = await conn.sobject("Lead")
                .findOne({ Email: mockUserInfo.user_data.email }, 
                    'Id, FirstName, LastName, Email, Company, Status, Title, ' +
                    'Employment_Type__c, Role_Type__c, accountid__c, ' +
                    'Created_by_Docebo_API__c, Race__c, Gender__c'
                );
            
            if (createdLead) {
                console.log(`✅ Lead created: ${createdLead.Id}`);
                console.log(`   Name: ${createdLead.FirstName} ${createdLead.LastName}`);
                console.log(`   Company: ${createdLead.Company}`);
                console.log(`   Status: ${createdLead.Status}`);
                console.log(`   Title: ${createdLead.Title}`);
                
                console.log('\n📋 Enhanced Lead Fields:');
                console.log(`   Employment Type: ${createdLead.Employment_Type__c || 'N/A'}`);
                console.log(`   Role Type: ${createdLead.Role_Type__c || 'N/A'}`);
                console.log(`   Account ID: ${createdLead.accountid__c || 'N/A'}`);
                console.log(`   Created by Docebo API: ${createdLead.Created_by_Docebo_API__c || 'N/A'}`);
                console.log(`   Race: ${createdLead.Race__c || 'N/A'}`);
                console.log(`   Gender: ${createdLead.Gender__c || 'N/A'}`);
                
                console.log('\n🎯 Success! Lead created with enhanced data from Docebo user information.');
                
            } else {
                console.log('❌ No Lead record found');
            }

            // Check Account record
            const createdAccount = await conn.sobject("Account")
                .findOne({ Id: createdUser.Account__c });
            
            if (createdAccount) {
                console.log(`✅ Account created: ${createdAccount.Id}`);
                console.log(`   Account Name: ${createdAccount.Name}`);
            }

        } else {
            console.log('❌ User creation failed');
        }

    } catch (error) {
        console.error('💥 Error in realistic lead creation test:', error);
        
        if (error.message && error.message.includes('INVALID_FIELD')) {
            console.log('\n💡 Field Error Details:');
            console.log('   Error:', error.message);
        }
    }
}

// Execute the test
console.log('🔄 Starting realistic lead creation test...');
testRealisticLeadCreation()
    .then(() => {
        console.log('\n✅ Test completed');
        process.exit(0);
    })
    .catch(err => {
        console.error('💥 Test failed:', err);
        process.exit(1);
    });
