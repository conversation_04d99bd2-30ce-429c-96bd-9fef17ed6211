require('dotenv').config();
const getConnection = require('./platform/salesforce/common/getConnection');
const doceboService = require('./platform/docebo/services');
const { tidyCourseData } = require('./platform/salesforce/courses/createCourse');

async function updateAllCoursesWithCategories() {
    try {
        console.log('🔄 Updating All Courses from Docebo to Salesforce with Categories');
        console.log('=' .repeat(80));
        
        const conn = await getConnection();
        if (!conn || !conn.accessToken) {
            console.error("❌ Invalid Salesforce connection");
            return;
        }

        // Step 1: Get all courses from Docebo
        console.log('\n📚 FETCHING ALL COURSES FROM DOCEBO...');
        console.log('-'.repeat(60));
        
        const doceboCourses = await doceboService.getTotalCourseListedInfo();
        
        if (!doceboCourses || doceboCourses.length === 0) {
            console.log('❌ No courses found in Docebo');
            return;
        }
        
        console.log(`✅ Found ${doceboCourses.length} courses in Docebo`);

        // Step 2: Get all existing courses from Salesforce
        console.log('\n🔍 FETCHING EXISTING COURSES FROM SALESFORCE...');
        console.log('-'.repeat(60));
        
        const existingSfCourses = await conn.sobject("Docebo_Course__c")
            .find({}, ['Id', 'Course_External_Id__c', 'Course_Name__c', 'Course_Category__c', 'Course_Category_Code__c'])
            .execute();
        
        console.log(`✅ Found ${existingSfCourses.length} courses in Salesforce`);
        
        // Create a map for quick lookup
        const sfCourseMap = new Map();
        existingSfCourses.forEach(course => {
            if (course.Course_External_Id__c) {
                sfCourseMap.set(course.Course_External_Id__c, course);
            }
        });

        // Step 3: Process courses in batches
        console.log('\n🔄 PROCESSING COURSES WITH CATEGORY UPDATES...');
        console.log('-'.repeat(60));
        
        const batchSize = 50;
        const totalBatches = Math.ceil(doceboCourses.length / batchSize);
        let processedCount = 0;
        let updatedCount = 0;
        let createdCount = 0;
        let errorCount = 0;
        let categoryImprovements = 0;
        
        const coursesToUpdate = [];
        const coursesToCreate = [];
        
        console.log(`📊 Processing ${doceboCourses.length} courses in ${totalBatches} batches of ${batchSize}...`);
        
        for (let batchIndex = 0; batchIndex < totalBatches; batchIndex++) {
            const startIndex = batchIndex * batchSize;
            const endIndex = Math.min(startIndex + batchSize, doceboCourses.length);
            const batch = doceboCourses.slice(startIndex, endIndex);
            
            console.log(`\n📦 Processing batch ${batchIndex + 1}/${totalBatches} (courses ${startIndex + 1}-${endIndex})...`);
            
            for (const doceboCourse of batch) {
                try {
                    processedCount++;
                    
                    // Get detailed course info from Docebo
                    let courseDetails = null;
                    try {
                        courseDetails = await doceboService.getCourseInfo(doceboCourse.id);
                        if (courseDetails && courseDetails.data) {
                            courseDetails = courseDetails.data;
                        }
                    } catch (detailError) {
                        console.log(`   ⚠️ Could not get details for course ${doceboCourse.id}: ${detailError.message}`);
                        courseDetails = doceboCourse; // Use basic course data
                    }
                    
                    // Use tidyCourseData to process the course with proper category mapping
                    const processedCourseData = tidyCourseData(courseDetails || doceboCourse, doceboCourse);
                    
                    // Check if course exists in Salesforce
                    const existingSfCourse = sfCourseMap.get(doceboCourse.id.toString());
                    
                    if (existingSfCourse) {
                        // Course exists - check if category data needs updating
                        const needsCategoryUpdate = 
                            existingSfCourse.Course_Category__c !== processedCourseData.Course_Category__c ||
                            existingSfCourse.Course_Category_Code__c !== processedCourseData.Course_Category_Code__c;
                        
                        if (needsCategoryUpdate) {
                            // Check if this is an improvement (not just changing from one real value to another)
                            const isImprovement = 
                                (existingSfCourse.Course_Category__c === "A" || existingSfCourse.Course_Category__c === "null" || !existingSfCourse.Course_Category__c) ||
                                (existingSfCourse.Course_Category_Code__c === "A" || existingSfCourse.Course_Category_Code__c === "null" || !existingSfCourse.Course_Category_Code__c);
                            
                            if (isImprovement) {
                                categoryImprovements++;
                            }
                            
                            coursesToUpdate.push({
                                Id: existingSfCourse.Id,
                                ...processedCourseData
                            });
                            
                            console.log(`   📝 Queued for update: ${doceboCourse.name}`);
                            console.log(`      Category: "${existingSfCourse.Course_Category__c}" → "${processedCourseData.Course_Category__c}"`);
                            console.log(`      Code: "${existingSfCourse.Course_Category_Code__c}" → "${processedCourseData.Course_Category_Code__c}"`);
                        } else {
                            console.log(`   ✅ Already up to date: ${doceboCourse.name}`);
                        }
                    } else {
                        // Course doesn't exist - create it
                        coursesToCreate.push(processedCourseData);
                        console.log(`   ➕ Queued for creation: ${doceboCourse.name}`);
                        console.log(`      Category: "${processedCourseData.Course_Category__c}"`);
                        console.log(`      Code: "${processedCourseData.Course_Category_Code__c}"`);
                    }
                    
                } catch (courseError) {
                    errorCount++;
                    console.error(`   ❌ Error processing course ${doceboCourse.id}: ${courseError.message}`);
                }
            }
            
            // Execute batch updates every 25 courses or at the end
            if (coursesToUpdate.length >= 25 || batchIndex === totalBatches - 1) {
                if (coursesToUpdate.length > 0) {
                    console.log(`\n🔄 Executing batch update of ${coursesToUpdate.length} courses...`);
                    
                    try {
                        const updateResults = await conn.sobject("Docebo_Course__c").update(coursesToUpdate);
                        const successfulUpdates = Array.isArray(updateResults) 
                            ? updateResults.filter(r => r.success).length 
                            : (updateResults.success ? 1 : 0);
                        
                        updatedCount += successfulUpdates;
                        console.log(`   ✅ Successfully updated ${successfulUpdates}/${coursesToUpdate.length} courses`);
                        
                        if (Array.isArray(updateResults)) {
                            updateResults.forEach((result, index) => {
                                if (!result.success) {
                                    console.error(`   ❌ Update failed for course ${coursesToUpdate[index].Course_Name__c}: ${JSON.stringify(result.errors)}`);
                                }
                            });
                        }
                    } catch (updateError) {
                        console.error(`   ❌ Batch update failed: ${updateError.message}`);
                    }
                    
                    coursesToUpdate.length = 0; // Clear the array
                }
            }
            
            // Execute batch creates every 25 courses or at the end
            if (coursesToCreate.length >= 25 || batchIndex === totalBatches - 1) {
                if (coursesToCreate.length > 0) {
                    console.log(`\n➕ Executing batch creation of ${coursesToCreate.length} courses...`);
                    
                    try {
                        const createResults = await conn.sobject("Docebo_Course__c").create(coursesToCreate);
                        const successfulCreates = Array.isArray(createResults) 
                            ? createResults.filter(r => r.success).length 
                            : (createResults.success ? 1 : 0);
                        
                        createdCount += successfulCreates;
                        console.log(`   ✅ Successfully created ${successfulCreates}/${coursesToCreate.length} courses`);
                        
                        if (Array.isArray(createResults)) {
                            createResults.forEach((result, index) => {
                                if (!result.success) {
                                    console.error(`   ❌ Creation failed for course ${coursesToCreate[index].Course_Name__c}: ${JSON.stringify(result.errors)}`);
                                }
                            });
                        }
                    } catch (createError) {
                        console.error(`   ❌ Batch creation failed: ${createError.message}`);
                    }
                    
                    coursesToCreate.length = 0; // Clear the array
                }
            }
            
            // Progress update
            const progressPercent = Math.round((processedCount / doceboCourses.length) * 100);
            console.log(`   📊 Progress: ${processedCount}/${doceboCourses.length} (${progressPercent}%)`);
        }

        // Step 4: Verify improvements
        console.log('\n🔍 VERIFYING CATEGORY IMPROVEMENTS...');
        console.log('-'.repeat(60));
        
        const updatedSfCourses = await conn.sobject("Docebo_Course__c")
            .find({}, ['Course_Category__c', 'Course_Category_Code__c', 'Course_Name__c'])
            .limit(20)
            .execute();
        
        let coursesWithRealCategories = 0;
        let coursesWithPlaceholderCategories = 0;
        
        updatedSfCourses.forEach(course => {
            const hasRealCategory = course.Course_Category__c && 
                                  course.Course_Category__c !== "A" && 
                                  course.Course_Category__c !== "null" && 
                                  course.Course_Category__c !== "";
            
            const hasRealCategoryCode = course.Course_Category_Code__c && 
                                      course.Course_Category_Code__c !== "A" && 
                                      course.Course_Category_Code__c !== "null" && 
                                      course.Course_Category_Code__c !== "";
            
            if (hasRealCategory || hasRealCategoryCode) {
                coursesWithRealCategories++;
            } else {
                coursesWithPlaceholderCategories++;
            }
        });
        
        console.log(`📊 Sample of updated courses (${updatedSfCourses.length} checked):`);
        console.log(`   Courses with real categories: ${coursesWithRealCategories}`);
        console.log(`   Courses with placeholder categories: ${coursesWithPlaceholderCategories}`);

        // Step 5: Final summary
        console.log('\n📊 COURSE UPDATE SUMMARY:');
        console.log('=' .repeat(80));
        
        console.log(`🎯 PROCESSING RESULTS:`);
        console.log(`   Total courses processed: ${processedCount}`);
        console.log(`   Courses updated: ${updatedCount}`);
        console.log(`   Courses created: ${createdCount}`);
        console.log(`   Category improvements: ${categoryImprovements}`);
        console.log(`   Errors encountered: ${errorCount}`);
        
        const successRate = Math.round(((updatedCount + createdCount) / processedCount) * 100);
        console.log(`   Success rate: ${successRate}%`);
        
        console.log(`\n✅ IMPROVEMENTS ACHIEVED:`);
        console.log(`   • Real category data now captured from Docebo`);
        console.log(`   • Course_Category__c field populated with actual category names`);
        console.log(`   • Course_Category_Code__c field populated with actual category codes`);
        console.log(`   • Historical courses updated with current Docebo data`);
        
        console.log(`\n🚀 NEXT STEPS:`);
        console.log(`   • Monitor webhook processing for new courses`);
        console.log(`   • Verify category data in Salesforce reports`);
        console.log(`   • Consider scheduling regular sync to keep data current`);
        
        if (categoryImprovements > 0) {
            console.log(`\n🎉 SUCCESS! ${categoryImprovements} courses now have improved category data!`);
        }

    } catch (error) {
        console.error('💥 Error in updateAllCoursesWithCategories:', error);
    }
}

// Execute the update
console.log('🔄 Starting comprehensive course update with categories...');
updateAllCoursesWithCategories()
    .then(() => {
        console.log('\n✅ Course update completed');
        process.exit(0);
    })
    .catch(err => {
        console.error('💥 Course update failed:', err);
        process.exit(1);
    });
