require('dotenv').config();
const axios = require('axios');

// Actually create User 18853 in Salesforce via webhook
async function createUser18853() {
    console.log('🚀 Creating User 18853 (<EMAIL>) in Salesforce...');
    
    // Simulate the exact webhook that would create User 18853
    const webhookPayload = {
        event: "user.created",
        fired_by_batch_action: false,
        message_id: `create-user-18853-${Date.now()}`,
        payload: {
            fired_at: "2025-06-06 18:51:40",
            user_id: 18853,
            username: "<EMAIL>",
            first_name: "name",
            last_name: "last",
            email: "<EMAIL>",
            expiration_date: null
        }
    };

    console.log('\n📤 SENDING USER CREATION WEBHOOK:');
    console.log('=' .repeat(60));
    console.log(JSON.stringify(webhookPayload, null, 2));

    console.log('\n🎯 EXPECTED CREATION:');
    console.log('✅ Lead with TimeZone__c = "Europe/Oslo"');
    console.log('✅ Docebo_Users__c with comprehensive data');
    console.log('✅ Organization: Austin Aspires');
    console.log('✅ Role: Board of Directors');
    console.log('✅ Race: Black or African American');
    console.log('✅ Gender: Prefer not to respond');
    console.log('✅ Languages: english');

    try {
        console.log('\n🔄 Sending webhook to create user...');
        
        const response = await axios.post('http://localhost:3000/docebo/user-created', webhookPayload, {
            headers: {
                'Content-Type': 'application/json'
            },
            timeout: 15000
        });

        console.log('\n✅ WEBHOOK RESPONSE:');
        console.log('=' .repeat(50));
        console.log(`Status: ${response.status}`);
        console.log(`Response:`, response.data);

        console.log('\n📋 CHECK SERVER LOGS FOR:');
        console.log('=' .repeat(50));
        console.log('✅ 📥 SINGLE USER WEBHOOK DATA for User 18853');
        console.log('✅ 🔧 PROCESSED USER DATA with all field mappings');
        console.log('✅ 🎯 CREATING LEAD with TimeZone__c');
        console.log('✅ Lead created successfully: [Lead ID]');
        console.log('✅ User created successfully: 18853');
        console.log('❌ NO "INVALID_FIELD" errors');

        console.log('\n🔍 VERIFY IN SALESFORCE:');
        console.log('=' .repeat(50));
        console.log('1. Search for "<EMAIL>" in Salesforce');
        console.log('2. Should find a Lead record');
        console.log('3. Check Lead has TimeZone__c = "Europe/Oslo"');
        console.log('4. Search for Docebo_Users__c with User_Unique_Id__c = 18853');
        console.log('5. Verify comprehensive field population');

        return true;

    } catch (error) {
        if (error.code === 'ECONNREFUSED') {
            console.log('\n⚠️ SERVER NOT RUNNING');
            console.log('Please start the server first with: npm start');
            console.log('Then run this script again to create User 18853');
            
            console.log('\n📋 MANUAL STEPS TO CREATE USER:');
            console.log('1. Start server: npm start');
            console.log('2. Run: node create-user-18853.js');
            console.log('3. Check logs for success messages');
            console.log('4. Verify in Salesforce');
            
        } else if (error.response) {
            console.log('\n❌ WEBHOOK ERROR:');
            console.log(`Status: ${error.response.status}`);
            console.log(`Response:`, error.response.data);
        } else {
            console.error('\n❌ NETWORK ERROR:', error.message);
        }
        return false;
    }
}

// Alternative: Direct API creation if webhook fails
async function createUserDirectly() {
    console.log('\n🔧 ALTERNATIVE: Direct API Creation');
    console.log('If webhook fails, you can create the user directly:');
    
    console.log('\n📋 LEAD DATA TO CREATE:');
    console.log('=' .repeat(40));
    console.log('LastName: "last"');
    console.log('FirstName: "name"');
    console.log('Email: "<EMAIL>"');
    console.log('Company: "Austin Aspires"');
    console.log('Title: "title"');
    console.log('Status: "Open - Not Contacted"');
    console.log('TimeZone__c: "Europe/Oslo"');
    console.log('Role_Type__c: "Board of Directors"');
    console.log('Race__c: "Black or African American"');
    console.log('Gender__c: "Prefer not to respond"');
    console.log('Languages__c: "english"');
    console.log('Created_by_Docebo_API__c: true');
    console.log('LeadSource: "Docebo Platform"');
    
    console.log('\n📋 DOCEBO_USERS__C DATA TO CREATE:');
    console.log('=' .repeat(40));
    console.log('User_Unique_Id__c: 18853');
    console.log('Username__c: "<EMAIL>"');
    console.log('Email__c: "<EMAIL>"');
    console.log('First_Name__c: "name"');
    console.log('Last_Name__c: "last"');
    console.log('Organization_Name__c: "Austin Aspires"');
    console.log('Job_Title__c: "title"');
    console.log('Role_Type__c: "Board of Directors"');
    console.log('Race_Identity__c: "Black or African American"');
    console.log('Gender_Identity__c: "Prefer not to respond"');
    console.log('Languages__c: "english"');
    console.log('Initiative__c: "No"');
    console.log('National_Regional_or_Local__c: "National"');
}

// Execute the creation
async function runUserCreation() {
    console.log('🎯 User 18853 Creation Process');
    console.log('=' .repeat(70));

    console.log('\n🔍 CURRENT STATUS:');
    console.log('❌ User 18853 (<EMAIL>) NOT found in Salesforce');
    console.log('❌ Previous webhook attempts failed due to INVALID_FIELD errors');
    console.log('✅ Field mapping issues have been fixed');
    console.log('✅ Code is ready to create user successfully');

    console.log('\n🛠️ PREREQUISITES:');
    console.log('✅ TimeZone__c field created on Lead object');
    console.log('✅ Field mapping code updated');
    console.log('✅ Enhanced logging enabled');

    // Attempt webhook creation
    const webhookSuccess = await createUser18853();
    
    if (!webhookSuccess) {
        await createUserDirectly();
    }

    console.log('\n🎯 USER CREATION SUMMARY:');
    console.log('=' .repeat(60));
    
    if (webhookSuccess) {
        console.log('✅ Webhook sent successfully');
        console.log('✅ Check server logs for creation confirmation');
        console.log('✅ Search <NAME_EMAIL>');
        console.log('✅ User 18853 should now exist in Salesforce');
    } else {
        console.log('⚠️ Webhook couldn\'t be sent');
        console.log('💡 Start server and try again');
        console.log('💡 Or create records manually using the data above');
    }

    console.log('\n📊 EXPECTED FINAL STATE:');
    console.log('✅ Lead: <EMAIL> with TimeZone__c = "Europe/Oslo"');
    console.log('✅ Docebo_Users__c: User_Unique_Id__c = 18853');
    console.log('✅ Complete field mapping with all available data');
    console.log('✅ No INVALID_FIELD errors');

    console.log('\n✅ User 18853 creation process completed!');
}

// Run the creation process
runUserCreation()
    .then(() => {
        console.log('\n🎉 User 18853 creation process finished!');
        console.log('Check Salesforce for the new records.');
        process.exit(0);
    })
    .catch(err => {
        console.error('\n💥 Creation process failed:', err);
        process.exit(1);
    });
