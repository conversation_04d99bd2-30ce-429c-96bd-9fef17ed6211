require('dotenv').config();
const doceboServices = require('./platform/docebo/services');

async function testCourseData(courseId) {
  try {
    console.log(`Fetching data for course ID: ${courseId}`);
    
    const result = await doceboServices.getCourseInfo(courseId);
    
    if (result && result.status === 200 && result.data) {
      console.log('Course data retrieved successfully:');
      console.log(JSON.stringify(result.data, null, 2));
      
      // Save output to file for reference
      const fs = require('fs');
      fs.writeFileSync('course-data-output.json', JSON.stringify(result.data, null, 2));
      console.log('\nOutput saved to course-data-output.json');
    } else {
      console.log('Failed to retrieve course data:');
      console.log(result);
    }
  } catch (error) {
    console.error('Error fetching course data:', error);
  }
}

// Parse command line arguments or use default
const courseId = process.argv[2] || 557;

// Execute
testCourseData(courseId)
  .then(() => console.log('Done'))
  .catch(err => console.error('<PERSON><PERSON><PERSON> failed:', err));