require('dotenv').config();
const axios = require('axios');

// Test the webhook endpoint for getting user enrollments
async function testWebhookUserEnrollments() {
    try {
        console.log('🧪 Testing Webhook User Enrollments Endpoint...');
        
        const baseUrl = 'http://localhost:5000';
        const testUserId = "18839"; // Using a known user ID
        
        console.log(`\n👤 Testing with User ID: ${testUserId}`);
        console.log(`🌐 Base URL: ${baseUrl}`);
        console.log('=' .repeat(60));

        // Test 1: Get enrollment summary (default)
        console.log('\n📊 TEST 1: Getting enrollment summary...');
        try {
            const summaryResponse = await axios.get(`${baseUrl}/webhook/docebo/user/${testUserId}/enrollments`);
            
            console.log('✅ Summary Response Status:', summaryResponse.status);
            console.log('📊 Summary Data:');
            console.log(JSON.stringify(summaryResponse.data, null, 2));
            
        } catch (summaryError) {
            console.error('❌ Summary request failed:', summaryError.message);
            if (summaryError.response) {
                console.error('Response status:', summaryError.response.status);
                console.error('Response data:', summaryError.response.data);
            }
        }

        // Test 2: Get basic enrollments
        console.log('\n📋 TEST 2: Getting basic enrollments...');
        try {
            const basicResponse = await axios.get(`${baseUrl}/webhook/docebo/user/${testUserId}/enrollments?format=basic`);
            
            console.log('✅ Basic Response Status:', basicResponse.status);
            console.log('📋 Basic Data Summary:');
            console.log(`   Total Enrollments: ${basicResponse.data.total_enrollments}`);
            console.log(`   Format: ${basicResponse.data.format}`);
            
            if (basicResponse.data.enrollments && basicResponse.data.enrollments.length > 0) {
                console.log('\n📋 Sample Basic Enrollment:');
                const sample = basicResponse.data.enrollments[0];
                console.log(`   Course ID: ${sample.course_id}`);
                console.log(`   Status: ${sample.status}`);
                console.log(`   Enrollment Date: ${sample.enrollment_date || sample.date_inscr || 'N/A'}`);
                console.log(`   Score: ${sample.score || sample.final_score || 'N/A'}`);
            }
            
        } catch (basicError) {
            console.error('❌ Basic request failed:', basicError.message);
            if (basicError.response) {
                console.error('Response status:', basicError.response.status);
                console.error('Response data:', basicError.response.data);
            }
        }

        // Test 3: Get detailed enrollments (limit processing time)
        console.log('\n📋 TEST 3: Getting detailed enrollments...');
        try {
            const detailedResponse = await axios.get(`${baseUrl}/webhook/docebo/user/${testUserId}/enrollments?format=detailed`, {
                timeout: 60000 // 60 second timeout for detailed requests
            });
            
            console.log('✅ Detailed Response Status:', detailedResponse.status);
            console.log('📋 Detailed Data Summary:');
            console.log(`   Total Enrollments: ${detailedResponse.data.total_enrollments}`);
            console.log(`   Format: ${detailedResponse.data.format}`);
            
            if (detailedResponse.data.enrollments && detailedResponse.data.enrollments.length > 0) {
                console.log('\n📋 Sample Detailed Enrollment:');
                const sample = detailedResponse.data.enrollments[0];
                console.log(`   Course ID: ${sample.course_id}`);
                console.log(`   Course Name: ${sample.course_name}`);
                console.log(`   Course Type: ${sample.course_type}`);
                console.log(`   Status: ${sample.status}`);
                console.log(`   Score: ${sample.score || 'N/A'}`);
                console.log(`   Progress: ${sample.progress || 'N/A'}`);
            }
            
        } catch (detailedError) {
            console.error('❌ Detailed request failed:', detailedError.message);
            if (detailedError.response) {
                console.error('Response status:', detailedError.response.status);
                console.error('Response data:', detailedError.response.data);
            }
        }

        // Test 4: Test with invalid user ID
        console.log('\n❌ TEST 4: Testing with invalid user ID...');
        try {
            const invalidResponse = await axios.get(`${baseUrl}/webhook/docebo/user/99999999/enrollments`);
            console.log('Response for invalid user:', invalidResponse.data);
            
        } catch (invalidError) {
            console.log('✅ Expected error for invalid user:', invalidError.response?.status || invalidError.message);
        }

        // Test 5: Test with missing user ID
        console.log('\n❌ TEST 5: Testing with missing user ID...');
        try {
            const missingResponse = await axios.get(`${baseUrl}/webhook/docebo/user//enrollments`);
            console.log('Response for missing user:', missingResponse.data);
            
        } catch (missingError) {
            console.log('✅ Expected error for missing user:', missingError.response?.status || missingError.message);
        }

        // Test 6: Test different users
        console.log('\n🔄 TEST 6: Testing with multiple users...');
        const testUserIds = ["18838", "18837", "18835"];
        
        for (const userId of testUserIds) {
            try {
                console.log(`\n👤 Testing User ${userId}:`);
                const userResponse = await axios.get(`${baseUrl}/webhook/docebo/user/${userId}/enrollments`);
                
                const data = userResponse.data;
                console.log(`   ✅ Total Enrollments: ${data.total_enrollments}`);
                console.log(`   📊 Completed: ${data.completed}`);
                console.log(`   🔄 In Progress: ${data.in_progress}`);
                console.log(`   ⏸️  Not Started: ${data.not_started}`);
                
            } catch (userError) {
                console.log(`   ❌ Error for User ${userId}: ${userError.message}`);
            }
            
            // Small delay between requests
            await new Promise(resolve => setTimeout(resolve, 1000));
        }

        console.log('\n🎯 WEBHOOK ENDPOINT USAGE EXAMPLES:');
        console.log('=' .repeat(60));
        console.log('// Get enrollment summary (default)');
        console.log(`GET ${baseUrl}/webhook/docebo/user/{userId}/enrollments`);
        console.log('');
        console.log('// Get basic enrollments');
        console.log(`GET ${baseUrl}/webhook/docebo/user/{userId}/enrollments?format=basic`);
        console.log('');
        console.log('// Get detailed enrollments with course info');
        console.log(`GET ${baseUrl}/webhook/docebo/user/{userId}/enrollments?format=detailed`);
        console.log('');
        console.log('// Example with curl:');
        console.log(`curl "${baseUrl}/webhook/docebo/user/${testUserId}/enrollments"`);

        console.log('\n✅ Webhook user enrollments endpoint testing completed!');
        
        return {
            success: true,
            message: 'All webhook enrollment tests completed'
        };

    } catch (error) {
        console.error('💥 Error testing webhook user enrollments:', error);
        return {
            success: false,
            error: error.message
        };
    }
}

// Execute the test
console.log('🔄 Starting webhook user enrollments endpoint test...');
console.log('⚠️  Make sure your webhook server is running on http://localhost:5000');

testWebhookUserEnrollments()
    .then((result) => {
        if (result && result.success) {
            console.log(`\n🎉 Webhook endpoint test completed successfully!`);
        } else {
            console.log('\n⚠️ Webhook endpoint test completed with issues');
            if (result && result.error) {
                console.log('Error:', result.error);
            }
        }
        process.exit(0);
    })
    .catch(err => {
        console.error('💥 Webhook endpoint test failed:', err);
        process.exit(1);
    });
