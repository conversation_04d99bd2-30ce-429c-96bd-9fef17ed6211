const getConnection = require("../common/getConnection");
const getSalesForceUserId = require("../common/getSalesForceUserId");

async function deleteUser (userId) {
    let result = "false";
    const conn = await getConnection();
    let getDoceboId = await getSalesForceUserId(userId);
    if (getDoceboId != null) {
        const delRes = await conn.sobject('Docebo_Users__c')
            .find({ User_Unique_Id__c: parseInt(userId) })
            .destroy(function (err, rets) {
                if (err) {
                    return err;
                } else {
                    return "sf deletion success, ";
                }
            });
        result = (delRes[0].id) ? "sf deletion success, " : "sf deletion failed";
        console.log("==== Here is delete result ====", result);
    }
    return result;
}

async function deleteEmptyUser () {
    let result = "false";
    const conn = await getConnection();
    if (conn.accessToken) {
        try {
            const recordsToDelete = await conn.sobject('Docebo_Users__c')
                .find({ User_Unique_Id__c: null });

            if (recordsToDelete.length === 0) {
                console.log("No empty users found to delete.");
                return "No records to delete.";
            }

            console.log(`Found ${recordsToDelete.length} empty users to delete.`);
            // Batch the record IDs into smaller chunks
            const batchSize = 200; // Adjust batch size as needed
            const recordIds = recordsToDelete.map(record => record.Id);
            for (let i = 0; i < recordIds.length; i += batchSize) {
                const batch = recordIds.slice(i, i + batchSize);
                const delRes = await conn.sobject('Docebo_Users__c').destroy(batch);

                if (delRes.every(res => res.success)) {
                    console.log(`Batch ${i / batchSize + 1}: All records deleted successfully.`);
                } else {
                    console.error(`Batch ${i / batchSize + 1}: Some records failed to delete.`, delRes);
                    result = "sf deletion partially failed";
                }
            }
            result = "sf deletion success";
        } catch (err) {
            console.error("Error during deletion:", err);
            result = "sf deletion failed";
        }
    } else {
        console.error("No valid Salesforce connection.");
        result = "No valid connection.";
    }
    return result;
};

module.exports = {
    deleteUser,
    deleteEmptyUser
}