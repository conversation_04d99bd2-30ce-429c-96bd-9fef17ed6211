require('dotenv').config();
const getConnection = require('./platform/salesforce/common/getConnection');
const { createNewUser } = require('./platform/salesforce/users/createUser');

// Mock user data with all the additional fields
const mockUserInfo = {
    user_data: {
        user_id: "99999",
        first_name: "Test",
        last_name: "Enhanced Lead",
        email: "<EMAIL>",
        username: "test_enhanced_lead",
        level: "User",
        manager_username: "test_manager",
        email_validation_status: "1",
        valid: "1"
    },
    additional_fields: [
        { id: "8", value: "Software Engineer", enabled: true }, // Job Title
        { id: "9", value: "Technical", enabled: true }, // Role Type
        { id: "10", value: "Full-time", enabled: true }, // Employment Type
        { id: "12", value: "Prefer not to say", enabled: true }, // Race Identity
        { id: "13", value: "Non-binary", enabled: true }, // Gender Identity
        { id: "14", value: "Tech Corp Inc", enabled: true }, // Organization Name
        { id: "15", value: "Yes", enabled: true }, // Backbone Partner
        { id: "16", value: "Technology Partner", enabled: true }, // Back Partner Type
        { id: "17", value: "2023-01-15", enabled: true }, // Employment Begin Date
        { id: "20", value: "Digital Transformation", enabled: true }, // Initiative
        { id: "21", value: "National", enabled: true }, // National/Regional/Local
        { id: "22", value: "San Francisco, CA", enabled: true } // Organization Headquarters
    ],
    branches: [
        {
            name: "Technology Branch",
            path: "/tech/software",
            codes: "TECH001"
        }
    ],
    fired_at: "2023-01-15 10:00:00",
    expiration_date: "2024-12-31 23:59:59"
};

const mockUserListedInfo = {
    last_access_date: "2024-01-15T14:30:00Z"
};

async function testEnhancedLeadCreation() {
    try {
        console.log('🧪 Testing enhanced lead creation with all fields...');
        
        // First, let's check if a user with this email already exists
        const conn = await getConnection();
        if (!conn || !conn.accessToken) {
            console.error("❌ Invalid Salesforce connection");
            return;
        }

        // Check for existing records and clean up if needed
        console.log('\n🧹 Cleaning up any existing test records...');
        
        try {
            // Delete existing Docebo_Users__c record
            const existingUsers = await conn.sobject("Docebo_Users__c")
                .find({ Email__c: mockUserInfo.user_data.email });
            
            if (existingUsers.length > 0) {
                for (const user of existingUsers) {
                    await conn.sobject("Docebo_Users__c").delete(user.Id);
                    console.log(`   Deleted existing Docebo_Users__c: ${user.Id}`);
                }
            }

            // Delete existing Lead records
            const existingLeads = await conn.sobject("Lead")
                .find({ Email: mockUserInfo.user_data.email });
            
            if (existingLeads.length > 0) {
                for (const lead of existingLeads) {
                    await conn.sobject("Lead").delete(lead.Id);
                    console.log(`   Deleted existing Lead: ${lead.Id}`);
                }
            }

            // Delete existing Contact records
            const existingContacts = await conn.sobject("Contact")
                .find({ Email: mockUserInfo.user_data.email });
            
            if (existingContacts.length > 0) {
                for (const contact of existingContacts) {
                    await conn.sobject("Contact").delete(contact.Id);
                    console.log(`   Deleted existing Contact: ${contact.Id}`);
                }
            }

        } catch (cleanupError) {
            console.log('   No existing records to clean up or cleanup failed:', cleanupError.message);
        }

        console.log('\n🚀 Creating new user with enhanced lead data...');
        
        // Create the user (which should trigger lead creation)
        const result = await createNewUser(mockUserInfo, mockUserListedInfo);
        
        if (result) {
            console.log('✅ User creation completed successfully');
            
            // Now let's verify what was created
            console.log('\n🔍 Verifying created records...');
            
            // Check Docebo_Users__c record
            const createdUser = await conn.sobject("Docebo_Users__c")
                .findOne({ Email__c: mockUserInfo.user_data.email });
            
            if (createdUser) {
                console.log(`✅ Docebo_Users__c created: ${createdUser.Id}`);
                console.log(`   Name: ${createdUser.First_Name__c} ${createdUser.Last_Name__c}`);
                console.log(`   Organization: ${createdUser.Organization_Name__c}`);
                console.log(`   Job Title: ${createdUser.Job_Title__c}`);
            }

            // Check Lead record
            const createdLead = await conn.sobject("Lead")
                .findOne({ Email: mockUserInfo.user_data.email });
            
            if (createdLead) {
                console.log(`✅ Lead created: ${createdLead.Id}`);
                console.log(`   Name: ${createdLead.FirstName} ${createdLead.LastName}`);
                console.log(`   Company: ${createdLead.Company}`);
                console.log(`   Status: ${createdLead.Status}`);
                
                // Check if custom fields were populated
                const detailedLead = await conn.sobject("Lead")
                    .findOne({ Id: createdLead.Id }, 
                        'Id, FirstName, LastName, Email, Company, Status, ' +
                        'Organization_Name__c, Job_Title__c, Role_Type__c, ' +
                        'Employment_Type__c, Gender_Identity__c, Race_Identity__c, ' +
                        'Initiative__c, Level__c, User_Unique_Id__c, Username__c'
                    );
                
                console.log('\n📋 Enhanced Lead Fields:');
                console.log(`   Organization Name: ${detailedLead.Organization_Name__c || 'N/A'}`);
                console.log(`   Job Title: ${detailedLead.Job_Title__c || 'N/A'}`);
                console.log(`   Role Type: ${detailedLead.Role_Type__c || 'N/A'}`);
                console.log(`   Employment Type: ${detailedLead.Employment_Type__c || 'N/A'}`);
                console.log(`   Gender Identity: ${detailedLead.Gender_Identity__c || 'N/A'}`);
                console.log(`   Race Identity: ${detailedLead.Race_Identity__c || 'N/A'}`);
                console.log(`   Initiative: ${detailedLead.Initiative__c || 'N/A'}`);
                console.log(`   Level: ${detailedLead.Level__c || 'N/A'}`);
                console.log(`   User ID: ${detailedLead.User_Unique_Id__c || 'N/A'}`);
                console.log(`   Username: ${detailedLead.Username__c || 'N/A'}`);
                
            } else {
                console.log('❌ No Lead record found - this might indicate the fields don\'t exist on Lead object');
            }

        } else {
            console.log('❌ User creation failed');
        }

    } catch (error) {
        console.error('💥 Error in enhanced lead creation test:', error);
        
        if (error.message && error.message.includes('INVALID_FIELD')) {
            console.log('\n💡 Field Error Detected:');
            console.log('   Some custom fields may not exist on the Lead object in Salesforce.');
            console.log('   You may need to create these custom fields on the Lead object first.');
        }
    }
}

// Execute the test
console.log('🔄 Starting enhanced lead creation test...');
testEnhancedLeadCreation()
    .then(() => {
        console.log('\n✅ Test completed');
        process.exit(0);
    })
    .catch(err => {
        console.error('💥 Test failed:', err);
        process.exit(1);
    });
