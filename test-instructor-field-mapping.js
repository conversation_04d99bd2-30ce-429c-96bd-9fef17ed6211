require('dotenv').config();
const getConnection = require('./platform/salesforce/common/getConnection');
const doceboServices = require('./platform/docebo/services');

async function testInstructorFieldMapping() {
    try {
        console.log('🧪 Testing Instructor Field Mapping from Docebo to Salesforce');
        console.log('=' .repeat(70));
        
        const conn = await getConnection();
        if (!conn || !conn.accessToken) {
            console.error("❌ Invalid Salesforce connection");
            return;
        }

        // Step 1: Check Salesforce Instructor object structure
        console.log('\n🔍 SALESFORCE INSTRUCTOR OBJECT ANALYSIS:');
        console.log('-'.repeat(50));
        
        try {
            const instructorObjectDesc = await conn.sobject("Instructor__c").describe();
            const instructorFields = instructorObjectDesc.fields;
            
            console.log(`📋 Instructor__c object has ${instructorFields.length} fields:`);
            
            const customFields = instructorFields.filter(f => f.name.endsWith('__c') || f.name === 'Name');
            console.log(`\n📋 Key fields for instructor data:`);
            customFields.forEach(field => {
                console.log(`   ${field.name}: ${field.label} (${field.type})`);
            });
            
        } catch (describeError) {
            console.log(`❌ Error describing Instructor__c object: ${describeError.message}`);
        }

        // Step 2: Check existing instructor records to see what data we have
        console.log('\n🔍 EXISTING INSTRUCTOR RECORDS ANALYSIS:');
        console.log('-'.repeat(50));
        
        const existingInstructors = await conn.sobject("Instructor__c")
            .find({})
            .limit(5)
            .execute();
        
        if (existingInstructors.length > 0) {
            console.log(`📋 Found ${existingInstructors.length} existing instructor records:`);
            
            existingInstructors.forEach((instructor, index) => {
                console.log(`\n   Instructor ${index + 1}: ${instructor.Id}`);
                console.log(`   Name (User ID): ${instructor.Name}`);
                console.log(`   First_Name__c: ${instructor.First_Name__c || 'N/A'}`);
                console.log(`   Last_Name__c: ${instructor.Last_Name__c || 'N/A'}`);
                console.log(`   Email__c: ${instructor.Email__c || 'N/A'}`);
                console.log(`   Idst__c: ${instructor.Idst__c || 'N/A'}`);
                console.log(`   Avatar__c: ${instructor.Avatar__c || 'N/A'}`);
                console.log(`   urlAvatar__c: ${instructor.urlAvatar__c || 'N/A'}`);
            });
        } else {
            console.log('❌ No existing instructor records found');
        }

        // Step 3: Test Docebo instructor API with a real instructor
        console.log('\n🔍 DOCEBO INSTRUCTOR API ANALYSIS:');
        console.log('-'.repeat(50));
        
        if (existingInstructors.length > 0) {
            const testInstructor = existingInstructors[0];
            const instructorUserId = testInstructor.Name; // This should be the Docebo user ID
            
            console.log(`📡 Testing Docebo API with instructor User ID: ${instructorUserId}`);
            
            // Find a course and session to test with
            const sampleCourse = await conn.sobject("Docebo_Course__c")
                .findOne({});
            
            const sampleSession = await conn.sobject("Docebo_Session__c")
                .findOne({});
            
            if (sampleCourse && sampleSession) {
                console.log(`📋 Using Course: ${sampleCourse.Course_External_Id__c}`);
                console.log(`📋 Using Session: ${sampleSession.Session_External_ID__c}`);
                
                try {
                    const instructorApiResponse = await doceboServices.getInstructorData(
                        instructorUserId,
                        sampleCourse.Course_External_Id__c,
                        sampleSession.Session_External_ID__c
                    );
                    
                    console.log(`\n📡 Docebo API Response:`);
                    console.log(`   Status: ${instructorApiResponse?.status || 'N/A'}`);
                    
                    if (instructorApiResponse && instructorApiResponse.data) {
                        console.log(`\n📋 Raw API Response Structure:`);
                        console.log(JSON.stringify(instructorApiResponse.data, null, 2));
                        
                        if (instructorApiResponse.data.instructors && instructorApiResponse.data.instructors.length > 0) {
                            const instructorData = instructorApiResponse.data.instructors[0];
                            
                            console.log(`\n📋 Available Instructor Fields from Docebo:`);
                            Object.keys(instructorData).forEach(key => {
                                const value = instructorData[key];
                                console.log(`   ${key}: ${value} (${typeof value})`);
                            });
                            
                            // Compare with what we're currently mapping
                            console.log(`\n🔄 CURRENT FIELD MAPPING ANALYSIS:`);
                            console.log('-'.repeat(50));
                            
                            const currentMapping = {
                                'Name': instructorUserId, // User ID
                                'First_Name__c': instructorData.firstname || '',
                                'Last_Name__c': instructorData.lastname || '',
                                'Email__c': instructorData.email || '',
                                'Idst__c': instructorData.idst || '',
                                'Avatar__c': instructorData.avatar || '',
                                'urlAvatar__c': instructorData.urlAvatar || ''
                            };
                            
                            console.log(`📋 Fields we're currently mapping:`);
                            Object.entries(currentMapping).forEach(([sfField, doceboValue]) => {
                                const hasValue = doceboValue && doceboValue !== '';
                                console.log(`   ${sfField}: ${hasValue ? '✅' : '❌'} "${doceboValue}"`);
                            });
                            
                            // Check for unmapped fields
                            const unmappedFields = Object.keys(instructorData).filter(key => 
                                !['firstname', 'lastname', 'email', 'idst', 'avatar', 'urlAvatar'].includes(key)
                            );
                            
                            if (unmappedFields.length > 0) {
                                console.log(`\n📋 Available but UNMAPPED Docebo fields:`);
                                unmappedFields.forEach(field => {
                                    const value = instructorData[field];
                                    console.log(`   ${field}: ${value} (${typeof value})`);
                                });
                            }
                            
                        } else {
                            console.log(`❌ No instructor data found in API response`);
                        }
                    } else {
                        console.log(`❌ Invalid API response structure`);
                    }
                    
                } catch (apiError) {
                    console.log(`❌ Error calling Docebo instructor API: ${apiError.message}`);
                }
            } else {
                console.log(`❌ No sample course or session found for API testing`);
            }
        } else {
            console.log(`❌ No existing instructors to test API with`);
        }

        // Step 4: Summary and recommendations
        console.log('\n📊 INSTRUCTOR FIELD MAPPING SUMMARY:');
        console.log('=' .repeat(50));
        
        console.log(`\n🔧 CURRENT SALESFORCE INSTRUCTOR TEMPLATE:`);
        console.log(`   Name: User ID from Docebo`);
        console.log(`   First_Name__c: instructor.firstname`);
        console.log(`   Last_Name__c: instructor.lastname`);
        console.log(`   Email__c: instructor.email`);
        console.log(`   Idst__c: instructor.idst`);
        console.log(`   Avatar__c: instructor.avatar`);
        console.log(`   urlAvatar__c: instructor.urlAvatar`);
        
        console.log(`\n📡 DOCEBO API ENDPOINT:`);
        console.log(`   /learn/v1/instructor/getInstructorData`);
        console.log(`   Parameters: user_id, course_id, session_id (optional)`);
        
        console.log(`\n💡 RECOMMENDATIONS:`);
        console.log(`   1. ✅ Current mapping covers basic instructor information`);
        console.log(`   2. 🔍 Check if additional fields are available from Docebo API`);
        console.log(`   3. 🔧 Consider adding more Salesforce fields if needed`);
        console.log(`   4. 📊 Verify instructor data quality and completeness`);

    } catch (error) {
        console.error('💥 Error in instructor field mapping test:', error);
    }
}

// Execute the test
console.log('🔄 Starting instructor field mapping analysis...');
testInstructorFieldMapping()
    .then(() => {
        console.log('\n✅ Analysis completed');
        process.exit(0);
    })
    .catch(err => {
        console.error('💥 Analysis failed:', err);
        process.exit(1);
    });
