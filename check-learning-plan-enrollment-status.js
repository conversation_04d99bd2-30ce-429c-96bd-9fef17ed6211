require('dotenv').config();
const getConnection = require('./platform/salesforce/common/getConnection');
const getApiData = require("./common/docebo/fetcher");
const APP_BASE = process.env.DOCEBO_API_BASE_URL;

async function checkLearningPlanEnrollmentStatus() {
    try {
        console.log('🔍 CHECKING LEARNING PLAN ENROLLMENT STATUS');
        console.log('=' .repeat(80));
        
        const conn = await getConnection();
        if (!conn || !conn.accessToken) {
            throw new Error('Failed to connect to Salesforce');
        }

        console.log('✅ Connected to Salesforce successfully');

        // Step 1: Check Salesforce Learning Plan Enrollments
        console.log('\n📊 STEP 1: Checking Salesforce Learning Plan Enrollments...');
        console.log('-'.repeat(50));
        
        const sfLearningPlanEnrollments = await conn.sobject("Docebo_Learning_Plan_Enrollment__c")
            .find({})
            .execute();
            
        console.log(`📊 Found ${sfLearningPlanEnrollments.length} Learning Plan Enrollments in Salesforce`);
        
        if (sfLearningPlanEnrollments.length > 0) {
            console.log('\n📋 Sample Learning Plan Enrollments in Salesforce:');
            sfLearningPlanEnrollments.slice(0, 5).forEach((enrollment, index) => {
                console.log(`   ${index + 1}. ID: ${enrollment.Id}`);
                console.log(`      Enrollment ID: ${enrollment.Enrolment_Id__c}`);
                console.log(`      Learning Plan ID: ${enrollment.Learning_Plan_Id__c}`);
                console.log(`      User ID: ${enrollment.Docebo_User_Id__c}`);
                console.log(`      Who: ${enrollment.Who__c}`);
                console.log('');
            });
        }

        // Step 2: Check Salesforce Learning Plans
        console.log('\n📚 STEP 2: Checking Salesforce Learning Plans...');
        console.log('-'.repeat(50));
        
        const sfLearningPlans = await conn.sobject("Docebo_Learning_Plan__c")
            .find({})
            .execute();
            
        console.log(`📊 Found ${sfLearningPlans.length} Learning Plans in Salesforce`);
        
        if (sfLearningPlans.length > 0) {
            console.log('\n📋 Learning Plans in Salesforce:');
            sfLearningPlans.forEach((plan, index) => {
                console.log(`   ${index + 1}. ${plan.Learning_Plan_Name__c} (ID: ${plan.Learning_Plan_External_Id__c})`);
            });
        }

        // Step 3: Check Docebo Learning Plan Enrollments via API
        console.log('\n🔍 STEP 3: Checking Docebo Learning Plan Enrollments...');
        console.log('-'.repeat(50));
        
        // Try different API endpoints for learning plan enrollments
        const endpoints = [
            '/learn/v1/learningplans/enrollments',
            '/learn/v1/enrollments?type=learningplan',
            '/learn/v1/learningplan/enrollments',
            '/course/v1/learningplans/enrollments'
        ];
        
        let doceboEnrollments = [];
        let workingEndpoint = null;
        
        for (const endpoint of endpoints) {
            console.log(`🧪 Testing endpoint: ${endpoint}`);
            
            try {
                const response = await getApiData(
                    'GET', 
                    `${APP_BASE}${endpoint}?page=1&page_size=10`, 
                    null
                );
                
                if (response && response.status === 200) {
                    const items = response.data?.items || [];
                    console.log(`   ✅ Success: ${items.length} items found`);
                    console.log(`   📊 Total count: ${response.data?.count || 'N/A'}`);
                    
                    if (items.length > 0) {
                        workingEndpoint = endpoint;
                        console.log(`   📋 Sample enrollment structure:`);
                        const sample = items[0];
                        console.log(`      Keys: ${Object.keys(sample).join(', ')}`);
                        console.log(`      Learning Plan ID: ${sample.learning_plan_id || sample.learningplan_id || 'N/A'}`);
                        console.log(`      User ID: ${sample.user_id || 'N/A'}`);
                        console.log(`      Enrollment Date: ${sample.enrollment_date || sample.date_inscr || 'N/A'}`);
                        break;
                    }
                } else {
                    console.log(`   ❌ No valid response`);
                }
            } catch (endpointError) {
                console.log(`   ❌ Error: ${endpointError.message}`);
            }
        }
        
        if (workingEndpoint) {
            console.log(`\n✅ Working endpoint found: ${workingEndpoint}`);
            console.log('🔄 Fetching all learning plan enrollments...');
            
            let page = 1;
            let hasMoreData = true;
            let totalEnrollments = 0;
            
            while (hasMoreData && page <= 10) { // Limit to 10 pages for initial check
                const response = await getApiData(
                    'GET', 
                    `${APP_BASE}${workingEndpoint}?page=${page}&page_size=200`, 
                    null
                );
                
                if (response && response.status === 200) {
                    const items = response.data?.items || [];
                    doceboEnrollments.push(...items);
                    totalEnrollments += items.length;
                    
                    console.log(`   📄 Page ${page}: ${items.length} enrollments (Total: ${totalEnrollments})`);
                    
                    hasMoreData = response.data?.has_more_data || false;
                    if (items.length === 0) hasMoreData = false;
                    page++;
                } else {
                    hasMoreData = false;
                }
            }
            
            console.log(`\n📊 Docebo Learning Plan Enrollment Summary:`);
            console.log(`   Total found: ${totalEnrollments} enrollments`);
            
            if (doceboEnrollments.length > 0) {
                // Analyze the data
                const uniqueUsers = new Set();
                const uniqueLearningPlans = new Set();
                const enrollmentsByPlan = new Map();
                
                doceboEnrollments.forEach(enrollment => {
                    const userId = enrollment.user_id;
                    const planId = enrollment.learning_plan_id || enrollment.learningplan_id;
                    
                    uniqueUsers.add(userId);
                    uniqueLearningPlans.add(planId);
                    
                    if (!enrollmentsByPlan.has(planId)) {
                        enrollmentsByPlan.set(planId, 0);
                    }
                    enrollmentsByPlan.set(planId, enrollmentsByPlan.get(planId) + 1);
                });
                
                console.log(`   Unique users: ${uniqueUsers.size}`);
                console.log(`   Unique learning plans: ${uniqueLearningPlans.size}`);
                
                console.log(`\n🏆 Top Learning Plans by Enrollment Count:`);
                const topPlans = Array.from(enrollmentsByPlan.entries())
                    .sort((a, b) => b[1] - a[1])
                    .slice(0, 10);
                
                topPlans.forEach(([planId, count], index) => {
                    console.log(`   ${index + 1}. Learning Plan ${planId}: ${count} enrollments`);
                });
            }
        } else {
            console.log('\n❌ No working endpoint found for learning plan enrollments');
        }

        // Step 4: Compare and identify gaps
        console.log('\n📊 STEP 4: Gap Analysis...');
        console.log('-'.repeat(50));
        
        console.log(`Salesforce Learning Plan Enrollments: ${sfLearningPlanEnrollments.length}`);
        console.log(`Docebo Learning Plan Enrollments: ${doceboEnrollments.length}`);
        
        const gap = doceboEnrollments.length - sfLearningPlanEnrollments.length;
        console.log(`Gap: ${gap} missing enrollments`);
        
        if (gap > 0) {
            console.log(`\n⚠️ ISSUE IDENTIFIED: ${gap} learning plan enrollments are missing from Salesforce!`);
            
            // Check if webhook is working
            console.log('\n🔍 Checking webhook functionality...');
            
            // Check recent enrollments in Salesforce
            const recentEnrollments = await conn.sobject("Docebo_Learning_Plan_Enrollment__c")
                .find({})
                .sort({ CreatedDate: -1 })
                .limit(5)
                .execute();
                
            if (recentEnrollments.length > 0) {
                console.log(`✅ Most recent enrollment in Salesforce: ${recentEnrollments[0].CreatedDate}`);
            } else {
                console.log(`❌ No enrollments found in Salesforce`);
            }
        } else {
            console.log(`\n✅ All learning plan enrollments appear to be synced!`);
        }

        return {
            success: true,
            salesforceCount: sfLearningPlanEnrollments.length,
            doceboCount: doceboEnrollments.length,
            gap: gap,
            workingEndpoint: workingEndpoint,
            learningPlansCount: sfLearningPlans.length
        };

    } catch (error) {
        console.error('💥 Error checking learning plan enrollment status:', error);
        return {
            success: false,
            error: error.message
        };
    }
}

// Execute the check
console.log('🔄 Starting Learning Plan Enrollment Status Check...');
checkLearningPlanEnrollmentStatus()
    .then((result) => {
        console.log('\n📋 LEARNING PLAN ENROLLMENT STATUS SUMMARY:');
        console.log('=' .repeat(70));
        
        if (result.success) {
            console.log(`📊 Salesforce Learning Plan Enrollments: ${result.salesforceCount}`);
            console.log(`📊 Docebo Learning Plan Enrollments: ${result.doceboCount}`);
            console.log(`📊 Learning Plans in Salesforce: ${result.learningPlansCount}`);
            console.log(`📊 Gap: ${result.gap} missing enrollments`);
            
            if (result.workingEndpoint) {
                console.log(`✅ Working API Endpoint: ${result.workingEndpoint}`);
            } else {
                console.log(`❌ No working API endpoint found`);
            }
            
            if (result.gap > 0) {
                console.log(`\n💡 NEXT STEPS:`);
                console.log(`1. 🔍 Investigate webhook functionality`);
                console.log(`2. 🔄 Create bulk sync script for missing enrollments`);
                console.log(`3. 🛡️ Implement duplicate prevention`);
                console.log(`4. 📊 Set up monitoring for ongoing sync health`);
            } else {
                console.log(`\n✅ Learning plan enrollments appear to be in sync!`);
            }
        } else {
            console.log(`❌ Check failed: ${result.error}`);
        }
        
        console.log('\n✅ Learning plan enrollment status check completed');
        process.exit(0);
    })
    .catch(err => {
        console.error('💥 Learning plan enrollment status check failed:', err);
        process.exit(1);
    });
