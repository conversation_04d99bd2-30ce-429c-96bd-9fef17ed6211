require('dotenv').config();
const getConnection = require('./platform/salesforce/common/getConnection');
const { createNewUser } = require('./platform/salesforce/users/createUser');

// Mock user data with REALISTIC dummy content for all fields
const mockUserInfo = {
    user_data: {
        user_id: "55555",
        first_name: "<PERSON>",
        last_name: "<PERSON><PERSON><PERSON>",
        email: "<EMAIL>",
        username: "alexandra_rodrigue<PERSON>",
        level: "Power User",
        manager_username: "maria_gonzale<PERSON>",
        email_validation_status: "1",
        valid: "1"
    },
    additional_fields: [
        { id: "8", value: "Senior Director of Community Engagement", enabled: true }, // Job Title
        { id: "9", value: "Community", enabled: true }, // Role Type -> "Community Engagement/Organizing"
        { id: "10", value: "Full-time", enabled: true }, // Employment Type
        { id: "12", value: "Hispanic", enabled: true }, // Race Identity -> "Hispanic or Latine"
        { id: "13", value: "Female", enabled: true }, // Gender Identity -> "Female"
        { id: "14", value: "Cincinnati Children's Hospital", enabled: true }, // Organization Name
        { id: "15", value: "Yes", enabled: true }, // Backbone Partner
        { id: "16", value: "Healthcare Partner", enabled: true }, // Back Partner Type
        { id: "17", value: "2018-03-15", enabled: true }, // Employment Begin Date
        { id: "20", value: "Cradle to Career Network", enabled: true }, // Initiative
        { id: "21", value: "Local", enabled: true }, // National/Regional/Local
        { id: "22", value: "Cincinnati, OH", enabled: true } // Organization Headquarters
    ],
    branches: [
        {
            name: "Healthcare & Community Wellness",
            path: "/healthcare/community-wellness",
            codes: "98765"
        }
    ],
    fired_at: "2018-03-15 08:30:00",
    expiration_date: "2026-12-31 23:59:59"
};

const mockUserListedInfo = {
    last_access_date: "2024-02-01T14:22:00Z"
};

// Override the lead creation to include realistic dummy data
async function createTestLeadWithDummyData() {
    try {
        console.log('🧪 Creating test lead with realistic dummy content...');
        
        const conn = await getConnection();
        if (!conn || !conn.accessToken) {
            console.error("❌ Invalid Salesforce connection");
            return;
        }

        // Clean up existing test records
        console.log('\n🧹 Cleaning up existing test records...');
        try {
            const existingUsers = await conn.sobject("Docebo_Users__c")
                .find({ Email__c: mockUserInfo.user_data.email });
            
            for (const user of existingUsers) {
                await conn.sobject("Docebo_Users__c").delete(user.Id);
                console.log(`   Deleted existing Docebo_Users__c: ${user.Id}`);
            }

            const existingLeads = await conn.sobject("Lead")
                .find({ Email: mockUserInfo.user_data.email });
            
            for (const lead of existingLeads) {
                await conn.sobject("Lead").delete(lead.Id);
                console.log(`   Deleted existing Lead: ${lead.Id}`);
            }
        } catch (cleanupError) {
            console.log('   No existing records to clean up');
        }

        // Create account first
        console.log('\n🏢 Creating account...');
        const accountData = {
            Name: "Alexandra Rodriguez-Martinez 55555",
            Website: "https://www.cincinnatichildrens.org",
        };
        const accountResult = await conn.sobject("Account").create(accountData);
        
        if (accountResult.success) {
            console.log(`✅ Account created: ${accountResult.id}`);
            
            // Create lead with comprehensive dummy data
            console.log('\n👤 Creating lead with realistic dummy content...');
            
            const leadData = {
                // Standard Lead fields with realistic data
                Company: "Cincinnati Children's Hospital",
                Email: "<EMAIL>",
                Title: "Senior Director of Community Engagement",
                FirstName: "Alexandra",
                LastName: "Rodriguez-Martinez",
                Website: "https://www.cincinnatichildrens.org",
                Status: "Open - Not Contacted",
                
                // Custom fields with realistic dummy content
                Created_by_Docebo_API__c: true,
                Gender__c: "Female",
                Role_Type__c: "Community Engagement/Organizing",
                Employment_Type__c: "Full-time",
                Race__c: "Hispanic or Latine",
                Contact_Type__c: "Healthcare Partner",
                Description: "Senior Director focused on community engagement and health equity initiatives. Leads cross-sector partnerships to improve child health outcomes in Cincinnati region.",
                Fax: "(*************",
                Salutation: "Dr.",
                Phone: "(*************",
                Languages__c: "English, Spanish",
                MailingCity__c: "Cincinnati",
                MailingCountry__c: "United States",
                MailingPostalCode__c: "45229",
                MailingState__c: "Ohio",
                MailingStreet__c: "3333 Burnet Avenue",
                Position_Role__c: "Community Engagement/Organizing",
                Annual_Revenue__c: **********, // $2.5B (hospital revenue)
                Industry__c: "Healthcare",
                LeadSource: "Docebo Platform",
                NumberOfEmployees__c: 17000,
                Rating__c: "Hot",
                Time_Zone__c: "America/New_York",
                accountid: String(accountResult.id),
                Active_Portal_User__c: true,
                FTE__c: 1.0,
                Gateway__c: "Docebo API",
                Inactive_Contact__c: false,
                Legacy_Id__c: "55555",
                No_Longer_Leadership__c: false,
                No_Longer_Staff__c: false,
                Number_of_Years_in_the_Partnership__c: 6, // Since 2018
                OwnerId: "005O400000BxnnxIAB",
                Type__c: "Healthcare Partner"
            };

            try {
                const leadResult = await conn.sobject("Lead").create(leadData);
                
                if (leadResult.success) {
                    console.log(`✅ Lead created successfully: ${leadResult.id}`);
                    console.log(`🔗 Lead URL: https://strivetogether--full.sandbox.my.salesforce.com/${leadResult.id}`);
                    
                    // Verify the created lead
                    console.log('\n🔍 Verifying created lead with dummy content...');
                    
                    const createdLead = await conn.sobject("Lead")
                        .findOne({ Id: leadResult.id });
                    
                    if (createdLead) {
                        console.log('\n📋 COMPREHENSIVE LEAD WITH DUMMY CONTENT:');
                        console.log('=' .repeat(70));
                        
                        console.log('\n👤 CONTACT INFORMATION:');
                        console.log(`   Full Name: ${createdLead.Salutation || ''} ${createdLead.FirstName} ${createdLead.LastName}`);
                        console.log(`   Email: ${createdLead.Email}`);
                        console.log(`   Phone: ${createdLead.Phone || 'N/A'}`);
                        console.log(`   Fax: ${createdLead.Fax || 'N/A'}`);
                        console.log(`   Languages: ${createdLead.Languages__c || 'N/A'}`);
                        
                        console.log('\n🏢 ORGANIZATION DETAILS:');
                        console.log(`   Company: ${createdLead.Company}`);
                        console.log(`   Title: ${createdLead.Title}`);
                        console.log(`   Website: ${createdLead.Website || 'N/A'}`);
                        console.log(`   Industry: ${createdLead.Industry__c || 'N/A'}`);
                        console.log(`   Annual Revenue: $${(createdLead.Annual_Revenue__c || 0).toLocaleString()}`);
                        console.log(`   Employees: ${(createdLead.NumberOfEmployees__c || 0).toLocaleString()}`);
                        
                        console.log('\n📍 ADDRESS INFORMATION:');
                        console.log(`   Street: ${createdLead.MailingStreet__c || 'N/A'}`);
                        console.log(`   City: ${createdLead.MailingCity__c || 'N/A'}`);
                        console.log(`   State: ${createdLead.MailingState__c || 'N/A'}`);
                        console.log(`   Postal Code: ${createdLead.MailingPostalCode__c || 'N/A'}`);
                        console.log(`   Country: ${createdLead.MailingCountry__c || 'N/A'}`);
                        console.log(`   Time Zone: ${createdLead.Time_Zone__c || 'N/A'}`);
                        
                        console.log('\n👥 DEMOGRAPHICS & ROLE:');
                        console.log(`   Gender: ${createdLead.Gender__c || 'N/A'}`);
                        console.log(`   Race/Ethnicity: ${createdLead.Race__c || 'N/A'}`);
                        console.log(`   Role Type: ${createdLead.Role_Type__c || 'N/A'}`);
                        console.log(`   Position Role: ${createdLead.Position_Role__c || 'N/A'}`);
                        console.log(`   Employment Type: ${createdLead.Employment_Type__c || 'N/A'}`);
                        console.log(`   FTE: ${createdLead.FTE__c || 'N/A'}`);
                        
                        console.log('\n🤝 PARTNERSHIP INFORMATION:');
                        console.log(`   Contact Type: ${createdLead.Contact_Type__c || 'N/A'}`);
                        console.log(`   Type: ${createdLead.Type__c || 'N/A'}`);
                        console.log(`   Years in Partnership: ${createdLead.Number_of_Years_in_the_Partnership__c || 'N/A'}`);
                        console.log(`   Active Portal User: ${createdLead.Active_Portal_User__c || 'N/A'}`);
                        console.log(`   Leadership Status: ${createdLead.No_Longer_Leadership__c ? 'Former' : 'Current'}`);
                        console.log(`   Staff Status: ${createdLead.No_Longer_Staff__c ? 'Former' : 'Current'}`);
                        
                        console.log('\n⚙️ SYSTEM INFORMATION:');
                        console.log(`   Lead Source: ${createdLead.LeadSource || 'N/A'}`);
                        console.log(`   Status: ${createdLead.Status || 'N/A'}`);
                        console.log(`   Rating: ${createdLead.Rating__c || 'N/A'}`);
                        console.log(`   Gateway: ${createdLead.Gateway__c || 'N/A'}`);
                        console.log(`   Legacy ID: ${createdLead.Legacy_Id__c || 'N/A'}`);
                        console.log(`   Created by Docebo API: ${createdLead.Created_by_Docebo_API__c || 'N/A'}`);
                        console.log(`   Account ID: ${createdLead.accountid || 'N/A'}`);
                        
                        console.log('\n📝 DESCRIPTION:');
                        console.log(`   ${createdLead.Description || 'N/A'}`);
                        
                        console.log('\n🎯 SUCCESS! Lead created with comprehensive dummy content!');
                        console.log(`🔗 View Lead: https://strivetogether--full.sandbox.my.salesforce.com/${leadResult.id}`);
                        
                        return leadResult.id;
                    }
                } else {
                    console.error("❌ Lead creation failed:", leadResult.errors);
                }
            } catch (leadError) {
                console.error("💥 Error creating lead:", leadError);
                console.log('\n💡 This error indicates which fields need to be created on the Lead object.');
            }
        }

    } catch (error) {
        console.error('💥 Error in dummy content test:', error);
    }
}

// Execute the test
console.log('🔄 Starting dummy content lead creation...');
createTestLeadWithDummyData()
    .then((leadId) => {
        if (leadId) {
            console.log(`\n✅ Test completed successfully! Lead ID: ${leadId}`);
        } else {
            console.log('\n⚠️ Test completed but lead creation failed due to missing fields');
        }
        process.exit(0);
    })
    .catch(err => {
        console.error('💥 Test failed:', err);
        process.exit(1);
    });
