const axios = require('axios');
require('dotenv').config();

const TOKEN_URL = process.env.SF_TOKEN_URL
const CLIENT_ID = process.env.SF_API_CLIENT_ID
const CLIENT_SECRET = process.env.SF_API_CLIENT_SECRET
const GRANT_TYPE = process.env.SF_API_GRANT_TYPE
const USER_NAME = process.env.SF_API_USER_NAME
const PASSWORD = process.env.SF_API_PASSWORD

let accessToken = "";

module.exports = async function getAccessToken() {
    try {
        const response = await axios.post(
            TOKEN_URL,
            new URLSearchParams({
                grant_type: GRANT_TYPE,
                client_id: CLIENT_ID,
                client_secret: CLIENT_SECRET,
                username: USER_NAME,
                password:PASSWORD
            }),
            {
                headers: { 'Content-Type': 'application/x-www-form-urlencoded' }
            }
        );

        accessToken = response.data.access_token;
        instanceUrl = response.data.instance_url;
        if (accessToken != "") {
            return {
                status: 200,
                data: {
                    accessToken: accessToken,
                    instanceUrl: instanceUrl
                }
            }
        } else {
            return {
                status: 203,
                accessToken: "No token"
            }
        }
    } catch (error) {
        return {
            status: 505,
            message: error.response?.data || error.message
        }
    }
}
