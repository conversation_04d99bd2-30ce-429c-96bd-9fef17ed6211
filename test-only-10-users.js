require('dotenv').config();
const getConnection = require('./platform/salesforce/common/getConnection');
const { processUser } = require('./platform/salesforce/users/historicalDataUpdate');
const doceboService = require('./platform/docebo/services');

async function fetchOnly10DoceboUsers() {
    try {
        console.log('📥 Fetching ONLY first 10 users from Docebo API...');
        
        // Get only first page with 10 users
        const getApiData = require("./common/docebo/fetcher");
        const APP_BASE = process.env.DOCEBO_API_BASE_URL;
        
        const response = await getApiData(
            'GET',
            `${APP_BASE}/manage/v1/user?page=1&page_size=10`,
            null
        );

        if (!response || !response.data || !response.data.items) {
            console.log('⚠️ No users found in Docebo API');
            return [];
        }

        const users = response.data.items;
        console.log(`✅ Found ${users.length} users in first page`);
        
        // For each user, get detailed info including additional fields
        const usersWithDetails = [];
        
        for (let i = 0; i < users.length; i++) {
            const user = users[i];
            console.log(`   Processing user ${i + 1}/10: ${user.email || 'No email'}`);
            
            try {
                // Get detailed user info with additional fields
                const userInfo = await doceboService.getUserInfo(user.user_id);
                
                if (userInfo.status === 200) {
                    const userData = userInfo.data;
                    userData["fired_at"] = user.creation_date || null;
                    userData["expiration_date"] = user.expiration_date || null;
                    
                    usersWithDetails.push({
                        userInfo: userData,
                        userListedInfo: user
                    });
                }
                
            } catch (userError) {
                console.error(`Error fetching details for user ${user.user_id}:`, userError.message);
            }
        }
        
        console.log(`✅ Successfully processed ${usersWithDetails.length} users with detailed info`);
        return usersWithDetails;
        
    } catch (error) {
        console.error('💥 Error fetching 10 users from Docebo:', error);
        return [];
    }
}

async function testOnly10UsersUpdate() {
    try {
        console.log('🧪 Testing Historical Data Update for ONLY 10 Users...');
        
        const conn = await getConnection();
        if (!conn || !conn.accessToken) {
            console.error("❌ Invalid Salesforce connection");
            return;
        }

        // Step 1: Fetch ONLY 10 users from Docebo
        const doceboUsers = await fetchOnly10DoceboUsers();
        
        if (!doceboUsers || doceboUsers.length === 0) {
            console.log('⚠️ No users found in Docebo API');
            return;
        }

        console.log(`🎯 Processing exactly ${doceboUsers.length} users...`);

        // Step 2: Process each user and collect results
        const results = [];
        let processedCount = 0;
        let updatedContacts = 0;
        let updatedLeads = 0;
        let createdLeads = 0;
        let updatedDoceboUsers = 0;
        let createdDoceboUsers = 0;

        for (const doceboUser of doceboUsers) {
            try {
                const userEmail = doceboUser.userInfo?.user_data?.email;
                const userId = doceboUser.userInfo?.user_data?.user_id;
                
                console.log(`\n📋 Processing user ${processedCount + 1}/${doceboUsers.length}: ${userEmail || 'No email'} (ID: ${userId})`);
                
                const result = await processUser(conn, doceboUser);
                
                // Track results
                if (result.contactUpdated) updatedContacts++;
                if (result.leadUpdated) updatedLeads++;
                if (result.leadCreated) createdLeads++;
                if (result.doceboUserUpdated) updatedDoceboUsers++;
                if (result.doceboUserCreated) createdDoceboUsers++;
                
                // Store user info for link generation
                if (userEmail) {
                    results.push({
                        email: userEmail,
                        userId: userId,
                        result: result
                    });
                }
                
                processedCount++;
                
            } catch (userError) {
                console.error(`❌ Error processing user ${doceboUser.userInfo?.user_data?.user_id}:`, userError.message);
            }
        }

        // Step 3: Get Salesforce record IDs for links
        console.log('\n🔍 Collecting Salesforce record links...');
        const recordLinks = [];

        for (const userResult of results) {
            try {
                // Check for Contact
                const contacts = await conn.sobject("Contact")
                    .find({ Email: userResult.email })
                    .execute();
                
                if (contacts.length > 0) {
                    recordLinks.push({
                        email: userResult.email,
                        userId: userResult.userId,
                        type: 'Contact',
                        recordId: contacts[0].Id,
                        url: `https://strivetogether--full.sandbox.my.salesforce.com/${contacts[0].Id}`,
                        action: userResult.result.contactUpdated ? 'Updated' : 'Existing'
                    });
                } else {
                    // Check for Lead
                    const leads = await conn.sobject("Lead")
                        .find({ Email: userResult.email })
                        .execute();
                    
                    if (leads.length > 0) {
                        recordLinks.push({
                            email: userResult.email,
                            userId: userResult.userId,
                            type: 'Lead',
                            recordId: leads[0].Id,
                            url: `https://strivetogether--full.sandbox.my.salesforce.com/${leads[0].Id}`,
                            action: userResult.result.leadUpdated ? 'Updated' : userResult.result.leadCreated ? 'Created' : 'Existing'
                        });
                    }
                }

                // Check for Docebo_Users__c
                const doceboUsers = await conn.sobject("Docebo_Users__c")
                    .find({ User_Unique_Id__c: Number(userResult.userId) })
                    .execute();
                
                if (doceboUsers.length > 0) {
                    recordLinks.push({
                        email: userResult.email,
                        userId: userResult.userId,
                        type: 'Docebo_Users__c',
                        recordId: doceboUsers[0].Id,
                        url: `https://strivetogether--full.sandbox.my.salesforce.com/${doceboUsers[0].Id}`,
                        action: userResult.result.doceboUserUpdated ? 'Updated' : userResult.result.doceboUserCreated ? 'Created' : 'Existing'
                    });
                }

            } catch (linkError) {
                console.error(`Error getting links for ${userResult.email}:`, linkError.message);
            }
        }

        // Step 4: Display results and links
        console.log('\n📊 EXACTLY 10 USERS UPDATE SUMMARY:');
        console.log('=' .repeat(80));
        console.log(`📥 Total Users Processed: ${processedCount}`);
        console.log(`👥 Contacts Updated: ${updatedContacts}`);
        console.log(`🎯 Leads Updated: ${updatedLeads}`);
        console.log(`🆕 Leads Created: ${createdLeads}`);
        console.log(`📋 Docebo_Users__c Updated: ${updatedDoceboUsers}`);
        console.log(`🆕 Docebo_Users__c Created: ${createdDoceboUsers}`);

        console.log('\n🔗 SALESFORCE RECORD LINKS:');
        console.log('=' .repeat(80));

        // Group by user
        const userGroups = {};
        recordLinks.forEach(link => {
            if (!userGroups[link.email]) {
                userGroups[link.email] = [];
            }
            userGroups[link.email].push(link);
        });

        Object.keys(userGroups).forEach((email, index) => {
            console.log(`\n👤 User ${index + 1}: ${email}`);
            console.log('-'.repeat(60));
            
            userGroups[email].forEach(link => {
                console.log(`   ${link.type} (${link.action}): ${link.url}`);
            });
        });

        console.log('\n🎯 SUCCESS! Exactly 10 users processed and links generated!');
        console.log('\nYou can now click on any of the links above to view the updated/created records in Salesforce.');

        return recordLinks;

    } catch (error) {
        console.error('💥 Error in 10 users update test:', error);
    }
}

// Execute the test
console.log('🔄 Starting EXACTLY 10 users historical data update test...');
testOnly10UsersUpdate()
    .then((links) => {
        if (links && links.length > 0) {
            console.log(`\n✅ Test completed successfully! Generated ${links.length} record links.`);
        } else {
            console.log('\n⚠️ Test completed but no links generated');
        }
        process.exit(0);
    })
    .catch(err => {
        console.error('💥 Test failed:', err);
        process.exit(1);
    });
