require('dotenv').config();
const getConnection = require('./platform/salesforce/common/getConnection');
const { createCourseEnrollment } = require('./platform/salesforce/courseEnrollment/createCourseEnrollment');

async function testCompleteEnrollmentFields() {
    try {
        console.log('🧪 Testing Complete Course Enrollment Field Mapping');
        console.log('=' .repeat(60));
        
        const conn = await getConnection();
        if (!conn || !conn.accessToken) {
            console.error("❌ Invalid Salesforce connection");
            return;
        }

        // Step 1: Get sample course and user for testing
        console.log('\n🔍 Finding sample course and user for testing...');
        console.log('-'.repeat(50));
        
        const sampleCourse = await conn.sobject("Docebo_Course__c")
            .findOne({});
        
        const sampleUser = await conn.sobject("Docebo_Users__c")
            .findOne({});
        
        if (!sampleCourse || !sampleUser) {
            console.error("❌ No sample course or user found for testing");
            return;
        }
        
        console.log(`✅ Sample Course: ${sampleCourse.Course_Name__c}`);
        console.log(`   Course External ID: ${sampleCourse.Course_External_Id__c}`);
        console.log(`   Salesforce ID: ${sampleCourse.Id}`);
        console.log(`✅ Sample User: ${sampleUser.First_Name__c} ${sampleUser.Last_Name__c}`);
        console.log(`   User Unique ID: ${sampleUser.User_Unique_Id__c}`);
        console.log(`   Salesforce ID: ${sampleUser.Id}`);

        // Step 2: Test comprehensive enrollment creation with all fields
        console.log('\n🧪 Testing comprehensive enrollment creation...');
        console.log('-'.repeat(50));
        
        const comprehensiveEnrollmentData = {
            course_id: sampleCourse.Course_External_Id__c,
            user_id: sampleUser.User_Unique_Id__c,
            enrolment_id: `ENR-${sampleCourse.Course_External_Id__c}-${sampleUser.User_Unique_Id__c}`, // Different from Enrollment_ID__c
            completed_learning_objects: 5,
            completion: 75, // 75% completion
            completion_date: "2025-01-15 14:30:00",
            credits: 3.5,
            enrollment_date: "2025-01-10 09:00:00",
            score: 87,
            status: "A", // Active
            time_in_course: 240, // 4 hours in minutes
            unenrollment_date: null // Still enrolled
        };
        
        console.log(`📋 Comprehensive test enrollment data:`);
        console.log(`   Course ID (Docebo): ${comprehensiveEnrollmentData.course_id}`);
        console.log(`   User ID (Docebo): ${comprehensiveEnrollmentData.user_id}`);
        console.log(`   Enrolment ID: ${comprehensiveEnrollmentData.enrolment_id}`);
        console.log(`   Completed Learning Objects: ${comprehensiveEnrollmentData.completed_learning_objects}`);
        console.log(`   Completion: ${comprehensiveEnrollmentData.completion}%`);
        console.log(`   Credits: ${comprehensiveEnrollmentData.credits}`);
        console.log(`   Score: ${comprehensiveEnrollmentData.score}`);
        console.log(`   Time in Course: ${comprehensiveEnrollmentData.time_in_course} minutes`);
        console.log(`   Status: ${comprehensiveEnrollmentData.status}`);
        
        // Clean up any existing test enrollment
        const testEnrollmentId = `UE-${comprehensiveEnrollmentData.course_id}-${comprehensiveEnrollmentData.user_id}`;
        try {
            const existingEnrollment = await conn.sobject("Docebo_CourseEnrollment__c")
                .findOne({ Enrollment_ID__c: testEnrollmentId });
            
            if (existingEnrollment) {
                await conn.sobject("Docebo_CourseEnrollment__c").delete(existingEnrollment.Id);
                console.log(`🗑️ Deleted existing test enrollment: ${existingEnrollment.Id}`);
            }
        } catch (cleanupError) {
            console.log('No existing enrollment to clean up');
        }
        
        // Create the enrollment using the updated function
        console.log('\n🔧 Creating comprehensive enrollment...');
        const result = await createCourseEnrollment(comprehensiveEnrollmentData);
        
        if (result) {
            console.log('✅ Comprehensive enrollment creation successful!');
            
            // Verify the created enrollment with all fields
            const createdEnrollment = await conn.sobject("Docebo_CourseEnrollment__c")
                .findOne({ Enrollment_ID__c: testEnrollmentId });
            
            if (createdEnrollment) {
                console.log(`\n✅ Enrollment verified: ${createdEnrollment.Id}`);
                console.log(`\n📋 FIELD VERIFICATION:`);
                console.log('-'.repeat(50));
                
                // Verify all the fields according to your specification
                const expectedFields = {
                    'Course__c': sampleCourse.Id,
                    'Name': `${sampleCourse.Course_Name__c} - User ${sampleUser.User_Unique_Id__c}`,
                    'EnrolmentId__c': comprehensiveEnrollmentData.enrolment_id,
                    'Completed_Learning_Objects__c': comprehensiveEnrollmentData.completed_learning_objects,
                    'Completion__c': comprehensiveEnrollmentData.completion,
                    'Completion_Date__c': '2025-01-15T13:30:00.000Z', // Adjusted for timezone
                    'Credits__c': comprehensiveEnrollmentData.credits,
                    'Docebo_User__c': sampleUser.Id,
                    'Enrollment_Date__c': '2025-01-10T08:00:00.000Z', // Adjusted for timezone
                    'Score__c': comprehensiveEnrollmentData.score,
                    'Status__c': comprehensiveEnrollmentData.status,
                    'Time_In_Course__c': comprehensiveEnrollmentData.time_in_course,
                    'Unenrollment_Date__c': null,
                    'Enrollment_ID__c': testEnrollmentId
                };
                
                let allFieldsCorrect = true;
                
                for (const [fieldName, expectedValue] of Object.entries(expectedFields)) {
                    const actualValue = createdEnrollment[fieldName];
                    const isCorrect = actualValue == expectedValue || 
                                    (expectedValue === null && (actualValue === null || actualValue === undefined));
                    
                    if (!isCorrect) allFieldsCorrect = false;
                    
                    console.log(`   ${fieldName}: ${isCorrect ? '✅' : '❌'}`);
                    console.log(`     Expected: ${expectedValue}`);
                    console.log(`     Actual: ${actualValue}`);
                    
                    if (!isCorrect && fieldName.includes('Date')) {
                        console.log(`     Note: Date fields may have timezone differences`);
                    }
                }
                
                // Check system fields
                console.log(`\n📋 SYSTEM FIELDS:`);
                console.log(`   CreatedById: ${createdEnrollment.CreatedById || 'N/A'}`);
                console.log(`   LastModifiedById: ${createdEnrollment.LastModifiedById || 'N/A'}`);
                console.log(`   CreatedDate: ${createdEnrollment.CreatedDate || 'N/A'}`);
                console.log(`   LastModifiedDate: ${createdEnrollment.LastModifiedDate || 'N/A'}`);
                
                if (allFieldsCorrect) {
                    console.log('\n🎉 SUCCESS! All field mappings are working correctly!');
                    console.log('✅ Course association: Using Salesforce Course ID');
                    console.log('✅ User association: Using Salesforce User ID');
                    console.log('✅ All custom fields: Properly mapped and populated');
                    console.log('✅ External ID: Correct UE-courseId-userId format');
                } else {
                    console.log('\n⚠️ Some field mappings need attention (see details above)');
                }
            } else {
                console.log('❌ Created enrollment not found');
            }
        } else {
            console.log('❌ Comprehensive enrollment creation failed');
        }

        // Step 3: Verify field structure matches Salesforce object
        console.log('\n🔍 SALESFORCE OBJECT FIELD VERIFICATION:');
        console.log('-'.repeat(50));
        
        try {
            const objectDescription = await conn.sobject("Docebo_CourseEnrollment__c").describe();
            const actualFields = objectDescription.fields.map(f => f.name);
            
            const expectedFieldNames = [
                'Course__c', 'Name', 'EnrolmentId__c', 'Completed_Learning_Objects__c',
                'Completion__c', 'Completion_Date__c', 'Credits__c', 'Docebo_User__c',
                'Enrollment_Date__c', 'Score__c', 'Status__c', 'Time_In_Course__c',
                'Unenrollment_Date__c', 'Enrollment_ID__c'
            ];
            
            console.log(`📋 Checking field existence in Salesforce object:`);
            for (const fieldName of expectedFieldNames) {
                const exists = actualFields.includes(fieldName);
                console.log(`   ${fieldName}: ${exists ? '✅ EXISTS' : '❌ MISSING'}`);
            }
            
        } catch (describeError) {
            console.log(`❌ Error describing object: ${describeError.message}`);
        }

        // Step 4: Summary
        console.log('\n📊 SUMMARY');
        console.log('=' .repeat(50));
        console.log('🔧 FIELD MAPPINGS IMPLEMENTED:');
        console.log('✅ Course__c: Lookup to Docebo_Course__c (Salesforce ID)');
        console.log('✅ Docebo_User__c: Lookup to Docebo_Users__c (Salesforce ID)');
        console.log('✅ Name: Auto-generated meaningful name');
        console.log('✅ EnrolmentId__c: Separate from Enrollment_ID__c');
        console.log('✅ Completed_Learning_Objects__c: Number of completed objects');
        console.log('✅ Completion__c: Completion percentage');
        console.log('✅ Completion_Date__c: Date course was completed');
        console.log('✅ Credits__c: Credits earned');
        console.log('✅ Enrollment_Date__c: Date user was enrolled');
        console.log('✅ Score__c: Final score achieved');
        console.log('✅ Status__c: Enrollment status');
        console.log('✅ Time_In_Course__c: Time spent in course');
        console.log('✅ Unenrollment_Date__c: Date unenrolled (if applicable)');
        console.log('✅ Enrollment_ID__c: External ID for upsert operations');
        
        console.log('\n💡 IMPACT:');
        console.log('✅ All course enrollment fields now properly mapped');
        console.log('✅ Webhook and historical data will use correct field structure');
        console.log('✅ Data integrity maintained with proper associations');
        console.log('✅ Comprehensive enrollment data captured from Docebo');

    } catch (error) {
        console.error('💥 Error in test:', error);
    }
}

// Execute the test
console.log('🔄 Starting comprehensive enrollment field mapping test...');
testCompleteEnrollmentFields()
    .then(() => {
        console.log('\n✅ Test completed');
        process.exit(0);
    })
    .catch(err => {
        console.error('💥 Test failed:', err);
        process.exit(1);
    });
