require('dotenv').config();
const getConnection = require('./platform/salesforce/common/getConnection');

async function detailedSalesforceCheck() {
  try {
    console.log('🔍 Detailed Salesforce verification...');
    
    const conn = await getConnection();
    if (!conn || !conn.accessToken) {
      console.error("❌ Invalid Salesforce connection");
      return;
    }

    console.log(`✅ Connected to Salesforce: ${conn.instanceUrl}`);

    // 1. Check if Instructor__c object exists
    console.log('\n📋 Checking if Instructor__c object exists...');
    try {
      const instructorObjectDesc = await conn.sobject("Instructor__c").describe();
      console.log(`✅ Instructor__c object exists!`);
      console.log(`   Label: ${instructorObjectDesc.label}`);
      console.log(`   API Name: ${instructorObjectDesc.name}`);
      console.log(`   Createable: ${instructorObjectDesc.createable}`);
      console.log(`   Queryable: ${instructorObjectDesc.queryable}`);
    } catch (error) {
      console.log(`❌ Instructor__c object does not exist or is not accessible`);
      console.log(`   Error: ${error.message}`);
      return;
    }

    // 2. Count instructor records
    console.log('\n📊 Counting Instructor__c records...');
    try {
      const count = await conn.sobject("Instructor__c").count();
      console.log(`📈 Total Instructor__c records: ${count}`);
      
      if (count === 0) {
        console.log('⚠️  No instructor records found!');
        
        // Check if we can create a test record
        console.log('\n🧪 Testing record creation...');
        try {
          const testRecord = {
            Name: 'TEST_USER_123',
            First_Name__c: 'Test',
            Last_Name__c: 'User',
            Email__c: '<EMAIL>'
          };
          
          const createResult = await conn.sobject("Instructor__c").create(testRecord);
          if (createResult.success) {
            console.log(`✅ Test record created successfully: ${createResult.id}`);
            
            // Delete the test record
            await conn.sobject("Instructor__c").delete(createResult.id);
            console.log(`🗑️  Test record deleted`);
          } else {
            console.log(`❌ Failed to create test record:`, createResult.errors);
          }
        } catch (createError) {
          console.log(`❌ Error creating test record: ${createError.message}`);
        }
      }
    } catch (countError) {
      console.log(`❌ Error counting records: ${countError.message}`);
    }

    // 3. Try to query some records
    console.log('\n📋 Querying Instructor__c records...');
    try {
      const records = await conn.sobject("Instructor__c")
        .find({}, 'Id, Name, First_Name__c, Last_Name__c, Email__c, CreatedDate')
        .limit(10)
        .execute();
        
      if (records.length > 0) {
        console.log(`✅ Found ${records.length} instructor records:`);
        records.forEach((record, index) => {
          console.log(`${index + 1}. ID: ${record.Id}`);
          console.log(`   Name: ${record.First_Name__c} ${record.Last_Name__c}`);
          console.log(`   User ID: ${record.Name}`);
          console.log(`   Email: ${record.Email__c || 'N/A'}`);
          console.log(`   Created: ${record.CreatedDate}`);
          console.log('');
        });
      } else {
        console.log('❌ No instructor records returned from query');
      }
    } catch (queryError) {
      console.log(`❌ Error querying records: ${queryError.message}`);
    }

    // 4. Check sessions with instructor lookups
    console.log('\n📅 Checking sessions with instructor associations...');
    try {
      const sessionsWithInstructors = await conn.sobject("Docebo_Session__c")
        .find({ Instructor__c: { $ne: null } }, 'Id, Session_Name__c, Instructor__c, LastModifiedDate')
        .limit(5)
        .execute();
        
      console.log(`📈 Sessions with instructor associations: ${sessionsWithInstructors.length}`);
      
      if (sessionsWithInstructors.length > 0) {
        console.log('✅ Sample sessions with instructors:');
        for (const session of sessionsWithInstructors) {
          console.log(`📖 Session: ${session.Id}`);
          console.log(`   Name: ${session.Session_Name__c || 'Unnamed'}`);
          console.log(`   Instructor ID: ${session.Instructor__c}`);
          console.log(`   Last Modified: ${session.LastModifiedDate}`);
          console.log('');
        }
      } else {
        console.log('❌ No sessions found with instructor associations');
      }
    } catch (sessionError) {
      console.log(`❌ Error checking sessions: ${sessionError.message}`);
    }

    // 5. Check our specific test case
    console.log('\n🎯 Checking specific test case (Session 247)...');
    try {
      const testSession = await conn.sobject("Docebo_Session__c")
        .findOne({ Session_External_ID__c: '247' }, 'Id, Session_Name__c, Instructor__c, LastModifiedDate');
        
      if (testSession) {
        console.log(`✅ Test session found: ${testSession.Id}`);
        console.log(`   Name: ${testSession.Session_Name__c}`);
        console.log(`   Instructor__c: ${testSession.Instructor__c || 'NULL'}`);
        console.log(`   Last Modified: ${testSession.LastModifiedDate}`);
        
        if (testSession.Instructor__c) {
          try {
            const instructor = await conn.sobject("Instructor__c")
              .findOne({ Id: testSession.Instructor__c }, 'Id, Name, First_Name__c, Last_Name__c');
            console.log(`   ✅ Linked instructor: ${instructor.First_Name__c} ${instructor.Last_Name__c} (${instructor.Name})`);
          } catch (instructorError) {
            console.log(`   ❌ Error fetching linked instructor: ${instructorError.message}`);
          }
        } else {
          console.log(`   ⚠️  No instructor linked to this session`);
        }
      } else {
        console.log('❌ Test session not found');
      }
    } catch (testError) {
      console.log(`❌ Error checking test session: ${testError.message}`);
    }

    // 6. Check recent activity
    console.log('\n⏰ Checking recent Instructor__c record activity...');
    try {
      const recentRecords = await conn.sobject("Instructor__c")
        .find({}, 'Id, Name, First_Name__c, Last_Name__c, CreatedDate, LastModifiedDate')
        .sort({ CreatedDate: -1 })
        .limit(5)
        .execute();
        
      if (recentRecords.length > 0) {
        console.log(`✅ Most recent instructor records:`);
        recentRecords.forEach((record, index) => {
          console.log(`${index + 1}. ${record.First_Name__c} ${record.Last_Name__c} (${record.Name})`);
          console.log(`   Created: ${record.CreatedDate}`);
          console.log(`   Modified: ${record.LastModifiedDate}`);
          console.log('');
        });
      } else {
        console.log('❌ No recent instructor records found');
      }
    } catch (recentError) {
      console.log(`❌ Error checking recent records: ${recentError.message}`);
    }

  } catch (error) {
    console.error('💥 Fatal error in detailed check:', error);
  }
}

detailedSalesforceCheck()
  .then(() => {
    console.log('\n✅ Detailed verification completed');
    process.exit(0);
  })
  .catch(err => {
    console.error('💥 Detailed verification failed:', err);
    process.exit(1);
  });
