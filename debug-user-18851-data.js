require('dotenv').config();
const doceboService = require('./platform/docebo/services');
const { tidyData } = require('./platform/salesforce/users/createUser');

async function debugUser18851Data() {
    try {
        console.log('🔍 Debugging User 18851 Data from Docebo API...');
        
        const userId = 18851;
        
        // Step 1: Get the same data that the webhook would have received
        console.log('\n📥 Fetching user data from Docebo API (same as webhook)...');
        
        const userInfo = await doceboService.getUserInfo(userId);
        const userListedInfo = await doceboService.getUserListedInfo(userId);
        
        if (userInfo.status !== 200) {
            console.error('❌ Failed to get user info:', userInfo);
            return;
        }
        
        console.log('✅ Successfully retrieved user data from Docebo API');
        
        // Step 2: Show the raw data structure
        console.log('\n📋 RAW DOCEBO USER DATA:');
        console.log('=' .repeat(80));
        console.log('userInfo.data:');
        console.log(JSON.stringify(userInfo.data, null, 2));
        
        console.log('\n📋 RAW DOCEBO USER LISTED INFO:');
        console.log('=' .repeat(80));
        console.log('userListedInfo:');
        console.log(JSON.stringify(userListedInfo, null, 2));
        
        // Step 3: Simulate webhook processing
        console.log('\n🔧 SIMULATING WEBHOOK PROCESSING:');
        console.log('=' .repeat(80));
        
        // Add the webhook fields that would be added
        const userData = userInfo.data;
        userData["fired_at"] = "2025-06-06 18:31:13"; // From webhook
        userData["expiration_date"] = ""; // From webhook (might be empty)
        
        console.log('userData with webhook additions:');
        console.log(JSON.stringify(userData, null, 2));
        
        // Step 4: Test the tidyData function
        console.log('\n🧪 TESTING tidyData FUNCTION:');
        console.log('=' .repeat(80));
        
        const processedData = tidyData(userData, userListedInfo);
        
        console.log('Processed data from tidyData:');
        console.log(JSON.stringify(processedData, null, 2));
        
        // Step 5: Analyze the additional_fields
        console.log('\n📊 ADDITIONAL FIELDS ANALYSIS:');
        console.log('=' .repeat(80));
        
        const additionalFields = userData.additional_fields || [];
        console.log(`Found ${additionalFields.length} additional fields:`);
        
        additionalFields.forEach((field, index) => {
            console.log(`   ${index + 1}. ID: ${field.id}, Value: "${field.value}", Enabled: ${field.enabled}`);
        });
        
        // Step 6: Check specific field mappings
        console.log('\n🎯 SPECIFIC FIELD MAPPING CHECK:');
        console.log('=' .repeat(80));
        
        const fieldMappings = [
            { name: 'Languages', id: '11', sfField: 'Languages__c', value: processedData.Languages__c },
            { name: 'TimeZone', source: 'user_data.timezone', sfField: 'TimeZone__c', value: processedData.TimeZone__c },
            { name: 'Phone', id: '27', sfField: 'Phone__c', value: processedData.Phone__c },
            { name: 'Fax', id: '28', sfField: 'Fax__c', value: processedData.Fax__c },
            { name: 'Salutation', id: '29', sfField: 'Salutation__c', value: processedData.Salutation__c },
            { name: 'Street', id: '24', sfField: 'MailingStreet__c', value: processedData.MailingStreet__c },
            { name: 'City', id: '19', sfField: 'MailingCity__c', value: processedData.MailingCity__c },
            { name: 'State', id: '18', sfField: 'MailingState__c', value: processedData.MailingState__c },
            { name: 'Postal Code', id: '25', sfField: 'MailingPostalCode__c', value: processedData.MailingPostalCode__c },
            { name: 'Country', id: '26', sfField: 'MailingCountry__c', value: processedData.MailingCountry__c },
            { name: 'Website', id: '23', sfField: 'Website__c', value: processedData.Website__c },
            { name: 'Industry', id: '30', sfField: 'Industry__c', value: processedData.Industry__c },
            { name: 'Annual Revenue', id: '31', sfField: 'AnnualRevenue__c', value: processedData.AnnualRevenue__c },
            { name: 'Employees', id: '32', sfField: 'NumberOfEmployees__c', value: processedData.NumberOfEmployees__c },
            { name: 'Rating', id: '33', sfField: 'Rating__c', value: processedData.Rating__c },
            { name: 'Position Role', id: '34', sfField: 'Position_Role__c', value: processedData.Position_Role__c }
        ];
        
        fieldMappings.forEach(mapping => {
            const hasValue = mapping.value && mapping.value !== "" && mapping.value !== 0;
            const status = hasValue ? '✅' : '❌';
            
            if (mapping.id) {
                const additionalField = additionalFields.find(f => f.id === mapping.id);
                if (additionalField) {
                    console.log(`${status} ${mapping.name}: ID ${mapping.id} = "${additionalField.value}" → ${mapping.sfField} = "${mapping.value}"`);
                } else {
                    console.log(`❌ ${mapping.name}: ID ${mapping.id} NOT FOUND in additional_fields → ${mapping.sfField} = "${mapping.value}"`);
                }
            } else {
                console.log(`${status} ${mapping.name}: ${mapping.source} → ${mapping.sfField} = "${mapping.value}"`);
            }
        });
        
        // Step 7: Check what's actually in user_data
        console.log('\n📋 USER_DATA FIELDS:');
        console.log('=' .repeat(50));
        const user_data = userData.user_data || {};
        Object.keys(user_data).forEach(key => {
            console.log(`   ${key}: ${user_data[key]}`);
        });
        
        // Step 8: Recommendations
        console.log('\n💡 DIAGNOSIS AND RECOMMENDATIONS:');
        console.log('=' .repeat(80));
        
        if (additionalFields.length === 0) {
            console.log('❌ ISSUE: No additional_fields found for this user');
            console.log('   → This user may not have additional profile data in Docebo');
            console.log('   → Check if this is a test user or if additional fields are configured in Docebo');
        } else {
            console.log(`✅ Found ${additionalFields.length} additional fields`);
            console.log('   → Check if the field IDs match our mapping expectations');
        }
        
        if (!user_data.timezone) {
            console.log('❌ ISSUE: No timezone in user_data');
        }
        
        if (!user_data.language) {
            console.log('❌ ISSUE: No language in user_data');
        }
        
        console.log('\n🎯 NEXT STEPS:');
        console.log('1. Check if this user has complete profile data in Docebo admin');
        console.log('2. Verify additional field IDs match our mapping');
        console.log('3. Test with a user that has more complete profile data');
        console.log('4. Check Docebo API documentation for field availability');
        
        return {
            userInfo: userInfo.data,
            userListedInfo: userListedInfo,
            processedData: processedData,
            additionalFieldsCount: additionalFields.length
        };
        
    } catch (error) {
        console.error('💥 Error debugging user 18851:', error);
        return null;
    }
}

// Execute the debug
console.log('🔄 Starting User 18851 data debug...');
debugUser18851Data()
    .then((result) => {
        if (result) {
            console.log(`\n✅ Debug completed successfully!`);
            console.log(`📊 Additional fields found: ${result.additionalFieldsCount}`);
        } else {
            console.log('\n⚠️ Debug completed but no results obtained');
        }
        process.exit(0);
    })
    .catch(err => {
        console.error('💥 Debug failed:', err);
        process.exit(1);
    });
