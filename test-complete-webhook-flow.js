require('dotenv').config();
const getConnection = require('./platform/salesforce/common/getConnection');
const { createNewUser } = require('./platform/salesforce/users/createUser');

// Test complete webhook flow: Lead + Docebo_Users__c creation
async function testCompleteWebhookFlow() {
    try {
        console.log('🧪 Testing complete webhook flow: Lead + Docebo_Users__c creation...');
        
        const conn = await getConnection();
        if (!conn || !conn.accessToken) {
            console.error("❌ Invalid Salesforce connection");
            return;
        }

        // Mock comprehensive user data
        const mockUserInfo = {
            user_data: {
                user_id: "555555",
                first_name: "<PERSON>",
                last_name: "Complete-Webhook-Test",
                email: "<EMAIL>",
                username: "sofia_complete_webhook",
                level: "Power User",
                manager_username: "supervisor_maria",
                email_validation_status: "1",
                valid: "1"
            },
            additional_fields: [
                { id: "8", value: "Director of Strategic Partnerships", enabled: true }, // Job Title
                { id: "9", value: "Executive Director", enabled: true }, // Role Type
                { id: "10", value: "Full-time", enabled: true }, // Employment Type
                { id: "12", value: "Hispanic", enabled: true }, // Race Identity
                { id: "13", value: "Female", enabled: true }, // Gender Identity
                { id: "14", value: "United Way of Greater Cincinnati", enabled: true }, // Organization Name
                { id: "15", value: "Yes", enabled: true }, // Backbone Partner
                { id: "16", value: "Community Partner", enabled: true }, // Back Partner Type
                { id: "17", value: "2020-01-15", enabled: true }, // Employment Begin Date
                { id: "20", value: "Education & Workforce Development", enabled: true }, // Initiative
                { id: "21", value: "Regional", enabled: true }, // National/Regional/Local
                { id: "22", value: "Cincinnati, OH", enabled: true } // Organization Headquarters
            ],
            branches: [
                {
                    name: "Strategic Partnerships Division",
                    path: "/partnerships/strategic",
                    codes: "12345"
                }
            ],
            fired_at: "2020-01-15 09:00:00",
            expiration_date: "2025-12-31 23:59:59"
        };

        const mockUserListedInfo = {
            last_access_date: "2024-02-05T16:30:00Z"
        };

        // Clean up existing test records
        console.log('\n🧹 Cleaning up existing test records...');
        try {
            const existingUsers = await conn.sobject("Docebo_Users__c")
                .find({ Email__c: mockUserInfo.user_data.email });
            
            for (const user of existingUsers) {
                await conn.sobject("Docebo_Users__c").delete(user.Id);
                console.log(`   Deleted existing Docebo_Users__c: ${user.Id}`);
            }

            const existingLeads = await conn.sobject("Lead")
                .find({ Email: mockUserInfo.user_data.email });
            
            for (const lead of existingLeads) {
                await conn.sobject("Lead").delete(lead.Id);
                console.log(`   Deleted existing Lead: ${lead.Id}`);
            }
        } catch (cleanupError) {
            console.log('   No existing records to clean up');
        }

        console.log('\n🚀 Creating user via webhook (should create both Lead and Docebo_Users__c)...');
        
        // Create the user using the webhook function
        const result = await createNewUser(mockUserInfo, mockUserListedInfo);
        
        if (result) {
            console.log('✅ Webhook execution completed successfully');
            
            // Verify what was created
            console.log('\n🔍 Verifying created records...');
            
            // Check Docebo_Users__c record with all your specified fields
            const doceboUserFields = [
                'Id', 'Name', 'User_Unique_Id__c', 'User_Level__c', 'Deactivated__c',
                'User_Creation_Date__c', 'User_Expiration_Date__c', 'User_Last_Access_Date__c',
                'User_Suspension_Date__c', 'Email_Validation_Status__c', 'First_Name__c',
                'Last_Name__c', 'Full_Name__c', 'Email__c', 'Organization_Name__c',
                'Organization_URL__c', 'Organization_Headquarters__c', 'Branch_Name__c',
                'Branch_Path__c', 'Branches_Codes__c', 'Job_Title__c', 'Employment_Type__c',
                'Role_Type__c', 'Employment_Begin_Date__c', 'Direct_Manager__c',
                'Backbone_Partner__c', 'Back_Partner_Type__c', 'Gender_Identity__c',
                'Race_Identity__c', 'Initiative__c', 'National_Regional_or_Local__c',
                'Username__c', 'Account__c', 'Contact__c', 'CreatedById', 'LastModifiedById', 'Level__c'
            ].join(', ');

            const createdUser = await conn.sobject("Docebo_Users__c")
                .findOne({ Email__c: mockUserInfo.user_data.email }, doceboUserFields);
            
            if (createdUser) {
                console.log(`✅ Docebo_Users__c created: ${createdUser.Id}`);
                
                console.log('\n📋 DOCEBO_USERS__C RECORD DETAILS:');
                console.log('=' .repeat(60));
                console.log(`   Name: ${createdUser.Name || 'N/A'}`);
                console.log(`   User_Unique_Id__c: ${createdUser.User_Unique_Id__c || 'N/A'}`);
                console.log(`   User_Level__c: ${createdUser.User_Level__c || 'N/A'}`);
                console.log(`   Deactivated__c: ${createdUser.Deactivated__c || 'N/A'}`);
                console.log(`   User_Creation_Date__c: ${createdUser.User_Creation_Date__c || 'N/A'}`);
                console.log(`   User_Expiration_Date__c: ${createdUser.User_Expiration_Date__c || 'N/A'}`);
                console.log(`   User_Last_Access_Date__c: ${createdUser.User_Last_Access_Date__c || 'N/A'}`);
                console.log(`   Email_Validation_Status__c: ${createdUser.Email_Validation_Status__c || 'N/A'}`);
                console.log(`   First_Name__c: ${createdUser.First_Name__c || 'N/A'}`);
                console.log(`   Last_Name__c: ${createdUser.Last_Name__c || 'N/A'}`);
                console.log(`   Full_Name__c: ${createdUser.Full_Name__c || 'N/A'}`);
                console.log(`   Email__c: ${createdUser.Email__c || 'N/A'}`);
                console.log(`   Organization_Name__c: ${createdUser.Organization_Name__c || 'N/A'}`);
                console.log(`   Organization_Headquarters__c: ${createdUser.Organization_Headquarters__c || 'N/A'}`);
                console.log(`   Branch_Name__c: ${createdUser.Branch_Name__c || 'N/A'}`);
                console.log(`   Branch_Path__c: ${createdUser.Branch_Path__c || 'N/A'}`);
                console.log(`   Job_Title__c: ${createdUser.Job_Title__c || 'N/A'}`);
                console.log(`   Employment_Type__c: ${createdUser.Employment_Type__c || 'N/A'}`);
                console.log(`   Role_Type__c: ${createdUser.Role_Type__c || 'N/A'}`);
                console.log(`   Employment_Begin_Date__c: ${createdUser.Employment_Begin_Date__c || 'N/A'}`);
                console.log(`   Direct_Manager__c: ${createdUser.Direct_Manager__c || 'N/A'}`);
                console.log(`   Backbone_Partner__c: ${createdUser.Backbone_Partner__c || 'N/A'}`);
                console.log(`   Back_Partner_Type__c: ${createdUser.Back_Partner_Type__c || 'N/A'}`);
                console.log(`   Gender_Identity__c: ${createdUser.Gender_Identity__c || 'N/A'}`);
                console.log(`   Race_Identity__c: ${createdUser.Race_Identity__c || 'N/A'}`);
                console.log(`   Initiative__c: ${createdUser.Initiative__c || 'N/A'}`);
                console.log(`   Username__c: ${createdUser.Username__c || 'N/A'}`);
                console.log(`   Account__c: ${createdUser.Account__c || 'N/A'}`);
                console.log(`   Contact__c: ${createdUser.Contact__c || 'N/A'}`);
            }

            // Check Lead record with standard field names
            const leadFields = [
                'Id', 'FirstName', 'LastName', 'Email', 'Company', 'Title', 'Website', 'Status',
                'Fax', 'AnnualRevenue', 'Industry', 'NumberOfEmployees', 'Rating',
                'Created_by_Docebo_API__c', 'Gender__c', 'Role_Type__c', 'Employment_Type__c', 'Race__c'
            ].join(', ');

            const createdLead = await conn.sobject("Lead")
                .findOne({ Email: mockUserInfo.user_data.email }, leadFields);
            
            if (createdLead) {
                console.log(`\n✅ Lead created: ${createdLead.Id}`);
                
                console.log('\n📋 LEAD RECORD DETAILS:');
                console.log('=' .repeat(60));
                console.log(`   Name: ${createdLead.FirstName} ${createdLead.LastName}`);
                console.log(`   Email: ${createdLead.Email}`);
                console.log(`   Company: ${createdLead.Company}`);
                console.log(`   Title: ${createdLead.Title}`);
                console.log(`   Website: ${createdLead.Website || 'N/A'}`);
                console.log(`   Status: ${createdLead.Status}`);
                console.log(`   Fax: ${createdLead.Fax || 'N/A'}`);
                console.log(`   AnnualRevenue: $${(createdLead.AnnualRevenue || 0).toLocaleString()}`);
                console.log(`   Industry: ${createdLead.Industry || 'N/A'}`);
                console.log(`   NumberOfEmployees: ${(createdLead.NumberOfEmployees || 0).toLocaleString()}`);
                console.log(`   Rating: ${createdLead.Rating || 'N/A'}`);
                console.log(`   Created_by_Docebo_API__c: ${createdLead.Created_by_Docebo_API__c}`);
                console.log(`   Gender__c: ${createdLead.Gender__c || 'N/A'}`);
                console.log(`   Role_Type__c: ${createdLead.Role_Type__c || 'N/A'}`);
                console.log(`   Employment_Type__c: ${createdLead.Employment_Type__c || 'N/A'}`);
                console.log(`   Race__c: ${createdLead.Race__c || 'N/A'}`);
                
                console.log('\n🎯 SUCCESS! Complete webhook flow working!');
                console.log(`🔗 Lead URL: https://strivetogether--full.sandbox.my.salesforce.com/${createdLead.Id}`);
                console.log(`🔗 Docebo User URL: https://strivetogether--full.sandbox.my.salesforce.com/${createdUser.Id}`);
                
                return { leadId: createdLead.Id, userId: createdUser.Id };
            } else {
                console.log('❌ No Lead record found');
            }

        } else {
            console.log('❌ Webhook execution failed');
        }

    } catch (error) {
        console.error('💥 Error in complete webhook flow test:', error);
    }
}

// Execute the test
console.log('🔄 Starting complete webhook flow test...');
testCompleteWebhookFlow()
    .then((result) => {
        if (result) {
            console.log(`\n✅ Test completed successfully!`);
            console.log(`   Lead ID: ${result.leadId}`);
            console.log(`   Docebo User ID: ${result.userId}`);
            console.log(`\n🎯 Your webhook now creates both Lead and Docebo_Users__c records with comprehensive data!`);
        } else {
            console.log('\n⚠️ Test completed but record creation failed');
        }
        process.exit(0);
    })
    .catch(err => {
        console.error('💥 Test failed:', err);
        process.exit(1);
    });
