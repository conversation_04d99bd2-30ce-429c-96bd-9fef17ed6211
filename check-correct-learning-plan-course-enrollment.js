require('dotenv').config();
const getConnection = require('./platform/salesforce/common/getConnection');

async function checkCorrectLearningPlanCourseEnrollment() {
    try {
        console.log('🔍 CHECKING LEARNING PLAN COURSE ENROLLMENT (CORRECT FIELDS)');
        console.log('=' .repeat(70));
        
        const conn = await getConnection();
        if (!conn || !conn.accessToken) {
            throw new Error('Failed to connect to Salesforce');
        }

        console.log('✅ Connected to Salesforce successfully');

        // Step 1: Get current count
        console.log('\n📊 STEP 1: Getting Current Count...');
        console.log('-'.repeat(40));
        
        const countResult = await conn.query("SELECT COUNT() FROM Docebo_Learning_Plan_Course_Enrollment__c");
        console.log(`📊 Total Learning Plan Course Enrollments: ${countResult.totalSize.toLocaleString()}`);

        // Step 2: Get recent records with correct field names
        console.log('\n📋 STEP 2: Getting Recent Records...');
        console.log('-'.repeat(40));
        
        const recentRecords = await conn.query(`
            SELECT Id, Name, Learning_Plan_Enrollment_Id__c, Course_Enrollment_Id__c, 
                   docebo_v3__CourseEnrollmentStatus__c, Enrollment_Date__c, 
                   Completion_Date__c, Completion_Percentage__c, CreatedDate
            FROM Docebo_Learning_Plan_Course_Enrollment__c 
            ORDER BY CreatedDate DESC 
            LIMIT 10
        `);
        
        console.log(`Found ${recentRecords.totalSize} total records, showing recent ones:`);
        
        recentRecords.records.forEach((record, index) => {
            console.log(`\n   ${index + 1}. ${record.Name || record.Id}`);
            console.log(`      Learning Plan Enrollment ID: ${record.Learning_Plan_Enrollment_Id__c}`);
            console.log(`      Course Enrollment ID: ${record.Course_Enrollment_Id__c}`);
            console.log(`      Status: ${record.docebo_v3__CourseEnrollmentStatus__c}`);
            console.log(`      Enrollment Date: ${record.Enrollment_Date__c}`);
            console.log(`      Completion Date: ${record.Completion_Date__c}`);
            console.log(`      Completion %: ${record.Completion_Percentage__c}`);
            console.log(`      Created: ${record.CreatedDate}`);
        });

        // Step 3: Check recent activity
        console.log('\n🔄 STEP 3: Checking Recent Activity...');
        console.log('-'.repeat(40));
        
        const todayActivity = await conn.query(`
            SELECT COUNT() 
            FROM Docebo_Learning_Plan_Course_Enrollment__c 
            WHERE CreatedDate = TODAY
        `);
        
        console.log(`Records created today: ${todayActivity.totalSize.toLocaleString()}`);
        
        const lastHourActivity = await conn.query(`
            SELECT COUNT() 
            FROM Docebo_Learning_Plan_Course_Enrollment__c 
            WHERE CreatedDate >= ${new Date(Date.now() - 60 * 60 * 1000).toISOString()}
        `);
        
        console.log(`Records created in last hour: ${lastHourActivity.totalSize.toLocaleString()}`);

        // Step 4: Check if our sync script is creating the wrong object
        console.log('\n🔍 STEP 4: Checking for Related Objects...');
        console.log('-'.repeat(40));
        
        // Check if there's a different object that might be getting created
        try {
            const lpEnrollmentCount = await conn.query("SELECT COUNT() FROM Docebo_Learning_Plan_Enrollment__c");
            console.log(`📊 Learning Plan Enrollments: ${lpEnrollmentCount.totalSize.toLocaleString()}`);
        } catch (e) {
            console.log(`❌ Could not query Learning Plan Enrollments: ${e.message}`);
        }
        
        try {
            const courseEnrollmentCount = await conn.query("SELECT COUNT() FROM Docebo_Course_Enrollment__c");
            console.log(`📊 Course Enrollments: ${courseEnrollmentCount.totalSize.toLocaleString()}`);
        } catch (e) {
            console.log(`❌ Could not query Course Enrollments: ${e.message}`);
        }

        // Step 5: Analysis
        console.log('\n🎯 STEP 5: Analysis...');
        console.log('-'.repeat(40));
        
        if (countResult.totalSize <= 2) {
            console.log(`⚠️ ISSUE IDENTIFIED: Still only ${countResult.totalSize} Learning Plan Course Enrollment records`);
            console.log(`🔍 This suggests our sync script may be:`);
            console.log(`   1. Creating records in a different object`);
            console.log(`   2. Using incorrect field names and failing silently`);
            console.log(`   3. Not running or encountering errors`);
            
            if (lastHourActivity.totalSize === 0) {
                console.log(`⚠️ No recent activity - sync process may have stopped or failed`);
            }
        } else {
            console.log(`✅ Found ${countResult.totalSize.toLocaleString()} records - sync appears to be working`);
        }

        return {
            success: true,
            totalRecords: countResult.totalSize,
            todayRecords: todayActivity.totalSize,
            lastHourRecords: lastHourActivity.totalSize
        };

    } catch (error) {
        console.error('💥 Error checking learning plan course enrollment:', error);
        return {
            success: false,
            error: error.message
        };
    }
}

console.log('🔄 Starting Correct Learning Plan Course Enrollment Check...');
checkCorrectLearningPlanCourseEnrollment()
    .then((result) => {
        console.log('\n📋 LEARNING PLAN COURSE ENROLLMENT STATUS:');
        console.log('=' .repeat(50));
        
        if (result.success) {
            console.log(`📊 Total Records: ${result.totalRecords.toLocaleString()}`);
            console.log(`📅 Created Today: ${result.todayRecords.toLocaleString()}`);
            console.log(`🕐 Created Last Hour: ${result.lastHourRecords.toLocaleString()}`);
            
            if (result.totalRecords <= 2) {
                console.log(`\n❌ PROBLEM: Still only ${result.totalRecords} records`);
                console.log(`🔍 The sync script needs to be fixed to use correct field names`);
                console.log(`💡 Need to update the sync script with proper field mapping`);
            } else {
                console.log(`\n✅ SUCCESS: ${result.totalRecords.toLocaleString()} records found`);
            }
        } else {
            console.log(`❌ Check failed: ${result.error}`);
        }
        
        console.log('\n✅ Learning Plan Course Enrollment check completed');
        process.exit(0);
    })
    .catch(err => {
        console.error('💥 Learning Plan Course Enrollment check failed:', err);
        process.exit(1);
    });
