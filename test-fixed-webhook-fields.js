require('dotenv').config();
const { tidyData } = require('./platform/salesforce/users/createUser');

// Test the fixed webhook field mapping
function testFixedWebhookFields() {
    console.log('🧪 Testing Fixed Webhook Field Mapping...');
    
    // Mock comprehensive Docebo webhook data
    const mockDoceboData = {
        userInfo: {
            user_data: {
                user_id: "12345",
                first_name: "<PERSON>",
                last_name: "<PERSON><PERSON>",
                email: "<EMAIL>",
                username: "john_doe",
                level: "User",
                manager_username: "manager_user",
                email_validation_status: "1",
                valid: "1",
                timezone: "America/New_York",
                language: "en",
                phone: "******-123-4567"
            },
            additional_fields: [
                { id: "8", value: "Senior Manager", enabled: true }, // Job Title
                { id: "9", value: "Operations/Business Management", enabled: true }, // Role Type
                { id: "10", value: "Full-time", enabled: true }, // Employment Type
                { id: "11", value: "English, Spanish", enabled: true }, // Languages
                { id: "12", value: "White", enabled: true }, // Race
                { id: "13", value: "Male", enabled: true }, // Gender
                { id: "14", value: "Example Organization", enabled: true }, // Organization
                { id: "15", value: "Yes", enabled: true }, // Backbone Partner
                { id: "16", value: "Community Partner", enabled: true }, // Partner Type
                { id: "17", value: "2020-01-15", enabled: true }, // Employment Begin Date
                { id: "18", value: "New York", enabled: true }, // State
                { id: "19", value: "New York", enabled: true }, // City
                { id: "20", value: "Education Initiative", enabled: true }, // Initiative
                { id: "21", value: "Regional", enabled: true }, // National/Regional/Local
                { id: "22", value: "New York, NY", enabled: true }, // Organization HQ
                { id: "23", value: "www.example.org", enabled: true }, // Website
                { id: "24", value: "123 Main St", enabled: true }, // Street Address
                { id: "25", value: "10001", enabled: true }, // Postal Code
                { id: "26", value: "USA", enabled: true }, // Country
                { id: "27", value: "******-987-6543", enabled: true }, // Phone
                { id: "28", value: "******-987-6544", enabled: true }, // Fax
                { id: "29", value: "Dr.", enabled: true }, // Salutation
                { id: "30", value: "Education", enabled: true }, // Industry
                { id: "31", value: "1000000", enabled: true }, // Annual Revenue
                { id: "32", value: "500", enabled: true }, // Number of Employees
                { id: "33", value: "Hot", enabled: true }, // Rating
                { id: "34", value: "Director of Operations", enabled: true } // Position Role
            ],
            branches: [
                {
                    name: "Main Branch",
                    path: "/main/branch",
                    codes: "12345"
                }
            ],
            fired_at: "2024-01-15 09:00:00",
            expiration_date: "2025-12-31 23:59:59"
        },
        userListedInfo: {
            last_access_date: "2024-02-07T14:30:00Z"
        }
    };

    console.log('\n📋 TESTING ENHANCED FIELD MAPPING:');
    console.log('=' .repeat(70));

    // Test the enhanced tidyData function
    const processedData = tidyData(mockDoceboData.userInfo, mockDoceboData.userListedInfo);

    // Your complete field list that should now be mapped
    const requiredFields = [
        { field: 'Company', salesforceField: 'Organization_Name__c', expected: 'Example Organization' },
        { field: 'Created_by_Docebo_API__c', salesforceField: 'Created_by_Docebo_API__c', expected: true },
        { field: 'Gender Identity', salesforceField: 'Gender_Identity__c', expected: 'Male' },
        { field: 'Role_Type__c', salesforceField: 'Role_Type__c', expected: 'Operations/Business Management' },
        { field: 'email', salesforceField: 'Email__c', expected: '<EMAIL>' },
        { field: 'EmploymentType', salesforceField: 'Employment_Type__c', expected: 'Full-time' },
        { field: 'title', salesforceField: 'Job_Title__c', expected: 'Senior Manager' },
        { field: 'Race__c', salesforceField: 'Race_Identity__c', expected: 'White' },
        { field: 'FirstName', salesforceField: 'First_Name__c', expected: 'John' },
        { field: 'LastName', salesforceField: 'Last_Name__c', expected: 'Doe' },
        { field: 'website', salesforceField: 'Website__c', expected: 'www.example.org' },
        { field: 'fax', salesforceField: 'Fax__c', expected: '******-987-6544' },
        { field: 'salutation', salesforceField: 'Salutation__c', expected: 'Dr.' },
        { field: 'phone', salesforceField: 'Phone__c', expected: '******-987-6643' }, // Should use additional_fields[27]
        { field: 'languages__c', salesforceField: 'Languages__c', expected: 'English, Spanish' },
        { field: 'mailingcity__c', salesforceField: 'MailingCity__c', expected: 'New York' },
        { field: 'mailingcountry__c', salesforceField: 'MailingCountry__c', expected: 'USA' },
        { field: 'mailingpostalcode__c', salesforceField: 'MailingPostalCode__c', expected: '10001' },
        { field: 'mailingstate__c', salesforceField: 'MailingState__c', expected: 'New York' },
        { field: 'mailingstreet__c', salesforceField: 'MailingStreet__c', expected: '123 Main St' },
        { field: 'position_role__c', salesforceField: 'Position_Role__c', expected: 'Director of Operations' },
        { field: 'AnnualRevenue', salesforceField: 'AnnualRevenue__c', expected: 1000000 },
        { field: 'Industry', salesforceField: 'Industry__c', expected: 'Education' },
        { field: 'NumberOfEmployees', salesforceField: 'NumberOfEmployees__c', expected: 500 },
        { field: 'rating', salesforceField: 'Rating__c', expected: 'Hot' },
        { field: 'TimeZone', salesforceField: 'TimeZone__c', expected: 'America/New_York' }
    ];

    console.log('\n✅ FIELD MAPPING RESULTS:');
    console.log('-'.repeat(80));

    let mappedCount = 0;
    let correctCount = 0;
    let missingCount = 0;

    requiredFields.forEach(item => {
        const actualValue = processedData[item.salesforceField];
        const hasValue = actualValue !== "" && actualValue !== 0 && actualValue !== null && actualValue !== undefined;
        
        if (hasValue) {
            mappedCount++;
            if (actualValue === item.expected || (typeof item.expected === 'number' && parseFloat(actualValue) === item.expected)) {
                correctCount++;
                console.log(`✅ ${item.field}: ${actualValue} (CORRECT)`);
            } else {
                console.log(`⚠️  ${item.field}: ${actualValue} (Expected: ${item.expected})`);
            }
        } else {
            missingCount++;
            console.log(`❌ ${item.field}: MISSING (Expected: ${item.expected})`);
        }
    });

    console.log('\n📊 ENHANCED MAPPING SUMMARY:');
    console.log('=' .repeat(60));
    console.log(`📋 Total Required Fields: ${requiredFields.length}`);
    console.log(`✅ Fields with Data: ${mappedCount} (${Math.round(mappedCount/requiredFields.length*100)}%)`);
    console.log(`🎯 Correctly Mapped: ${correctCount} (${Math.round(correctCount/requiredFields.length*100)}%)`);
    console.log(`❌ Still Missing: ${missingCount} (${Math.round(missingCount/requiredFields.length*100)}%)`);

    // Show the complete processed data structure
    console.log('\n📋 COMPLETE PROCESSED DATA STRUCTURE:');
    console.log('=' .repeat(60));
    console.log(JSON.stringify(processedData, null, 2));

    // Test Lead data creation
    console.log('\n🎯 LEAD DATA CREATION TEST:');
    console.log('=' .repeat(50));
    
    const leadData = {
        LastName: processedData.Last_Name__c || "Unknown",
        FirstName: processedData.First_Name__c,
        Email: processedData.Email__c,
        Company: processedData.Organization_Name__c || "-",
        Title: processedData.Job_Title__c || "",
        Website: processedData.Website__c || "",
        Status: "Open - Not Contacted",
        Created_by_Docebo_API__c: true,
        Gender__c: processedData.Gender_Identity__c,
        Role_Type__c: processedData.Role_Type__c,
        Employment_Type__c: processedData.Employment_Type__c || "",
        Race__c: processedData.Race_Identity__c,
        Fax: processedData.Fax__c || "",
        Salutation: processedData.Salutation__c || "",
        Phone: processedData.Phone__c || "",
        Languages__c: processedData.Languages__c || "",
        MailingCity__c: processedData.MailingCity__c || "",
        MailingCountry__c: processedData.MailingCountry__c || "",
        MailingPostalCode__c: processedData.MailingPostalCode__c || "",
        MailingState__c: processedData.MailingState__c || "",
        MailingStreet__c: processedData.MailingStreet__c || "",
        Position_Role__c: processedData.Position_Role__c || "",
        AnnualRevenue: processedData.AnnualRevenue__c || 0,
        Industry: processedData.Industry__c || "",
        LeadSource: "Docebo Platform",
        NumberOfEmployees: processedData.NumberOfEmployees__c || 0,
        Rating: processedData.Rating__c || "",
        Time_Zone__c: processedData.TimeZone__c || ""
    };

    console.log('Lead data that would be created:');
    Object.keys(leadData).forEach(key => {
        const value = leadData[key];
        if (value && value !== "" && value !== 0) {
            console.log(`   ${key}: ${value}`);
        }
    });

    return {
        totalFields: requiredFields.length,
        mappedCount: mappedCount,
        correctCount: correctCount,
        missingCount: missingCount,
        improvementPercentage: Math.round(mappedCount/requiredFields.length*100)
    };
}

// Execute the test
console.log('🔄 Starting fixed webhook fields test...');
const result = testFixedWebhookFields();

console.log('\n🎉 WEBHOOK FIELD MAPPING IMPROVEMENT:');
console.log('=' .repeat(60));
console.log(`📈 Field Coverage: ${result.improvementPercentage}% (was 32%)`);
console.log(`📊 Improvement: +${result.improvementPercentage - 32}% more fields mapped`);

if (result.improvementPercentage >= 80) {
    console.log('🎉 EXCELLENT! Webhook field mapping is now comprehensive!');
} else if (result.improvementPercentage >= 60) {
    console.log('✅ GOOD! Significant improvement in field mapping');
} else {
    console.log('⚠️ More work needed to improve field mapping');
}

console.log('\n✅ Fixed webhook fields test completed');
process.exit(0);
