# Lead Creation Fixes Summary

## 🎯 Problem Identified
The webhook was failing to create leads in Salesforce due to several field mapping and validation issues:

### Primary Error from Logs:
```
"bad value for restricted picklist field: Prefer Not To Say"
"errorCode": "INVALID_OR_NULL_FOR_RESTRICTED_PICKLIST"
"fields": ["Gender__c"]
```

## 🔍 Root Cause Analysis

### 1. **Gender Field Mapping Issue**
- **Problem**: The code was mapping gender values to "Prefer Not To Say" but Salesforce expects "Prefer not to respond"
- **Valid Salesforce Values**: "Man", "Woman", "Non-Binary or other gender identity", "Prefer not to respond"
- **Invalid Values Used**: "Male", "Female", "Prefer Not To Say"

### 2. **Non-existent Fields on Lead Object**
- **Problem**: <PERSON> was trying to set fields that don't exist on the Salesforce Lead object
- **Missing Fields**: `Annual_Revenue__c`, `Industry__c`, `NumberOfEmployees__c`, `Rating__c`, `TimeZone`

### 3. **Inconsistent Field Mapping Functions**
- **Problem**: Different mapping functions in different files had inconsistent mappings

## ✅ Fixes Implemented

### 1. **Updated Gender Mapping Function**

**Before:**
```javascript
function mapGenderToValidValue(doceboGender) {
    const genderMapping = {
        "Male": "Male",
        "Female": "Female",
        "Non-binary": "Non-Binary",
        "Non-Binary": "Non-Binary",
        "Prefer not to say": "Prefer Not To Say",
        "Prefer Not To Say": "Prefer Not To Say"
    };
    return genderMapping[doceboGender] || "Prefer Not To Say";
}
```

**After:**
```javascript
function mapGenderToValidValue(doceboGender) {
    const genderMapping = {
        "Male": "Man",
        "Female": "Woman",
        "Man": "Man", 
        "Woman": "Woman",
        "Non-binary": "Non-Binary or other gender identity",
        "Non-Binary": "Non-Binary or other gender identity",
        "Prefer not to say": "Prefer not to respond",
        "Prefer Not To Say": "Prefer not to respond"
    };
    return genderMapping[doceboGender] || "Prefer not to respond";
}
```

### 2. **Removed Non-existent Fields from Lead Creation**

**Before:**
```javascript
const leadData = {
    // ... other fields
    Annual_Revenue__c: tmpUserInfo.Annual_Revenue__c || 0,
    Industry__c: tmpUserInfo.Industry__c || "",
    NumberOfEmployees__c: tmpUserInfo.NumberOfEmployees__c || 0,
    Rating__c: tmpUserInfo.Rating__c || "",
    TimeZone: userInfo.user_data.timezone || "",
    // ... other fields
};
```

**After:**
```javascript
const leadData = {
    // ... other fields
    LeadSource: "Docebo Platform",
    // REMOVED: These fields don't exist on Lead object
    // Annual_Revenue__c: tmpUserInfo.Annual_Revenue__c || 0,
    // Industry__c: tmpUserInfo.Industry__c || "",
    // NumberOfEmployees__c: tmpUserInfo.NumberOfEmployees__c || 0,
    // Rating__c: tmpUserInfo.Rating__c || "",
    // TimeZone: userInfo.user_data.timezone || "",
    // ... other fields
};
```

### 3. **Updated Files Modified**
- `platform/salesforce/users/createUser.js` - Main lead creation logic
- `platform/salesforce/users/historicalDataUpdate.js` - Historical data processing
- Both files now use consistent, correct field mappings

## 🧪 Testing Results

### Test Script: `test-fixed-lead-creation.js`
- ✅ **Gender mapping**: "Prefer not to say" → "Prefer not to respond" 
- ✅ **Lead creation**: Successfully creates lead without field errors
- ✅ **Field validation**: All fields now use valid Salesforce values
- ✅ **No more picklist errors**: Eliminated "INVALID_OR_NULL_FOR_RESTRICTED_PICKLIST" errors

### Sample Successful Output:
```
✅ Lead created: 00QO400000XYGtmMAH
   Name: Fixed Test
   Company: Test Organization
   Status: Open - Not Contacted
   Title: Test Manager
   Gender: Prefer not to respond  ← FIXED!
   Role Type: Communications
   Race: Other
   Lead Source: Docebo Platform
   Created by Docebo API: true
```

## 📋 Valid Salesforce Picklist Values Confirmed

### Gender__c Field:
- "Man" ✅
- "Woman" ✅  
- "Non-Binary or other gender identity" ✅
- "Prefer not to respond" ✅

### Role_Type__c Field:
- "Administrative" ✅
- "Board of Directors" ✅
- "Communications" ✅
- "Community Engagement/Organizing" ✅
- "Data and Research" ✅
- "Executive Director" ✅
- "Facilitator" ✅
- "Fundraising/Development" ✅
- "Leadership Table Member" ✅
- "Operations/Business Management" ✅
- "Other" ✅
- "Partnership Table Member" ✅
- "Policy/Government" ✅
- "Programs" ✅
- "Youth/Families/Community" ✅

### Race__c Field:
- "American Indian or Alaskan Native" ✅
- "Asian" ✅
- "Black or African American" ✅
- "Hispanic or Latine" ✅
- "Middle Eastern or North African" ✅
- "Multi-Racial" ✅
- "Native Hawaiian or Other Pacific Islander" ✅
- "Other" ✅
- "Prefer not to respond" ✅
- "White" ✅

## 🚀 Impact
- **Webhook reliability**: No more lead creation failures due to field validation errors
- **Data integrity**: Proper mapping ensures consistent data in Salesforce
- **Error reduction**: Eliminated the primary cause of webhook failures
- **Future-proof**: Mapping functions now use correct Salesforce field values

## 📝 Next Steps
1. Monitor webhook logs to ensure no more "INVALID_OR_NULL_FOR_RESTRICTED_PICKLIST" errors
2. Consider adding validation tests for other picklist fields if needed
3. Update any documentation that references the old field mappings
