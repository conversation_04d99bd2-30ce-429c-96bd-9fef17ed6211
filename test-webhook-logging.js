require('dotenv').config();
const axios = require('axios');

// Test webhook with enhanced logging
async function testWebhookLogging() {
    console.log('🧪 Testing Webhook with Enhanced Logging...');
    
    // Simulate a user creation webhook payload
    const webhookPayload = {
        event: "user.created",
        fired_by_batch_action: false,
        message_id: `test-webhook-${Date.now()}`,
        payload: {
            fired_at: new Date().toISOString(),
            user_id: 18851, // Use the same user we debugged
            username: "<EMAIL>",
            first_name: "Fname",
            last_name: "lName",
            email: "<EMAIL>",
            expiration_date: null
        }
    };

    console.log('\n📤 SENDING WEBHOOK PAYLOAD:');
    console.log('=' .repeat(60));
    console.log(JSON.stringify(webhookPayload, null, 2));

    try {
        console.log('\n🔄 Sending webhook to local server...');
        
        const response = await axios.post('http://localhost:3000/docebo/user-created', webhookPayload, {
            headers: {
                'Content-Type': 'application/json'
            },
            timeout: 5000
        });

        console.log('\n✅ WEBHOOK RESPONSE:');
        console.log('=' .repeat(50));
        console.log(`Status: ${response.status}`);
        console.log(`Response:`, response.data);

        console.log('\n📋 WHAT TO LOOK FOR IN LOGS:');
        console.log('=' .repeat(60));
        console.log('1. 📥 SINGLE USER WEBHOOK DATA - Raw Docebo API data');
        console.log('2. 🔧 PROCESSED USER DATA - Processed by tidyData function');
        console.log('3. 📊 KEY FIELD MAPPINGS - Individual field values');
        console.log('4. 🎯 CREATING LEAD - Lead data being created');
        console.log('5. 📊 KEY LEAD FIELDS - Lead field values');
        console.log('6. ✅ Lead created successfully - Final result');

        console.log('\n💡 CHECK THE SERVER LOGS NOW!');
        console.log('Look for the enhanced logging output to see:');
        console.log('- Raw webhook data received');
        console.log('- Processed field mappings');
        console.log('- Lead creation data');
        console.log('- Docebo_Users__c creation data');

        return true;

    } catch (error) {
        if (error.code === 'ECONNREFUSED') {
            console.log('\n⚠️ SERVER NOT RUNNING');
            console.log('Please start the server first with: npm start');
            console.log('Then run this test again to see the enhanced logging');
        } else {
            console.error('\n❌ WEBHOOK TEST ERROR:', error.message);
        }
        return false;
    }
}

// Test learning plan enrollment webhook too
async function testLearningPlanWebhookLogging() {
    console.log('\n🧪 Testing Learning Plan Webhook with Enhanced Logging...');
    
    const learningPlanPayload = {
        event: "learningplan.enrollment.created",
        fired_by_batch_action: false,
        message_id: `test-lp-webhook-${Date.now()}`,
        payload: {
            fired_at: new Date().toISOString(),
            user_id: 18851,
            username: "<EMAIL>",
            learning_plan_id: 9,
            learning_plan_name: "Introduction to Place-Based Partnerships",
            learning_plan_code: "EnrollmentRule",
            enrollment_date: new Date().toISOString(),
            subscribed_by_id: 0,
            enrollment_date_begin_validity: null,
            enrollment_date_end_validity: null
        }
    };

    console.log('\n📤 SENDING LEARNING PLAN WEBHOOK PAYLOAD:');
    console.log('=' .repeat(60));
    console.log(JSON.stringify(learningPlanPayload, null, 2));

    try {
        const response = await axios.post('http://localhost:3000/docebo/learning-plan-enrollment-created', learningPlanPayload, {
            headers: {
                'Content-Type': 'application/json'
            },
            timeout: 5000
        });

        console.log('\n✅ LEARNING PLAN WEBHOOK RESPONSE:');
        console.log('=' .repeat(50));
        console.log(`Status: ${response.status}`);
        console.log(`Response:`, response.data);

        return true;

    } catch (error) {
        if (error.code === 'ECONNREFUSED') {
            console.log('\n⚠️ SERVER NOT RUNNING');
        } else {
            console.error('\n❌ LEARNING PLAN WEBHOOK ERROR:', error.message);
        }
        return false;
    }
}

// Execute the tests
async function runWebhookLoggingTests() {
    console.log('🚀 Starting Webhook Logging Tests...');
    console.log('=' .repeat(70));

    console.log('\n📋 ENHANCED LOGGING FEATURES ADDED:');
    console.log('✅ Raw webhook payload logging');
    console.log('✅ Raw Docebo API response logging');
    console.log('✅ Processed field mapping logging');
    console.log('✅ Lead creation data logging');
    console.log('✅ Key field value verification logging');

    // Test user creation webhook
    const userWebhookSuccess = await testWebhookLogging();
    
    if (userWebhookSuccess) {
        // Wait a bit then test learning plan webhook
        console.log('\n⏳ Waiting 3 seconds before testing learning plan webhook...');
        await new Promise(resolve => setTimeout(resolve, 3000));
        
        await testLearningPlanWebhookLogging();
    }

    console.log('\n🎯 WEBHOOK LOGGING TEST SUMMARY:');
    console.log('=' .repeat(60));
    console.log('✅ Enhanced logging has been added to:');
    console.log('   - User creation webhooks');
    console.log('   - Learning plan enrollment webhooks');
    console.log('   - Course enrollment webhooks');
    console.log('   - Field mapping processing');
    console.log('   - Lead creation');
    console.log('   - Docebo_Users__c creation');

    console.log('\n💡 NEXT STEPS:');
    console.log('1. Start the server: npm start');
    console.log('2. Run this test: node test-webhook-logging.js');
    console.log('3. Check server logs for detailed webhook data');
    console.log('4. Verify field mappings are working correctly');

    console.log('\n✅ Webhook logging enhancement completed!');
}

// Run the tests
runWebhookLoggingTests()
    .then(() => {
        console.log('\n🎉 Webhook logging tests completed!');
        process.exit(0);
    })
    .catch(err => {
        console.error('\n💥 Test failed:', err);
        process.exit(1);
    });
