require('dotenv').config();
const getConnection = require('./platform/salesforce/common/getConnection');

async function testContactFieldCleanup() {
    try {
        console.log('🔧 Testing Contact Field Cleanup');
        console.log('=' .repeat(70));
        console.log('🎯 TESTING FIX FOR:');
        console.log('   "No such column \'First_Name__c\' on sobject of type Contact"');
        console.log('   Removed all non-existent Contact fields');
        console.log('   Using ONLY approved Contact fields from your list');
        console.log('=' .repeat(70));
        
        const conn = await getConnection();
        if (!conn || !conn.accessToken) {
            console.error("❌ Invalid Salesforce connection");
            return;
        }

        // Step 1: Verify Contact object fields
        console.log('\n🔍 Step 1: Verifying Contact object fields...');
        
        const contactDescription = await conn.sobject("Contact").describe();
        
        // Check for the problematic fields that were causing errors
        const problematicFields = ['First_Name__c', 'Last_Name__c', 'Email__c', 'Job_Title__c'];
        const approvedFields = [
            'FirstName', 'LastName', 'Email', 'Title', // Standard fields
            'FTE__c', 'AccountId', 'Active_Portal_User__c', 'Annual_Revenue__c', 
            'Company__c', 'Contact_Type__c', 'Created_by_Docebo_API__c', 
            'Description', 'Employment_Type__c', 'Fax', 'Gateway__c', 
            'GenderIdentity', 'Inactive_Contact__c', 'Industry__c', 
            'Initiative__c', 'Languages__c', 'OwnerId', 'LeadSource',
            'Legacy_ID__c', 'mailingcity__c', 'mailingcountry__c', 
            'mailingpostalcode__c', 'mailingstate__c', 'mailingstreet__c',
            'Network_Partnership_Association__c', 'No_Longer_Leadership__c',
            'No_Longer_Staff__c', 'NumberOfEmployees__c', 
            'Number_of_years_in_the_partnership__c', 'Organization_Employer__c',
            'Phone', 'Position_Role__c', 'Race__c', 'Rating__c', 'Role_Type__c',
            'Time_Zone__c', 'Type__c', 'Website__c'
        ];
        
        console.log('\n📋 Checking problematic fields (should NOT exist):');
        for (const fieldName of problematicFields) {
            const fieldExists = contactDescription.fields.some(f => f.name === fieldName);
            console.log(`   ${fieldName}: ${fieldExists ? '❌ EXISTS (PROBLEM)' : '✅ DOES NOT EXIST (GOOD)'}`);
        }
        
        console.log('\n📋 Checking approved fields (should exist):');
        let missingApprovedFields = [];
        for (const fieldName of approvedFields) {
            const fieldExists = contactDescription.fields.some(f => f.name === fieldName);
            if (fieldExists) {
                console.log(`   ${fieldName}: ✅ EXISTS`);
            } else {
                console.log(`   ${fieldName}: ❌ MISSING`);
                missingApprovedFields.push(fieldName);
            }
        }
        
        if (missingApprovedFields.length > 0) {
            console.log(`\n⚠️ Missing approved fields: ${missingApprovedFields.join(', ')}`);
            console.log('   These fields may need to be created in Salesforce or removed from code');
        }

        // Step 2: Test Contact creation with approved fields only
        console.log('\n🧪 Step 2: Testing Contact creation with approved fields...');
        
        const testEmail = '<EMAIL>';
        
        // Clean up any existing test data
        const existingContacts = await conn.sobject("Contact")
            .find({ Email: testEmail })
            .execute();
        
        for (const contact of existingContacts) {
            await conn.sobject("Contact").delete(contact.Id);
            console.log(`   🗑️ Deleted existing Contact: ${contact.Id}`);
        }
        
        // Create test Account first
        const accountData = {
            Name: "Test Contact Field Cleanup Company",
            Type: "Customer"
        };
        
        const accountResult = await conn.sobject("Account").create(accountData);
        if (!accountResult.success) {
            console.error('❌ Failed to create test Account:', accountResult.errors);
            return;
        }
        
        console.log(`✅ Created test Account: ${accountResult.id}`);
        
        // Create Contact with ONLY approved fields
        const contactData = {
            // Standard fields
            FirstName: "Test",
            LastName: "Contact Field Cleanup",
            Email: testEmail,
            Title: "Test Manager",
            AccountId: accountResult.id,
            
            // Approved custom fields only
            Active_Portal_User__c: true,
            Created_by_Docebo_API__c: true,
            LeadSource: "Docebo Platform",
            Annual_Revenue__c: 100000,
            Company__c: "Test Contact Field Cleanup Company",
            Contact_Type__c: "Other", // Valid value
            Employment_Type__c: "Full-Time", // Valid value
            Gateway__c: "Docebo API", // Not a picklist field
            GenderIdentity: "Woman", // Valid value
            Industry__c: "Not For Profit", // FIX: Use valid value from approved list
            Initiative__c: "Test Initiative",
            Languages__c: "English",
            Legacy_ID__c: "TEST123",
            Network_Partnership_Association__c: "Test Network",
            NumberOfEmployees__c: 50,
            Position_Role__c: "Administrative", // FIX: Use valid Role_Type value
            Race__c: "Asian", // Valid value
            Rating__c: "Hot", // Valid value
            Role_Type__c: "Administrative", // FIX: Use valid value from approved list
            Time_Zone__c: "America/New_York",
            Type__c: "Backbone Staff", // FIX: Use valid picklist value
            Website__c: "https://testcontactfieldcleanup.com"
        };
        
        console.log('🔄 Creating Contact with approved fields only...');
        
        const contactResult = await conn.sobject("Contact").create(contactData);
        
        if (contactResult.success) {
            console.log('✅ Contact creation successful!');
            console.log(`   Contact ID: ${contactResult.id}`);
            
            // Verify the created Contact
            const createdContact = await conn.sobject("Contact")
                .findOne({ Id: contactResult.id });
            
            console.log('\n📊 CREATED CONTACT VERIFICATION:');
            console.log(`   Name: ${createdContact.FirstName} ${createdContact.LastName}`);
            console.log(`   Email: ${createdContact.Email}`);
            console.log(`   Title: ${createdContact.Title}`);
            console.log(`   Active Portal User: ${createdContact.Active_Portal_User__c}`);
            console.log(`   Created by Docebo API: ${createdContact.Created_by_Docebo_API__c}`);
            console.log(`   Lead Source: ${createdContact.LeadSource}`);
            console.log(`   Company: ${createdContact.Company__c}`);
            console.log(`   Gateway: ${createdContact.Gateway__c}`);
            
            // Test Contact update with approved fields
            console.log('\n🔄 Testing Contact update with approved fields...');
            
            const updateData = {
                Id: contactResult.id,
                FirstName: "Updated",
                LastName: "Contact Field Cleanup",
                Title: "Updated Manager",
                Annual_Revenue__c: 150000,
                Company__c: "Updated Test Company",
                Gateway__c: "Updated Docebo API",
                Time_Zone__c: "America/Los_Angeles"
            };
            
            const updateResult = await conn.sobject("Contact").update(updateData);
            
            if (updateResult.success) {
                console.log('✅ Contact update successful!');
                
                // Verify the update
                const updatedContact = await conn.sobject("Contact")
                    .findOne({ Id: contactResult.id });
                
                console.log('\n📊 UPDATED CONTACT VERIFICATION:');
                console.log(`   Name: ${updatedContact.FirstName} ${updatedContact.LastName}`);
                console.log(`   Title: ${updatedContact.Title}`);
                console.log(`   Annual Revenue: ${updatedContact.Annual_Revenue__c}`);
                console.log(`   Company: ${updatedContact.Company__c}`);
                console.log(`   Gateway: ${updatedContact.Gateway__c}`);
                console.log(`   Time Zone: ${updatedContact.Time_Zone__c}`);
                
            } else {
                console.log('❌ Contact update failed:', updateResult.errors);
            }
            
            // Clean up test data
            console.log('\n🗑️ Cleaning up test data...');
            
            await conn.sobject("Contact").delete(contactResult.id);
            console.log('   ✅ Test Contact deleted');
            
        } else {
            console.log('❌ Contact creation failed:', contactResult.errors);
            
            // Analyze the errors
            if (contactResult.errors) {
                console.log('\n🔍 ERROR ANALYSIS:');
                for (const error of contactResult.errors) {
                    console.log(`   Error: ${error.message}`);
                    if (error.fields) {
                        console.log(`   Fields: ${error.fields.join(', ')}`);
                    }
                    
                    // Check if it's a field-related error
                    if (error.message.includes('No such column') || error.message.includes('INVALID_FIELD')) {
                        console.log('   🚨 FIELD ERROR DETECTED - field may not exist or have wrong permissions');
                    }
                }
            }
        }
        
        await conn.sobject("Account").delete(accountResult.id);
        console.log('   ✅ Test Account deleted');

        // Step 3: Summary
        console.log('\n📊 CONTACT FIELD CLEANUP SUMMARY:');
        console.log('=' .repeat(70));
        
        const hasProblematicFields = problematicFields.some(field => 
            contactDescription.fields.some(f => f.name === field)
        );
        
        console.log('🔧 CLEANUP PERFORMED:');
        console.log('   ✅ Removed First_Name__c, Last_Name__c, Email__c, Job_Title__c');
        console.log('   ✅ Using only standard fields: FirstName, LastName, Email, Title');
        console.log('   ✅ Using only approved custom fields from your list');
        console.log('   ✅ Updated all Contact creation/update code');
        
        console.log('\n🎯 TEST RESULTS:');
        if (contactResult && contactResult.success) {
            console.log('   ✅ CONTACT FIELD CLEANUP SUCCESSFUL!');
            console.log('   ✅ No "No such column" errors');
            console.log('   ✅ Contact creation working with approved fields');
            console.log('   ✅ Contact update working with approved fields');
        } else {
            console.log('   ❌ CONTACT FIELD ISSUES REMAIN');
            console.log('   ❌ Further field investigation needed');
        }
        
        if (missingApprovedFields.length > 0) {
            console.log('\n⚠️ MISSING FIELDS:');
            console.log(`   ${missingApprovedFields.length} approved fields are missing from Contact object`);
            console.log('   These may need to be created in Salesforce or removed from code');
        }
        
        console.log('\n🚀 PRODUCTION BENEFITS:');
        console.log('   • No more "No such column" errors for Contact fields');
        console.log('   • Clean Contact creation and updates');
        console.log('   • Consistent field usage across webhook and batch');
        console.log('   • Better error handling and debugging');

        return {
            success: true,
            contactCreated: contactResult && contactResult.success,
            missingFields: missingApprovedFields.length,
            hasProblematicFields: hasProblematicFields
        };

    } catch (error) {
        console.error('💥 Error in Contact field cleanup test:', error);
        return {
            success: false,
            error: error.message
        };
    }
}

// Execute the test
console.log('🔄 Starting Contact field cleanup test...');
testContactFieldCleanup()
    .then((result) => {
        console.log('\n✅ Contact field cleanup test completed');
        if (result.success) {
            if (result.contactCreated) {
                console.log('🎉 CONTACT FIELD CLEANUP SUCCESSFUL!');
                console.log('🚀 No more field mapping errors!');
            } else {
                console.log('⚠️ Contact field issues may remain');
            }
            if (result.missingFields > 0) {
                console.log(`⚠️ ${result.missingFields} approved fields are missing from Salesforce`);
            }
        } else {
            console.log('❌ Test failed. Check the logs above.');
        }
        process.exit(0);
    })
    .catch(err => {
        console.error('💥 Test failed:', err);
        process.exit(1);
    });
