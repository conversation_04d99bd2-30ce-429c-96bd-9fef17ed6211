require('dotenv').config();
const getConnection = require('./platform/salesforce/common/getConnection');

// Test the missing fields that we identified earlier
async function testMissingFields() {
    try {
        console.log('🧪 Testing the missing fields that were identified earlier...');
        
        const conn = await getConnection();
        if (!conn || !conn.accessToken) {
            console.error("❌ Invalid Salesforce connection");
            return;
        }

        // Clean up existing test records
        console.log('\n🧹 Cleaning up existing test records...');
        const testEmail = "<EMAIL>";
        
        try {
            const existingLeads = await conn.sobject("Lead")
                .find({ Email: testEmail });
            
            for (const lead of existingLeads) {
                await conn.sobject("Lead").delete(lead.Id);
                console.log(`   Deleted existing Lead: ${lead.Id}`);
            }
        } catch (cleanupError) {
            console.log('   No existing records to clean up');
        }

        // Create account first
        console.log('\n🏢 Creating account...');
        const accountData = {
            Name: "Test Missing Fields Account",
            Website: "https://www.example.com",
        };
        const accountResult = await conn.sobject("Account").create(accountData);
        
        if (accountResult.success) {
            console.log(`✅ Account created: ${accountResult.id}`);
            
            // Test the missing fields from our earlier analysis
            console.log('\n🔍 Testing missing fields...');
            
            const missingFieldsToTest = {
                // Standard fields that might be missing
                Fax: "(*************",
                
                // Custom fields that were identified as missing
                Annual_Revenue__c: 5000000,
                Industry__c: "Healthcare",
                NumberOfEmployees__c: 15000,
                Rating__c: "Hot",
                Time_Zone__c: "America/New_York",
                accountid: String(accountResult.id)
            };

            // Base lead data (fields we know work)
            const baseLeadData = {
                Company: "Test Missing Fields Company",
                Email: testEmail,
                Title: "Test Director",
                FirstName: "Test",
                LastName: "Missing Fields",
                Website: "https://www.example.com",
                Status: "Open - Not Contacted",
                Description: "Testing missing fields functionality",
                Salutation: "Mr.",
                Phone: "(*************",
                LeadSource: "Docebo Platform",
                OwnerId: "005O400000BxnnxIAB",
                Created_by_Docebo_API__c: true,
                Gender__c: "Male",
                Role_Type__c: "Operations/Business Management",
                Employment_Type__c: "Full-time",
                Race__c: "White"
            };

            // Test each missing field individually
            console.log('\n🔬 Testing each missing field individually:');
            console.log('=' .repeat(60));

            const successfulFields = [];
            const failedFields = [];

            for (const [fieldName, fieldValue] of Object.entries(missingFieldsToTest)) {
                console.log(`\n🧪 Testing field: ${fieldName}`);
                
                // Create test lead data with base + current field
                const testLeadData = {
                    ...baseLeadData,
                    Email: `${fieldName.toLowerCase()}.<EMAIL>`,
                    [fieldName]: fieldValue
                };

                try {
                    const testResult = await conn.sobject("Lead").create(testLeadData);
                    
                    if (testResult.success) {
                        console.log(`   ✅ ${fieldName}: SUCCESS - Field exists and accepts value`);
                        successfulFields.push({ field: fieldName, value: fieldValue, leadId: testResult.id });
                        
                        // Clean up the test lead
                        await conn.sobject("Lead").delete(testResult.id);
                    } else {
                        console.log(`   ❌ ${fieldName}: FAILED - ${testResult.errors?.[0]?.message || 'Unknown error'}`);
                        failedFields.push({ field: fieldName, error: testResult.errors?.[0]?.message || 'Unknown error' });
                    }
                } catch (error) {
                    console.log(`   ❌ ${fieldName}: ERROR - ${error.message}`);
                    failedFields.push({ field: fieldName, error: error.message });
                }
            }

            // Summary
            console.log('\n📊 MISSING FIELDS TEST RESULTS:');
            console.log('=' .repeat(50));
            
            if (successfulFields.length > 0) {
                console.log(`\n✅ SUCCESSFUL FIELDS (${successfulFields.length}):`);
                successfulFields.forEach((item, index) => {
                    console.log(`   ${index + 1}. ${item.field} - Value: ${item.value}`);
                });
            }
            
            if (failedFields.length > 0) {
                console.log(`\n❌ FAILED FIELDS (${failedFields.length}):`);
                failedFields.forEach((item, index) => {
                    console.log(`   ${index + 1}. ${item.field} - Error: ${item.error}`);
                });
                
                console.log('\n💡 ANALYSIS OF FAILED FIELDS:');
                console.log('-'.repeat(40));
                
                failedFields.forEach(item => {
                    if (item.error.includes('No such column')) {
                        console.log(`   ${item.field}: Field does not exist - needs to be created`);
                    } else if (item.error.includes('INVALID_OR_NULL_FOR_RESTRICTED_PICKLIST')) {
                        console.log(`   ${item.field}: Invalid picklist value - check valid values`);
                    } else if (item.error.includes('FIELD_CUSTOM_VALIDATION_EXCEPTION')) {
                        console.log(`   ${item.field}: Custom validation rule failed`);
                    } else {
                        console.log(`   ${item.field}: ${item.error}`);
                    }
                });
            }

            // Now create a comprehensive lead with all successful fields
            if (successfulFields.length > 0) {
                console.log('\n🚀 Creating comprehensive lead with all working fields...');
                
                const comprehensiveLeadData = { ...baseLeadData };
                
                // Add all successful fields
                successfulFields.forEach(item => {
                    comprehensiveLeadData[item.field] = item.value;
                });
                
                // Update email for final test
                comprehensiveLeadData.Email = "<EMAIL>";
                comprehensiveLeadData.LastName = "Comprehensive Test";
                
                try {
                    const finalResult = await conn.sobject("Lead").create(comprehensiveLeadData);
                    
                    if (finalResult.success) {
                        console.log(`✅ Comprehensive lead created: ${finalResult.id}`);
                        console.log(`🔗 Lead URL: https://strivetogether--full.sandbox.my.salesforce.com/${finalResult.id}`);
                        
                        console.log('\n🎯 SUCCESS! Created lead with:');
                        console.log(`   📊 Base fields: ${Object.keys(baseLeadData).length}`);
                        console.log(`   ✅ Additional working fields: ${successfulFields.length}`);
                        console.log(`   📈 Total fields: ${Object.keys(baseLeadData).length + successfulFields.length}`);
                        
                        return finalResult.id;
                    }
                } catch (finalError) {
                    console.error('💥 Error creating comprehensive lead:', finalError.message);
                }
            }

            console.log('\n📋 SUMMARY:');
            console.log(`   ✅ Working fields: ${successfulFields.length}/${Object.keys(missingFieldsToTest).length}`);
            console.log(`   ❌ Missing fields: ${failedFields.length}/${Object.keys(missingFieldsToTest).length}`);
            
            if (failedFields.length > 0) {
                console.log('\n🔧 FIELDS THAT NEED TO BE CREATED:');
                failedFields.forEach((item, index) => {
                    console.log(`   ${index + 1}. ${item.field}`);
                });
            }
        }

    } catch (error) {
        console.error('💥 Error in missing fields test:', error);
    }
}

// Execute the test
console.log('🔄 Starting missing fields test...');
testMissingFields()
    .then((leadId) => {
        if (leadId) {
            console.log(`\n✅ Test completed successfully! Lead ID: ${leadId}`);
            console.log(`🔗 Direct Link: https://strivetogether--full.sandbox.my.salesforce.com/${leadId}`);
        } else {
            console.log('\n⚠️ Test completed - check results above');
        }
        process.exit(0);
    })
    .catch(err => {
        console.error('💥 Test failed:', err);
        process.exit(1);
    });
