require('dotenv').config();
const getConnection = require('./platform/salesforce/common/getConnection');

async function fixSpecificEnrollment() {
    try {
        console.log('🔧 Fixing Specific Enrollment: UE-21-17928');
        console.log('=' .repeat(60));
        
        const conn = await getConnection();
        if (!conn || !conn.accessToken) {
            console.error("❌ Invalid Salesforce connection");
            return;
        }

        const enrollmentId = "UE-21-17928";
        
        // Step 1: Find the enrollment record using the correct field
        console.log(`\n📋 Finding enrollment with Enrollment_ID__c: ${enrollmentId}`);
        console.log('-'.repeat(50));
        
        const enrollment = await conn.sobject("Docebo_CourseEnrollment__c")
            .findOne({ Enrollment_ID__c: enrollmentId });
        
        if (!enrollment) {
            console.log(`❌ Enrollment not found with Enrollment_ID__c: ${enrollmentId}`);
            return;
        }
        
        console.log(`✅ Found enrollment record: ${enrollment.Id}`);
        console.log(`   Enrollment_ID__c: ${enrollment.Enrollment_ID__c}`);
        console.log(`   Course__c: ${enrollment.Course__c || 'MISSING ❌'}`);
        console.log(`   Docebo_User__c: ${enrollment.Docebo_User__c || 'MISSING ❌'}`);
        console.log(`   Status__c: ${enrollment.Status__c}`);
        console.log(`   Enrollment_Date__c: ${enrollment.Enrollment_Date__c}`);
        
        // Step 2: Parse the Enrollment_ID to get course and user IDs
        console.log(`\n🔍 Parsing Enrollment_ID structure...`);
        console.log('-'.repeat(50));
        
        const parts = enrollment.Enrollment_ID__c.split('-');
        console.log(`📋 Enrollment_ID parts: ${JSON.stringify(parts)}`);
        
        if (parts.length !== 3 || parts[0] !== 'UE') {
            console.log(`❌ Unexpected Enrollment_ID format: ${enrollment.Enrollment_ID__c}`);
            console.log(`   Expected format: UE-courseId-userId`);
            return;
        }
        
        const courseId = parts[1]; // "21"
        const userId = parts[2]; // "17928"
        
        console.log(`   Course ID: "${courseId}"`);
        console.log(`   User ID: "${userId}"`);
        
        // Step 3: Find the course
        console.log(`\n🔍 Looking for course with ID: ${courseId}`);
        console.log('-'.repeat(50));
        
        let foundCourse = null;
        
        // Try different course lookup methods
        const courseLookupMethods = [
            { field: 'Course_External_Id__c', value: parseInt(courseId), type: 'int' },
            { field: 'Course_External_Id__c', value: courseId, type: 'string' },
            { field: 'Course_Unique_Id__c', value: courseId, type: 'string' },
            { field: 'Course_Internal_ID__c', value: parseInt(courseId), type: 'int' }
        ];
        
        for (const method of courseLookupMethods) {
            try {
                console.log(`   Trying ${method.field} = ${method.value} (${method.type})`);
                const query = {};
                query[method.field] = method.value;
                
                const course = await conn.sobject("Docebo_Course__c")
                    .findOne(query);
                
                if (course) {
                    console.log(`   ✅ FOUND! Course: ${course.Id} - ${course.Course_Name__c}`);
                    foundCourse = course;
                    break;
                } else {
                    console.log(`   ❌ Not found`);
                }
            } catch (error) {
                console.log(`   ❌ Error: ${error.message}`);
            }
        }
        
        // Step 4: Find the user
        console.log(`\n🔍 Looking for user with ID: ${userId}`);
        console.log('-'.repeat(50));
        
        let foundUser = null;
        
        const userLookupMethods = [
            { field: 'User_Unique_Id__c', value: parseInt(userId), type: 'int' },
            { field: 'User_Unique_Id__c', value: userId, type: 'string' }
        ];
        
        for (const method of userLookupMethods) {
            try {
                console.log(`   Trying ${method.field} = ${method.value} (${method.type})`);
                const query = {};
                query[method.field] = method.value;
                
                const user = await conn.sobject("Docebo_Users__c")
                    .findOne(query);
                
                if (user) {
                    console.log(`   ✅ FOUND! User: ${user.Id} - ${user.First_Name__c} ${user.Last_Name__c}`);
                    foundUser = user;
                    break;
                } else {
                    console.log(`   ❌ Not found`);
                }
            } catch (error) {
                console.log(`   ❌ Error: ${error.message}`);
            }
        }
        
        // Step 5: Fix the associations
        console.log(`\n🔧 Fixing associations...`);
        console.log('-'.repeat(50));
        
        const updates = {};
        let needsUpdate = false;
        
        if (foundCourse && !enrollment.Course__c) {
            updates.Course__c = foundCourse.Id;
            needsUpdate = true;
            console.log(`✅ Will associate with Course: ${foundCourse.Id} (${foundCourse.Course_Name__c})`);
        } else if (!foundCourse) {
            console.log(`❌ Cannot associate course - course not found`);
        } else {
            console.log(`✅ Course already associated: ${enrollment.Course__c}`);
        }
        
        if (foundUser && !enrollment.Docebo_User__c) {
            updates.Docebo_User__c = foundUser.Id;
            needsUpdate = true;
            console.log(`✅ Will associate with User: ${foundUser.Id} (${foundUser.First_Name__c} ${foundUser.Last_Name__c})`);
        } else if (!foundUser) {
            console.log(`❌ Cannot associate user - user not found`);
        } else {
            console.log(`✅ User already associated: ${enrollment.Docebo_User__c}`);
        }
        
        if (needsUpdate) {
            try {
                updates.Id = enrollment.Id;
                const updateResult = await conn.sobject("Docebo_CourseEnrollment__c").update(updates);
                
                if (updateResult.success) {
                    console.log(`\n🎉 SUCCESS! Enrollment ${enrollment.Id} updated successfully!`);
                    
                    // Verify the update
                    const updatedEnrollment = await conn.sobject("Docebo_CourseEnrollment__c")
                        .findOne({ Id: enrollment.Id });
                    
                    console.log(`\n✅ Verification:`);
                    console.log(`   Course__c: ${updatedEnrollment.Course__c || 'STILL MISSING ❌'}`);
                    console.log(`   Docebo_User__c: ${updatedEnrollment.Docebo_User__c || 'STILL MISSING ❌'}`);
                    
                    if (updatedEnrollment.Course__c && updatedEnrollment.Docebo_User__c) {
                        console.log(`\n🎯 PERFECT! Enrollment is now fully associated!`);
                    }
                    
                } else {
                    console.log(`❌ Update failed:`, updateResult.errors);
                }
            } catch (updateError) {
                console.log(`❌ Update error:`, updateError.message);
            }
        } else {
            console.log(`\n✅ No updates needed - enrollment is already properly associated`);
        }
        
        // Step 6: Test the fixed enrollment creation logic
        console.log(`\n🧪 Testing fixed enrollment creation logic...`);
        console.log('-'.repeat(50));
        
        if (foundCourse && foundUser) {
            // Test creating a new enrollment with the fixed logic
            const testEnrollmentData = {
                course_id: courseId,
                user_id: userId,
                enrollment_date: "2025-06-07 12:00:00",
                status: "Test",
                score: 100
            };
            
            console.log(`📋 Test data:`);
            console.log(`   Course ID: ${testEnrollmentData.course_id}`);
            console.log(`   User ID: ${testEnrollmentData.user_id}`);
            console.log(`   Expected Enrollment_ID__c: UE-${testEnrollmentData.course_id}-${testEnrollmentData.user_id}`);
            
            // This would test the createCourseEnrollment function with the fixes
            console.log(`✅ The fixed logic should now properly:`);
            console.log(`   1. Use Enrollment_ID__c instead of External_Id__c`);
            console.log(`   2. Format as UE-courseId-userId`);
            console.log(`   3. Associate Course__c and Docebo_User__c properly`);
            console.log(`   4. Use the correct Salesforce object name`);
        }

    } catch (error) {
        console.error('💥 Error in fix:', error);
    }
}

// Execute the fix
console.log('🔄 Starting specific enrollment fix...');
fixSpecificEnrollment()
    .then(() => {
        console.log('\n✅ Fix completed');
        process.exit(0);
    })
    .catch(err => {
        console.error('💥 Fix failed:', err);
        process.exit(1);
    });
