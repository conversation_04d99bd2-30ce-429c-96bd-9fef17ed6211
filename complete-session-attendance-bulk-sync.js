require('dotenv').config();
const getConnection = require('./platform/salesforce/common/getConnection');
const getApiData = require("./common/docebo/fetcher");
const APP_BASE = process.env.DOCEBO_API_BASE_URL;

async function completeSessionAttendanceBulkSync() {
    try {
        console.log('🚀 COMPLETE SESSION ATTENDANCE BULK SYNC - USING CORRECT API ENDPOINT');
        console.log('=' .repeat(80));
        console.log('🎯 Target: Fetch ALL session attendance and sync to Salesforce');
        console.log('📡 API: course/v1/sessions/ilt (verified working)');
        
        const conn = await getConnection();
        if (!conn || !conn.accessToken) {
            throw new Error('Failed to connect to Salesforce');
        }

        console.log('✅ Connected to Salesforce successfully');

        // Step 1: Get all sessions from Salesforce
        console.log('\n📚 STEP 1: Getting All Sessions from Salesforce...');
        console.log('-'.repeat(50));
        
        const sfSessions = await conn.sobject("Docebo_Session__c")
            .find({})
            .execute();
            
        if (!sfSessions || sfSessions.length === 0) {
            throw new Error('No sessions found in Salesforce');
        }
        
        console.log(`✅ Found ${sfSessions.length} sessions in Salesforce`);
        
        // Create session mapping
        const sessionMapping = new Map();
        sfSessions.forEach(session => {
            if (session.Session_External_ID__c) {
                sessionMapping.set(session.Session_External_ID__c.toString(), {
                    id: session.Id,
                    name: session.Session_Name__c,
                    externalId: session.Session_External_ID__c,
                    courseId: session.CourseId__c
                });
            }
        });
        
        console.log(`📊 Session mapping created for ${sessionMapping.size} sessions`);

        // Step 2: Get all courses from Salesforce for reference
        console.log('\n📚 STEP 2: Getting All Courses from Salesforce...');
        console.log('-'.repeat(50));
        
        const sfCourses = await conn.sobject("Docebo_Course__c")
            .find({})
            .execute();
            
        const courseMapping = new Map();
        sfCourses.forEach(course => {
            if (course.Course_External_Id__c) {
                courseMapping.set(course.Course_External_Id__c.toString(), {
                    id: course.Id,
                    name: course.Course_Name__c,
                    externalId: course.Course_External_Id__c
                });
            }
        });
        
        console.log(`📊 Course mapping created for ${courseMapping.size} courses`);

        // Step 3: Get existing Salesforce session attendance (stored in CourseEnrollment object)
        console.log('\n📊 STEP 3: Getting Existing Session Attendance (Duplicate Prevention)...');
        console.log('-'.repeat(50));
        
        const existingAttendance = await conn.sobject("Docebo_CourseEnrollment__c")
            .find({})
            .execute();
            
        const existingAttendanceIds = new Set();
        const existingUserSessionCombo = new Set();
        const attendanceBySession = new Map();
        
        existingAttendance.forEach(attendance => {
            // Track by Enrollment_ID__c (primary duplicate check)
            if (attendance.Enrollment_ID__c) {
                existingAttendanceIds.add(attendance.Enrollment_ID__c);
                
                // Handle both session and course enrollment ID patterns
                if (attendance.Enrollment_ID__c.includes('-')) {
                    const parts = attendance.Enrollment_ID__c.split('-');
                    if (parts.length >= 2) {
                        const sessionOrCourseId = parts[0] === 'UE' ? parts[1] : parts[0];
                        const userId = parts[0] === 'UE' ? parts[2] : parts[1];
                        
                        // Add both formats to prevent duplicates
                        existingAttendanceIds.add(`${sessionOrCourseId}-${userId}`);
                        existingAttendanceIds.add(`UE-${sessionOrCourseId}-${userId}`);
                        existingAttendanceIds.add(`S-${sessionOrCourseId}-${userId}`); // Session format
                    }
                }
            }
            
            // Track by User + Course combination (secondary duplicate check)
            if (attendance.Docebo_User__c && attendance.Course__c) {
                const userCourseKey = `${attendance.Docebo_User__c}-${attendance.Course__c}`;
                existingUserSessionCombo.add(userCourseKey);
            }
            
            // Track by course for statistics
            const courseId = attendance.Course__c;
            if (!attendanceBySession.has(courseId)) {
                attendanceBySession.set(courseId, 0);
            }
            attendanceBySession.set(courseId, attendanceBySession.get(courseId) + 1);
        });
        
        console.log(`Found ${existingAttendance.length.toLocaleString()} existing attendance records in Salesforce`);
        console.log(`📋 Duplicate prevention: ${existingAttendanceIds.size.toLocaleString()} attendance IDs tracked`);
        console.log(`📋 User-Course combinations: ${existingUserSessionCombo.size.toLocaleString()} tracked`);

        // Step 4: Fetch ALL session attendance from Docebo using correct endpoint
        console.log('\n🔍 STEP 4: Fetching ALL Session Attendance from Docebo...');
        console.log('-'.repeat(50));

        let allSessionAttendance = [];
        let attendanceBySessionId = new Map();
        let totalProcessed = 0;
        let sessionsProcessed = 0;

        console.log('📡 Using correct endpoint: /learn/v1/enrollments?session_id=X for each session');
        console.log('🔄 This may take 10-15 minutes to fetch all session attendance...');

        // Get all session IDs from our mapping
        const sessionIds = Array.from(sessionMapping.keys());
        console.log(`📊 Processing attendance for ${sessionIds.length} sessions...`);

        for (let i = 0; i < sessionIds.length; i++) {
            const sessionId = sessionIds[i];
            const sessionInfo = sessionMapping.get(sessionId);

            console.log(`   📄 Processing session ${i + 1}/${sessionIds.length}: ${sessionId} (${sessionInfo.name})...`);

            try {
                let page = 1;
                let hasMoreData = true;
                let sessionAttendanceCount = 0;
                let maxPages = 50; // Safety limit to prevent infinite loops

                while (hasMoreData && page <= maxPages) {
                    console.log(`      📄 Fetching page ${page} for session ${sessionId}...`);

                    // Add timeout wrapper for API calls
                    const timeoutPromise = new Promise((_, reject) => {
                        setTimeout(() => reject(new Error('API call timeout')), 30000); // 30 second timeout
                    });

                    const apiPromise = getApiData(
                        'GET',
                        `${APP_BASE}/learn/v1/enrollments?session_id=${sessionId}&page=${page}&page_size=200`,
                        null
                    );

                    const response = await Promise.race([apiPromise, timeoutPromise]);

                    if (response && response.status === 200) {
                        const items = response.data?.items || [];
                        totalProcessed += items.length;

                        console.log(`      📊 Page ${page}: ${items.length} enrollments found`);

                        // Process attendance records for this session
                        items.forEach(enrollment => {
                            // Add session context to enrollment
                            const attendanceRecord = {
                                ...enrollment,
                                session_id: sessionId,
                                session_name: sessionInfo.name,
                                course_id: enrollment.course_id || sessionInfo.courseId,
                                session_start_date: enrollment.session_date_begin,
                                session_end_date: enrollment.session_date_end || enrollment.session_date_begin
                            };

                            allSessionAttendance.push(attendanceRecord);
                            sessionAttendanceCount++;

                            // Track by session ID
                            if (!attendanceBySessionId.has(sessionId)) {
                                attendanceBySessionId.set(sessionId, []);
                            }
                            attendanceBySessionId.get(sessionId).push(attendanceRecord);
                        });

                        hasMoreData = response.data?.has_more_data || false;
                        if (items.length === 0) hasMoreData = false;
                        page++;

                        // Add small delay between pages to avoid rate limiting
                        if (hasMoreData) {
                            await new Promise(resolve => setTimeout(resolve, 1000)); // 1 second delay
                        }

                    } else {
                        console.log(`      ⚠️ No valid response for page ${page}`);
                        hasMoreData = false;
                    }
                }

                if (page > maxPages) {
                    console.log(`      ⚠️ Reached maximum pages (${maxPages}) for session ${sessionId}`);
                }

                if (sessionAttendanceCount > 0) {
                    console.log(`      ✅ Found ${sessionAttendanceCount} attendance records`);
                } else {
                    console.log(`      ⚪ No attendance records`);
                }

                sessionsProcessed++;

                // Progress indicator every 10 sessions (more frequent)
                if (sessionsProcessed % 10 === 0) {
                    console.log(`   📊 Progress: ${sessionsProcessed}/${sessionIds.length} sessions, ${allSessionAttendance.length.toLocaleString()} total attendance records`);

                    // Show top sessions by attendance count so far
                    const topSessions = Array.from(attendanceBySessionId.entries())
                        .sort((a, b) => b[1].length - a[1].length)
                        .slice(0, 5);

                    console.log(`   🏆 Top sessions by attendance count so far:`);
                    topSessions.forEach(([sessionId, attendanceList], index) => {
                        const sessionInfo = sessionMapping.get(sessionId.toString());
                        const sessionName = sessionInfo?.name || 'Unknown';
                        console.log(`      ${index + 1}. Session ${sessionId} (${sessionName}): ${attendanceList.length.toLocaleString()} attendees`);
                    });
                }

            } catch (sessionError) {
                console.log(`      ❌ Error processing session ${sessionId}: ${sessionError.message}`);
                sessionsProcessed++; // Still count as processed to avoid infinite loop
                continue;
            }
        }
        
        console.log(`\n✅ Docebo fetch completed:`);
        console.log(`   Total sessions processed: ${totalProcessed.toLocaleString()}`);
        console.log(`   Total session attendance found: ${allSessionAttendance.length.toLocaleString()}`);
        console.log(`   Sessions with attendance: ${attendanceBySessionId.size}`);

        if (allSessionAttendance.length === 0) {
            throw new Error('No session attendance found via API');
        }

        // Step 5: Identify missing attendance records
        console.log('\n🔍 STEP 5: Identifying Missing Attendance Records (Multi-Layer Duplicate Check)...');
        console.log('-'.repeat(50));
        
        const missingAttendance = [];
        let duplicatesFound = 0;
        
        for (const doceboAttendance of allSessionAttendance) {
            const sessionId = doceboAttendance.session_id;
            const userId = doceboAttendance.user_id;
            
            // Primary duplicate check: Session Attendance ID patterns
            const sessionAttendanceId = `S-${sessionId}-${userId}`;
            const courseAttendanceId = `${doceboAttendance.course_id}-${userId}`;
            const oldFormatId = `UE-${sessionId}-${userId}`;
            
            const isDuplicateById = existingAttendanceIds.has(sessionAttendanceId) || 
                                   existingAttendanceIds.has(courseAttendanceId) ||
                                   existingAttendanceIds.has(oldFormatId);
            
            if (!isDuplicateById) {
                missingAttendance.push(doceboAttendance);
            } else {
                duplicatesFound++;
            }
        }
        
        console.log(`Found ${missingAttendance.length.toLocaleString()} missing attendance records to sync`);
        console.log(`Already synced: ${(allSessionAttendance.length - missingAttendance.length).toLocaleString()} attendance records`);
        console.log(`🛡️ Duplicates prevented: ${duplicatesFound.toLocaleString()} attendance records`);

        if (missingAttendance.length === 0) {
            console.log('✅ All session attendance is already synced!');
            return { 
                success: true, 
                synced: 0, 
                total: allSessionAttendance.length,
                existing: existingAttendance.length,
                totalProcessed: totalProcessed,
                sessionsProcessed: attendanceBySessionId.size
            };
        }

        // Step 6: Get user mappings
        console.log('\n👥 STEP 6: Getting User Mappings...');
        console.log('-'.repeat(50));
        
        const userIds = [...new Set(missingAttendance.map(a => a.user_id))];
        console.log(`Need to map ${userIds.length.toLocaleString()} unique users`);
        
        const userMappings = new Map();
        
        // Batch query users in chunks
        const chunkSize = 100;
        for (let i = 0; i < userIds.length; i += chunkSize) {
            const chunk = userIds.slice(i, i + chunkSize);
            
            const users = await conn.sobject("Docebo_Users__c")
                .find({ User_Unique_Id__c: { $in: chunk } })
                .execute();
                
            users.forEach(user => {
                userMappings.set(user.User_Unique_Id__c, user.Id);
            });
            
            const chunkNum = Math.floor(i/chunkSize) + 1;
            const totalChunks = Math.ceil(userIds.length/chunkSize);
            
            if (chunkNum % 10 === 0 || chunkNum === totalChunks) {
                console.log(`   Mapped users: chunk ${chunkNum}/${totalChunks} (Total mapped: ${userMappings.size.toLocaleString()})`);
            }
        }
        
        console.log(`✅ Successfully mapped ${userMappings.size.toLocaleString()} users`);

        return {
            success: true,
            doceboTotal: allSessionAttendance.length,
            salesforceInitial: existingAttendance.length,
            missingCount: missingAttendance.length,
            userMappings: userMappings.size,
            totalProcessed: totalProcessed,
            sessionsProcessed: attendanceBySessionId.size,
            // Continue with Step 7 in next part...
            missingAttendance: missingAttendance,
            sessionMapping: sessionMapping,
            courseMapping: courseMapping,
            userMappings: userMappings,
            existingUserSessionCombo: existingUserSessionCombo,
            existingAttendanceIds: existingAttendanceIds
        };

    } catch (error) {
        console.error('💥 Error in complete session attendance bulk sync:', error);
        return {
            success: false,
            error: error.message
        };
    }
}

// Execute the complete bulk sync
console.log('🚀 Starting Complete Session Attendance Bulk Sync...');
completeSessionAttendanceBulkSync()
    .then((result) => {
        if (result.success && result.missingAttendance) {
            console.log('\n📋 PHASE 1 COMPLETE - CONTINUING TO SYNC PHASE...');
            console.log('=' .repeat(60));
            console.log(`📊 Found ${result.missingCount.toLocaleString()} missing attendance records to sync`);
            console.log(`👥 Mapped ${result.userMappings} users`);
            console.log(`📚 ${result.sessionsProcessed} sessions with attendance data`);
            
            // Continue with the sync phase
            require('./complete-session-attendance-sync-phase2.js')(result);
        } else {
            console.log('\n📋 SESSION ATTENDANCE BULK SYNC SUMMARY:');
            console.log('=' .repeat(60));
            
            if (result.success) {
                console.log(`📊 Total Sessions Processed: ${result.totalProcessed?.toLocaleString() || 'N/A'}`);
                console.log(`📊 Total Attendance Found: ${result.doceboTotal.toLocaleString()}`);
                console.log(`📊 Sessions Processed: ${result.sessionsProcessed || 'N/A'}`);
                console.log(`📊 Salesforce Initial: ${result.salesforceInitial.toLocaleString()}`);
                console.log(`✅ All attendance already synced!`);
            } else {
                console.log(`❌ Session attendance bulk sync failed: ${result.error}`);
            }
            
            console.log('\n✅ Session attendance bulk sync completed');
            process.exit(0);
        }
    })
    .catch(err => {
        console.error('💥 Session attendance bulk sync failed:', err);
        process.exit(1);
    });
