require('dotenv').config();
const getConnection = require('./platform/salesforce/common/getConnection');
const getApiData = require("./common/docebo/fetcher");
const APP_BASE = process.env.DOCEBO_API_BASE_URL;

async function syncLearningPlanCourseEnrollments() {
    try {
        console.log('🚀 LEARNING PLAN COURSE ENROLLMENT SYNC');
        console.log('=' .repeat(80));
        console.log('🎯 Target: Docebo_Learning_Plan_Course_Enrollment__c object');
        console.log('📡 API: /learningplan/v1/learningplans/enrollments');
        
        const conn = await getConnection();
        if (!conn || !conn.accessToken) {
            throw new Error('Failed to connect to Salesforce');
        }

        console.log('✅ Connected to Salesforce successfully');

        // Step 1: Get existing Learning Plan Course Enrollments from Salesforce
        console.log('\n📊 STEP 1: Getting Existing Learning Plan Course Enrollments...');
        console.log('-'.repeat(50));
        
        const existingEnrollments = await conn.sobject("Docebo_Learning_Plan_Course_Enrollment__c")
            .find({})
            .execute();
            
        console.log(`Found ${existingEnrollments.length} existing learning plan course enrollments in Salesforce`);

        // Step 2: Get reference data from Salesforce
        console.log('\n📚 STEP 2: Getting Reference Data from Salesforce...');
        console.log('-'.repeat(50));
        
        // Get Learning Plans
        const sfLearningPlans = await conn.sobject("Docebo_Learning_Plan__c")
            .find({})
            .execute();
            
        const learningPlanMapping = new Map();
        sfLearningPlans.forEach(plan => {
            if (plan.Learning_Plan_External_Id__c) {
                learningPlanMapping.set(plan.Learning_Plan_External_Id__c.toString(), {
                    id: plan.Id,
                    name: plan.Learning_Plan_Name__c
                });
            }
        });
        
        console.log(`📊 Learning Plans: ${learningPlanMapping.size}`);

        // Get Courses
        const sfCourses = await conn.sobject("Docebo_Course__c")
            .find({})
            .execute();
            
        const courseMapping = new Map();
        sfCourses.forEach(course => {
            if (course.Course_External_Id__c) {
                courseMapping.set(course.Course_External_Id__c.toString(), {
                    id: course.Id,
                    name: course.Course_Name__c
                });
            }
        });
        
        console.log(`📊 Courses: ${courseMapping.size}`);

        // Get Users
        const sfUsers = await conn.sobject("Docebo_Users__c")
            .find({})
            .execute();
            
        const userMapping = new Map();
        sfUsers.forEach(user => {
            if (user.User_Unique_Id__c) {
                userMapping.set(user.User_Unique_Id__c.toString(), {
                    id: user.Id,
                    email: user.Email__c
                });
            }
        });
        
        console.log(`📊 Users: ${userMapping.size}`);

        // Step 3: Fetch Learning Plan Enrollments from Docebo
        console.log('\n🔍 STEP 3: Fetching Learning Plan Enrollments from Docebo...');
        console.log('-'.repeat(50));
        
        let allLearningPlanEnrollments = [];
        let page = 1;
        let hasMoreData = true;
        
        console.log('📡 Using endpoint: /learningplan/v1/learningplans/enrollments');
        
        while (hasMoreData) {
            console.log(`   📄 Fetching page ${page}...`);
            
            try {
                const response = await getApiData(
                    'GET', 
                    `${APP_BASE}/learningplan/v1/learningplans/enrollments?page=${page}&page_size=200`, 
                    null
                );
                
                if (response && response.status === 200) {
                    const items = response.data?.items || [];
                    allLearningPlanEnrollments.push(...items);
                    
                    console.log(`      Found ${items.length} LP enrollments (Total: ${allLearningPlanEnrollments.length.toLocaleString()})`);
                    
                    hasMoreData = response.data?.has_more_data || false;
                    if (items.length === 0) hasMoreData = false;
                    page++;
                    
                    // Show sample structure on first page
                    if (page === 2 && items.length > 0) {
                        const sample = items[0];
                        console.log(`      📋 Sample structure: ${Object.keys(sample).join(', ')}`);
                        console.log(`      📋 Learning Plan ID: ${sample.learning_plan_id}`);
                        console.log(`      📋 User ID: ${sample.user_id}`);
                        console.log(`      📋 Has courses?: ${sample.courses ? 'Yes' : 'No'}`);
                    }
                    
                } else {
                    console.log(`      No data on page ${page}`);
                    hasMoreData = false;
                }
            } catch (pageError) {
                console.log(`      ❌ Error on page ${page}: ${pageError.message}`);
                hasMoreData = false;
            }
        }
        
        console.log(`\n✅ Found ${allLearningPlanEnrollments.length.toLocaleString()} learning plan enrollments`);

        if (allLearningPlanEnrollments.length === 0) {
            console.log('⚠️ No learning plan enrollments found');
            return { success: true, message: 'No data found' };
        }

        // Step 4: For each Learning Plan Enrollment, get the courses and create course enrollment records
        console.log('\n🔍 STEP 4: Processing Learning Plan Enrollments to Create Course Enrollments...');
        console.log('-'.repeat(50));
        
        let courseEnrollmentsToCreate = [];
        let processedCount = 0;
        let skippedCount = 0;
        
        for (const lpEnrollment of allLearningPlanEnrollments) {
            processedCount++;
            
            const learningPlanId = lpEnrollment.learning_plan_id;
            const userId = lpEnrollment.user_id;
            
            // Check if we have the learning plan and user in Salesforce
            const salesforceLearningPlan = learningPlanMapping.get(learningPlanId.toString());
            const salesforceUser = userMapping.get(userId.toString());
            
            if (!salesforceLearningPlan || !salesforceUser) {
                skippedCount++;
                continue;
            }
            
            // Now we need to get the courses in this learning plan
            try {
                console.log(`   📚 Getting courses for LP ${learningPlanId} (${processedCount}/${allLearningPlanEnrollments.length})...`);
                
                const coursesResponse = await getApiData(
                    'GET', 
                    `${APP_BASE}/learningplan/v1/learningplans/${learningPlanId}/courses`, 
                    null
                );
                
                if (coursesResponse && coursesResponse.status === 200) {
                    const courses = coursesResponse.data?.items || [];
                    console.log(`      Found ${courses.length} courses in LP ${learningPlanId}`);
                    
                    // Create a course enrollment record for each course in the learning plan
                    for (const course of courses) {
                        const courseId = course.id || course.course_id;

                        // Skip if courseId is undefined or null
                        if (!courseId) {
                            console.log(`      ⚠️ Skipping course with undefined ID in LP ${learningPlanId}`);
                            continue;
                        }

                        const salesforceCourse = courseMapping.get(courseId.toString());

                        if (salesforceCourse) {
                            // Parse enrollment date
                            let enrollmentDate = "";
                            if (lpEnrollment.enrollment_date || lpEnrollment.date_inscr) {
                                const dateStr = lpEnrollment.enrollment_date || lpEnrollment.date_inscr;
                                try {
                                    enrollmentDate = new Date(dateStr.replace(' ', 'T')).toISOString();
                                } catch (e) {
                                    enrollmentDate = "";
                                }
                            }
                            
                            const courseEnrollmentRecord = {
                                Learning_Plan__c: salesforceLearningPlan.id,
                                Course__c: salesforceCourse.id,
                                User__c: salesforceUser.id,
                                Status__c: lpEnrollment.status || 'enrolled',
                                Enrollment_Date__c: enrollmentDate,
                                Progress__c: 0, // Will be updated by course completion webhooks
                                Score__c: 0,
                                Time_Spent__c: 0
                            };
                            
                            courseEnrollmentsToCreate.push(courseEnrollmentRecord);
                        }
                    }
                } else {
                    console.log(`      ❌ Could not get courses for LP ${learningPlanId}`);
                }
                
            } catch (courseError) {
                console.log(`      ❌ Error getting courses for LP ${learningPlanId}: ${courseError.message}`);
            }
            
            // Progress indicator every 50 enrollments
            if (processedCount % 50 === 0) {
                console.log(`   📊 Progress: ${processedCount}/${allLearningPlanEnrollments.length} LP enrollments processed, ${courseEnrollmentsToCreate.length.toLocaleString()} course enrollments prepared`);
            }
        }
        
        console.log(`\n📊 Processing Summary:`);
        console.log(`   LP Enrollments Processed: ${processedCount.toLocaleString()}`);
        console.log(`   Course Enrollments Prepared: ${courseEnrollmentsToCreate.length.toLocaleString()}`);
        console.log(`   Skipped: ${skippedCount.toLocaleString()}`);

        if (courseEnrollmentsToCreate.length === 0) {
            console.log('⚠️ No course enrollments to create');
            return { 
                success: true, 
                message: 'No course enrollments to create',
                processed: processedCount,
                skipped: skippedCount
            };
        }

        // Step 5: Remove duplicates and create records
        console.log('\n🛡️ STEP 5: Removing Duplicates and Creating Records...');
        console.log('-'.repeat(50));
        
        // Create unique key for each course enrollment
        const existingKeys = new Set();
        existingEnrollments.forEach(enrollment => {
            if (enrollment.Learning_Plan__c && enrollment.Course__c && enrollment.User__c) {
                const key = `${enrollment.Learning_Plan__c}-${enrollment.Course__c}-${enrollment.User__c}`;
                existingKeys.add(key);
            }
        });
        
        const uniqueCourseEnrollments = [];
        const duplicateKeys = new Set();
        let duplicatesSkipped = 0;
        
        for (const enrollment of courseEnrollmentsToCreate) {
            const key = `${enrollment.Learning_Plan__c}-${enrollment.Course__c}-${enrollment.User__c}`;
            
            if (existingKeys.has(key) || duplicateKeys.has(key)) {
                duplicatesSkipped++;
                continue;
            }
            
            duplicateKeys.add(key);
            uniqueCourseEnrollments.push(enrollment);
        }
        
        console.log(`Unique course enrollments to create: ${uniqueCourseEnrollments.length.toLocaleString()}`);
        console.log(`🛡️ Duplicates prevented: ${duplicatesSkipped.toLocaleString()}`);

        if (uniqueCourseEnrollments.length === 0) {
            console.log('✅ All learning plan course enrollments already exist!');
            return {
                success: true,
                message: 'All enrollments already exist',
                processed: processedCount,
                duplicates: duplicatesSkipped
            };
        }

        // Step 6: Create records in batches
        console.log('\n💾 STEP 6: Creating Learning Plan Course Enrollments...');
        console.log('-'.repeat(50));
        
        let successCount = 0;
        let errorCount = 0;
        const batchSize = 50;
        const totalBatches = Math.ceil(uniqueCourseEnrollments.length / batchSize);
        
        for (let i = 0; i < uniqueCourseEnrollments.length; i += batchSize) {
            const batch = uniqueCourseEnrollments.slice(i, i + batchSize);
            const batchNum = Math.floor(i / batchSize) + 1;
            
            console.log(`   📦 Processing batch ${batchNum}/${totalBatches} (${batch.length} records)...`);
            
            try {
                const results = await conn.sobject("Docebo_Learning_Plan_Course_Enrollment__c")
                    .create(batch);
                    
                const resultArray = Array.isArray(results) ? results : [results];
                
                let batchSuccessCount = 0;
                let batchErrorCount = 0;
                
                resultArray.forEach((result) => {
                    if (result.success) {
                        successCount++;
                        batchSuccessCount++;
                    } else {
                        errorCount++;
                        batchErrorCount++;
                        
                        if (batchErrorCount <= 2) {
                            const errorMessage = result.errors?.[0]?.message || 'Unknown error';
                            console.log(`      ⚠️ Error: ${errorMessage}`);
                        }
                    }
                });
                
                console.log(`      ✅ Batch ${batchNum}: ${batchSuccessCount} success, ${batchErrorCount} errors`);
                
                // Rate limiting
                if (batchNum < totalBatches) {
                    await new Promise(resolve => setTimeout(resolve, 500));
                }
                
            } catch (batchError) {
                console.error(`      ❌ Batch ${batchNum} failed:`, batchError.message);
                errorCount += batch.length;
            }
        }

        // Step 7: Final verification
        console.log('\n🔍 STEP 7: Final Verification...');
        console.log('-'.repeat(50));
        
        const finalEnrollments = await conn.sobject("Docebo_Learning_Plan_Course_Enrollment__c")
            .find({})
            .execute();

        return {
            success: true,
            initialCount: existingEnrollments.length,
            finalCount: finalEnrollments.length,
            synced: successCount,
            errors: errorCount,
            processed: processedCount,
            skipped: skippedCount,
            duplicates: duplicatesSkipped
        };

    } catch (error) {
        console.error('💥 Error in learning plan course enrollment sync:', error);
        return {
            success: false,
            error: error.message
        };
    }
}

// Execute the sync
console.log('🔄 Starting Learning Plan Course Enrollment Sync...');
syncLearningPlanCourseEnrollments()
    .then((result) => {
        console.log('\n📋 LEARNING PLAN COURSE ENROLLMENT SYNC SUMMARY:');
        console.log('=' .repeat(70));
        
        if (result.success) {
            if (result.initialCount !== undefined && result.finalCount !== undefined) {
                console.log(`📊 Initial Count: ${result.initialCount.toLocaleString()}`);
                console.log(`📊 Final Count: ${result.finalCount.toLocaleString()}`);
                console.log(`✅ Successfully Synced: ${result.synced.toLocaleString()}`);
                console.log(`❌ Errors: ${result.errors.toLocaleString()}`);
                console.log(`⏭️ Skipped: ${result.skipped.toLocaleString()}`);
                console.log(`🛡️ Duplicates Prevented: ${result.duplicates.toLocaleString()}`);
                
                const netIncrease = result.finalCount - result.initialCount;
                console.log(`\n🎉 NET RESULT: ${netIncrease.toLocaleString()} new learning plan course enrollments added!`);
                
                if (netIncrease > 0) {
                    console.log(`🎉 SUCCESS: Learning plan course enrollments are now synced!`);
                } else {
                    console.log(`✅ All learning plan course enrollments were already synced.`);
                }
            } else {
                console.log(`📋 Result: ${result.message}`);
                if (result.processed) {
                    console.log(`📊 Processed: ${result.processed.toLocaleString()}`);
                }
                if (result.skipped) {
                    console.log(`⏭️ Skipped: ${result.skipped.toLocaleString()}`);
                }
                if (result.duplicates) {
                    console.log(`🛡️ Duplicates: ${result.duplicates.toLocaleString()}`);
                }
            }
        } else {
            console.log(`❌ Sync failed: ${result.error}`);
        }
        
        console.log('\n✅ Learning plan course enrollment sync completed');
        process.exit(0);
    })
    .catch(err => {
        console.error('💥 Learning plan course enrollment sync failed:', err);
        process.exit(1);
    });
