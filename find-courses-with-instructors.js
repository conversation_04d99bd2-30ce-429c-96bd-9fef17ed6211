require('dotenv').config();
const doceboServices = require('./platform/docebo/services');
const { getCourseSalesForceId } = require('./platform/salesforce/courses/createCourse');

async function findCoursesWithInstructors() {
  try {
    console.log('🔍 Finding courses with sessions and instructors...');
    
    // Get all courses
    const allCourses = await doceboServices.getTotalCourseListedInfo();
    
    if (!allCourses || allCourses.length === 0) {
      console.log('❌ No courses found in Docebo');
      return;
    }
    
    console.log(`📚 Checking ${allCourses.length} courses for sessions with instructors...`);
    
    const coursesWithInstructors = [];
    let checkedCount = 0;
    
    // Check first 50 courses to find some with instructors
    const coursesToCheck = allCourses.slice(0, 50);
    
    for (const course of coursesToCheck) {
      checkedCount++;
      
      try {
        // Check if course exists in Salesforce
        const courseSalesforceId = await getCourseSalesForceId(course.id);
        if (!courseSalesforceId) {
          continue; // Skip courses not in Salesforce
        }
        
        // Get sessions for this course
        const sessionsResult = await doceboServices.getSessionListedInfo(course.id);
        
        if (!sessionsResult || sessionsResult.length === 0) {
          continue; // Skip courses without sessions
        }
        
        console.log(`📖 Course ${course.id} (${course.name || 'Unnamed'}) has ${sessionsResult.length} sessions`);
        
        let courseHasInstructors = false;
        let totalInstructors = 0;
        
        // Check each session for instructors
        for (const session of sessionsResult) {
          try {
            const sessionInfo = await doceboServices.getCourseSessionInfo(session.id);
            
            if (sessionInfo && sessionInfo.status === 200 && sessionInfo.data) {
              const sessionData = sessionInfo.data;
              
              if (sessionData.instructors && sessionData.instructors.length > 0) {
                courseHasInstructors = true;
                totalInstructors += sessionData.instructors.length;
                
                console.log(`   📅 Session ${session.id}: ${sessionData.instructors.length} instructors`);
                sessionData.instructors.forEach(instructor => {
                  console.log(`      👤 ${instructor.firstname} ${instructor.lastname} (ID: ${instructor.user_id})`);
                });
              }
            }
          } catch (sessionError) {
            console.error(`   ❌ Error checking session ${session.id}:`, sessionError.message);
          }
        }
        
        if (courseHasInstructors) {
          coursesWithInstructors.push({
            id: course.id,
            name: course.name || 'Unnamed',
            sessionsCount: sessionsResult.length,
            instructorsCount: totalInstructors
          });
          
          console.log(`   ✅ Course ${course.id} has ${totalInstructors} total instructors\n`);
        }
        
        // Stop after finding 5 courses with instructors
        if (coursesWithInstructors.length >= 5) {
          break;
        }
        
      } catch (courseError) {
        console.error(`❌ Error checking course ${course.id}:`, courseError.message);
      }
      
      // Progress update
      if (checkedCount % 10 === 0) {
        console.log(`📊 Progress: Checked ${checkedCount} courses, found ${coursesWithInstructors.length} with instructors`);
      }
    }
    
    console.log('\n🎯 Summary of courses with instructors:');
    if (coursesWithInstructors.length > 0) {
      coursesWithInstructors.forEach((course, index) => {
        console.log(`${index + 1}. Course ${course.id}: ${course.name}`);
        console.log(`   Sessions: ${course.sessionsCount}, Instructors: ${course.instructorsCount}`);
      });
      
      console.log(`\n✅ Found ${coursesWithInstructors.length} courses with instructors out of ${checkedCount} checked`);
      console.log('\n🚀 You can now test with one of these course IDs:');
      coursesWithInstructors.forEach(course => {
        console.log(`   node test-instructor-association.js ${course.id}`);
      });
      
    } else {
      console.log(`❌ No courses with instructors found in the first ${checkedCount} courses checked`);
      console.log('   You may need to check more courses or verify that sessions have instructor data');
    }
    
  } catch (error) {
    console.error('💥 Error finding courses with instructors:', error);
  }
}

findCoursesWithInstructors()
  .then(() => {
    console.log('\n✅ Search completed');
    process.exit(0);
  })
  .catch(err => {
    console.error('💥 Search failed:', err);
    process.exit(1);
  });
