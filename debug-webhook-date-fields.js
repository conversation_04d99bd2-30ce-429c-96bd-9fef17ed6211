require('dotenv').config();
const { tidyData } = require('./platform/salesforce/users/createUser');

// Function to debug webhook date field mapping
function debugWebhookDateFields() {
    console.log('🔍 Debugging Webhook Date Field Mapping...');
    
    // Simulate webhook data structure
    const mockWebhookData = {
        userInfo: {
            user_data: {
                user_id: "12345",
                first_name: "Test",
                last_name: "Webhook User",
                email: "<EMAIL>",
                username: "test_webhook",
                level: "User",
                manager_username: "manager_test",
                email_validation_status: "1",
                valid: "1"
            },
            additional_fields: [
                { id: "8", value: "Test Manager", enabled: true },
                { id: "9", value: "Operations/Business Management", enabled: true },
                { id: "10", value: "Full-time", enabled: true },
                { id: "12", value: "White", enabled: true },
                { id: "13", value: "Male", enabled: true },
                { id: "14", value: "Test Organization", enabled: true }
            ],
            branches: [
                {
                    name: "Test Branch",
                    path: "/test/branch",
                    codes: "12345"
                }
            ],
            fired_at: "2024-01-15 09:00:00",
            expiration_date: "2025-12-31 23:59:59"
        },
        userListedInfo: {
            last_access_date: "2024-02-07T14:30:00Z"
        }
    };

    console.log('\n📋 MOCK WEBHOOK DATA:');
    console.log('=' .repeat(60));
    console.log('fired_at:', mockWebhookData.userInfo.fired_at);
    console.log('expiration_date:', mockWebhookData.userInfo.expiration_date);
    console.log('last_access_date:', mockWebhookData.userListedInfo.last_access_date);

    // Test the tidyData function
    console.log('\n🔧 Testing tidyData function...');
    const processedData = tidyData(mockWebhookData.userInfo, mockWebhookData.userListedInfo);

    console.log('\n📊 PROCESSED DATE FIELDS:');
    console.log('=' .repeat(60));
    console.log('User_Creation_Date__c:', processedData.User_Creation_Date__c);
    console.log('User_Expiration_Date__c:', processedData.User_Expiration_Date__c);
    console.log('User_Last_Access_Date__c:', processedData.User_Last_Access_Date__c);
    console.log('User_Suspension_Date__c:', processedData.User_Suspension_Date__c);

    // Check if dates are properly formatted
    console.log('\n✅ DATE VALIDATION:');
    console.log('=' .repeat(40));
    
    const validateDate = (dateStr, fieldName) => {
        if (!dateStr) {
            console.log(`❌ ${fieldName}: MISSING`);
            return false;
        }
        
        try {
            const date = new Date(dateStr);
            if (isNaN(date.getTime())) {
                console.log(`❌ ${fieldName}: INVALID FORMAT (${dateStr})`);
                return false;
            } else {
                console.log(`✅ ${fieldName}: VALID (${dateStr})`);
                return true;
            }
        } catch (error) {
            console.log(`❌ ${fieldName}: ERROR (${error.message})`);
            return false;
        }
    };

    validateDate(processedData.User_Creation_Date__c, 'User_Creation_Date__c');
    validateDate(processedData.User_Expiration_Date__c, 'User_Expiration_Date__c');
    validateDate(processedData.User_Last_Access_Date__c, 'User_Last_Access_Date__c');
    validateDate(processedData.User_Suspension_Date__c, 'User_Suspension_Date__c');

    // Show complete processed data structure
    console.log('\n📋 COMPLETE PROCESSED DATA:');
    console.log('=' .repeat(60));
    console.log(JSON.stringify(processedData, null, 2));

    return processedData;
}

// Test different webhook data scenarios
function testWebhookScenarios() {
    console.log('\n🧪 Testing Different Webhook Scenarios...');
    
    const scenarios = [
        {
            name: "Complete Data",
            data: {
                userInfo: {
                    user_data: { user_id: "1", email: "<EMAIL>", valid: "1" },
                    fired_at: "2024-01-15 09:00:00",
                    expiration_date: "2025-12-31 23:59:59",
                    additional_fields: []
                },
                userListedInfo: {
                    last_access_date: "2024-02-07T14:30:00Z"
                }
            }
        },
        {
            name: "Missing fired_at",
            data: {
                userInfo: {
                    user_data: { user_id: "2", email: "<EMAIL>", valid: "1" },
                    expiration_date: "2025-12-31 23:59:59",
                    additional_fields: []
                },
                userListedInfo: {
                    last_access_date: "2024-02-07T14:30:00Z"
                }
            }
        },
        {
            name: "Missing userListedInfo",
            data: {
                userInfo: {
                    user_data: { user_id: "3", email: "<EMAIL>", valid: "1" },
                    fired_at: "2024-01-15 09:00:00",
                    expiration_date: "2025-12-31 23:59:59",
                    additional_fields: []
                },
                userListedInfo: null
            }
        },
        {
            name: "Suspended User",
            data: {
                userInfo: {
                    user_data: { user_id: "4", email: "<EMAIL>", valid: "0" },
                    fired_at: "2024-01-15 09:00:00",
                    expiration_date: "2025-12-31 23:59:59",
                    additional_fields: []
                },
                userListedInfo: {
                    last_access_date: "2024-02-07T14:30:00Z"
                }
            }
        }
    ];

    scenarios.forEach(scenario => {
        console.log(`\n📋 Scenario: ${scenario.name}`);
        console.log('-'.repeat(40));
        
        try {
            const processed = tidyData(scenario.data.userInfo, scenario.data.userListedInfo);
            console.log(`   User_Creation_Date__c: ${processed.User_Creation_Date__c || 'MISSING'}`);
            console.log(`   User_Expiration_Date__c: ${processed.User_Expiration_Date__c || 'MISSING'}`);
            console.log(`   User_Last_Access_Date__c: ${processed.User_Last_Access_Date__c || 'MISSING'}`);
            console.log(`   User_Suspension_Date__c: ${processed.User_Suspension_Date__c || 'MISSING'}`);
        } catch (error) {
            console.log(`   ❌ Error: ${error.message}`);
        }
    });
}

// Execute the debug
console.log('🔄 Starting webhook date fields debug...');
debugWebhookDateFields();
testWebhookScenarios();

console.log('\n💡 RECOMMENDATIONS:');
console.log('=' .repeat(50));
console.log('1. Check if webhook data includes fired_at and expiration_date');
console.log('2. Verify userListedInfo is being passed to tidyData function');
console.log('3. Ensure date formatting is correct for Salesforce');
console.log('4. Add fallback values for missing dates');
console.log('5. Test with actual webhook payload');

console.log('\n✅ Debug completed');
process.exit(0);
