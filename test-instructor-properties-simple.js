require('dotenv').config();
const doceboServices = require('./platform/docebo/services');
const logger = require('./utils/logger');

async function testInstructorDataEndpoint() {
  try {
    // The specific course ID from the existing test
    const courseId = 557;
    
    // We'll use the same approach as the existing test to find a valid user ID
    // First, get a list of users
    logger.info('Fetching users to test as potential instructors...');
    const usersList = await doceboServices.getTotalUserListedInfo();
    
    if (!usersList || usersList.length === 0) {
      logger.error('No users found to test');
      return;
    }
    
    logger.info(`Found ${usersList.length} users to test`);
    
    // Try each user with the instructor data endpoint
    logger.info(`Testing each user with the instructor API for course ID ${courseId}...`);
    let foundInstructor = false;
    
    for (const user of usersList.slice(0, 20)) { // Limit to first 20 users for performance
      const userId = user.id || user.user_id;
      logger.info(`Trying user ID: ${userId}`);
      
      // Use the existing service function to get instructor data
      const instructorData = await doceboServices.getInstructorData(userId, courseId);
      
      if (instructorData && 
          instructorData.data && 
          instructorData.data.instructors && 
          instructorData.data.instructors.length > 0) {
        
        logger.info('SUCCESS! Found instructor data!');
        console.log('\nInstructor data:');
        console.log(JSON.stringify(instructorData.data, null, 2));
        
        // Log the instructor properties
        console.log('\nInstructor object properties:');
        const instructorSample = instructorData.data.instructors[0];
        Object.keys(instructorSample).forEach(key => {
          console.log(`- ${key}: ${typeof instructorSample[key]}`);
          
          // Show example values
          if (typeof instructorSample[key] === 'object' && instructorSample[key] !== null) {
            console.log(`  Sample: ${JSON.stringify(instructorSample[key]).substring(0, 100)}...`);
          } else {
            console.log(`  Example value: ${instructorSample[key]}`);
          }
        });
        
        foundInstructor = true;
        break;
      }
    }
    
    if (!foundInstructor) {
      logger.info(`Could not find any users with instructor data for course ID ${courseId}`);
      logger.info('Try providing specific user_id and course_id values if you know them');
      
      // Example of the expected data structure based on code analysis
      console.log('\nExpected instructor data structure:');
      console.log({
        "instructors": [
          {
            "id": "user_id_value",
            "name": "Instructor Name",
            "email": "<EMAIL>",
            "type": "instructor_type"
            // Additional properties may be present
          }
        ]
      });
    }
    
    return foundInstructor;
  } catch (error) {
    logger.error(`Test failed: ${error.message}`);
    console.error(error);
  }
}

// Execute the test if this file is run directly
if (require.main === module) {
  testInstructorDataEndpoint()
    .then(() => logger.info('Test completed'))
    .catch(err => logger.error(`Test failed with error: ${err.message}`));
}

module.exports = {
  testInstructorDataEndpoint
};