require('dotenv').config();
const getConnection = require('./platform/salesforce/common/getConnection');

async function checkLeadFields() {
    try {
        console.log('🔍 Checking available fields on Lead object...');
        
        const conn = await getConnection();
        if (!conn || !conn.accessToken) {
            console.error("❌ Invalid Salesforce connection");
            return;
        }

        // Describe the Lead object to see all available fields
        const leadDescription = await conn.sobject("Lead").describe();
        
        console.log(`\n📋 Lead Object Information:`);
        console.log(`   Label: ${leadDescription.label}`);
        console.log(`   API Name: ${leadDescription.name}`);
        console.log(`   Total Fields: ${leadDescription.fields.length}`);

        // Filter for custom fields and commonly used fields
        const customFields = leadDescription.fields.filter(field => 
            field.name.includes('__c') && field.createable
        );

        const standardFields = leadDescription.fields.filter(field => 
            !field.name.includes('__c') && field.createable && 
            ['FirstName', 'LastName', 'Email', 'Company', 'Status', 'Title', 'Phone', 'Website'].includes(field.name)
        );

        console.log(`\n✅ Standard Lead Fields (createable):`);
        standardFields.forEach(field => {
            console.log(`   - ${field.name} (${field.type}) - ${field.label}`);
        });

        console.log(`\n🔧 Custom Lead Fields (createable):`);
        if (customFields.length > 0) {
            customFields.forEach(field => {
                console.log(`   - ${field.name} (${field.type}) - ${field.label}`);
            });
        } else {
            console.log('   No custom fields found on Lead object');
        }

        // Check for specific fields we want to use
        const desiredFields = [
            'Back_Partner_Type__c',
            'Backbone_Partner__c', 
            'Best_Describes_Your_Affiliation__c',
            'Branch_Name__c',
            'Branch_Path__c',
            'Direct_Manager__c',
            'Employment_Begin_Date__c',
            'Employment_Type__c',
            'Gender_Identity__c',
            'Job_Title__c',
            'Initiative__c',
            'Level__c',
            'National_Regional_or_Local__c',
            'Network_Partnership_Association__c',
            'Organization_Name__c',
            'Organization_Headquarters__c',
            'Organization_URL__c',
            'Partner_with_a_Member_of_StriveTogether__c',
            'Race_Identity__c',
            'Role_Type__c',
            'StriveTogether_Network_Member__c',
            'User_Creation_Date__c',
            'User_Expiration_Date__c',
            'User_Last_Access_Date__c',
            'User_Level__c',
            'User_Unique_Id__c',
            'Username__c',
            'Who__c',
            'accountid__c'
        ];

        console.log(`\n🎯 Checking for desired custom fields:`);
        const availableFields = [];
        const missingFields = [];

        desiredFields.forEach(fieldName => {
            const field = leadDescription.fields.find(f => f.name === fieldName);
            if (field && field.createable) {
                availableFields.push({
                    name: field.name,
                    type: field.type,
                    label: field.label
                });
                console.log(`   ✅ ${fieldName} (${field.type}) - ${field.label}`);
            } else {
                missingFields.push(fieldName);
                console.log(`   ❌ ${fieldName} - Not found or not createable`);
            }
        });

        console.log(`\n📊 Summary:`);
        console.log(`   Available custom fields: ${availableFields.length}/${desiredFields.length}`);
        console.log(`   Missing fields: ${missingFields.length}`);

        if (missingFields.length > 0) {
            console.log(`\n⚠️  Missing fields that need to be created on Lead object:`);
            missingFields.forEach(field => {
                console.log(`   - ${field}`);
            });
        }

        // Generate code for available fields only
        if (availableFields.length > 0) {
            console.log(`\n💻 Code for available fields only:`);
            console.log(`const leadData = {`);
            console.log(`    // Standard Lead fields`);
            console.log(`    LastName: tmpUserInfo.Last_Name__c,`);
            console.log(`    FirstName: tmpUserInfo.First_Name__c,`);
            console.log(`    Email: tmpUserInfo.Email__c,`);
            console.log(`    Company: tmpUserInfo.Organization_Name__c || "-",`);
            console.log(`    Status: "Open - Not Contacted",`);
            console.log(`    `);
            console.log(`    // Available custom fields`);
            availableFields.forEach(field => {
                console.log(`    ${field.name}: tmpUserInfo.${field.name},`);
            });
            console.log(`};`);
        }

    } catch (error) {
        console.error('💥 Error checking Lead fields:', error);
    }
}

// Execute the check
console.log('🔄 Starting Lead field check...');
checkLeadFields()
    .then(() => {
        console.log('\n✅ Field check completed');
        process.exit(0);
    })
    .catch(err => {
        console.error('💥 Field check failed:', err);
        process.exit(1);
    });
