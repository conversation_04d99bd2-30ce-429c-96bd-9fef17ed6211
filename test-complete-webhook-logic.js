require('dotenv').config();
const { createNewUser } = require('./platform/salesforce/users/createUser');

// Test the complete webhook logic: Create vs Update scenarios
async function testCompleteWebhookLogic() {
    console.log('🧪 Testing Complete Webhook Logic...');
    console.log('=' .repeat(80));
    
    // Mock Docebo webhook data
    const mockUserInfo = {
        user_data: {
            user_id: "99999", // Use a test ID that likely doesn't exist
            first_name: "Test",
            last_name: "User",
            email: "<EMAIL>", // Use a test email
            username: "test_webhook_user",
            level: "User",
            manager_username: "test_manager",
            email_validation_status: "1",
            valid: "1",
            timezone: "America/New_York",
            language: "English",
            phone: "******-TEST-123"
        },
        additional_fields: [
            {
                id: "8", // Job Title
                enabled: true,
                value: "Test Engineer",
                type: "textfield"
            },
            {
                id: "9", // Role Type
                enabled: true,
                value: "2",
                options: [
                    { id: "1", label: "Administrative" },
                    { id: "2", label: "Technical" },
                    { id: "3", label: "Management" }
                ]
            },
            {
                id: "10", // Employment Type
                enabled: true,
                value: "Full-Time",
                type: "textfield"
            },
            {
                id: "12", // Race
                enabled: true,
                value: "3",
                options: [
                    { id: "1", label: "White" },
                    { id: "2", label: "Black" },
                    { id: "3", label: "Asian" },
                    { id: "4", label: "Hispanic" }
                ]
            },
            {
                id: "13", // Gender
                enabled: true,
                value: "1",
                options: [
                    { id: "1", label: "Male" },
                    { id: "2", label: "Female" },
                    { id: "3", label: "Non-binary" }
                ]
            },
            {
                id: "14", // Organization
                enabled: true,
                value: "Test Corp Inc",
                type: "textfield"
            },
            {
                id: "24", // City
                enabled: true,
                value: "Test City",
                type: "textfield"
            },
            {
                id: "25", // State
                enabled: true,
                value: "2",
                options: [
                    { id: "1", label: "New York" },
                    { id: "2", label: "California" },
                    { id: "3", label: "Texas" }
                ]
            }
        ],
        fired_at: new Date().toISOString(),
        expiration_date: null
    };

    const mockUserListedInfo = {
        last_access_date: new Date().toISOString()
    };

    console.log('\n📋 TEST SCENARIOS:');
    console.log('1. 🆕 NEW USER: Should create Lead + Docebo_Users__c');
    console.log('2. 🔄 EXISTING USER: Should update Docebo_Users__c + Contact/Lead');
    console.log('3. 🔍 LOGIC VERIFICATION: Check all update paths work correctly');

    console.log('\n📤 MOCK USER DATA:');
    console.log('=' .repeat(60));
    console.log(`User ID: ${mockUserInfo.user_data.user_id}`);
    console.log(`Name: ${mockUserInfo.user_data.first_name} ${mockUserInfo.user_data.last_name}`);
    console.log(`Email: ${mockUserInfo.user_data.email}`);
    console.log(`Phone: ${mockUserInfo.user_data.phone}`);
    console.log(`Language: ${mockUserInfo.user_data.language}`);
    console.log(`Timezone: ${mockUserInfo.user_data.timezone}`);
    console.log(`Organization: Test Corp Inc`);
    console.log(`Job Title: Test Engineer`);
    console.log(`Role: Technical`);

    console.log('\n🎯 EXPECTED WEBHOOK BEHAVIOR:');
    console.log('=' .repeat(60));
    console.log('✅ Check if Docebo_Users__c exists by User_Unique_Id__c');
    console.log('   ├─ If EXISTS: Update Docebo_Users__c + Check for Contact/Lead to update');
    console.log('   └─ If NOT EXISTS: Check for Contact by email');
    console.log('      ├─ If Contact EXISTS: Update Contact + Create Docebo_Users__c');
    console.log('      └─ If Contact NOT EXISTS: Create Lead + Create Docebo_Users__c');

    try {
        console.log('\n🚀 EXECUTING WEBHOOK LOGIC...');
        console.log('=' .repeat(60));
        
        // Execute the webhook function
        const result = await createNewUser(mockUserInfo, mockUserListedInfo);
        
        if (result) {
            console.log('\n✅ WEBHOOK EXECUTION COMPLETED SUCCESSFULLY!');
            console.log('=' .repeat(60));
            
            console.log('\n📊 WHAT SHOULD HAVE HAPPENED:');
            console.log('Based on the logs above, the webhook should have:');
            console.log('1. ✅ Checked for existing Docebo_Users__c record');
            console.log('2. ✅ Either updated existing records OR created new Lead + Docebo_Users__c');
            console.log('3. ✅ Applied all field mappings correctly');
            console.log('4. ✅ Used proper Salesforce field names');
            
            console.log('\n🔍 VERIFICATION STEPS:');
            console.log('=' .repeat(60));
            console.log('To verify the webhook worked correctly, check:');
            console.log('1. 📋 Salesforce Lead/Contact record was created/updated');
            console.log('2. 📋 Docebo_Users__c record was created/updated');
            console.log('3. 📋 All field mappings are populated correctly');
            console.log('4. 📋 Phone, Languages, Mailing Address fields have data');
            
            return { success: true, message: 'Webhook logic executed successfully' };
            
        } else {
            console.log('\n❌ WEBHOOK EXECUTION FAILED');
            console.log('Check the error logs above for details');
            return { success: false, message: 'Webhook execution failed' };
        }
        
    } catch (error) {
        console.error('\n💥 WEBHOOK EXECUTION ERROR:', error);
        return { success: false, error: error.message };
    }
}

// Test different scenarios
async function runAllTests() {
    console.log('🔄 Starting Complete Webhook Logic Tests...');
    console.log('⚠️  Make sure your Salesforce connection is configured correctly');
    console.log('⚠️  This test will create/update records in Salesforce');
    
    try {
        const result = await testCompleteWebhookLogic();
        
        console.log('\n🎉 TEST SUMMARY:');
        console.log('=' .repeat(80));
        
        if (result.success) {
            console.log('✅ WEBHOOK LOGIC TEST PASSED');
            console.log('✅ The webhook now properly handles:');
            console.log('   ├─ 🆕 New users: Creates Lead + Docebo_Users__c');
            console.log('   ├─ 🔄 Existing users: Updates Docebo_Users__c + Contact/Lead');
            console.log('   ├─ 📊 All field mappings with correct Salesforce field names');
            console.log('   ├─ 📞 Phone numbers from user_data.phone');
            console.log('   ├─ 🌐 Languages from user_data.language');
            console.log('   ├─ 🏠 Mailing addresses from additional fields');
            console.log('   └─ 🎯 Position roles copied from Role_Type__c');
            
            console.log('\n🚀 WEBHOOK IS READY FOR PRODUCTION!');
            
        } else {
            console.log('❌ WEBHOOK LOGIC TEST FAILED');
            if (result.error) {
                console.log(`Error: ${result.error}`);
            }
            console.log('Please check the logs above and fix any issues');
        }
        
    } catch (error) {
        console.error('💥 Test execution failed:', error);
    }
}

// Execute the tests
runAllTests()
    .then(() => {
        console.log('\n✨ Test execution completed');
        process.exit(0);
    })
    .catch(err => {
        console.error('💥 Test failed:', err);
        process.exit(1);
    });
