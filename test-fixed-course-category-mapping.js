require('dotenv').config();
const getConnection = require('./platform/salesforce/common/getConnection');
const { createNewCourse, tidyCourseData } = require('./platform/salesforce/courses/createCourse');

async function testFixedCourseCategoryMapping() {
    try {
        console.log('🧪 Testing Fixed Course Category Mapping');
        console.log('=' .repeat(70));
        
        const conn = await getConnection();
        if (!conn || !conn.accessToken) {
            console.error("❌ Invalid Salesforce connection");
            return;
        }

        // Step 1: Test tidyCourseData function with mock data
        console.log('\n🔧 Testing tidyCourseData function with category data...');
        console.log('-'.repeat(50));
        
        const mockCourseInfo = {
            id: "TEST-CATEGORY-123",
            code: "TEST-CAT-001",
            name: "Test Course with Category",
            description: "<p>Test course description with <strong>HTML</strong> tags</p>",
            status: "published",
            type: "elearning",
            created_on: "2024-01-15T10:30:00Z",
            updated_on: "2024-12-01T15:30:00Z",
            time_options: {
                duration: { days: 30 },
                date_begin: "2024-01-15T10:30:00Z",
                date_end: "2024-12-31T23:59:59Z"
            },
            language: { name: "English" },
            category: {
                id: 42,
                name: "Leadership Development", // FIX: This should now be mapped
                code: "LEAD-DEV" // FIX: This should now be mapped
            },
            skills: ["Leadership", "Communication"],
            slug_name: "test-course-category",
            thumbnail: { url: "https://example.com/thumbnail.jpg" },
            average_completion_time: 3600,
            credits: 2
        };
        
        const mockCourseListedInfo = {
            actions: [{ id: 1 }, { id: 2 }],
            sessions_count: 3,
            is_deleted: false,
            removed_at: null,
            category: {
                id: 42,
                name: "Leadership Development",
                code: "LEAD-DEV"
            }
        };
        
        // Test the tidyCourseData function
        const processedCourseData = tidyCourseData(mockCourseInfo, mockCourseListedInfo);
        
        console.log('📋 Processed course data:');
        console.log(`   Course_Name__c: "${processedCourseData.Course_Name__c}"`);
        console.log(`   Course_Category__c: "${processedCourseData.Course_Category__c}" ← SHOULD BE "Leadership Development"`);
        console.log(`   Course_Category_Code__c: "${processedCourseData.Course_Category_Code__c}" ← SHOULD BE "LEAD-DEV"`);
        console.log(`   Course_Type__c: "${processedCourseData.Course_Type__c}"`);
        console.log(`   Type__c: "${processedCourseData.Type__c}"`);

        // Step 2: Test with course that has no category data
        console.log('\n🔧 Testing with course that has no category data...');
        console.log('-'.repeat(50));
        
        const mockCourseInfoNoCategory = {
            ...mockCourseInfo,
            id: "TEST-NO-CATEGORY-456",
            name: "Test Course without Category",
            category: null // No category data
        };
        
        const mockCourseListedInfoNoCategory = {
            ...mockCourseListedInfo,
            category: null // No category data
        };
        
        const processedCourseDataNoCategory = tidyCourseData(mockCourseInfoNoCategory, mockCourseListedInfoNoCategory);
        
        console.log('📋 Processed course data (no category):');
        console.log(`   Course_Name__c: "${processedCourseDataNoCategory.Course_Name__c}"`);
        console.log(`   Course_Category__c: "${processedCourseDataNoCategory.Course_Category__c}" ← SHOULD BE "A" (fallback)`);
        console.log(`   Course_Category_Code__c: "${processedCourseDataNoCategory.Course_Category_Code__c}" ← SHOULD BE "A" (fallback)`);

        // Step 3: Test actual course creation with category data
        console.log('\n🔧 Testing actual course creation with category data...');
        console.log('-'.repeat(50));
        
        // Clean up any existing test course
        try {
            const existingCourse = await conn.sobject("Docebo_Course__c")
                .findOne({ Course_External_Id__c: mockCourseInfo.id });
            
            if (existingCourse) {
                await conn.sobject("Docebo_Course__c").delete(existingCourse.Id);
                console.log(`🗑️ Deleted existing test course: ${existingCourse.Id}`);
            }
        } catch (cleanupError) {
            console.log('No existing test course to clean up');
        }
        
        // Mock the doceboService.getCourseListedInfo function for testing
        const originalDoceboService = require('./platform/docebo/services');
        const mockDoceboService = {
            ...originalDoceboService,
            getCourseListedInfo: async (courseId) => {
                if (courseId === mockCourseInfo.id) {
                    return mockCourseListedInfo;
                }
                return originalDoceboService.getCourseListedInfo(courseId);
            }
        };
        
        // Temporarily replace the service
        require.cache[require.resolve('./platform/docebo/services')] = {
            exports: mockDoceboService
        };
        
        try {
            // Create the test course
            const courseId = await createNewCourse(mockCourseInfo);
            
            if (courseId) {
                console.log(`✅ Test course created successfully: ${courseId}`);
                
                // Verify the created course
                const createdCourse = await conn.sobject("Docebo_Course__c")
                    .findOne({ Id: courseId });
                
                if (createdCourse) {
                    console.log(`\n📋 COURSE CATEGORY VERIFICATION:`);
                    console.log('-'.repeat(50));
                    
                    const categorySuccess = createdCourse.Course_Category__c === "Leadership Development";
                    const categoryCodeSuccess = createdCourse.Course_Category_Code__c === "LEAD-DEV";
                    
                    console.log(`   Course_Category__c: ${categorySuccess ? '✅' : '❌'} "${createdCourse.Course_Category__c}"`);
                    console.log(`   Course_Category_Code__c: ${categoryCodeSuccess ? '✅' : '❌'} "${createdCourse.Course_Category_Code__c}"`);
                    console.log(`   Course_Name__c: "${createdCourse.Course_Name__c}"`);
                    console.log(`   Course_Type__c: "${createdCourse.Course_Type__c}"`);
                    console.log(`   Type__c: "${createdCourse.Type__c}"`);
                    
                    if (categorySuccess && categoryCodeSuccess) {
                        console.log(`\n🎉 SUCCESS! Course category mapping is working correctly!`);
                    } else {
                        console.log(`\n❌ FAILURE! Course category mapping is not working properly.`);
                        
                        // Check if it's a picklist issue
                        if (createdCourse.Course_Category__c === "A" || createdCourse.Course_Category_Code__c === "A") {
                            console.log(`\n⚠️ PICKLIST ISSUE DETECTED:`);
                            console.log(`   The fields are still using "A" values, which suggests:`);
                            console.log(`   1. The fields are restricted picklists`);
                            console.log(`   2. "Leadership Development" and "LEAD-DEV" are not valid picklist values`);
                            console.log(`   3. The Salesforce admin needs to add these values to the picklist`);
                            console.log(`   4. OR the fields need to be changed from picklist to text fields`);
                        }
                    }
                    
                } else {
                    console.log('❌ Created course not found');
                }
            } else {
                console.log('❌ Course creation failed');
            }
        } catch (createError) {
            console.error('❌ Error creating test course:', createError);
        }

        // Step 4: Summary and recommendations
        console.log('\n📊 COURSE CATEGORY MAPPING FIXES SUMMARY:');
        console.log('=' .repeat(50));
        
        console.log('🔧 FIXES IMPLEMENTED:');
        console.log('✅ Updated createNewCourse to use actual category data');
        console.log('✅ Added tidyCourseData function for batch processing');
        console.log('✅ Added fallback to "A" for picklist requirements');
        console.log('✅ Added logging for category mapping debugging');
        
        console.log('\n💡 CURRENT STATUS:');
        console.log('✅ Code now attempts to map real category data');
        console.log('⚠️ Success depends on Salesforce field configuration');
        
        console.log('\n🔧 NEXT STEPS IF CATEGORIES STILL SHOW "A":');
        console.log('1. Check if Course_Category__c and Course_Category_Code__c are picklist fields');
        console.log('2. If picklist, add all possible Docebo category values to Salesforce');
        console.log('3. OR change fields from picklist to text to allow any values');
        console.log('4. Test with a course that has category data in Docebo');

        // Step 5: Check existing courses to see if any have real category data now
        console.log('\n🔍 CHECKING EXISTING COURSES FOR CATEGORY DATA:');
        console.log('-'.repeat(50));
        
        const recentCourses = await conn.sobject("Docebo_Course__c")
            .find({}, ['Course_Category__c', 'Course_Category_Code__c', 'Course_Name__c', 'CreatedDate'])
            .sort({ CreatedDate: -1 })
            .limit(5)
            .execute();
        
        if (recentCourses.length > 0) {
            console.log('📋 Recent courses category data:');
            recentCourses.forEach(course => {
                const hasRealCategory = course.Course_Category__c && course.Course_Category__c !== "A" && course.Course_Category__c !== "null";
                const hasRealCategoryCode = course.Course_Category_Code__c && course.Course_Category_Code__c !== "A" && course.Course_Category_Code__c !== "null";
                
                console.log(`   • ${course.Course_Name__c}:`);
                console.log(`     Category: ${hasRealCategory ? '✅' : '❌'} "${course.Course_Category__c}"`);
                console.log(`     Category Code: ${hasRealCategoryCode ? '✅' : '❌'} "${course.Course_Category_Code__c}"`);
            });
        }

    } catch (error) {
        console.error('💥 Error in course category mapping test:', error);
    }
}

// Execute the test
console.log('🔄 Starting fixed course category mapping test...');
testFixedCourseCategoryMapping()
    .then(() => {
        console.log('\n✅ Test completed');
        process.exit(0);
    })
    .catch(err => {
        console.error('💥 Test failed:', err);
        process.exit(1);
    });
