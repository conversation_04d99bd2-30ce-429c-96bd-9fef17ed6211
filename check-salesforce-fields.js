require('dotenv').config();
const getConnection = require('./platform/salesforce/common/getConnection');

async function checkSalesforceFields() {
    try {
        console.log('🔍 Checking Salesforce Field Names...');
        
        const conn = await getConnection();
        
        console.log('\n📋 CHECKING LEAD OBJECT FIELDS:');
        console.log('=' .repeat(60));
        
        // Get Lead object metadata
        const leadMetadata = await conn.sobject('Lead').describe();
        
        // Look for timezone-related fields
        const timezoneFields = leadMetadata.fields.filter(field => 
            field.name.toLowerCase().includes('time') || 
            field.name.toLowerCase().includes('zone') ||
            field.name.toLowerCase().includes('timezone')
        );
        
        console.log('Timezone-related fields on Lead:');
        timezoneFields.forEach(field => {
            console.log(`   ✅ ${field.name} (${field.type}) - ${field.label}`);
        });
        
        if (timezoneFields.length === 0) {
            console.log('   ❌ No timezone-related fields found on Lead object');
        }
        
        console.log('\n📋 CHECKING DOCEBO_USERS__C OBJECT FIELDS:');
        console.log('=' .repeat(60));
        
        // Get Docebo_Users__c object metadata
        const doceboUserMetadata = await conn.sobject('Docebo_Users__c').describe();
        
        // Look for timezone-related fields
        const doceboTimezoneFields = doceboUserMetadata.fields.filter(field => 
            field.name.toLowerCase().includes('time') || 
            field.name.toLowerCase().includes('zone') ||
            field.name.toLowerCase().includes('timezone')
        );
        
        console.log('Timezone-related fields on Docebo_Users__c:');
        doceboTimezoneFields.forEach(field => {
            console.log(`   ✅ ${field.name} (${field.type}) - ${field.label}`);
        });
        
        if (doceboTimezoneFields.length === 0) {
            console.log('   ❌ No timezone-related fields found on Docebo_Users__c object');
        }
        
        // Check for other common field variations
        console.log('\n🔍 CHECKING FOR COMMON FIELD VARIATIONS:');
        console.log('=' .repeat(60));
        
        const commonFieldVariations = [
            'Time_Zone__c',
            'TimeZone__c', 
            'Timezone__c',
            'User_Timezone__c',
            'User_Time_Zone__c',
            'TZ__c',
            'GMT_Offset__c'
        ];
        
        console.log('Checking Lead object for field variations:');
        commonFieldVariations.forEach(fieldName => {
            const field = leadMetadata.fields.find(f => f.name === fieldName);
            if (field) {
                console.log(`   ✅ ${fieldName} EXISTS - ${field.label} (${field.type})`);
            } else {
                console.log(`   ❌ ${fieldName} NOT FOUND`);
            }
        });
        
        console.log('\nChecking Docebo_Users__c object for field variations:');
        commonFieldVariations.forEach(fieldName => {
            const field = doceboUserMetadata.fields.find(f => f.name === fieldName);
            if (field) {
                console.log(`   ✅ ${fieldName} EXISTS - ${field.label} (${field.type})`);
            } else {
                console.log(`   ❌ ${fieldName} NOT FOUND`);
            }
        });
        
        // Check for language fields too
        console.log('\n🌐 CHECKING LANGUAGE FIELDS:');
        console.log('=' .repeat(50));
        
        const languageFields = leadMetadata.fields.filter(field => 
            field.name.toLowerCase().includes('lang') || 
            field.name.toLowerCase().includes('language')
        );
        
        console.log('Language-related fields on Lead:');
        languageFields.forEach(field => {
            console.log(`   ✅ ${field.name} (${field.type}) - ${field.label}`);
        });
        
        const doceboLanguageFields = doceboUserMetadata.fields.filter(field => 
            field.name.toLowerCase().includes('lang') || 
            field.name.toLowerCase().includes('language')
        );
        
        console.log('Language-related fields on Docebo_Users__c:');
        doceboLanguageFields.forEach(field => {
            console.log(`   ✅ ${field.name} (${field.type}) - ${field.label}`);
        });
        
        console.log('\n💡 RECOMMENDATIONS:');
        console.log('=' .repeat(50));
        
        // Find the correct field names
        const correctLeadTimezoneField = leadMetadata.fields.find(f => f.name === 'Time_Zone__c');
        const correctDoceboTimezoneField = doceboUserMetadata.fields.find(f => f.name === 'TimeZone__c');
        
        if (correctLeadTimezoneField) {
            console.log(`✅ Use "${correctLeadTimezoneField.name}" for Lead timezone field`);
        } else {
            console.log('❌ Time_Zone__c field not found on Lead - may need to be created');
        }
        
        if (correctDoceboTimezoneField) {
            console.log(`✅ Use "${correctDoceboTimezoneField.name}" for Docebo_Users__c timezone field`);
        } else {
            console.log('❌ TimeZone__c field not found on Docebo_Users__c - may need to be created');
        }
        
        return {
            leadTimezoneField: correctLeadTimezoneField?.name,
            doceboTimezoneField: correctDoceboTimezoneField?.name,
            leadLanguageFields: languageFields.map(f => f.name),
            doceboLanguageFields: doceboLanguageFields.map(f => f.name)
        };
        
    } catch (error) {
        console.error('💥 Error checking Salesforce fields:', error);
        return null;
    }
}

// Execute the check
console.log('🔄 Starting Salesforce field check...');
checkSalesforceFields()
    .then((result) => {
        if (result) {
            console.log('\n✅ Salesforce field check completed!');
            console.log('\n📊 SUMMARY:');
            console.log(`Lead timezone field: ${result.leadTimezoneField || 'NOT FOUND'}`);
            console.log(`Docebo timezone field: ${result.doceboTimezoneField || 'NOT FOUND'}`);
            console.log(`Lead language fields: ${result.leadLanguageFields.join(', ') || 'NONE'}`);
            console.log(`Docebo language fields: ${result.doceboLanguageFields.join(', ') || 'NONE'}`);
        } else {
            console.log('\n⚠️ Field check failed');
        }
        process.exit(0);
    })
    .catch(err => {
        console.error('💥 Field check failed:', err);
        process.exit(1);
    });
