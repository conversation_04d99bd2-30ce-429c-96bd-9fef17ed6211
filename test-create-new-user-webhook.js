require('dotenv').config();
const { createNewUser } = require('./platform/salesforce/users/createUser');

// Test creating a new user with realistic webhook data
async function testCreateNewUserWebhook() {
    console.log('🧪 Testing Create New User with Webhook Data...');
    console.log('=' .repeat(80));
    
    // Realistic webhook data structure from Docebo
    const webhookUserData = {
        user_data: {
            user_id: "99999", // Use a test ID that likely doesn't exist
            first_name: "<PERSON>",
            last_name: "<PERSON>",
            email: "<EMAIL>", // Use a test email
            username: "sarah_johnson_test",
            level: "User",
            manager_username: "manager_test",
            email_validation_status: "1",
            valid: "1",
            timezone: "America/Chicago",
            language: "English",
            phone: "******-TEST-789"
        },
        additional_fields: [
            {
                id: "8", // Job Title
                enabled: true,
                value: "Senior Marketing Manager",
                type: "textfield"
            },
            {
                id: "9", // Role Type
                enabled: true,
                value: "3", // Management
                options: [
                    { id: "1", label: "Administrative" },
                    { id: "2", label: "Technical" },
                    { id: "3", label: "Management" },
                    { id: "4", label: "Executive" }
                ]
            },
            {
                id: "10", // Employment Type
                enabled: true,
                value: "Full-Time",
                type: "textfield"
            },
            {
                id: "12", // Race
                enabled: true,
                value: "1", // White
                options: [
                    { id: "1", label: "White" },
                    { id: "2", label: "Black" },
                    { id: "3", label: "Asian" },
                    { id: "4", label: "Hispanic" }
                ]
            },
            {
                id: "13", // Gender
                enabled: true,
                value: "2", // Female
                options: [
                    { id: "1", label: "Male" },
                    { id: "2", label: "Female" },
                    { id: "3", label: "Non-binary" }
                ]
            },
            {
                id: "14", // Organization
                enabled: true,
                value: "TechCorp Solutions Inc",
                type: "textfield"
            },
            {
                id: "15", // Backbone Partner
                enabled: true,
                value: "Yes",
                type: "textfield"
            },
            {
                id: "16", // Back Partner Type
                enabled: true,
                value: "Strategic Partner",
                type: "textfield"
            },
            {
                id: "20", // Initiative
                enabled: true,
                value: "Digital Transformation",
                type: "textfield"
            },
            {
                id: "21", // National/Regional/Local
                enabled: true,
                value: "Regional",
                type: "textfield"
            },
            {
                id: "22", // Organization Headquarters
                enabled: true,
                value: "Chicago, IL",
                type: "textfield"
            },
            {
                id: "24", // Mailing City
                enabled: true,
                value: "Chicago",
                type: "textfield"
            },
            {
                id: "25", // Mailing State
                enabled: true,
                value: "3", // Illinois
                options: [
                    { id: "1", label: "New York" },
                    { id: "2", label: "California" },
                    { id: "3", label: "Illinois" },
                    { id: "4", label: "Texas" }
                ]
            },
            {
                id: "26", // Fax
                enabled: true,
                value: "******-TEST-FAX",
                type: "textfield"
            },
            {
                id: "27", // Salutation
                enabled: true,
                value: "Ms.",
                type: "textfield"
            },
            {
                id: "28", // Mailing Country
                enabled: true,
                value: "United States",
                type: "textfield"
            },
            {
                id: "29", // Mailing Postal Code
                enabled: true,
                value: "60601",
                type: "textfield"
            },
            {
                id: "30", // Mailing Street
                enabled: true,
                value: "123 Michigan Avenue, Suite 500",
                type: "textfield"
            }
        ],
        branches: [
            {
                name: "Marketing Department",
                path: "/Corporate/Marketing",
                codes: "MKT001"
            }
        ],
        fired_at: new Date().toISOString(),
        expiration_date: null
    };

    const webhookUserListedInfo = {
        last_access_date: new Date(Date.now() - 24 * 60 * 60 * 1000).toISOString() // Yesterday
    };

    console.log('\n📋 WEBHOOK USER DATA:');
    console.log('=' .repeat(60));
    console.log(`👤 Name: ${webhookUserData.user_data.first_name} ${webhookUserData.user_data.last_name}`);
    console.log(`📧 Email: ${webhookUserData.user_data.email}`);
    console.log(`🆔 User ID: ${webhookUserData.user_data.user_id}`);
    console.log(`📞 Phone: ${webhookUserData.user_data.phone}`);
    console.log(`🌐 Language: ${webhookUserData.user_data.language}`);
    console.log(`🕐 Timezone: ${webhookUserData.user_data.timezone}`);
    console.log(`🏢 Organization: TechCorp Solutions Inc`);
    console.log(`💼 Job Title: Senior Marketing Manager`);
    console.log(`👥 Role Type: Management`);
    console.log(`⚧ Gender: Female`);
    console.log(`🌍 Race: White`);
    console.log(`🏠 Address: 123 Michigan Avenue, Suite 500, Chicago, IL 60601, United States`);

    console.log('\n🎯 EXPECTED WEBHOOK BEHAVIOR:');
    console.log('=' .repeat(60));
    console.log('1. ✅ Check if Docebo_Users__c exists (should NOT exist for test user)');
    console.log('2. ✅ Check if Contact exists by email (should NOT exist for test email)');
    console.log('3. ✅ Create new Lead with ALL field mappings');
    console.log('4. ✅ Create new Docebo_Users__c record');
    console.log('5. ✅ All fields should be populated with actual data');

    try {
        console.log('\n🚀 EXECUTING WEBHOOK - CREATING NEW USER...');
        console.log('=' .repeat(60));
        
        // Execute the webhook function
        const result = await createNewUser(webhookUserData, webhookUserListedInfo);
        
        if (result) {
            console.log('\n🎉 WEBHOOK EXECUTION SUCCESSFUL!');
            console.log('=' .repeat(60));
            
            console.log('\n📊 WHAT SHOULD HAVE BEEN CREATED:');
            console.log('✅ NEW LEAD RECORD with:');
            console.log('   ├─ 👤 Name: Sarah Johnson');
            console.log('   ├─ 📧 Email: <EMAIL>');
            console.log('   ├─ 🏢 Company: TechCorp Solutions Inc');
            console.log('   ├─ 💼 Title: Senior Marketing Manager');
            console.log('   ├─ 📞 Phone: ******-TEST-789');
            console.log('   ├─ 🌐 Languages: English');
            console.log('   ├─ 🕐 TimeZone: America/Chicago');
            console.log('   ├─ 👥 Role_Type__c: Operations/Business Management (mapped from Management)');
            console.log('   ├─ ⚧ Gender__c: Female');
            console.log('   ├─ 🌍 Race__c: White');
            console.log('   ├─ 🏠 MailingStreet__c: 123 Michigan Avenue, Suite 500');
            console.log('   ├─ 🏙️ MailingCity__c: Chicago');
            console.log('   ├─ 🗺️ MailingState__c: Illinois');
            console.log('   ├─ 📮 MailingPostalCode__c: 60601');
            console.log('   ├─ 🌎 MailingCountry__c: United States');
            console.log('   ├─ 📠 Fax: ******-TEST-FAX');
            console.log('   ├─ 👋 Salutation: Ms.');
            console.log('   └─ 🎯 Position_Role__c: Operations/Business Management');
            
            console.log('\n✅ NEW DOCEBO_USERS__C RECORD with:');
            console.log('   ├─ 🆔 User_Unique_Id__c: 99999');
            console.log('   ├─ 📧 Email__c: <EMAIL>');
            console.log('   ├─ 🏢 Organization_Name__c: TechCorp Solutions Inc');
            console.log('   ├─ 💼 Job_Title__c: Senior Marketing Manager');
            console.log('   ├─ 🌿 Branch_Name__c: Marketing Department');
            console.log('   ├─ 🎯 Initiative__c: Digital Transformation');
            console.log('   └─ 📍 National_Regional_or_Local__c: Regional');

            console.log('\n🔗 NEXT STEPS:');
            console.log('=' .repeat(60));
            console.log('1. 🔍 Check Salesforce for the new Lead record');
            console.log('2. 🔍 Check Salesforce for the new Docebo_Users__c record');
            console.log('3. ✅ Verify all field mappings are populated correctly');
            console.log('4. ✅ Confirm no "newUser is not defined" errors occurred');
            
            return { 
                success: true, 
                message: 'New user created successfully via webhook',
                userId: webhookUserData.user_data.user_id,
                email: webhookUserData.user_data.email
            };
            
        } else {
            console.log('\n❌ WEBHOOK EXECUTION FAILED');
            console.log('Check the error logs above for details');
            return { 
                success: false, 
                message: 'Webhook execution failed',
                userId: webhookUserData.user_data.user_id
            };
        }
        
    } catch (error) {
        console.error('\n💥 WEBHOOK EXECUTION ERROR:', error);
        console.error('Stack trace:', error.stack);
        return { 
            success: false, 
            error: error.message,
            userId: webhookUserData.user_data.user_id
        };
    }
}

// Execute the test
console.log('🔄 Starting webhook user creation test...');
console.log('⚠️  This will create actual records in Salesforce');
console.log('⚠️  Make sure your Salesforce connection is configured correctly');

testCreateNewUserWebhook()
    .then((result) => {
        console.log('\n📋 FINAL TEST SUMMARY:');
        console.log('=' .repeat(80));
        
        if (result.success) {
            console.log('🎉 ✅ WEBHOOK USER CREATION TEST PASSED!');
            console.log(`✅ User ID ${result.userId} processed successfully`);
            console.log(`✅ Email: ${result.email}`);
            console.log('✅ Lead and Docebo_Users__c records should be created');
            console.log('✅ All field mappings should be populated');
            console.log('✅ No variable scope errors occurred');
            
            console.log('\n🚀 WEBHOOK IS PRODUCTION READY!');
            
        } else {
            console.log('❌ WEBHOOK USER CREATION TEST FAILED');
            console.log(`❌ User ID ${result.userId} failed to process`);
            if (result.error) {
                console.log(`❌ Error: ${result.error}`);
            }
            console.log('Please check the logs above and fix any issues');
        }
        
        process.exit(0);
    })
    .catch(err => {
        console.error('💥 Test execution failed:', err);
        process.exit(1);
    });
