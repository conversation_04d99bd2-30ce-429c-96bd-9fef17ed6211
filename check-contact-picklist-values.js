require('dotenv').config();
const getConnection = require('./platform/salesforce/common/getConnection');

async function checkContactPicklistValues() {
    try {
        console.log('🔍 Checking Contact Picklist Values');
        console.log('=' .repeat(70));
        console.log('🎯 INVESTIGATING:');
        console.log('   Type__c field restricted picklist values');
        console.log('   Error: "bad value for restricted picklist field: Customer"');
        console.log('=' .repeat(70));
        
        const conn = await getConnection();
        if (!conn || !conn.accessToken) {
            console.error("❌ Invalid Salesforce connection");
            return;
        }

        // Step 1: Describe the Contact object to get field details
        console.log('\n📋 Step 1: Getting Contact object field details...');
        
        const contactDescription = await conn.sobject("Contact").describe();
        
        // Find the Type__c field
        const typeField = contactDescription.fields.find(field => field.name === 'Type__c');
        
        if (typeField) {
            console.log('\n📊 Type__c Field Details:');
            console.log(`   Label: ${typeField.label}`);
            console.log(`   Type: ${typeField.type}`);
            console.log(`   Required: ${typeField.nillable ? 'No' : 'Yes'}`);
            console.log(`   Restricted: ${typeField.restrictedPicklist ? 'Yes' : 'No'}`);
            console.log(`   Updateable: ${typeField.updateable ? 'Yes' : 'No'}`);
            console.log(`   Createable: ${typeField.createable ? 'Yes' : 'No'}`);
            
            if (typeField.picklistValues && typeField.picklistValues.length > 0) {
                console.log('\n📋 Valid Picklist Values:');
                for (let i = 0; i < typeField.picklistValues.length; i++) {
                    const value = typeField.picklistValues[i];
                    console.log(`   ${i + 1}. "${value.value}" (${value.label}) - Active: ${value.active}`);
                }
                
                // Check if "Customer" is in the list
                const customerValue = typeField.picklistValues.find(v => 
                    v.value.toLowerCase() === 'customer' || v.label.toLowerCase() === 'customer'
                );
                
                if (customerValue) {
                    console.log(`\n✅ "Customer" value found: "${customerValue.value}" (Active: ${customerValue.active})`);
                } else {
                    console.log('\n❌ "Customer" value NOT found in picklist');
                    console.log('   This explains the INVALID_OR_NULL_FOR_RESTRICTED_PICKLIST error');
                }
                
            } else {
                console.log('\n⚠️ No picklist values found (field might not be a picklist)');
            }
            
        } else {
            console.log('\n❌ Type__c field not found on Contact object');
        }

        // Step 2: Check other common picklist fields that might cause issues
        console.log('\n🔍 Step 2: Checking other Contact picklist fields...');
        
        const picklistFields = contactDescription.fields.filter(field => 
            field.type === 'picklist' && field.restrictedPicklist
        );
        
        console.log(`📊 Total restricted picklist fields: ${picklistFields.length}`);
        
        for (const field of picklistFields) {
            console.log(`\n📋 ${field.name} (${field.label}):`);
            if (field.picklistValues && field.picklistValues.length > 0) {
                const activeValues = field.picklistValues.filter(v => v.active);
                console.log(`   Active values: ${activeValues.length}`);
                if (activeValues.length <= 10) {
                    // Show all values if 10 or fewer
                    for (const value of activeValues) {
                        console.log(`     • "${value.value}"`);
                    }
                } else {
                    // Show first 5 values if more than 10
                    for (let i = 0; i < 5; i++) {
                        console.log(`     • "${activeValues[i].value}"`);
                    }
                    console.log(`     ... and ${activeValues.length - 5} more`);
                }
            }
        }

        // Step 3: Check what values are currently used in production
        console.log('\n🔍 Step 3: Checking current Type__c values in production...');
        
        if (typeField) {
            try {
                // Get unique Type__c values currently in use
                const contacts = await conn.sobject("Contact")
                    .find({ Type__c: { $ne: null } })
                    .limit(1000)
                    .execute();
                
                const uniqueValues = [...new Set(contacts.map(c => c.Type__c).filter(v => v))];
                
                console.log(`📊 Contacts with Type__c values: ${contacts.length}`);
                console.log(`📊 Unique Type__c values in use: ${uniqueValues.length}`);
                
                if (uniqueValues.length > 0) {
                    console.log('\n📋 Current Type__c values in production:');
                    for (const value of uniqueValues.sort()) {
                        const count = contacts.filter(c => c.Type__c === value).length;
                        console.log(`   • "${value}" (${count} contacts)`);
                    }
                }
                
            } catch (queryError) {
                console.log(`⚠️ Could not query Type__c values: ${queryError.message}`);
            }
        }

        // Step 4: Recommendations
        console.log('\n💡 Step 4: Recommendations...');
        
        if (typeField && typeField.picklistValues) {
            const activeValues = typeField.picklistValues.filter(v => v.active);
            
            console.log('\n🔧 IMMEDIATE FIXES:');
            console.log('   1. Update Contact creation code to use valid Type__c values');
            console.log('   2. Choose from these valid options:');
            
            if (activeValues.length > 0) {
                // Suggest the most appropriate values
                const suggestedValues = activeValues.slice(0, 3);
                for (const value of suggestedValues) {
                    console.log(`      • "${value.value}" (${value.label})`);
                }
                
                console.log('\n📝 CODE UPDATE NEEDED:');
                console.log('   Replace this line in Contact creation:');
                console.log('   Type__c: "Customer"');
                console.log('   With one of these:');
                for (const value of suggestedValues) {
                    console.log(`   Type__c: "${value.value}"`);
                }
            }
            
            console.log('\n🛡️ PREVENTION:');
            console.log('   • Always check picklist values before using them in code');
            console.log('   • Use Salesforce Setup > Object Manager > Contact > Fields to verify values');
            console.log('   • Consider making picklist fields optional if appropriate');
            console.log('   • Add validation in code to handle invalid picklist values gracefully');
        }

        return {
            success: true,
            typeFieldExists: !!typeField,
            validValues: typeField ? typeField.picklistValues?.filter(v => v.active).map(v => v.value) : [],
            isRestrictedPicklist: typeField ? typeField.restrictedPicklist : false
        };

    } catch (error) {
        console.error('💥 Error checking Contact picklist values:', error);
        return {
            success: false,
            error: error.message
        };
    }
}

// Execute the check
console.log('🔄 Starting Contact picklist values check...');
checkContactPicklistValues()
    .then((result) => {
        console.log('\n✅ Contact picklist values check completed');
        if (result.success) {
            if (result.typeFieldExists) {
                if (result.isRestrictedPicklist) {
                    console.log('🚨 RESTRICTED PICKLIST CONFIRMED');
                    console.log(`📋 Valid values: ${result.validValues.join(', ')}`);
                    console.log('🔧 Code update needed to use valid values');
                } else {
                    console.log('✅ Type__c field is not restricted');
                }
            } else {
                console.log('⚠️ Type__c field not found');
            }
        } else {
            console.log('❌ Check failed. See error details above.');
        }
        process.exit(0);
    })
    .catch(err => {
        console.error('💥 Check failed:', err);
        process.exit(1);
    });
