const getConnection = require("../common/getConnection");
const doceboService = require("../../docebo/services");
const APP_BASE = process.env.DOCEBO_API_BASE_URL;

async function deleteAllCourses() {
    const conn = await getConnection();

    if (!conn.accessToken) {
        console.error("No valid Salesforce connection.");
        return false;
    }

    try {
        // Fetch all course IDs from Salesforce
        const courses = await conn.sobject("Docebo_Course__c")
            .find({}, "Id")
            .execute();

        if (courses.length === 0) {
            console.log("No courses found to delete.");
            return true;
        }

        console.log(`Found ${courses.length} courses to delete.`);

        // Extract course IDs
        const courseIds = courses.map(course => course.Id);
        const result = await conn.sobject("Docebo_Course__c").destroy(courseIds);
        result.forEach((res, index) => {
            if (res.success) {
                console.log(`Course ${courseIds[index]} deleted successfully.`);
            } else {
                console.error(`Failed to delete course ${courseIds[index]}:`, res.errors);
            }
        });
        return true;
    } catch (err) {
        console.error("Error deleting courses:", err);
        return false;
    }
}

module.exports = {
    deleteAllCourses
};