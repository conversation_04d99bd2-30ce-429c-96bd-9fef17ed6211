
const getConnection = require("./getConnection");
module.exports = async function getSalesForceUserId(userId) {
    console.log("====== Get SalesForce User ID ======");
    const conn = await getConnection();
    let getRes = null;
    
    // Check if userId is valid and can be parsed as integer
    const parsedUserId = userId ? parseInt(userId, 10) : null;
    if (isNaN(parsedUserId)) {
        console.error(`Invalid user ID: ${userId} cannot be parsed as an integer`);
        return null;
    }
    
    if (conn?.accessToken) {
        try {
            const record = await conn.sobject("Docebo_Users__c")
                .findOne({ User_Unique_Id__c: parsedUserId });
                
            if (!record) {
                console.log(`User doesn't exist in Salesforce: ${userId}`);
                return null;
            }
            
            console.log(`SalesForce User found. ID: ${record.Id}`);
            return record.Id;
        } catch (err) {
            console.error("Error finding record:", err);
            return null;
        }
    }
    return getRes;
}