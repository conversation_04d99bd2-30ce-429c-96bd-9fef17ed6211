require('dotenv').config();
const getConnection = require('./platform/salesforce/common/getConnection');

async function fixLeadCreationIssues() {
    try {
        console.log('🔧 FIXING LEAD CREATION FIELD_FILTER_VALIDATION_EXCEPTION ISSUES');
        console.log('=' .repeat(80));
        
        const conn = await getConnection();
        if (!conn || !conn.accessToken) {
            throw new Error('Failed to connect to Salesforce');
        }

        console.log('✅ Connected to Salesforce successfully');

        // Step 1: Get users without Lead associations
        console.log('\n👥 STEP 1: Finding Users Without Lead Associations...');
        console.log('-'.repeat(50));
        
        const usersWithoutLeads = await conn.sobject("Docebo_Users__c")
            .find({ Lead__c: null })
            .sort({ CreatedDate: -1 })
            .limit(20)
            .execute();
            
        console.log(`Found ${usersWithoutLeads.length} users without Lead associations`);

        if (usersWithoutLeads.length === 0) {
            console.log('✅ All users have Lead associations');
            return { success: true, leadsCreated: 0 };
        }

        // Step 2: Analyze Lead field requirements
        console.log('\n🔍 STEP 2: Analyzing Lead Field Requirements...');
        console.log('-'.repeat(50));
        
        const leadDescription = await conn.sobject("Lead").describe();
        const requiredFields = leadDescription.fields.filter(field => 
            !field.nillable && !field.defaultedOnCreate && field.createable
        );
        
        console.log('Required fields for Lead creation:');
        requiredFields.forEach(field => {
            console.log(`  - ${field.name} (${field.type})`);
        });

        // Step 3: Test Lead creation with different field combinations
        console.log('\n🧪 STEP 3: Testing Lead Creation Strategies...');
        console.log('-'.repeat(50));
        
        const testUser = usersWithoutLeads[0];
        console.log(`Testing with user: ${testUser.First_Name__c} ${testUser.Last_Name__c} (${testUser.User_Unique_Id__c})`);

        // Strategy 1: Minimal required fields only
        console.log('\n📝 Strategy 1: Minimal Required Fields...');
        const minimalLeadData = {
            FirstName: testUser.First_Name__c || 'Unknown',
            LastName: testUser.Last_Name__c || 'User',
            Company: testUser.Organization_Name__c || 'Unknown Organization',
            Status: 'Open - Not Contacted'
        };

        let strategy1Success = false;
        try {
            const result1 = await conn.sobject("Lead").create(minimalLeadData);
            if (result1.success) {
                console.log('✅ Strategy 1 SUCCESS - Minimal fields work');
                strategy1Success = true;
                // Clean up
                await conn.sobject("Lead").delete(result1.id);
            }
        } catch (error1) {
            console.log(`❌ Strategy 1 FAILED: ${error1.message}`);
        }

        // Strategy 2: Add LeadSource
        console.log('\n📝 Strategy 2: Adding LeadSource...');
        const leadSourceData = {
            ...minimalLeadData,
            LeadSource: 'Web'
        };

        let strategy2Success = false;
        try {
            const result2 = await conn.sobject("Lead").create(leadSourceData);
            if (result2.success) {
                console.log('✅ Strategy 2 SUCCESS - With LeadSource');
                strategy2Success = true;
                // Clean up
                await conn.sobject("Lead").delete(result2.id);
            }
        } catch (error2) {
            console.log(`❌ Strategy 2 FAILED: ${error2.message}`);
        }

        // Strategy 3: Add more standard fields
        console.log('\n📝 Strategy 3: Adding More Standard Fields...');
        const extendedLeadData = {
            ...leadSourceData,
            Email: testUser.Email__c || null,
            Phone: testUser.Phone__c || null,
            Industry: 'Education',
            Rating: 'Warm'
        };

        let strategy3Success = false;
        try {
            const result3 = await conn.sobject("Lead").create(extendedLeadData);
            if (result3.success) {
                console.log('✅ Strategy 3 SUCCESS - With extended fields');
                strategy3Success = true;
                // Clean up
                await conn.sobject("Lead").delete(result3.id);
            }
        } catch (error3) {
            console.log(`❌ Strategy 3 FAILED: ${error3.message}`);
            
            if (error3.message.includes('FIELD_FILTER_VALIDATION_EXCEPTION')) {
                console.log('\n🚨 FIELD_FILTER_VALIDATION_EXCEPTION detected!');
                console.log('   This indicates lookup filter restrictions');
                
                // Try to identify which field is causing the issue
                console.log('\n🔍 Analyzing field values that might cause filter issues:');
                Object.keys(extendedLeadData).forEach(key => {
                    console.log(`   ${key}: "${extendedLeadData[key]}"`);
                });
            }
        }

        // Step 4: Determine best strategy and create Leads
        console.log('\n💾 STEP 4: Creating Leads with Best Strategy...');
        console.log('-'.repeat(50));
        
        let bestStrategy = null;
        let bestData = null;
        
        if (strategy1Success) {
            bestStrategy = 'Minimal Fields';
            bestData = minimalLeadData;
        } else if (strategy2Success) {
            bestStrategy = 'With LeadSource';
            bestData = leadSourceData;
        } else if (strategy3Success) {
            bestStrategy = 'Extended Fields';
            bestData = extendedLeadData;
        }
        
        if (!bestStrategy) {
            console.log('❌ No working strategy found - Lead creation is blocked by field filters');
            return {
                success: false,
                error: 'All Lead creation strategies failed due to field validation'
            };
        }
        
        console.log(`✅ Using strategy: ${bestStrategy}`);
        
        let successCount = 0;
        let errorCount = 0;
        
        // Create Leads for users without them (limit to 5 for safety)
        for (const user of usersWithoutLeads.slice(0, 5)) {
            try {
                const leadData = {
                    FirstName: user.First_Name__c || 'Unknown',
                    LastName: user.Last_Name__c || 'User',
                    Company: user.Organization_Name__c || 'Unknown Organization',
                    Email: user.Email__c || null,
                    Status: 'Open - Not Contacted'
                };
                
                // Add fields based on best strategy
                if (bestStrategy !== 'Minimal Fields') {
                    leadData.LeadSource = 'Docebo API';
                }
                
                const leadResult = await conn.sobject("Lead").create(leadData);
                
                if (leadResult.success) {
                    // Associate the Lead with the Docebo User
                    await conn.sobject("Docebo_Users__c").update({
                        Id: user.Id,
                        Lead__c: leadResult.id
                    });
                    
                    successCount++;
                    console.log(`  ✅ Created Lead for ${user.First_Name__c} ${user.Last_Name__c} - ID: ${leadResult.id}`);
                } else {
                    errorCount++;
                    console.log(`  ❌ Failed to create Lead for ${user.First_Name__c} ${user.Last_Name__c}: ${JSON.stringify(leadResult.errors)}`);
                }
                
            } catch (createError) {
                errorCount++;
                console.log(`  ❌ Error creating Lead for ${user.First_Name__c} ${user.Last_Name__c}: ${createError.message}`);
            }
        }

        // Step 5: Update user creation code recommendations
        console.log('\n🔄 STEP 5: Code Update Recommendations...');
        console.log('-'.repeat(50));
        
        console.log('To fix the FIELD_FILTER_VALIDATION_EXCEPTION issues:');
        console.log(`1. 🔧 Update Lead creation to use "${bestStrategy}" strategy`);
        console.log('2. 🛠️ Remove or modify lookup filters that are too restrictive');
        console.log('3. 📊 Add better error handling for field validation exceptions');
        console.log('4. 🔄 Re-enable Lead creation in the user creation process');
        
        if (bestStrategy === 'Minimal Fields') {
            console.log('\n💡 Recommended Lead creation data:');
            console.log('   - FirstName: user.first_name');
            console.log('   - LastName: user.last_name');
            console.log('   - Company: user.organization_name || "Unknown Organization"');
            console.log('   - Status: "Open - Not Contacted"');
        }

        return {
            success: true,
            leadsCreated: successCount,
            errors: errorCount,
            bestStrategy: bestStrategy,
            usersWithoutLeads: usersWithoutLeads.length
        };

    } catch (error) {
        console.error('💥 Error fixing Lead creation issues:', error);
        return {
            success: false,
            error: error.message
        };
    }
}

// Execute the fix
console.log('🔄 Starting Lead Creation Issues Fix...');
fixLeadCreationIssues()
    .then((result) => {
        console.log('\n📋 LEAD CREATION FIX SUMMARY:');
        console.log('=' .repeat(50));
        
        if (result.success) {
            console.log(`✅ Leads Created: ${result.leadsCreated}`);
            if (result.errors > 0) {
                console.log(`❌ Errors: ${result.errors}`);
            }
            if (result.bestStrategy) {
                console.log(`🎯 Best Strategy: ${result.bestStrategy}`);
            }
            console.log(`👥 Users Without Leads: ${result.usersWithoutLeads}`);
            
            console.log('\n🎉 Lead Creation Fix Completed!');
            
        } else {
            console.log(`❌ Fix failed: ${result.error}`);
        }
        
        console.log('\n✅ Fix process completed');
        process.exit(0);
    })
    .catch(err => {
        console.error('💥 Fix process failed:', err);
        process.exit(1);
    });
