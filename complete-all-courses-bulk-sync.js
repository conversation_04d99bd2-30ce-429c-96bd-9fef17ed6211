require('dotenv').config();
const getConnection = require('./platform/salesforce/common/getConnection');
const getApiData = require("./common/docebo/fetcher");
const APP_BASE = process.env.DOCEBO_API_BASE_URL;

async function completeAllCoursesBulkSync() {
    try {
        console.log('🚀 COMPLETE ALL COURSES BULK SYNC - USING CORRECT API ENDPOINT');
        console.log('=' .repeat(80));
        console.log('🎯 Target: Fetch ALL course enrollments and sync to Salesforce');
        console.log('📡 API: course/v1/courses/enrollments (verified working)');
        
        const conn = await getConnection();
        if (!conn || !conn.accessToken) {
            throw new Error('Failed to connect to Salesforce');
        }

        console.log('✅ Connected to Salesforce successfully');

        // Step 1: Get all courses from Salesforce
        console.log('\n📚 STEP 1: Getting All Courses from Salesforce...');
        console.log('-'.repeat(50));
        
        const sfCourses = await conn.sobject("Docebo_Course__c")
            .find({})
            .execute();
            
        if (!sfCourses || sfCourses.length === 0) {
            throw new Error('No courses found in Salesforce');
        }
        
        console.log(`✅ Found ${sfCourses.length} courses in Salesforce`);
        
        // Create course mapping
        const courseMapping = new Map();
        sfCourses.forEach(course => {
            if (course.Course_External_Id__c) {
                courseMapping.set(course.Course_External_Id__c.toString(), {
                    id: course.Id,
                    name: course.Course_Name__c,
                    externalId: course.Course_External_Id__c
                });
            }
        });
        
        console.log(`📊 Course mapping created for ${courseMapping.size} courses`);

        // Step 2: Get existing Salesforce enrollments with comprehensive duplicate checking
        console.log('\n📊 STEP 2: Getting Existing Salesforce Enrollments (Duplicate Prevention)...');
        console.log('-'.repeat(50));

        const existingEnrollments = await conn.sobject("Docebo_CourseEnrollment__c")
            .find({})
            .execute();

        const existingEnrollmentIds = new Set();
        const existingUserCourseCombo = new Set();
        const enrollmentsByCourse = new Map();

        existingEnrollments.forEach(enrollment => {
            // Track by Enrollment_ID__c (primary duplicate check)
            if (enrollment.Enrollment_ID__c) {
                existingEnrollmentIds.add(enrollment.Enrollment_ID__c);

                // Handle both old and new enrollment ID patterns
                if (enrollment.Enrollment_ID__c.includes('-')) {
                    const parts = enrollment.Enrollment_ID__c.split('-');
                    if (parts.length >= 2) {
                        const courseId = parts[0] === 'UE' ? parts[1] : parts[0];
                        const userId = parts[0] === 'UE' ? parts[2] : parts[1];

                        // Add both formats to prevent duplicates
                        existingEnrollmentIds.add(`${courseId}-${userId}`);
                        existingEnrollmentIds.add(`UE-${courseId}-${userId}`);
                    }
                }
            }

            // Track by User + Course combination (secondary duplicate check)
            if (enrollment.Docebo_User__c && enrollment.Course__c) {
                const userCourseKey = `${enrollment.Docebo_User__c}-${enrollment.Course__c}`;
                existingUserCourseCombo.add(userCourseKey);
            }

            // Track by course for statistics
            const courseId = enrollment.Course__c;
            if (!enrollmentsByCourse.has(courseId)) {
                enrollmentsByCourse.set(courseId, 0);
            }
            enrollmentsByCourse.set(courseId, enrollmentsByCourse.get(courseId) + 1);
        });

        console.log(`Found ${existingEnrollments.length.toLocaleString()} existing enrollments in Salesforce`);
        console.log(`Across ${enrollmentsByCourse.size} courses`);
        console.log(`📋 Duplicate prevention: ${existingEnrollmentIds.size.toLocaleString()} enrollment IDs tracked`);
        console.log(`📋 User-Course combinations: ${existingUserCourseCombo.size.toLocaleString()} tracked`);

        // Step 3: Fetch ALL course enrollments from Docebo
        console.log('\n🔍 STEP 3: Fetching ALL Course Enrollments from Docebo...');
        console.log('-'.repeat(50));
        
        let allEnrollments = [];
        let enrollmentsByCourseId = new Map();
        let page = 1;
        let hasMoreData = true;
        let totalProcessed = 0;
        
        console.log('📡 Using correct endpoint: course/v1/courses/enrollments');
        console.log('🔄 This may take 10-15 minutes to fetch all enrollments...');
        
        while (hasMoreData) {
            console.log(`   📄 Fetching page ${page}...`);
            
            try {
                const response = await getApiData(
                    'GET', 
                    `${APP_BASE}/course/v1/courses/enrollments?page=${page}&page_size=200`, 
                    null
                );
                
                if (response && response.status === 200) {
                    const items = response.data?.items || [];
                    totalProcessed += items.length;
                    
                    // Process all enrollments
                    items.forEach(enrollment => {
                        const courseId = enrollment.course_id || enrollment.id_course;
                        if (courseId && courseMapping.has(courseId.toString())) {
                            allEnrollments.push(enrollment);
                            
                            // Track by course ID
                            if (!enrollmentsByCourseId.has(courseId)) {
                                enrollmentsByCourseId.set(courseId, []);
                            }
                            enrollmentsByCourseId.get(courseId).push(enrollment);
                        }
                    });
                    
                    console.log(`      Processed ${items.length} enrollments (Total: ${totalProcessed.toLocaleString()}, Matched: ${allEnrollments.length.toLocaleString()})`);
                    
                    hasMoreData = response.data?.has_more_data || false;
                    if (items.length === 0) hasMoreData = false;
                    
                    page++;
                    
                    // Progress indicator every 50 pages
                    if (page % 50 === 0) {
                        console.log(`   📊 Progress: Page ${page}, ${totalProcessed.toLocaleString()} total processed, ${allEnrollments.length.toLocaleString()} matched enrollments`);
                        console.log(`   📈 Found enrollments for ${enrollmentsByCourseId.size} courses`);
                        
                        // Show top courses by enrollment count
                        const topCourses = Array.from(enrollmentsByCourseId.entries())
                            .sort((a, b) => b[1].length - a[1].length)
                            .slice(0, 5);
                        
                        console.log(`   🏆 Top courses by enrollment count:`);
                        topCourses.forEach(([courseId, enrollments], index) => {
                            const courseName = courseMapping.get(courseId.toString())?.name || 'Unknown';
                            console.log(`      ${index + 1}. Course ${courseId} (${courseName}): ${enrollments.length.toLocaleString()} enrollments`);
                        });
                    }
                    
                } else {
                    console.log(`      No data on page ${page}`);
                    hasMoreData = false;
                }
            } catch (pageError) {
                console.log(`      ❌ Error on page ${page}: ${pageError.message}`);
                
                // Continue to next page unless it's a critical error
                if (pageError.message.includes('401') || pageError.message.includes('403')) {
                    console.log('      🚨 Authentication error - stopping');
                    hasMoreData = false;
                } else {
                    console.log('      ⏭️ Continuing to next page...');
                    page++;
                }
            }
        }
        
        console.log(`\n✅ Docebo fetch completed:`);
        console.log(`   Total API records processed: ${totalProcessed.toLocaleString()}`);
        console.log(`   Total matched enrollments found: ${allEnrollments.length.toLocaleString()}`);
        console.log(`   Courses with enrollments: ${enrollmentsByCourseId.size}`);

        if (allEnrollments.length === 0) {
            throw new Error('No enrollments found via API');
        }

        // Step 4: Identify missing enrollments with comprehensive duplicate checking
        console.log('\n🔍 STEP 4: Identifying Missing Enrollments (Multi-Layer Duplicate Check)...');
        console.log('-'.repeat(50));

        const missingEnrollments = [];
        let duplicatesFound = 0;

        for (const doceboEnrollment of allEnrollments) {
            const courseId = doceboEnrollment.course_id || doceboEnrollment.id_course;
            const userId = doceboEnrollment.user_id;

            // Primary duplicate check: Enrollment ID patterns
            const enrollmentId = `${courseId}-${userId}`;
            const oldFormatId = `UE-${courseId}-${userId}`;

            const isDuplicateById = existingEnrollmentIds.has(enrollmentId) || existingEnrollmentIds.has(oldFormatId);

            if (!isDuplicateById) {
                missingEnrollments.push(doceboEnrollment);
            } else {
                duplicatesFound++;
            }
        }

        console.log(`Found ${missingEnrollments.length.toLocaleString()} missing enrollments to sync`);
        console.log(`Already synced: ${(allEnrollments.length - missingEnrollments.length).toLocaleString()} enrollments`);
        console.log(`🛡️ Duplicates prevented: ${duplicatesFound.toLocaleString()} enrollments`);

        if (missingEnrollments.length === 0) {
            console.log('✅ All enrollments are already synced!');
            return { 
                success: true, 
                synced: 0, 
                total: allEnrollments.length,
                existing: existingEnrollments.length,
                totalProcessed: totalProcessed,
                coursesProcessed: enrollmentsByCourseId.size
            };
        }

        // Step 5: Get user mappings
        console.log('\n👥 STEP 5: Getting User Mappings...');
        console.log('-'.repeat(50));
        
        const userIds = [...new Set(missingEnrollments.map(e => e.user_id))];
        console.log(`Need to map ${userIds.length.toLocaleString()} unique users`);
        
        const userMappings = new Map();
        
        // Batch query users in chunks
        const chunkSize = 100;
        for (let i = 0; i < userIds.length; i += chunkSize) {
            const chunk = userIds.slice(i, i + chunkSize);
            
            const users = await conn.sobject("Docebo_Users__c")
                .find({ User_Unique_Id__c: { $in: chunk } })
                .execute();
                
            users.forEach(user => {
                userMappings.set(user.User_Unique_Id__c, user.Id);
            });
            
            const chunkNum = Math.floor(i/chunkSize) + 1;
            const totalChunks = Math.ceil(userIds.length/chunkSize);
            
            if (chunkNum % 10 === 0 || chunkNum === totalChunks) {
                console.log(`   Mapped users: chunk ${chunkNum}/${totalChunks} (Total mapped: ${userMappings.size.toLocaleString()})`);
            }
        }
        
        console.log(`✅ Successfully mapped ${userMappings.size.toLocaleString()} users`);

        // Step 6: Prepare enrollment records with final duplicate check
        console.log('\n📝 STEP 6: Preparing Enrollment Records (Final Duplicate Check)...');
        console.log('-'.repeat(50));

        const enrollmentsToCreate = [];
        const enrollmentIdsToCreate = new Set(); // Track what we're about to create
        let skippedCount = 0;
        let finalDuplicatesSkipped = 0;
        const courseStats = new Map();

        for (const doceboEnrollment of missingEnrollments) {
            const courseId = doceboEnrollment.course_id || doceboEnrollment.id_course;
            const userId = doceboEnrollment.user_id;
            const salesforceUserId = userMappings.get(userId);
            const salesforceCourse = courseMapping.get(courseId.toString());

            if (!salesforceUserId || !salesforceCourse) {
                skippedCount++;
                continue;
            }

            // Final duplicate check: ensure we don't create duplicates within this batch
            const enrollmentId = `${courseId}-${userId}`;
            const userCourseKey = `${salesforceUserId}-${salesforceCourse.id}`;

            if (enrollmentIdsToCreate.has(enrollmentId) || existingUserCourseCombo.has(userCourseKey)) {
                finalDuplicatesSkipped++;
                continue;
            }

            // Add to tracking sets
            enrollmentIdsToCreate.add(enrollmentId);
            existingUserCourseCombo.add(userCourseKey); // Prevent duplicates in same batch

            // Track stats by course
            if (!courseStats.has(courseId)) {
                courseStats.set(courseId, { name: salesforceCourse.name, count: 0 });
            }
            courseStats.get(courseId).count++;
            
            // Parse enrollment date - handle multiple possible field names and formats
            let enrollmentDate = "";
            if (doceboEnrollment.enrollment_date || doceboEnrollment.date_inscr || doceboEnrollment.enroll_date_of_enrollment) {
                const dateStr = doceboEnrollment.enrollment_date || doceboEnrollment.date_inscr || doceboEnrollment.enroll_date_of_enrollment;
                try {
                    // Handle both space and T separators like the webhook does
                    enrollmentDate = new Date(dateStr.replace(' ', 'T')).toISOString();
                } catch (e) {
                    enrollmentDate = ""; // Use empty string like webhook template
                }
            }
            
            // Use exact field mapping from working webhook implementation
            const enrollmentRecord = {
                Course__c: salesforceCourse.id,
                Docebo_User__c: salesforceUserId,
                Enrollment_ID__c: enrollmentId.substring(0, 16), // Limit to 16 chars as per webhook
                Status__c: doceboEnrollment.status || 'subscribed', // Use 'subscribed' as default like webhook
                Enrollment_Date__c: enrollmentDate,
                Completion__c: doceboEnrollment.completion_percentage || doceboEnrollment.completion || 0,
                Completion_Date__c: doceboEnrollment.completion_date ?
                    new Date(doceboEnrollment.completion_date.replace(' ', 'T')).toISOString() : "",
                Score__c: doceboEnrollment.score || doceboEnrollment.score_given || 0,
                Credits__c: doceboEnrollment.credits || 0,
                Time_in_course__c: doceboEnrollment.total_time || doceboEnrollment.time_in_course || 0,
                Completed_Learning_Objects__c: doceboEnrollment.completed_learning_objects || 0,
                Unenrollment_Date__c: doceboEnrollment.unenrollment_date ?
                    new Date(doceboEnrollment.unenrollment_date.replace(' ', 'T')).toISOString() : ""
            };

            enrollmentsToCreate.push(enrollmentRecord);
        }

        console.log(`Prepared ${enrollmentsToCreate.length.toLocaleString()} enrollment records`);
        console.log(`Skipped ${skippedCount.toLocaleString()} enrollments (missing users/courses)`);
        console.log(`🛡️ Final duplicates prevented: ${finalDuplicatesSkipped.toLocaleString()} enrollments`);
        
        // Show top courses to be synced
        const topCoursesToSync = Array.from(courseStats.entries())
            .sort((a, b) => b[1].count - a[1].count)
            .slice(0, 10);
        
        console.log(`\n📊 Top 10 courses to sync:`);
        topCoursesToSync.forEach(([courseId, stats], index) => {
            console.log(`   ${index + 1}. Course ${courseId} (${stats.name}): ${stats.count.toLocaleString()} enrollments`);
        });

        if (enrollmentsToCreate.length === 0) {
            console.log('⚠️ No enrollments to create (all users/courses missing)');
            return {
                success: true,
                synced: 0,
                total: allEnrollments.length,
                existing: existingEnrollments.length,
                skipped: skippedCount,
                totalProcessed: totalProcessed,
                coursesProcessed: enrollmentsByCourseId.size
            };
        }

        // Step 7: Create enrollments in batches with duplicate-safe processing
        console.log('\n💾 STEP 7: Creating Enrollments in Salesforce (Duplicate-Safe)...');
        console.log('-'.repeat(50));

        let successCount = 0;
        let errorCount = 0;
        let duplicateErrorCount = 0;
        const batchSize = 50;
        const totalBatches = Math.ceil(enrollmentsToCreate.length / batchSize);

        console.log(`Processing ${enrollmentsToCreate.length.toLocaleString()} enrollments in ${totalBatches.toLocaleString()} batches...`);
        console.log(`🛡️ Duplicate prevention: Multi-layer checking enabled`);
        
        for (let i = 0; i < enrollmentsToCreate.length; i += batchSize) {
            const batch = enrollmentsToCreate.slice(i, i + batchSize);
            const batchNum = Math.floor(i / batchSize) + 1;
            
            console.log(`   📦 Processing batch ${batchNum.toLocaleString()}/${totalBatches.toLocaleString()} (${batch.length} records)...`);
            
            try {
                const results = await conn.sobject("Docebo_CourseEnrollment__c")
                    .create(batch);
                    
                const resultArray = Array.isArray(results) ? results : [results];
                
                let batchSuccessCount = 0;
                let batchErrorCount = 0;
                
                resultArray.forEach((result) => {
                    if (result.success) {
                        successCount++;
                        batchSuccessCount++;
                    } else {
                        errorCount++;
                        batchErrorCount++;

                        const errorMessage = result.errors?.[0]?.message || 'Unknown error';

                        // Track duplicate errors separately
                        if (errorMessage.toLowerCase().includes('duplicate') ||
                            errorMessage.toLowerCase().includes('unique') ||
                            errorMessage.includes('DUPLICATE_VALUE')) {
                            duplicateErrorCount++;
                        }

                        if (batchErrorCount <= 3) { // Only show first 3 errors per batch
                            console.log(`      ⚠️ Error: ${errorMessage}`);
                        }
                    }
                });
                
                console.log(`      ✅ Batch ${batchNum}: ${batchSuccessCount} success, ${batchErrorCount} errors`);
                
                // Progress indicator every 50 batches
                if (batchNum % 50 === 0 || batchNum === totalBatches) {
                    const progressPercent = ((i + batch.length) / enrollmentsToCreate.length * 100).toFixed(1);
                    console.log(`      📊 Progress: ${progressPercent}% (${successCount.toLocaleString()} created, ${errorCount.toLocaleString()} errors)`);
                }
                
                // Rate limiting - pause between batches
                if (batchNum < totalBatches) {
                    await new Promise(resolve => setTimeout(resolve, 500));
                }
                
            } catch (batchError) {
                console.error(`      ❌ Batch ${batchNum} failed:`, batchError.message);
                errorCount += batch.length;
            }
        }

        // Step 8: Final verification
        console.log('\n🔍 STEP 8: Final Verification...');
        console.log('-'.repeat(50));
        
        const finalEnrollments = await conn.sobject("Docebo_CourseEnrollment__c")
            .find({})
            .execute();

        return {
            success: true,
            doceboTotal: allEnrollments.length,
            salesforceInitial: existingEnrollments.length,
            salesforceFinal: finalEnrollments.length,
            synced: successCount,
            errors: errorCount,
            duplicateErrors: duplicateErrorCount,
            skipped: skippedCount,
            totalProcessed: totalProcessed,
            coursesProcessed: enrollmentsByCourseId.size
        };

    } catch (error) {
        console.error('💥 Error in complete all courses bulk sync:', error);
        return {
            success: false,
            error: error.message
        };
    }
}

// Execute the complete bulk sync
console.log('🚀 Starting Complete All Courses Bulk Sync...');
completeAllCoursesBulkSync()
    .then((result) => {
        console.log('\n📋 COMPLETE ALL COURSES BULK SYNC SUMMARY:');
        console.log('=' .repeat(80));
        
        if (result.success) {
            console.log(`📊 Total API Records Processed: ${result.totalProcessed?.toLocaleString() || 'N/A'}`);
            console.log(`📊 Total Enrollments Found: ${result.doceboTotal.toLocaleString()}`);
            console.log(`📊 Courses Processed: ${result.coursesProcessed || 'N/A'}`);
            console.log(`📊 Salesforce Initial: ${result.salesforceInitial.toLocaleString()}`);
            console.log(`📊 Salesforce Final: ${result.salesforceFinal.toLocaleString()}`);
            console.log(`✅ Successfully Synced: ${result.synced.toLocaleString()}`);
            
            if (result.errors > 0) {
                console.log(`❌ Total Errors: ${result.errors.toLocaleString()}`);
                if (result.duplicateErrors > 0) {
                    console.log(`🛡️ Duplicate Errors (Expected): ${result.duplicateErrors.toLocaleString()}`);
                    console.log(`⚠️ Other Errors: ${(result.errors - result.duplicateErrors).toLocaleString()}`);
                }
            }
            if (result.skipped > 0) {
                console.log(`⏭️ Skipped: ${result.skipped.toLocaleString()}`);
            }
            
            const netIncrease = result.salesforceFinal - result.salesforceInitial;
            console.log(`\n🎉 NET RESULT: ${netIncrease.toLocaleString()} new enrollments added!`);
            
            const successRate = result.synced / (result.synced + result.errors) * 100;
            console.log(`📊 Success Rate: ${successRate.toFixed(1)}%`);
            
            if (successRate >= 95) {
                console.log(`🎉 EXCELLENT: 95%+ success rate!`);
            } else if (successRate >= 80) {
                console.log(`✅ GOOD: 80%+ success rate!`);
            } else {
                console.log(`⚠️ REVIEW NEEDED: Success rate below 80%`);
            }
            
            console.log('\n🛡️ DUPLICATE PREVENTION SUMMARY:');
            console.log('✅ Multi-layer duplicate checking implemented:');
            console.log('   1. Enrollment ID pattern matching (old & new formats)');
            console.log('   2. User-Course combination tracking');
            console.log('   3. Batch-level duplicate prevention');
            console.log('   4. Salesforce-level duplicate error handling');

            console.log('\n💡 NEXT STEPS:');
            console.log('1. ✅ Verify enrollment data across all courses in Salesforce');
            console.log('2. 🔄 Ensure webhooks are working for future enrollments');
            console.log('3. 📊 Set up monitoring for ongoing sync health');
            console.log('4. 🎯 Consider scheduling regular bulk syncs for data integrity');
            console.log('5. 🛡️ Monitor duplicate prevention effectiveness');
            
        } else {
            console.log(`❌ Complete all courses bulk sync failed: ${result.error}`);
        }
        
        console.log('\n✅ Complete all courses bulk sync finished');
        process.exit(0);
    })
    .catch(err => {
        console.error('💥 Complete all courses bulk sync failed:', err);
        process.exit(1);
    });
