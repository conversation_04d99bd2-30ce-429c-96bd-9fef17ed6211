require('dotenv').config();
const getConnection = require('./platform/salesforce/common/getConnection');

async function checkTestContacts() {
    try {
        console.log('🔍 Checking for Test Contacts');
        console.log('=' .repeat(70));
        console.log('🎯 INVESTIGATING:');
        console.log('   Contact IDs: 0034P00004NzI5kQAF and 0034P00004NzI5lQAF');
        console.log('   Checking if these are from our testing');
        console.log('=' .repeat(70));
        
        const conn = await getConnection();
        if (!conn || !conn.accessToken) {
            console.error("❌ Invalid Salesforce connection");
            return;
        }

        const contactIds = ['0034P00004NzI5kQAF', '0034P00004NzI5lQAF'];

        // Step 1: Get details of these specific Contacts
        console.log('\n📋 Step 1: Getting Contact details...');
        
        for (let i = 0; i < contactIds.length; i++) {
            const contactId = contactIds[i];
            console.log(`\n🔍 Contact ${i + 1}: ${contactId}`);
            
            try {
                const contact = await conn.sobject("Contact").findOne({ Id: contactId });
                
                if (contact) {
                    console.log(`   Name: ${contact.FirstName} ${contact.LastName}`);
                    console.log(`   Email: ${contact.Email}`);
                    console.log(`   Created Date: ${contact.CreatedDate}`);
                    console.log(`   Created by Docebo API: ${contact.Created_by_Docebo_API__c}`);
                    console.log(`   Active Portal User: ${contact.Active_Portal_User__c}`);
                    console.log(`   Lead Source: ${contact.LeadSource}`);
                    console.log(`   Account ID: ${contact.AccountId}`);
                    console.log(`   Company: ${contact.Company__c}`);
                    console.log(`   Gateway: ${contact.Gateway__c}`);
                    
                    // Check if this looks like a test contact
                    const isTestContact = 
                        (contact.FirstName && contact.FirstName.toLowerCase().includes('test')) ||
                        (contact.LastName && contact.LastName.toLowerCase().includes('test')) ||
                        (contact.Email && contact.Email.toLowerCase().includes('test')) ||
                        (contact.Company__c && contact.Company__c.toLowerCase().includes('test')) ||
                        (contact.Gateway__c && contact.Gateway__c === 'Docebo API') ||
                        (contact.Created_by_Docebo_API__c === true && contact.LeadSource === 'Docebo Platform');
                    
                    console.log(`   Looks like test data: ${isTestContact ? '✅ YES' : '❌ NO'}`);
                    
                    // Check for associated Account
                    if (contact.AccountId) {
                        const account = await conn.sobject("Account").findOne({ Id: contact.AccountId });
                        if (account) {
                            console.log(`   Account Name: ${account.Name}`);
                            console.log(`   Account Type: ${account.Type}`);
                            
                            const isTestAccount = account.Name && account.Name.toLowerCase().includes('test');
                            console.log(`   Account looks like test: ${isTestAccount ? '✅ YES' : '❌ NO'}`);
                        }
                    }
                    
                } else {
                    console.log(`   ❌ Contact not found`);
                }
            } catch (error) {
                console.log(`   ❌ Error retrieving Contact: ${error.message}`);
            }
        }

        // Step 2: Check for other test Contacts
        console.log('\n🔍 Step 2: Looking for other test Contacts...');
        
        const testContacts = await conn.sobject("Contact")
            .find({
                $or: [
                    { FirstName: { $like: '%test%' } },
                    { LastName: { $like: '%test%' } },
                    { Email: { $like: '%test%' } },
                    { Company__c: { $like: '%test%' } },
                    { Gateway__c: 'Docebo API' }
                ]
            })
            .execute();
        
        console.log(`📊 Total test-like Contacts found: ${testContacts.length}`);
        
        if (testContacts.length > 0) {
            console.log('\n📋 Test Contacts found:');
            for (let i = 0; i < Math.min(testContacts.length, 10); i++) { // Show first 10
                const contact = testContacts[i];
                console.log(`   ${i + 1}. ${contact.Id}: ${contact.FirstName} ${contact.LastName} (${contact.Email})`);
                console.log(`      Created: ${contact.CreatedDate}`);
                console.log(`      Company: ${contact.Company__c || 'N/A'}`);
                console.log(`      Gateway: ${contact.Gateway__c || 'N/A'}`);
            }
            
            if (testContacts.length > 10) {
                console.log(`   ... and ${testContacts.length - 10} more`);
            }
        }

        // Step 3: Check for associated Docebo_Users__c records
        console.log('\n🔍 Step 3: Checking for associated Docebo_Users__c records...');
        
        for (const contactId of contactIds) {
            const doceboUsers = await conn.sobject("Docebo_Users__c")
                .find({ Contact__c: contactId })
                .execute();
            
            console.log(`\n📋 Contact ${contactId}:`);
            console.log(`   Associated Docebo_Users__c: ${doceboUsers.length}`);
            
            for (const user of doceboUsers) {
                console.log(`     User ID: ${user.Id}`);
                console.log(`     User Unique ID: ${user.User_Unique_Id__c}`);
                console.log(`     Email: ${user.Email__c}`);
                console.log(`     Name: ${user.First_Name__c} ${user.Last_Name__c}`);
            }
        }

        // Step 4: Cleanup recommendation
        console.log('\n💡 Step 4: Cleanup recommendations...');
        
        const shouldCleanup = testContacts.length > 0;
        
        if (shouldCleanup) {
            console.log('\n🧹 CLEANUP RECOMMENDED:');
            console.log(`   Found ${testContacts.length} test-like Contacts`);
            console.log('   These appear to be from our testing and should be cleaned up');
            
            console.log('\n🔧 CLEANUP ACTIONS:');
            console.log('   1. Delete test Docebo_Users__c records first');
            console.log('   2. Delete test Contacts');
            console.log('   3. Delete test Accounts (if no other Contacts use them)');
            
            // Ask if user wants to proceed with cleanup
            console.log('\n❓ Would you like to clean up test data? (This will be done manually for safety)');
            
            console.log('\n📋 MANUAL CLEANUP STEPS:');
            console.log('   1. Go to Salesforce');
            console.log('   2. Navigate to Docebo_Users__c tab');
            console.log('   3. Filter by Contact__c field containing the test Contact IDs');
            console.log('   4. Delete the Docebo_Users__c records');
            console.log('   5. Navigate to Contacts tab');
            console.log('   6. Delete the test Contacts');
            console.log('   7. Navigate to Accounts tab');
            console.log('   8. Delete any test Accounts that are no longer used');
            
        } else {
            console.log('\n✅ NO CLEANUP NEEDED:');
            console.log('   No obvious test Contacts found');
        }

        // Step 5: Prevention for future testing
        console.log('\n🛡️ Step 5: Prevention for future testing...');
        
        console.log('\n💡 FUTURE TESTING BEST PRACTICES:');
        console.log('   ✅ Use clearly identifiable test emails (e.g., <EMAIL>)');
        console.log('   ✅ Always clean up test data at the end of each test');
        console.log('   ✅ Use try/finally blocks to ensure cleanup happens even if test fails');
        console.log('   ✅ Keep a list of created test record IDs for batch cleanup');
        console.log('   ✅ Use a dedicated test organization name prefix');

        return {
            success: true,
            testContactsFound: testContacts.length,
            specificContactsExist: contactIds.map(id => 
                testContacts.some(contact => contact.Id === id)
            ),
            cleanupNeeded: shouldCleanup
        };

    } catch (error) {
        console.error('💥 Error checking test Contacts:', error);
        return {
            success: false,
            error: error.message
        };
    }
}

// Execute the check
console.log('🔄 Starting test Contacts check...');
checkTestContacts()
    .then((result) => {
        console.log('\n✅ Test Contacts check completed');
        if (result.success) {
            if (result.testContactsFound > 0) {
                console.log(`🧹 CLEANUP NEEDED: ${result.testContactsFound} test Contacts found`);
                console.log('🔧 Please clean up test data manually for safety');
            } else {
                console.log('✅ No test Contacts found');
            }
        } else {
            console.log('❌ Check failed. See error details above.');
        }
        process.exit(0);
    })
    .catch(err => {
        console.error('💥 Check failed:', err);
        process.exit(1);
    });
