# 🚀 Docebo-Salesforce Integration Tool - Technical Documentation

## 📋 Table of Contents
1. [Overview](#overview)
2. [Architecture](#architecture)
3. [Webhook Endpoints](#webhook-endpoints)
4. [API Endpoints](#api-endpoints)
5. [Services & Functions](#services--functions)
6. [Queue Management](#queue-management)
7. [Data Flow](#data-flow)
8. [Configuration](#configuration)
9. [Error Handling](#error-handling)
10. [Monitoring & Logging](#monitoring--logging)

## 🎯 Overview

The Docebo-Salesforce Integration Tool is a Node.js Express application that provides real-time synchronization between Docebo LMS and Salesforce CRM through webhooks and batch API processing.

### Key Features
- **Real-time webhook processing** for immediate data synchronization
- **Batch API processing** for historical data migration
- **Queue-based background processing** to prevent timeouts
- **Comprehensive logging** for debugging and monitoring
- **Duplicate prevention** using message queues
- **Error handling and retry mechanisms**

### Technology Stack
- **Runtime**: Node.js with Express.js
- **Database Integration**: Salesforce (jsforce), Docebo API
- **Queue Management**: In-memory queues with background processing
- **Logging**: Custom daily log files
- **HTTP Client**: Axios for API calls

## 🏗️ Architecture

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Docebo LMS    │───▶│  Integration    │───▶│   Salesforce    │
│                 │    │     Tool        │    │      CRM        │
│  - Webhooks     │    │                 │    │  - Leads        │
│  - API Calls    │    │  - Controllers  │    │  - Contacts     │
│  - User Data    │    │  - Services     │    │  - Courses      │
│  - Course Data  │    │  - Queues       │    │  - Enrollments  │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

### Core Components
- **Controllers**: Handle webhook and API requests
- **Services**: Business logic and external API interactions
- **Queues**: Background processing for complex operations
- **Common**: Shared utilities and configurations

## 🔗 Webhook Endpoints

### Base URL: `http://localhost:5000/webhook/docebo`

### 1. User Management Webhook
**Endpoint**: `POST /user/manage`
**Function**: `userManagement`
**Events Handled**:
- `user.created`
- `user.updated` 
- `user.deleted`
- `user.selfregistrationrequest.approved`

**Payload Structure**:
```json
{
  "message_id": "unique_message_id",
  "event": "user.created",
  "payloads": [
    {
      "user_id": 12345,
      "fired_at": "2024-01-01T10:00:00Z",
      "expiration_date": "2024-12-31T23:59:59Z"
    }
  ]
}
```

**Processing Logic**:
1. Prevents duplicate processing using `messageQue`
2. Fetches complete user data via `getUserInfo()` and `getUserListedInfo()`
3. Creates/updates Salesforce Lead and Docebo_Users__c records
4. Handles both single and batch payloads

### 2. Session Management Webhook
**Endpoint**: `POST /session/manage`
**Function**: `sessionManagement`
**Events Handled**:
- `ilt.session.created`
- `ilt.session.updated`
- `ilt.session.enrollment.created`
- `ilt.session.enrollment.updated`
- `ilt.session.enrollment.deleted`

**Background Processing**: Uses `sessionManagementQueue` for async processing

### 3. Learning Plan Management Webhook
**Endpoint**: `POST /lp/manage`
**Function**: `lpManagement`
**Events Handled**:
- `learningplan.enrollment.created`
- `learningplan.enrollment.deleted`
- `learningplan.enrollment.completed`
- `badge.earned`

**Background Processing**: Uses `lpManagementQueue` for async processing

### 4. Course Management Webhook
**Endpoint**: `POST /course/manage`
**Function**: `courseManagement`
**Events Handled**:
- `course.created`
- `course.updated`
- `course.properties_changed`
- `course.deleted`

**Background Processing**: Uses `courseManagementQueue` for async processing

### 5. Course Enrollment Webhooks
**Endpoints**:
- `POST /user/enrollment/created` - `courseEnrollmentCreated`
- `POST /user/enrollment/deleted` - `courseUnEnrollment`
- `POST /user/enrollment/completed` - `courseCompleted`

**Background Processing**: Uses `courseEnrollmentQueue` and `courseCompletionQueue`

### 6. Instructor Assignment Webhook
**Endpoint**: `POST /session/instructor/assigned`
**Function**: `sessionInstructorAssigned`
**Purpose**: Handles instructor assignments to course sessions

## 📡 API Endpoints

### 1. User Enrollments API
**Endpoint**: `GET /user/:userId/enrollments`
**Function**: `getUserEnrollments`
**Query Parameters**:
- `details`: boolean - Include detailed course information
- `format`: string - Response format (`summary`, `basic`, `detailed`)

**Response Formats**:
```json
{
  "success": true,
  "user_id": "12345",
  "format": "detailed",
  "total_enrollments": 5,
  "enrollments": [...]
}
```

## ⚙️ Services & Functions

### Docebo Services (`platform/docebo/services.js`)

#### Core API Functions
```javascript
// User Management
getUserInfo(userId)                    // Get detailed user information
getUserListedInfo(userId)              // Get user from listings
getTotalUserListedInfo()               // Get all users (paginated)

// Course Management  
getCourseInfo(courseId)                // Get detailed course information
getCourseListedInfo()                  // Get course listings
getTotalCourseListedInfo()             // Get all courses (paginated)

// Enrollment Management
getEnrolledInfo(courseId, userId)      // Get specific enrollment details

// Session Management
getCourseSessionInfo(sessionId)        // Get session details

// Learning Plan Management
getLearningPlan(lpId)                  // Get learning plan details
getTotalLearningPlanListedInfo()       // Get all learning plans
getTotalLearningPlanEnrollmentListedInfo() // Get all LP enrollments

// Instructor Management
getInstructorData(userId, courseId, sessionId) // Get instructor information
```

### Salesforce Services (`platform/salesforce/services.js`)

#### User Management
```javascript
createNewUser(userData, userListedInfo)    // Create/update user records
updateUser(userData, userListedInfo)       // Update existing user
deleteUser(userId)                         // Soft delete user
```

#### Course Management
```javascript
createNewCourse(courseData)                // Create/update course records
updateCourse(courseData)                   // Update existing course
deleteCourse(courseId)                     // Soft delete course
```

**Enhanced Course Field Mapping**:
- `Course_Category_Code__c` - Mapped from `category.code`
- `Course_Start_Date__c` - Mapped from `time_options.date_begin`
- `Course_End_Date__c` - Mapped from `time_options.date_end`
- `Session_Time_min__c` - Calculated from session durations or course duration
- `Enrollment_Date__c` - Mapped from `catalog_options.self_enrollment.start_date`

#### Enrollment Management
```javascript
updateCourseEnrollment(enrollmentData)     // Create/update enrollment
deleteCourseEnrollment(courseId, userId)   // Delete enrollment
saveCourseEnrollmentsInBatch(batchData)    // Batch enrollment processing
```

#### Session Management
```javascript
createNewSession(sessionData, courseData)  // Create ILT session
updateILTSession(sessionData, courseData)  // Update ILT session
createIltSessionEnrollment(payload)        // Create session enrollment
updateIltSessionEnrollment(payload)        // Update session enrollment
deleteIltSessionEnrollment(payload)        // Delete session enrollment
```

#### Learning Plan Management
```javascript
learningPlanEnrollment(payload)            // Create LP enrollment
learningPlanUnEnrollment(payload)          // Delete LP enrollment
learningPlanEnrollmentCompleted(payload)   // Mark LP as completed
```

## 🔄 Queue Management

### Queue Types and Processing

#### 1. Session Management Queue
- **Queue**: `sessionManagementQueue`
- **Processor**: `processSessionManagementQueue()`
- **Processing**: Sequential (one at a time)
- **Delay**: 100ms between items

#### 2. Learning Plan Queue
- **Queue**: `lpManagementQueue`
- **Processor**: `processLpManagementQueue()`
- **Processing**: Sequential (one at a time)
- **Delay**: 100ms between items

#### 3. Course Enrollment Queue
- **Queue**: `courseEnrollmentQueue`
- **Processor**: `processCourseEnrollmentQueue()`
- **Processing**: Batch processing (up to 10 items)
- **Features**: 
  - Automatic user creation if not exists
  - Batch API calls for efficiency
  - Fallback to individual processing

#### 4. Course Completion Queue
- **Queue**: `courseCompletionQueue`
- **Processor**: `processCourseCompletionQueue()`
- **Processing**: Batch processing (up to 10 items)
- **Delay**: 100ms between batches

#### 5. Course Management Queue
- **Queue**: `courseManagementQueue`
- **Processor**: `processCourseManagementQueue()`
- **Processing**: Sequential with 1000ms delay
- **Features**: Handles course CRUD operations

### Queue Processing Features
- **Duplicate Prevention**: Uses `messageQue` array to track processed messages
- **Background Processing**: Immediate HTTP response, async processing
- **Error Handling**: Individual item error handling within batches
- **Retry Logic**: Automatic queue continuation after errors
- **Logging**: Comprehensive logging for debugging

## 📊 Data Flow

### Webhook Processing Flow
```
1. Webhook Received → 2. Duplicate Check → 3. Queue Addition → 4. Immediate Response
                                                    ↓
5. Background Processing ← 4. Error Handling ← 3. API Calls ← 2. Data Validation
                ↓
6. Salesforce Update → 7. Logging → 8. Queue Continuation
```

### API Processing Flow
```
1. API Request → 2. Parameter Validation → 3. Docebo API Call → 4. Data Processing
                                                    ↓
5. Response Formatting ← 4. Error Handling ← 3. Data Transformation ← 2. Result Return
```

## ⚙️ Configuration

### Environment Variables
```bash
# Docebo API Configuration
DOCEBO_API_BASE_URL=https://your-domain.docebosaas.com/api
DOCEBO_CLIENT_ID=your_client_id
DOCEBO_CLIENT_SECRET=your_client_secret
DOCEBO_USERNAME=your_username
DOCEBO_PASSWORD=your_password

# Salesforce Configuration
SALESFORCE_LOGIN_URL=https://login.salesforce.com
SALESFORCE_USERNAME=your_username
SALESFORCE_PASSWORD=your_password
SALESFORCE_SECURITY_TOKEN=your_security_token

# Server Configuration
PORT=5000
NODE_ENV=production
```

### Package Dependencies
```json
{
  "dependencies": {
    "express": "^4.21.2",
    "axios": "^1.7.9",
    "jsforce": "^3.6.3",
    "cors": "^2.8.5",
    "dotenv": "^16.4.7",
    "uuid": "^11.0.5"
  }
}
```

### Application Structure
```
docebo_salesforce/
├── app.js                          # Main application entry point
├── package.json                    # Dependencies and scripts
├── platform/
│   ├── docebo/
│   │   ├── controller.js           # Webhook controllers
│   │   ├── router.js              # Route definitions
│   │   ├── services.js            # Docebo API services
│   │   └── getUserEnrollments.js  # Enrollment utilities
│   ├── salesforce/
│   │   ├── services.js            # Salesforce services
│   │   ├── users/                 # User management
│   │   ├── courses/               # Course management
│   │   ├── courseEnrollment/      # Enrollment management
│   │   ├── session/               # Session management
│   │   └── LearningPlan/          # Learning plan management
│   └── global.js                  # Global services and cron jobs
├── common/
│   └── docebo/
│       └── fetcher.js             # HTTP client for Docebo API
└── utils/
    └── logger.js                  # Logging utilities
```

## 🚨 Error Handling

### Webhook Error Handling
- **Duplicate Prevention**: Message ID tracking prevents duplicate processing
- **Graceful Degradation**: Individual item failures don't stop batch processing
- **Comprehensive Logging**: All errors logged with context
- **Queue Continuation**: Processing continues after errors

### API Error Handling
- **Connection Validation**: Salesforce connection checked before operations
- **Data Validation**: Input parameters validated before processing
- **Timeout Handling**: HTTP timeouts configured for external API calls
- **Fallback Mechanisms**: Batch processing falls back to individual processing

### Common Error Scenarios
1. **Salesforce Connection Failures**: Automatic reconnection attempts
2. **Docebo API Rate Limits**: Built-in retry mechanisms
3. **Data Validation Errors**: Detailed error logging and graceful handling
4. **Queue Processing Errors**: Individual item error isolation

## 📊 Monitoring & Logging

### Logging Features
- **Daily Log Files**: Automatic log rotation by date
- **Structured Logging**: JSON format for easy parsing
- **Context-Rich Logs**: Include user IDs, course IDs, and operation details
- **Error Tracking**: Comprehensive error logging with stack traces

### Log Categories
```javascript
// Webhook Processing
console.log("📥 WEBHOOK USER DATA for User ${userId}:");
console.log("📋 WEBHOOK PAYLOAD:", JSON.stringify(payload, null, 2));

// API Responses
console.log("📡 Session API Response Keys:", Object.keys(data));
console.log("✅ New session created successfully.");

// Queue Processing
console.log(`Processing ${queueLength} queued items`);
console.log("✅ Queue processing completed");

// Error Logging
console.error("❌ Error creating session:", error);
console.error("⚠️ Duplicate webhook message ignored");
```

### Monitoring Endpoints
- **Health Check**: Server status and connection validation
- **Queue Status**: Current queue lengths and processing status
- **Log Viewing**: Built-in log viewing utilities

### Performance Metrics
- **Processing Times**: Track webhook and API response times
- **Queue Lengths**: Monitor queue buildup and processing rates
- **Success Rates**: Track successful vs failed operations
- **API Usage**: Monitor Docebo and Salesforce API call volumes

## 🔧 Deployment & Operations

### Server Startup
```bash
# Development
npm run dev

# Production
npm start

# View Logs
npm run logs
npm run logs:today
npm run logs:date
```

### Health Monitoring
- **Server Status**: Monitor HTTP server availability
- **Database Connections**: Validate Salesforce connectivity
- **Queue Processing**: Monitor background queue processing
- **API Connectivity**: Validate Docebo API accessibility

### Maintenance Tasks
- **Log Cleanup**: Automatic daily log rotation
- **Queue Monitoring**: Monitor for stuck or growing queues
- **Connection Refresh**: Periodic Salesforce token refresh
- **Data Validation**: Regular data integrity checks

## 📈 Best Practices

### Development
- **Error Handling**: Always wrap API calls in try-catch blocks
- **Logging**: Include context in all log messages
- **Validation**: Validate all input data before processing
- **Testing**: Test webhook endpoints with sample payloads

### Production
- **Monitoring**: Set up alerts for queue buildup and errors
- **Backup**: Regular backup of configuration and logs
- **Security**: Secure webhook endpoints and API credentials
- **Performance**: Monitor response times and optimize as needed

### Troubleshooting
- **Check Logs**: Review daily log files for errors
- **Validate Connections**: Test Salesforce and Docebo connectivity
- **Monitor Queues**: Check for stuck or growing queues
- **Test Webhooks**: Use sample payloads to test webhook processing
