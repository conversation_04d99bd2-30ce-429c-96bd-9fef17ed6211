const getConnection = require('./platform/salesforce/common/getConnection');

module.exports = async function completeLearningPlanEnrollmentSyncPhase2(phase1Result) {
    try {
        console.log('🚀 PHASE 2: SYNCING LEARNING PLAN ENROLLMENTS TO SALESFORCE');
        console.log('=' .repeat(80));
        
        const conn = await getConnection();
        if (!conn || !conn.accessToken) {
            throw new Error('Failed to connect to Salesforce in Phase 2');
        }

        const {
            enrollmentsToCreate,
            skippedCount
        } = phase1Result;

        // Step 7: Create learning plan enrollment records in batches
        console.log('\n💾 STEP 7: Creating Learning Plan Enrollments in Salesforce (Duplicate-Safe)...');
        console.log('-'.repeat(50));
        
        let successCount = 0;
        let errorCount = 0;
        let duplicateErrorCount = 0;
        const batchSize = 50;
        const totalBatches = Math.ceil(enrollmentsToCreate.length / batchSize);
        
        console.log(`Processing ${enrollmentsToCreate.length.toLocaleString()} learning plan enrollments in ${totalBatches.toLocaleString()} batches...`);
        console.log(`🛡️ Duplicate prevention: Multi-layer checking enabled`);
        
        for (let i = 0; i < enrollmentsToCreate.length; i += batchSize) {
            const batch = enrollmentsToCreate.slice(i, i + batchSize);
            const batchNum = Math.floor(i / batchSize) + 1;
            
            console.log(`   📦 Processing batch ${batchNum.toLocaleString()}/${totalBatches.toLocaleString()} (${batch.length} records)...`);
            
            try {
                const results = await conn.sobject("Docebo_Learning_Plan_Enrollment__c")
                    .create(batch);
                    
                const resultArray = Array.isArray(results) ? results : [results];
                
                let batchSuccessCount = 0;
                let batchErrorCount = 0;
                
                resultArray.forEach((result) => {
                    if (result.success) {
                        successCount++;
                        batchSuccessCount++;
                    } else {
                        errorCount++;
                        batchErrorCount++;
                        
                        const errorMessage = result.errors?.[0]?.message || 'Unknown error';
                        
                        // Track duplicate errors separately
                        if (errorMessage.toLowerCase().includes('duplicate') || 
                            errorMessage.toLowerCase().includes('unique') ||
                            errorMessage.includes('DUPLICATE_VALUE')) {
                            duplicateErrorCount++;
                        }
                        
                        if (batchErrorCount <= 3) { // Only show first 3 errors per batch
                            console.log(`      ⚠️ Error: ${errorMessage}`);
                        }
                    }
                });
                
                console.log(`      ✅ Batch ${batchNum}: ${batchSuccessCount} success, ${batchErrorCount} errors`);
                
                // Progress indicator every 20 batches
                if (batchNum % 20 === 0 || batchNum === totalBatches) {
                    const progressPercent = ((i + batch.length) / enrollmentsToCreate.length * 100).toFixed(1);
                    console.log(`      📊 Progress: ${progressPercent}% (${successCount.toLocaleString()} created, ${errorCount.toLocaleString()} errors)`);
                }
                
                // Rate limiting - pause between batches
                if (batchNum < totalBatches) {
                    await new Promise(resolve => setTimeout(resolve, 500));
                }
                
            } catch (batchError) {
                console.error(`      ❌ Batch ${batchNum} failed:`, batchError.message);
                errorCount += batch.length;
            }
        }

        // Step 8: Final verification
        console.log('\n🔍 STEP 8: Final Verification...');
        console.log('-'.repeat(50));
        
        const finalEnrollments = await conn.sobject("Docebo_Learning_Plan_Enrollment__c")
            .find({})
            .execute();

        return {
            success: true,
            doceboTotal: phase1Result.doceboTotal,
            salesforceInitial: phase1Result.salesforceInitial,
            salesforceFinal: finalEnrollments.length,
            synced: successCount,
            errors: errorCount,
            duplicateErrors: duplicateErrorCount,
            skipped: skippedCount,
            totalProcessed: phase1Result.totalProcessed
        };

    } catch (error) {
        console.error('💥 Error in learning plan enrollment sync phase 2:', error);
        return {
            success: false,
            error: error.message
        };
    }
};

// If this file is run directly, show completion message
if (require.main === module) {
    console.log('📋 LEARNING PLAN ENROLLMENT BULK SYNC PHASE 2 SUMMARY:');
    console.log('=' .repeat(70));
    
    const result = process.argv[2] ? JSON.parse(process.argv[2]) : {};
    
    if (result.success) {
        console.log(`📊 Total Enrollments Processed: ${result.totalProcessed?.toLocaleString() || 'N/A'}`);
        console.log(`📊 Learning Plan Enrollments Found: ${result.doceboTotal.toLocaleString()}`);
        console.log(`📊 Salesforce Initial: ${result.salesforceInitial.toLocaleString()}`);
        console.log(`📊 Salesforce Final: ${result.salesforceFinal.toLocaleString()}`);
        console.log(`✅ Successfully Synced: ${result.synced.toLocaleString()}`);
        
        if (result.errors > 0) {
            console.log(`❌ Total Errors: ${result.errors.toLocaleString()}`);
            if (result.duplicateErrors > 0) {
                console.log(`🛡️ Duplicate Errors (Expected): ${result.duplicateErrors.toLocaleString()}`);
                console.log(`⚠️ Other Errors: ${(result.errors - result.duplicateErrors).toLocaleString()}`);
            }
        }
        if (result.skipped > 0) {
            console.log(`⏭️ Skipped: ${result.skipped.toLocaleString()}`);
        }
        
        const netIncrease = result.salesforceFinal - result.salesforceInitial;
        console.log(`\n🎉 NET RESULT: ${netIncrease.toLocaleString()} new learning plan enrollments added!`);
        
        const successRate = result.synced / (result.synced + result.errors) * 100;
        console.log(`📊 Success Rate: ${successRate.toFixed(1)}%`);
        
        if (successRate >= 95) {
            console.log(`🎉 EXCELLENT: 95%+ success rate!`);
        } else if (successRate >= 80) {
            console.log(`✅ GOOD: 80%+ success rate!`);
        } else {
            console.log(`⚠️ REVIEW NEEDED: Success rate below 80%`);
        }
        
        console.log('\n🛡️ DUPLICATE PREVENTION SUMMARY:');
        console.log('✅ Multi-layer duplicate checking implemented:');
        console.log('   1. Learning Plan Enrollment ID pattern matching (LP-planId-userId)');
        console.log('   2. User-Learning Plan combination tracking');
        console.log('   3. Batch-level duplicate prevention');
        console.log('   4. Salesforce-level duplicate error handling');
        
        console.log('\n💡 NEXT STEPS:');
        console.log('1. ✅ Verify learning plan enrollment data in Salesforce');
        console.log('2. 🔄 Ensure learning plan webhooks are working for future enrollments');
        console.log('3. 📊 Set up monitoring for ongoing learning plan sync health');
        console.log('4. 🎯 Consider scheduling regular bulk syncs for data integrity');
        console.log('5. 🛡️ Monitor duplicate prevention effectiveness');
        
    } else {
        console.log(`❌ Learning plan enrollment bulk sync failed: ${result.error}`);
    }
    
    console.log('\n✅ Complete learning plan enrollment bulk sync finished');
    process.exit(0);
}
