# 🔧 Docebo Integration - Troubleshooting & FAQ

## 🚨 Common Issues and Solutions

### 1. Webhook Not Receiving Data

#### Symptoms
- No logs showing webhook received
- Docebo shows delivery failures
- 404 or connection errors

#### Troubleshooting Steps

**Check Server Status**:
```bash
# Verify server is running
curl http://localhost:5000/webhook/docebo/user/manage
```

**Verify Endpoint URLs**:
```javascript
// Check router configuration in platform/docebo/router.js
router.post("/user/manage", userManagement);
router.post("/session/manage", sessionManagement);
router.post("/course/manage", courseManagement);
```

**Check Firewall/Network**:
- Ensure port 5000 is accessible
- Verify ngrok tunnel is active (for local development)
- Check SSL certificate validity

**Solution**:
1. Restart the application: `npm start`
2. Verify webhook URLs in Docebo admin panel
3. Test with curl or Postman
4. Check application logs for startup errors

### 2. Duplicate Processing Issues

#### Symptoms
- Same webhook processed multiple times
- Duplicate records in Salesforce
- "Duplicate webhook message ignored" in logs

#### Root Cause
Docebo retries webhooks if response is slow or times out

#### Solution (Already Implemented)
```javascript
// Message deduplication in controller.js
if (!messageQue.includes(payload.message_id)) {
    messageQue.push(payload.message_id);
    // Process webhook
}
```

**Additional Steps**:
1. Monitor response times
2. Optimize background processing
3. Increase webhook timeout in Docebo if possible

### 3. Salesforce Connection Failures

#### Symptoms
- "Invalid Salesforce connection" errors
- Authentication failures
- API limit exceeded errors

#### Troubleshooting Steps

**Check Credentials**:
```bash
# Verify environment variables
echo $SALESFORCE_USERNAME
echo $SALESFORCE_LOGIN_URL
```

**Test Connection**:
```javascript
const { getConnection } = require('./platform/salesforce/common/connection');
const conn = await getConnection();
console.log('Connection status:', conn.accessToken ? 'Connected' : 'Failed');
```

**Solutions**:
1. **Invalid Credentials**: Update `.env` file with correct credentials
2. **Expired Token**: Restart application to refresh token
3. **API Limits**: Monitor API usage and implement rate limiting
4. **IP Restrictions**: Add server IP to Salesforce trusted IP ranges

### 4. User Creation Failures

#### Symptoms
- "User creation failed" in logs
- Missing required fields errors
- Picklist value errors

#### Common Causes and Solutions

**Missing Required Fields**:
```javascript
// Ensure LastName is set (required for Lead)
if (!tmpUserInfo.Last_Name__c || tmpUserInfo.Last_Name__c.trim() === "") {
    tmpUserInfo.Last_Name__c = "Unknown";
}
```

**Picklist Value Errors**:
- Check Salesforce picklist values match Docebo data
- Convert restricted picklists to text fields if needed
- Map values in `tidyData()` function

**Email Validation**:
```javascript
// Validate email format
const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
if (!emailRegex.test(userData.email)) {
    console.error('Invalid email format:', userData.email);
    return false;
}
```

### 5. Queue Processing Issues

#### Symptoms
- Queues growing without processing
- "Queue processing stuck" messages
- Background tasks not completing

#### Troubleshooting Steps

**Check Queue Status**:
```javascript
console.log('Session queue length:', sessionManagementQueue.length);
console.log('Enrollment queue length:', courseEnrollmentQueue.length);
console.log('Is processing:', isProcessingQueue);
```

**Monitor Processing Flags**:
```javascript
// Reset stuck processing flags
isProcessingQueue = false;
isProcessingSessionQueue = false;
isProcessingLpQueue = false;
```

**Solutions**:
1. Restart application to reset queue states
2. Check for infinite loops in processing functions
3. Monitor memory usage for queue buildup
4. Implement queue size limits if needed

### 6. API Rate Limiting

#### Symptoms
- "Too Many Requests" errors
- Slow response times
- API quota exceeded messages

#### Solutions

**Implement Rate Limiting**:
```javascript
const rateLimit = require('express-rate-limit');

const webhookLimiter = rateLimit({
    windowMs: 1 * 60 * 1000, // 1 minute
    max: 100, // limit each IP to 100 requests per windowMs
    message: 'Too many webhook requests'
});

app.use('/webhook', webhookLimiter);
```

**Batch Processing**:
- Use existing batch processing for enrollments
- Implement similar batching for other operations
- Add delays between API calls if needed

## ❓ Frequently Asked Questions

### Q1: How do I test webhooks locally?

**A**: Use ngrok to create a public tunnel:
```bash
# Install ngrok
npm install -g ngrok

# Start your application
npm start

# In another terminal, create tunnel
ngrok http 5000

# Use the ngrok URL in Docebo webhook configuration
https://abc123.ngrok.io/webhook/docebo/user/manage
```

### Q2: What happens if a webhook fails?

**A**: The system has multiple layers of error handling:
1. Individual item errors don't stop batch processing
2. Failed items are logged with detailed error information
3. Queues continue processing remaining items
4. Docebo will retry failed webhooks automatically

### Q3: How can I monitor webhook processing?

**A**: Several monitoring options are available:
```bash
# View real-time logs
npm run logs

# Check today's logs
npm run logs:today

# Monitor queue status
curl http://localhost:5000/status
```

### Q4: Can I replay failed webhooks?

**A**: Yes, you can manually trigger webhook processing:
```javascript
// Create test payload and send to webhook endpoint
const testPayload = {
    message_id: 'manual-test-123',
    event: 'user.created',
    payload: { user_id: 12345, fired_at: new Date().toISOString() }
};

// Send POST request to webhook endpoint
```

### Q5: How do I handle large data volumes?

**A**: The system includes several optimizations:
- **Background Processing**: Prevents webhook timeouts
- **Batch Processing**: Handles multiple items efficiently
- **Queue Management**: Processes items in manageable chunks
- **Connection Pooling**: Reuses database connections

### Q6: What fields are synchronized between Docebo and Salesforce?

**A**: Comprehensive field mapping includes:

**User Fields**:
- Personal information (name, email, phone)
- Organization details
- Role and employment information
- Demographics (race, gender identity)
- System fields (creation date, status)

**Course Fields**:
- Course metadata (name, description, type)
- Category and skill information
- Duration and credit information
- Status and availability

**Enrollment Fields**:
- Enrollment and completion dates
- Progress and score information
- Time spent and credits earned
- Status and completion percentage

### Q7: How do I add new webhook events?

**A**: Follow these steps:
1. Add event handling in the appropriate controller function
2. Update the switch statement with new event type
3. Implement processing logic
4. Add to queue processing if needed
5. Update documentation

Example:
```javascript
case 'new.event.type':
    console.log("======= New Event Type =======");
    // Add processing logic here
    break;
```

### Q8: Can I customize field mappings?

**A**: Yes, field mappings are handled in the `tidyData()` function:
```javascript
// Located in platform/salesforce/users/createUser.js
function tidyData(userInfo, userListedInfo) {
    // Customize field mappings here
    return mappedData;
}
```

### Q9: How do I handle different Docebo environments?

**A**: Use environment-specific configuration:
```bash
# Development
DOCEBO_API_BASE_URL=https://dev.docebosaas.com/api

# Staging  
DOCEBO_API_BASE_URL=https://staging.docebosaas.com/api

# Production
DOCEBO_API_BASE_URL=https://prod.docebosaas.com/api
```

### Q10: What's the difference between webhooks and batch processing?

**A**: 
- **Webhooks**: Real-time processing of individual events as they occur
- **Batch Processing**: Bulk processing of historical data or scheduled synchronization
- **Use Cases**: Webhooks for real-time sync, batch for initial data migration

## 🔍 Debugging Techniques

### Enable Verbose Logging
```javascript
// Add to controller functions
console.log('📥 Raw webhook data:', JSON.stringify(req.body, null, 2));
console.log('🔄 Processing payload:', payload);
console.log('✅ Operation completed successfully');
```

### Test Individual Components
```javascript
// Test Docebo API connection
const userInfo = await doceboService.getUserInfo(12345);
console.log('User info response:', userInfo);

// Test Salesforce connection
const conn = await getConnection();
console.log('Salesforce connected:', !!conn.accessToken);
```

### Monitor Queue Processing
```javascript
// Add queue monitoring
setInterval(() => {
    console.log('Queue Status:', {
        session: sessionManagementQueue.length,
        enrollment: courseEnrollmentQueue.length,
        completion: courseCompletionQueue.length
    });
}, 30000); // Every 30 seconds
```

## 📞 Support and Resources

### Log Files Location
```
logs/
├── app-2024-01-01.log
├── app-2024-01-02.log
└── app-2024-01-03.log
```

### Key Configuration Files
- `app.js` - Main application entry point
- `platform/docebo/controller.js` - Webhook handlers
- `platform/docebo/services.js` - Docebo API services
- `platform/salesforce/services.js` - Salesforce services
- `.env` - Environment configuration

### Useful Commands
```bash
# Start application
npm start

# Development mode with auto-restart
npm run dev

# View logs
npm run logs

# Test webhook endpoints
curl -X POST http://localhost:5000/webhook/docebo/user/manage \
  -H "Content-Type: application/json" \
  -d '{"message_id":"test","event":"user.created","payload":{"user_id":123}}'
```
