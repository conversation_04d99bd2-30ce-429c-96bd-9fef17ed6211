require('dotenv').config();
const getConnection = require('./platform/salesforce/common/getConnection');
const { createNewUser } = require('./platform/salesforce/users/createUser');

// Test data that mimics the real webhook data that was failing
const mockWebhookData = {
    user_data: {
        user_id: 99999,
        username: "<EMAIL>",
        email: "<EMAIL>",
        first_name: "Fixed",
        last_name: "Test",
        level: "user",
        phone: "******-999-0000",
        language: "en",
        timezone: "America/New_York",
        email_validation_status: "1",
        valid: "1",
        manager_username: ""
    },
    additional_fields: [
        { id: "8", value: "Test Manager", enabled: true }, // Job Title
        { id: "9", value: "Communications", enabled: true }, // Role Type
        { id: "10", value: "Full-Time", enabled: true }, // Employment Type
        { id: "12", value: "Other", enabled: true }, // Race Identity
        { id: "13", value: "Prefer not to say", enabled: true }, // Gender Identity - this was causing the error
        { id: "14", value: "Test Organization", enabled: true }, // Organization Name
        { id: "23", value: "https://testorg.com", enabled: true }, // Organization URL
        { id: "24", value: "Test City", enabled: true }, // Mailing City
        { 
            id: "25", 
            value: "CA", 
            enabled: true,
            options: [
                { id: "CA", label: "California" },
                { id: "NY", label: "New York" },
                { id: "TX", label: "Texas" }
            ]
        }, // Mailing State
        { id: "28", value: "United States", enabled: true }, // Mailing Country
        { id: "29", value: "90210", enabled: true }, // Mailing Postal Code
        { id: "30", value: "123 Test Street", enabled: true } // Mailing Street
    ],
    branches: [
        {
            name: "Test Branch",
            path: "/test/branch",
            codes: "123"
        }
    ],
    fired_at: "2025-06-07 17:05:40"
};

const mockUserListedInfo = {
    last_access_date: "2025-06-07T17:00:00Z"
};

async function testFixedLeadCreation() {
    try {
        console.log('🧪 Testing Fixed Lead Creation');
        console.log('=' .repeat(60));
        
        const conn = await getConnection();
        if (!conn || !conn.accessToken) {
            console.error("❌ Invalid Salesforce connection");
            return;
        }

        // Clean up any existing test records first
        console.log('\n🧹 Cleaning up existing test records...');
        try {
            // Clean up Docebo_Users__c
            const existingUsers = await conn.sobject("Docebo_Users__c")
                .find({ Email__c: mockWebhookData.user_data.email })
                .execute();
            
            for (const user of existingUsers) {
                await conn.sobject("Docebo_Users__c").delete(user.Id);
                console.log(`   🗑️ Deleted existing Docebo_Users__c: ${user.Id}`);
            }

            // Clean up Leads
            const existingLeads = await conn.sobject("Lead")
                .find({ Email: mockWebhookData.user_data.email })
                .execute();
            
            for (const lead of existingLeads) {
                await conn.sobject("Lead").delete(lead.Id);
                console.log(`   🗑️ Deleted existing Lead: ${lead.Id}`);
            }

            // Clean up Contacts
            const existingContacts = await conn.sobject("Contact")
                .find({ Email: mockWebhookData.user_data.email })
                .execute();
            
            for (const contact of existingContacts) {
                await conn.sobject("Contact").delete(contact.Id);
                console.log(`   🗑️ Deleted existing Contact: ${contact.Id}`);
            }

        } catch (cleanupError) {
            console.log('   No existing records to clean up or cleanup failed:', cleanupError.message);
        }

        console.log('\n🚀 Creating user with webhook data that was previously failing...');
        
        // Test the createNewUser function with the mock data
        const result = await createNewUser(mockWebhookData, mockUserListedInfo);
        
        if (result) {
            console.log('\n✅ User creation completed successfully!');
            
            // Verify what was created
            console.log('\n🔍 Verifying created records...');
            
            // Check Docebo_Users__c record
            const createdUser = await conn.sobject("Docebo_Users__c")
                .findOne({ Email__c: mockWebhookData.user_data.email });
            
            if (createdUser) {
                console.log(`✅ Docebo_Users__c created: ${createdUser.Id}`);
                console.log(`   Name: ${createdUser.First_Name__c} ${createdUser.Last_Name__c}`);
                console.log(`   Email: ${createdUser.Email__c}`);
                console.log(`   User ID: ${createdUser.User_Unique_Id__c}`);
                console.log(`   Organization: ${createdUser.Organization_Name__c}`);
                console.log(`   Role Type: ${createdUser.Role_Type__c}`);
                console.log(`   Gender Identity: ${createdUser.Gender_Identity__c}`);
                console.log(`   Race Identity: ${createdUser.Race_Identity__c}`);
            } else {
                console.log('❌ No Docebo_Users__c record found');
            }

            // Check Lead record
            const createdLead = await conn.sobject("Lead")
                .findOne({ Email: mockWebhookData.user_data.email });
            
            if (createdLead) {
                console.log(`\n✅ Lead created: ${createdLead.Id}`);
                console.log(`   Name: ${createdLead.FirstName} ${createdLead.LastName}`);
                console.log(`   Company: ${createdLead.Company}`);
                console.log(`   Status: ${createdLead.Status}`);
                console.log(`   Title: ${createdLead.Title}`);
                console.log(`   Gender: ${createdLead.Gender__c}`);
                console.log(`   Role Type: ${createdLead.Role_Type__c}`);
                console.log(`   Race: ${createdLead.Race__c}`);
                console.log(`   Lead Source: ${createdLead.LeadSource}`);
                console.log(`   Created by Docebo API: ${createdLead.Created_by_Docebo_API__c}`);
                
                console.log('\n🎯 SUCCESS! Lead created with corrected field mappings!');
                console.log('   ✅ Gender field now uses correct Salesforce values');
                console.log('   ✅ No more "Prefer Not To Say" errors');
                console.log('   ✅ Removed non-existent fields from Lead object');
                
            } else {
                console.log('❌ No Lead record found');
            }

        } else {
            console.log('❌ User creation failed');
        }

    } catch (error) {
        console.error('💥 Error in test:', error);
        
        if (error.errorCode === 'INVALID_OR_NULL_FOR_RESTRICTED_PICKLIST') {
            console.log('\n💡 Picklist Error Details:');
            console.log(`   Field: ${error.fields ? error.fields.join(', ') : 'Unknown'}`);
            console.log(`   Message: ${error.message}`);
            console.log('   This indicates the field mapping still needs adjustment.');
        }
    }
}

// Execute the test
console.log('🔄 Starting Fixed Lead Creation Test...');
testFixedLeadCreation()
    .then(() => {
        console.log('\n✅ Test completed');
        process.exit(0);
    })
    .catch(err => {
        console.error('💥 Test failed:', err);
        process.exit(1);
    });
