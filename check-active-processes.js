require('dotenv').config();

async function checkActiveProcesses() {
    try {
        console.log('🔍 Checking for Active Processes That Might Cause Contact Errors');
        console.log('=' .repeat(80));
        console.log('🎯 INVESTIGATING:');
        console.log('   Any running batch processes that might be updating Contacts');
        console.log('   Historical data update processes');
        console.log('   Webhook processes');
        console.log('   Background synchronization jobs');
        console.log('=' .repeat(80));
        
        // Check if any batch processes are currently running
        console.log('\n📋 Step 1: Checking for running Node.js processes...');
        
        const { exec } = require('child_process');
        const util = require('util');
        const execPromise = util.promisify(exec);
        
        try {
            // Check for running Node.js processes (Windows)
            const { stdout } = await execPromise('tasklist /FI "IMAGENAME eq node.exe" /FO CSV');
            const lines = stdout.split('\n').filter(line => line.includes('node.exe'));
            
            console.log(`📊 Found ${lines.length} Node.js processes running:`);
            
            if (lines.length > 0) {
                for (let i = 0; i < lines.length; i++) {
                    const line = lines[i];
                    const parts = line.split(',');
                    if (parts.length >= 2) {
                        const pid = parts[1].replace(/"/g, '');
                        const memory = parts[4] ? parts[4].replace(/"/g, '') : 'Unknown';
                        console.log(`   Process ${i + 1}: PID ${pid}, Memory: ${memory}`);
                    }
                }
                
                console.log('\n⚠️ MULTIPLE NODE.JS PROCESSES DETECTED!');
                console.log('One of these might be running a batch process that\'s causing Contact errors.');
                console.log('\n🔧 RECOMMENDED ACTIONS:');
                console.log('1. Check if any batch sync processes are running');
                console.log('2. Stop any historical data update processes');
                console.log('3. Stop any user batch sync processes');
                console.log('4. Restart only the main webhook server');
                
            } else {
                console.log('✅ No additional Node.js processes found');
            }
            
        } catch (processError) {
            console.log('⚠️ Could not check running processes (might be on non-Windows system)');
            console.log('Please manually check for any running batch processes');
        }
        
        // Check for log files that might indicate active processes
        console.log('\n📋 Step 2: Checking recent log activity...');
        
        const fs = require('fs');
        const path = require('path');
        
        try {
            const logsDir = './logs';
            if (fs.existsSync(logsDir)) {
                const logFiles = fs.readdirSync(logsDir)
                    .filter(file => file.endsWith('.log'))
                    .sort()
                    .reverse(); // Most recent first
                
                console.log(`📊 Found ${logFiles.length} log files:`);
                
                if (logFiles.length > 0) {
                    // Check the most recent log file
                    const recentLogFile = path.join(logsDir, logFiles[0]);
                    const logContent = fs.readFileSync(recentLogFile, 'utf8');
                    const logLines = logContent.split('\n');
                    
                    // Get last 10 lines
                    const recentLines = logLines.slice(-10).filter(line => line.trim());
                    
                    console.log(`\n📄 Recent activity in ${logFiles[0]}:`);
                    for (const line of recentLines) {
                        if (line.includes('Contact') || line.includes('First_Name__c') || line.includes('batch') || line.includes('historical')) {
                            console.log(`   🔍 ${line}`);
                        }
                    }
                    
                    // Check for specific error patterns
                    const contactErrors = logLines.filter(line => 
                        line.includes('First_Name__c') || 
                        line.includes('Contact association workaround') ||
                        line.includes('No such column')
                    );
                    
                    if (contactErrors.length > 0) {
                        console.log(`\n🚨 FOUND ${contactErrors.length} CONTACT-RELATED ERRORS:`);
                        const recentErrors = contactErrors.slice(-5); // Last 5 errors
                        for (const error of recentErrors) {
                            console.log(`   ❌ ${error.trim()}`);
                        }
                        
                        console.log('\n🔧 IMMEDIATE ACTION REQUIRED:');
                        console.log('   These errors confirm that Contact operations are still running');
                        console.log('   The Contact workaround has been disabled but something is still calling it');
                    }
                }
            } else {
                console.log('⚠️ No logs directory found');
            }
            
        } catch (logError) {
            console.log('⚠️ Could not check log files:', logError.message);
        }
        
        // Check for common batch process files
        console.log('\n📋 Step 3: Checking for batch process files...');
        
        const batchFiles = [
            'run-historical-data-update.js',
            'run-user-batch-sync.js',
            'run-user-batch-sync-streamlined.js',
            'test-historical-data-update.js',
            'test-first-10-users-update.js',
            'test-only-10-users.js'
        ];
        
        console.log('📊 Batch process files that could cause Contact errors:');
        for (const file of batchFiles) {
            if (fs.existsSync(file)) {
                console.log(`   ✅ ${file} - EXISTS (could be running)`);
            } else {
                console.log(`   ⚪ ${file} - Not found`);
            }
        }
        
        console.log('\n💡 Step 4: Recommendations...');
        console.log('=' .repeat(50));
        
        console.log('\n🛑 TO STOP CONTACT ERRORS IMMEDIATELY:');
        console.log('1. Kill any running batch processes:');
        console.log('   • Press Ctrl+C in any terminal running batch sync');
        console.log('   • Stop any historical data update processes');
        console.log('   • Stop any test processes');
        
        console.log('\n✅ WHAT WE\'VE FIXED:');
        console.log('   • Contact workaround disabled in createUser.js');
        console.log('   • Contact update disabled in historicalDataUpdate.js');
        console.log('   • All Contact operations commented out');
        
        console.log('\n🚀 TO RESTART CLEANLY:');
        console.log('1. Stop ALL Node.js processes');
        console.log('2. Start only the main webhook server: npm start');
        console.log('3. Test with a single webhook to verify no Contact errors');
        
        console.log('\n🔍 MONITORING:');
        console.log('   Watch the logs for any new "First_Name__c" errors');
        console.log('   If errors continue, there might be another entry point we missed');
        
        return {
            success: true,
            message: 'Process check completed'
        };

    } catch (error) {
        console.error('💥 Error checking active processes:', error);
        return {
            success: false,
            error: error.message
        };
    }
}

// Execute the check
console.log('🔄 Starting active processes check...');
checkActiveProcesses()
    .then((result) => {
        console.log('\n✅ Active processes check completed');
        if (result.success) {
            console.log('🎯 Check the recommendations above to stop Contact errors');
        } else {
            console.log('❌ Check failed. See error details above.');
        }
        process.exit(0);
    })
    .catch(err => {
        console.error('💥 Check failed:', err);
        process.exit(1);
    });
