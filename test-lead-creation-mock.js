require('dotenv').config();
const getConnection = require('./platform/salesforce/common/getConnection');

// Helper functions to map Docebo values to valid Salesforce picklist values
function mapGenderToValidValue(doceboGender) {
    if (!doceboGender) return "";

    const genderMapping = {
        "Male": "Man",
        "Female": "Woman",
        "Man": "Man",
        "Woman": "Woman",
        "Non-binary": "Non-Binary or other gender identity",
        "Non-Binary": "Non-Binary or other gender identity",
        "Prefer not to say": "Prefer not to respond",
        "Prefer Not To Say": "Prefer not to respond"
    };

    return genderMapping[doceboGender] || "Prefer not to respond";
}

function mapRoleTypeToValidValue(doceboRoleType) {
    if (!doceboRoleType) return "";

    const roleMapping = {
        "Management": "Operations/Business Management",
        "Technical": "Programs",
        "Administrative": "Administrative",
        "Executive": "Executive Director",
        "Communications": "Communications",
        "Data": "Data and Research",
        "Policy": "Policy/Government",
        "Community": "Community Engagement/Organizing",
        "Fundraising": "Fundraising/Development",
        "Board": "Board of Directors"
    };

    return roleMapping[doceboRoleType] || "Other";
}

function mapRaceToValidValue(doceboRace) {
    if (!doceboRace) return "";

    const raceMapping = {
        "Asian": "Asian",
        "Black": "Black or African American",
        "African American": "Black or African American",
        "White": "White",
        "Hispanic": "Hispanic or Latine",
        "Latino": "Hispanic or Latine",
        "Latina": "Hispanic or Latine",
        "Latine": "Hispanic or Latine",
        "Native American": "American Indian or Alaskan Native",
        "Pacific Islander": "Native Hawaiian or Other Pacific Islander",
        "Multi-Racial": "Multi-Racial",
        "Mixed": "Multi-Racial",
        "Prefer not to say": "Prefer not to respond",
        "Prefer not to respond": "Prefer not to respond"
    };

    return raceMapping[doceboRace] || "Other";
}

// Mock data that mimics the failing webhook data from the logs
const mockFailingData = {
    FirstName: "eqwdsa",
    LastName: "ewqdas",
    Email: "<EMAIL>",
    Company: "Test Company",
    Title: "Test Title",
    Website: "https://test.com",
    Status: "Open - Not Contacted",

    // These are the problematic fields from the logs
    Created_by_Docebo_API__c: true,
    Gender__c: "Prefer Not To Say", // This is causing the error - invalid value
    Role_Type__c: "Communications",
    Employment_Type__c: "Full-Time",
    Race__c: "Other",
    Description: "Test user from Docebo",
    Fax: "",
    Salutation: "",
    Phone: "",
    Languages__c: "English",
    MailingCity__c: "dasdas",
    MailingCountry__c: "United States",
    MailingPostalCode__c: "12345",
    MailingState__c: "Delaware",
    MailingStreet__c: "123 Test St",
    Position_Role__c: "Communications",
    // REMOVED: These fields don't exist on Lead object
    // Annual_Revenue__c: 0,
    // Industry__c: "",
    // NumberOfEmployees__c: 0,
    // Rating__c: "",
    LeadSource: "Docebo Platform"
    // REMOVED: TimeZone field doesn't exist on Lead object
};

// Mock data with corrected values
const mockCorrectedData = {
    FirstName: "John",
    LastName: "Doe",
    Email: "<EMAIL>",
    Company: "Test Company",
    Title: "Test Manager",
    Website: "https://testcompany.com",
    Status: "Open - Not Contacted",

    // Corrected fields using proper mapping functions
    Created_by_Docebo_API__c: true,
    Gender__c: mapGenderToValidValue("Male"), // Should map to "Man"
    Role_Type__c: mapRoleTypeToValidValue("Communications"), // Should map to "Communications"
    Employment_Type__c: "Full-Time",
    Race__c: mapRaceToValidValue("White"), // Should map to "White"
    Description: "Test user from Docebo - corrected mapping",
    Fax: "",
    Salutation: "",
    Phone: "******-123-4567",
    Languages__c: "English",
    MailingCity__c: "Test City",
    MailingCountry__c: "United States",
    MailingPostalCode__c: "12345",
    MailingState__c: "California",
    MailingStreet__c: "123 Test Street",
    Position_Role__c: mapRoleTypeToValidValue("Communications"),
    // REMOVED: These fields don't exist on Lead object
    // Annual_Revenue__c: 1000000,
    // Industry__c: "Technology",
    // NumberOfEmployees__c: 50,
    // Rating__c: "Hot",
    LeadSource: "Docebo Platform"
    // REMOVED: TimeZone field doesn't exist on Lead object
};

// Mock data with only core fields that definitely exist
const mockCoreFieldsData = {
    FirstName: "Jane",
    LastName: "Smith",
    Email: "<EMAIL>",
    Company: "Core Test Company",
    Title: "Core Test Manager",
    Status: "Open - Not Contacted",

    // Only use fields we know exist
    Gender__c: mapGenderToValidValue("Female"), // Should map to "Woman"
    Role_Type__c: mapRoleTypeToValidValue("Communications"), // Should map to "Communications"
    Race__c: mapRaceToValidValue("Asian"), // Should map to "Asian"
    LeadSource: "Docebo Platform"
};

async function testLeadCreation() {
    try {
        console.log('🧪 Testing Lead Creation with Mock Data');
        console.log('=' .repeat(60));
        
        const conn = await getConnection();
        if (!conn || !conn.accessToken) {
            console.error("❌ Invalid Salesforce connection");
            return;
        }

        // First, let's check what picklist values are actually valid
        console.log('\n🔍 Step 1: Checking valid picklist values...');
        await checkPicklistValues(conn);

        // Test 1: Try to create lead with the failing data (should fail)
        console.log('\n❌ Step 2: Testing with FAILING data (from logs)...');
        await testLeadCreationWithData(conn, mockFailingData, "FAILING");

        // Test 2: Try to create lead with corrected data (should succeed)
        console.log('\n✅ Step 3: Testing with CORRECTED data...');
        await testLeadCreationWithData(conn, mockCorrectedData, "CORRECTED");

        // Test 3: Try to create lead with only core fields (should definitely succeed)
        console.log('\n🎯 Step 4: Testing with CORE FIELDS ONLY...');
        await testLeadCreationWithData(conn, mockCoreFieldsData, "CORE_FIELDS");

    } catch (error) {
        console.error('💥 Error in test:', error);
    }
}

async function checkPicklistValues(conn) {
    try {
        const leadDescription = await conn.sobject("Lead").describe();
        
        const picklistFields = ['Gender__c', 'Role_Type__c', 'Race__c', 'Status'];
        
        for (const fieldName of picklistFields) {
            const field = leadDescription.fields.find(f => f.name === fieldName);
            if (field && field.type === 'picklist') {
                console.log(`\n📋 ${fieldName} valid values:`);
                field.picklistValues.forEach(value => {
                    console.log(`   - "${value.value}" ${value.active ? '(active)' : '(inactive)'}`);
                });
            } else if (field) {
                console.log(`\n📋 ${fieldName}: ${field.type} field`);
            } else {
                console.log(`\n❌ ${fieldName}: Field not found`);
            }
        }
    } catch (error) {
        console.error('Error checking picklist values:', error.message);
    }
}

async function testLeadCreationWithData(conn, leadData, testType) {
    try {
        console.log(`\n🚀 Creating lead with ${testType} data...`);
        
        // Clean up any existing test records first
        try {
            const existingLeads = await conn.sobject("Lead")
                .find({ Email: leadData.Email })
                .execute();
            
            for (const lead of existingLeads) {
                await conn.sobject("Lead").delete(lead.Id);
                console.log(`   🗑️ Deleted existing lead: ${lead.Id}`);
            }
        } catch (cleanupError) {
            console.log('   No existing records to clean up');
        }

        // Show the data we're trying to create
        console.log('\n📝 Lead data being sent:');
        Object.keys(leadData).forEach(key => {
            const value = leadData[key];
            if (value !== "" && value !== 0 && value !== null && value !== undefined) {
                console.log(`   ${key}: "${value}"`);
            }
        });

        // Attempt to create the lead
        const result = await conn.sobject("Lead").create(leadData);
        
        if (result.success) {
            console.log(`\n✅ ${testType} Lead created successfully!`);
            console.log(`   Lead ID: ${result.id}`);
            
            // Verify the created lead
            const createdLead = await conn.sobject("Lead").findOne({ Id: result.id });
            console.log(`   Verified: ${createdLead.FirstName} ${createdLead.LastName}`);
            console.log(`   Email: ${createdLead.Email}`);
            console.log(`   Gender: ${createdLead.Gender__c}`);
            console.log(`   Role Type: ${createdLead.Role_Type__c}`);
            console.log(`   Race: ${createdLead.Race__c}`);
            
        } else {
            console.log(`\n❌ ${testType} Lead creation failed:`);
            console.log('   Errors:', JSON.stringify(result.errors, null, 2));
        }
        
    } catch (error) {
        console.log(`\n❌ ${testType} Lead creation error:`);
        console.log('   Error:', JSON.stringify(error, null, 2));
        
        if (error.errorCode === 'INVALID_OR_NULL_FOR_RESTRICTED_PICKLIST') {
            console.log('\n💡 This is a picklist validation error!');
            console.log(`   Field: ${error.fields ? error.fields.join(', ') : 'Unknown'}`);
            console.log(`   Message: ${error.message}`);
        }
    }
}

// Execute the test
console.log('🔄 Starting Lead Creation Mock Test...');
testLeadCreation()
    .then(() => {
        console.log('\n✅ Test completed');
        process.exit(0);
    })
    .catch(err => {
        console.error('💥 Test failed:', err);
        process.exit(1);
    });
