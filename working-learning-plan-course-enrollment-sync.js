require('dotenv').config();
const getConnection = require('./platform/salesforce/common/getConnection');
const getApiData = require("./common/docebo/fetcher");
const APP_BASE = process.env.DOCEBO_API_BASE_URL;

async function workingLearningPlanCourseEnrollmentSync() {
    try {
        console.log('🚀 WORKING LEARNING PLAN COURSE ENROLLMENT SYNC');
        console.log('=' .repeat(80));
        console.log('🎯 Target: Docebo_Learning_Plan_Course_Enrollment__c object');
        console.log('📡 Strategy: Use correct field types and lookup relationships');
        
        const conn = await getConnection();
        if (!conn || !conn.accessToken) {
            throw new Error('Failed to connect to Salesforce');
        }

        console.log('✅ Connected to Salesforce successfully');

        // Step 1: Create the uidCourse to Salesforce mapping
        console.log('\n🗺️ STEP 1: Creating uidCourse to Salesforce Mapping...');
        console.log('-'.repeat(50));
        
        // Get all courses from Docebo to create ID to uidCourse mapping
        let allDoceboCourses = [];
        let page = 1;
        let hasMoreData = true;
        
        while (hasMoreData && page <= 10) {
            const response = await getApiData(
                'GET', 
                `${APP_BASE}/course/v1/courses?page=${page}&page_size=200`, 
                null
            );
            
            if (response && response.status === 200) {
                const items = response.data?.items || [];
                allDoceboCourses.push(...items);
                
                hasMoreData = response.data?.has_more_data || false;
                if (items.length === 0) hasMoreData = false;
                page++;
            } else {
                hasMoreData = false;
            }
        }
        
        console.log(`Found ${allDoceboCourses.length} courses from Docebo`);
        
        // Create numeric ID to uidCourse mapping
        const numericIdToUidMap = new Map();
        allDoceboCourses.forEach(course => {
            if (course.id && course.uidCourse) {
                numericIdToUidMap.set(course.id.toString(), course.uidCourse);
            }
        });
        
        // Get Salesforce courses and create uidCourse to Salesforce ID mapping
        const sfCourses = await conn.sobject("Docebo_Course__c")
            .find({})
            .execute();
            
        const uidToSalesforceIdMap = new Map();
        sfCourses.forEach(sfCourse => {
            const numericId = sfCourse.Course_External_Id__c?.toString();
            if (numericId && numericIdToUidMap.has(numericId)) {
                const uidCourse = numericIdToUidMap.get(numericId);
                uidToSalesforceIdMap.set(uidCourse, {
                    id: sfCourse.Id,
                    name: sfCourse.Course_Name__c,
                    externalId: sfCourse.Course_External_Id__c
                });
            }
        });
        
        console.log(`✅ Created mapping for ${uidToSalesforceIdMap.size} courses`);

        // Step 2: Get reference data with correct field types
        console.log('\n📚 STEP 2: Getting Reference Data...');
        console.log('-'.repeat(50));
        
        // Learning Plans
        const sfLearningPlans = await conn.sobject("Docebo_Learning_Plan__c")
            .find({})
            .execute();
            
        const learningPlanMapping = new Map();
        sfLearningPlans.forEach(plan => {
            if (plan.Learning_Plan_External_Id__c) {
                learningPlanMapping.set(plan.Learning_Plan_External_Id__c.toString(), {
                    id: plan.Id,
                    name: plan.Learning_Plan_Name__c,
                    externalId: plan.Learning_Plan_External_Id__c
                });
            }
        });
        
        // Users (User_Unique_Id__c is a double field, not string!)
        const sfUsers = await conn.sobject("Docebo_Users__c")
            .find({})
            .execute();
            
        const userMapping = new Map();
        sfUsers.forEach(user => {
            if (user.User_Unique_Id__c) {
                // Convert to string for mapping but store as number
                userMapping.set(user.User_Unique_Id__c.toString(), {
                    id: user.Id,
                    email: user.Email__c,
                    uniqueId: user.User_Unique_Id__c
                });
            }
        });
        
        // Learning Plan Enrollments
        const sfLearningPlanEnrollments = await conn.sobject("Docebo_Learning_Plan_Enrollment__c")
            .find({})
            .execute();
            
        const learningPlanEnrollmentMapping = new Map();
        sfLearningPlanEnrollments.forEach(enrollment => {
            if (enrollment.Learning_Plan_Id__c && enrollment.Docebo_User_Id__c) {
                const key = `${enrollment.Learning_Plan_Id__c}-${enrollment.Docebo_User_Id__c}`;
                learningPlanEnrollmentMapping.set(key, enrollment.Id);
            }
        });
        
        // Course Enrollments
        const sfCourseEnrollments = await conn.sobject("Docebo_CourseEnrollment__c")
            .find({})
            .execute();
            
        const courseEnrollmentMapping = new Map();
        sfCourseEnrollments.forEach(enrollment => {
            if (enrollment.Course__c && enrollment.Docebo_User__c) {
                const key = `${enrollment.Course__c}-${enrollment.Docebo_User__c}`;
                courseEnrollmentMapping.set(key, enrollment.Id);
            }
        });
        
        console.log(`📊 Learning Plans: ${learningPlanMapping.size}`);
        console.log(`📊 Courses: ${uidToSalesforceIdMap.size}`);
        console.log(`📊 Users: ${userMapping.size}`);
        console.log(`📊 Learning Plan Enrollments: ${learningPlanEnrollmentMapping.size}`);
        console.log(`📊 Course Enrollments: ${courseEnrollmentMapping.size}`);

        // Step 3: Get existing Learning Plan Course Enrollments
        console.log('\n📊 STEP 3: Getting Existing Learning Plan Course Enrollments...');
        console.log('-'.repeat(50));
        
        const existingEnrollments = await conn.sobject("Docebo_Learning_Plan_Course_Enrollment__c")
            .find({})
            .execute();
            
        console.log(`Found ${existingEnrollments.length} existing learning plan course enrollments`);

        // Step 4: Use the learning plan with most enrollments (from debug)
        console.log('\n🔍 STEP 4: Fetching Learning Plan Course Enrollments...');
        console.log('-'.repeat(50));
        
        // Use Learning Plan External ID 9 (which has 5,587 enrollments)
        const testLearningPlanId = "9";
        const planInfo = learningPlanMapping.get(testLearningPlanId);
        
        if (!planInfo) {
            throw new Error(`Learning Plan with External ID ${testLearningPlanId} not found`);
        }
        
        console.log(`📊 Processing Learning Plan: ${planInfo.name} (External ID: ${testLearningPlanId})`);
        
        let allLearningPlanCourseEnrollments = [];
        
        try {
            // Get first page of enrollments for this learning plan
            const response = await getApiData(
                'GET', 
                `${APP_BASE}/learn/v1/enrollments?learning_plan_id=${testLearningPlanId}&page=1&page_size=50`, 
                null
            );
            
            if (response && response.status === 200) {
                const items = response.data?.items || [];
                console.log(`   📄 Found ${items.length} enrollment items from Docebo`);
                
                // Filter for valid enrollments
                const validItems = items.filter(item => {
                    const hasUidCourse = item.uidCourse && uidToSalesforceIdMap.has(item.uidCourse);
                    const hasUser = item.user_id && userMapping.has(item.user_id.toString());
                    
                    if (!hasUidCourse) {
                        console.log(`      ⚠️ Missing course mapping for uidCourse: ${item.uidCourse}`);
                    }
                    if (!hasUser) {
                        console.log(`      ⚠️ Missing user mapping for user_id: ${item.user_id}`);
                    }
                    
                    return hasUidCourse && hasUser;
                });
                
                validItems.forEach(enrollment => {
                    enrollment.learning_plan_id = testLearningPlanId;
                    enrollment.learning_plan_name = planInfo.name;
                });
                
                allLearningPlanCourseEnrollments.push(...validItems);
                console.log(`   ✅ Found ${validItems.length} valid course enrollments`);
                
            } else {
                console.log(`   ❌ Could not fetch enrollments: ${response?.status || 'No response'}`);
            }
            
        } catch (planError) {
            console.log(`   ❌ Error processing LP ${testLearningPlanId}: ${planError.message}`);
        }
        
        console.log(`\n✅ Docebo fetch completed: ${allLearningPlanCourseEnrollments.length} course enrollments found`);

        if (allLearningPlanCourseEnrollments.length === 0) {
            console.log('⚠️ No learning plan course enrollments found');
            return {
                success: true,
                doceboTotal: 0,
                salesforceInitial: existingEnrollments.length,
                message: 'No data found'
            };
        }

        // Step 5: Prepare records with proper lookup relationships
        console.log('\n🔍 STEP 5: Preparing Records with Proper Lookups...');
        console.log('-'.repeat(50));
        
        const enrollmentsToCreate = [];
        let skippedCount = 0;
        let missingLpEnrollments = 0;
        let missingCourseEnrollments = 0;
        
        for (const doceboEnrollment of allLearningPlanCourseEnrollments) {
            const learningPlanId = doceboEnrollment.learning_plan_id;
            const courseUid = doceboEnrollment.uidCourse;
            const userId = doceboEnrollment.user_id;
            
            const salesforceLearningPlan = learningPlanMapping.get(learningPlanId.toString());
            const salesforceCourse = uidToSalesforceIdMap.get(courseUid);
            const salesforceUser = userMapping.get(userId.toString());
            
            if (!salesforceLearningPlan || !salesforceCourse || !salesforceUser) {
                skippedCount++;
                continue;
            }
            
            // Find Learning Plan Enrollment
            const lpEnrollmentKey = `${salesforceLearningPlan.id}-${salesforceUser.id}`;
            const learningPlanEnrollmentId = learningPlanEnrollmentMapping.get(lpEnrollmentKey);
            
            if (!learningPlanEnrollmentId) {
                missingLpEnrollments++;
                continue;
            }
            
            // Find Course Enrollment
            const courseEnrollmentKey = `${salesforceCourse.id}-${salesforceUser.id}`;
            const courseEnrollmentId = courseEnrollmentMapping.get(courseEnrollmentKey);
            
            if (!courseEnrollmentId) {
                missingCourseEnrollments++;
                continue;
            }
            
            // Parse dates
            let enrollmentDate = "";
            if (doceboEnrollment.enroll_date_of_enrollment) {
                try {
                    enrollmentDate = new Date(doceboEnrollment.enroll_date_of_enrollment.replace(' ', 'T')).toISOString();
                } catch (e) {
                    enrollmentDate = "";
                }
            }
            
            let completionDate = "";
            if (doceboEnrollment.course_complete_date) {
                try {
                    completionDate = new Date(doceboEnrollment.course_complete_date.replace(' ', 'T')).toISOString();
                } catch (e) {
                    completionDate = "";
                }
            }
            
            // Create record with proper lookup relationships
            const enrollmentRecord = {
                Learning_Plan_Enrollment_Id__c: learningPlanEnrollmentId,
                Course_Enrollment_Id__c: courseEnrollmentId,
                Enrollment_Date__c: enrollmentDate,
                Completion_Date__c: completionDate,
                Completion_Percentage__c: 0,
                Effective__c: true,
                Completed__c: doceboEnrollment.course_complete_date ? true : false
            };
            
            enrollmentsToCreate.push(enrollmentRecord);
        }
        
        console.log(`Prepared ${enrollmentsToCreate.length} learning plan course enrollment records`);
        console.log(`Skipped ${skippedCount} enrollments (missing references)`);
        console.log(`Missing LP Enrollments: ${missingLpEnrollments}`);
        console.log(`Missing Course Enrollments: ${missingCourseEnrollments}`);

        if (enrollmentsToCreate.length === 0) {
            console.log('⚠️ No records to create - missing required lookup relationships');
            return {
                success: true,
                doceboTotal: allLearningPlanCourseEnrollments.length,
                salesforceInitial: existingEnrollments.length,
                synced: 0,
                skipped: skippedCount,
                missingLpEnrollments: missingLpEnrollments,
                missingCourseEnrollments: missingCourseEnrollments
            };
        }

        // Step 6: Create records (limit to 10 for testing)
        console.log('\n💾 STEP 6: Creating Learning Plan Course Enrollments (TEST)...');
        console.log('-'.repeat(50));
        
        const testRecords = enrollmentsToCreate.slice(0, 10);
        console.log(`🧪 TEST MODE: Creating ${testRecords.length} records`);
        
        console.log(`🔍 Sample record structure:`);
        console.log(JSON.stringify(testRecords[0], null, 2));
        
        let successCount = 0;
        let errorCount = 0;
        
        try {
            const results = await conn.sobject("Docebo_Learning_Plan_Course_Enrollment__c")
                .create(testRecords);
                
            const resultArray = Array.isArray(results) ? results : [results];
            
            resultArray.forEach((result, index) => {
                if (result.success) {
                    successCount++;
                    console.log(`   ✅ Record ${index + 1}: Created successfully`);
                } else {
                    errorCount++;
                    const errorMessage = result.errors?.[0]?.message || 'Unknown error';
                    console.log(`   ❌ Record ${index + 1}: ${errorMessage}`);
                }
            });
            
            console.log(`\n📊 Results: ${successCount} created, ${errorCount} errors`);
            
        } catch (createError) {
            console.error(`❌ Create failed:`, createError.message);
            errorCount = testRecords.length;
        }

        // Step 7: Final verification
        console.log('\n🔍 STEP 7: Final Verification...');
        console.log('-'.repeat(50));
        
        const finalEnrollments = await conn.sobject("Docebo_Learning_Plan_Course_Enrollment__c")
            .find({})
            .execute();

        return {
            success: true,
            doceboTotal: allLearningPlanCourseEnrollments.length,
            salesforceInitial: existingEnrollments.length,
            salesforceFinal: finalEnrollments.length,
            synced: successCount,
            errors: errorCount,
            skipped: skippedCount,
            missingLpEnrollments: missingLpEnrollments,
            missingCourseEnrollments: missingCourseEnrollments,
            testMode: true
        };

    } catch (error) {
        console.error('💥 Error in working learning plan course enrollment sync:', error);
        return {
            success: false,
            error: error.message
        };
    }
}

// Execute the working sync
console.log('🔄 Starting Working Learning Plan Course Enrollment Sync...');
workingLearningPlanCourseEnrollmentSync()
    .then((result) => {
        console.log('\n📋 WORKING SYNC SUMMARY:');
        console.log('=' .repeat(50));
        
        if (result.success) {
            if (result.salesforceFinal !== undefined) {
                console.log(`📊 Total Course Enrollments Found: ${result.doceboTotal}`);
                console.log(`📊 Salesforce Initial: ${result.salesforceInitial}`);
                console.log(`📊 Salesforce Final: ${result.salesforceFinal}`);
                console.log(`✅ Successfully Synced: ${result.synced}`);
                console.log(`❌ Errors: ${result.errors}`);
                console.log(`⏭️ Skipped: ${result.skipped}`);
                
                if (result.missingLpEnrollments !== undefined) {
                    console.log(`⚠️ Missing LP Enrollments: ${result.missingLpEnrollments}`);
                }
                if (result.missingCourseEnrollments !== undefined) {
                    console.log(`⚠️ Missing Course Enrollments: ${result.missingCourseEnrollments}`);
                }
                
                const netIncrease = result.salesforceFinal - result.salesforceInitial;
                console.log(`\n🎉 NET RESULT: ${netIncrease} new learning plan course enrollments added!`);
                
                if (netIncrease > 0) {
                    console.log(`🎉 SUCCESS: Learning Plan Course Enrollment sync is working!`);
                    console.log(`🚀 Ready to run full sync for all learning plans!`);
                } else if (result.errors === 0 && result.synced === 0) {
                    console.log(`⚠️ No records created - check missing lookup relationships`);
                } else {
                    console.log(`⚠️ Issues detected - review errors and missing lookups`);
                }
            } else {
                console.log(`📋 Result: ${result.message}`);
            }
        } else {
            console.log(`❌ Sync failed: ${result.error}`);
        }
        
        console.log('\n✅ Working learning plan course enrollment sync completed');
        process.exit(0);
    })
    .catch(err => {
        console.error('💥 Working learning plan course enrollment sync failed:', err);
        process.exit(1);
    });
