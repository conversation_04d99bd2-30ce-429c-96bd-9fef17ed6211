sessions

[
    'archive_date__c',
    'archived_enrollment__c',
    'calendar_sync_url__c',
    'completion_date__c',
    'date_end__c',
    'date_start__c',
    'deleted__c',
    'deletion_date__c',
    'enrollment_date__c',
    'evaluation_status__c',
    'event_attendance_hours__c',
    'event_attendance_status__c',
    'instructor_feedback__c',
    'learner_evaluation__c',
    'salesforcedeleted',
    'salesforcelastsynctime',
    'salesforceobjectid',
    'session_attendance_type__c',
    'session_code__c',
    'session_completion_date__c',
    'session_enrollment_date__c',
    'session_external_id__c',
    'session_maximum_enrollments__c',
    'session_minimum_enrollments__c',
    'session_name__c',
    'session_unique_id',
    'session_url__c',
    'time_in_session__c',
    'user_course_level__c',
    'webinar_tool__c',
    'webinar_tool_time_in_session__c'
  ]
  
  
const sessionTemplate = {
    Archive_Date__c: "",
    Archived_Enrollment__c: "",
    Calendar_Sync_url__c: "",
    Completion_date__c: "",
    CourseId__c: "",
    Date_End__c: "",
    Date_Start__c: "",
    Deleted__c: false,
    Deletion_Date__c: "",
    Enrollment_Date__c: "",
    Evaluation_Status__c: "",
    Event_Attendance_Hours__c: 0,
    Event_Attendance_Status__c: "",
    Instructor_Feedback__c: "",
    Learner_Evaluation__c: "",
    Session_Attendance_Type__c: "",
    Session_Code__c: "",
    Session_Completion_Date__c: "",
    Session_Enrollment_Date__c: "",
    Session_External_ID__c: "",
    Session_Maximum_Enrollments__c: "",
    Session_Minimum_Enrollments__c: "",
    Session_Name__c: "",
    Name: "",
    Session_URL__c: "",
    Time_in_Session__c: "",
    User_Course_Level__c: "",
    Webinar_Tool__c: "",
    Webinar_Tool_Time_in_Session__c: 0
}