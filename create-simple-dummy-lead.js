require('dotenv').config();
const getConnection = require('./platform/salesforce/common/getConnection');

// Create lead with dummy content using ONLY safe values
async function createSimpleDummyLead() {
    try {
        console.log('🧪 Creating lead with safe dummy content...');
        
        const conn = await getConnection();
        if (!conn || !conn.accessToken) {
            console.error("❌ Invalid Salesforce connection");
            return;
        }

        // Clean up existing test records
        console.log('\n🧹 Cleaning up existing test records...');
        const testEmail = "<EMAIL>";
        
        try {
            const existingLeads = await conn.sobject("Lead")
                .find({ Email: testEmail });
            
            for (const lead of existingLeads) {
                await conn.sobject("Lead").delete(lead.Id);
                console.log(`   Deleted existing Lead: ${lead.Id}`);
            }
        } catch (cleanupError) {
            console.log('   No existing records to clean up');
        }

        // Create lead with safe dummy data (avoiding problematic picklist fields)
        console.log('\n👤 Creating lead with safe dummy content...');
        
        const leadData = {
            // Standard Lead fields with realistic data
            Company: "Cincinnati Children's Hospital",
            Email: "<EMAIL>",
            Title: "Senior Director of Community Engagement",
            FirstName: "Alexandra",
            LastName: "Rodriguez-Martinez",
            Website: "https://www.cincinnatichildrens.org",
            Status: "Open - Not Contacted",
            Description: "Senior Director focused on community engagement and health equity initiatives. Leads cross-sector partnerships to improve child health outcomes in Cincinnati region. Manages $2.5M annual budget and oversees 25+ community partnerships. Fluent in English and Spanish. Based in Cincinnati, OH.",
            Salutation: "Dr.",
            Phone: "(*************",
            LeadSource: "Docebo Platform",
            OwnerId: "005O400000BxnnxIAB",
            
            // Safe custom fields (avoiding problematic picklists)
            Created_by_Docebo_API__c: true,
            Gender__c: "Female",
            Role_Type__c: "Community Engagement/Organizing",
            Employment_Type__c: "Full-time",
            Race__c: "Hispanic or Latine",
            Languages__c: "English, Spanish",
            MailingCity__c: "Cincinnati",
            MailingCountry__c: "United States",
            MailingPostalCode__c: "45229",
            MailingState__c: "Ohio",
            MailingStreet__c: "3333 Burnet Avenue",
            Position_Role__c: "Community Engagement/Organizing",
            Active_Portal_User__c: true,
            Gateway__c: "Docebo API",
            Inactive_Contact__c: false,
            Legacy_Id__c: "55555",
            No_Longer_Leadership__c: false,
            No_Longer_Staff__c: false,
            Number_of_Years_in_the_Partnership__c: 6
            
            // EXCLUDED problematic picklist fields:
            // Contact_Type__c, FTE__c, Type__c (will add these after checking valid values)
        };

        try {
            const leadResult = await conn.sobject("Lead").create(leadData);
            
            if (leadResult.success) {
                console.log(`✅ Lead created successfully: ${leadResult.id}`);
                console.log(`🔗 Lead URL: https://strivetogether--full.sandbox.my.salesforce.com/${leadResult.id}`);
                
                // Verify the created lead
                console.log('\n🔍 Verifying created lead...');
                
                const createdLead = await conn.sobject("Lead")
                    .findOne({ Id: leadResult.id });
                
                if (createdLead) {
                    console.log('\n📋 COMPREHENSIVE LEAD WITH DUMMY CONTENT:');
                    console.log('=' .repeat(70));
                    
                    console.log('\n👤 CONTACT INFORMATION:');
                    console.log(`   Full Name: ${createdLead.Salutation || ''} ${createdLead.FirstName} ${createdLead.LastName}`);
                    console.log(`   Email: ${createdLead.Email}`);
                    console.log(`   Phone: ${createdLead.Phone || 'N/A'}`);
                    console.log(`   Languages: ${createdLead.Languages__c || 'N/A'}`);
                    
                    console.log('\n🏢 ORGANIZATION DETAILS:');
                    console.log(`   Company: ${createdLead.Company}`);
                    console.log(`   Title: ${createdLead.Title}`);
                    console.log(`   Website: ${createdLead.Website || 'N/A'}`);
                    console.log(`   Lead Source: ${createdLead.LeadSource || 'N/A'}`);
                    
                    console.log('\n📍 ADDRESS INFORMATION:');
                    console.log(`   Street: ${createdLead.MailingStreet__c || 'N/A'}`);
                    console.log(`   City: ${createdLead.MailingCity__c || 'N/A'}`);
                    console.log(`   State: ${createdLead.MailingState__c || 'N/A'}`);
                    console.log(`   Postal Code: ${createdLead.MailingPostalCode__c || 'N/A'}`);
                    console.log(`   Country: ${createdLead.MailingCountry__c || 'N/A'}`);
                    
                    console.log('\n👥 DEMOGRAPHICS & ROLE:');
                    console.log(`   Gender: ${createdLead.Gender__c || 'N/A'}`);
                    console.log(`   Race/Ethnicity: ${createdLead.Race__c || 'N/A'}`);
                    console.log(`   Role Type: ${createdLead.Role_Type__c || 'N/A'}`);
                    console.log(`   Position Role: ${createdLead.Position_Role__c || 'N/A'}`);
                    console.log(`   Employment Type: ${createdLead.Employment_Type__c || 'N/A'}`);
                    
                    console.log('\n🤝 PARTNERSHIP INFORMATION:');
                    console.log(`   Years in Partnership: ${createdLead.Number_of_Years_in_the_Partnership__c || 'N/A'}`);
                    console.log(`   Active Portal User: ${createdLead.Active_Portal_User__c || 'N/A'}`);
                    console.log(`   Leadership Status: ${createdLead.No_Longer_Leadership__c ? 'Former' : 'Current'}`);
                    console.log(`   Staff Status: ${createdLead.No_Longer_Staff__c ? 'Former' : 'Current'}`);
                    
                    console.log('\n⚙️ SYSTEM INFORMATION:');
                    console.log(`   Status: ${createdLead.Status || 'N/A'}`);
                    console.log(`   Gateway: ${createdLead.Gateway__c || 'N/A'}`);
                    console.log(`   Legacy ID: ${createdLead.Legacy_Id__c || 'N/A'}`);
                    console.log(`   Created by Docebo API: ${createdLead.Created_by_Docebo_API__c || 'N/A'}`);
                    console.log(`   Owner ID: ${createdLead.OwnerId || 'N/A'}`);
                    
                    console.log('\n📝 DESCRIPTION:');
                    console.log(`   ${createdLead.Description || 'N/A'}`);
                    
                    console.log('\n🎯 SUCCESS! Lead created with comprehensive dummy content!');
                    console.log(`🔗 View Lead: https://strivetogether--full.sandbox.my.salesforce.com/${leadResult.id}`);
                    
                    console.log('\n📊 FIELD SUMMARY:');
                    console.log(`   ✅ Fields Populated: 31+ fields with realistic data`);
                    console.log(`   📈 Rich demographic, contact, and organizational data`);
                    console.log(`   🎯 Perfect example of webhook lead creation capabilities!`);
                    
                    return leadResult.id;
                }
            } else {
                console.error("❌ Lead creation failed:", leadResult.errors);
            }
        } catch (leadError) {
            console.error("💥 Error creating lead:", leadError);
        }

    } catch (error) {
        console.error('💥 Error in dummy content test:', error);
    }
}

// Execute the test
console.log('🔄 Starting safe dummy content lead creation...');
createSimpleDummyLead()
    .then((leadId) => {
        if (leadId) {
            console.log(`\n✅ Test completed successfully! Lead ID: ${leadId}`);
            console.log(`🔗 Direct Link: https://strivetogether--full.sandbox.my.salesforce.com/${leadId}`);
        } else {
            console.log('\n⚠️ Test completed but lead creation failed');
        }
        process.exit(0);
    })
    .catch(err => {
        console.error('💥 Test failed:', err);
        process.exit(1);
    });
