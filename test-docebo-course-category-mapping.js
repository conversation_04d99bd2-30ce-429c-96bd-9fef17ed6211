require('dotenv').config();
const getConnection = require('./platform/salesforce/common/getConnection');
const doceboService = require('./platform/docebo/services');

async function testDoceboCourseCategory() {
    try {
        console.log('🧪 Testing Docebo Course Category Mapping');
        console.log('=' .repeat(70));
        
        const conn = await getConnection();
        if (!conn || !conn.accessToken) {
            console.error("❌ Invalid Salesforce connection");
            return;
        }

        // Step 1: Check Docebo_Course__c object structure for category fields
        console.log('\n🔍 SALESFORCE DOCEBO_COURSE__C CATEGORY FIELDS:');
        console.log('-'.repeat(50));
        
        const courseObjectDesc = await conn.sobject("Docebo_Course__c").describe();
        const categoryFields = courseObjectDesc.fields.filter(field => 
            field.name.includes('Category') || field.name.includes('category')
        );
        
        console.log(`📋 Found ${categoryFields.length} category-related fields:`);
        categoryFields.forEach(field => {
            console.log(`   • ${field.name} (${field.type}) - ${field.label}`);
            if (field.type === 'picklist' && field.picklistValues) {
                console.log(`     Valid values:`);
                field.picklistValues.forEach(value => {
                    console.log(`       - "${value.value}" ${value.active ? '(Active)' : '(Inactive)'}`);
                });
            }
        });

        // Step 2: Get sample Docebo course data to see category structure
        console.log('\n🔍 DOCEBO COURSE DATA STRUCTURE:');
        console.log('-'.repeat(50));
        
        try {
            // Get a sample course from Docebo
            const coursesResponse = await doceboService.getCourseList(1, 1); // Get just 1 course
            
            if (coursesResponse && coursesResponse.data && coursesResponse.data.length > 0) {
                const sampleCourse = coursesResponse.data[0];
                console.log(`📋 Sample course data structure:`);
                console.log(`   Course ID: ${sampleCourse.id}`);
                console.log(`   Course Name: ${sampleCourse.name}`);
                console.log(`   Course Type: ${sampleCourse.type}`);
                
                // Check if category data exists
                if (sampleCourse.category) {
                    console.log(`   ✅ Category data found:`);
                    console.log(`     Category Name: "${sampleCourse.category.name}"`);
                    console.log(`     Category Code: "${sampleCourse.category.code}"`);
                    console.log(`     Category ID: ${sampleCourse.category.id}`);
                } else {
                    console.log(`   ❌ No category data found in course object`);
                }
                
                // Get detailed course info
                const courseListedInfo = await doceboService.getCourseListedInfo(sampleCourse.id);
                if (courseListedInfo && courseListedInfo.category) {
                    console.log(`   ✅ Category data in courseListedInfo:`);
                    console.log(`     Category Name: "${courseListedInfo.category.name}"`);
                    console.log(`     Category Code: "${courseListedInfo.category.code}"`);
                    console.log(`     Category ID: ${courseListedInfo.category.id}`);
                } else {
                    console.log(`   ❌ No category data found in courseListedInfo`);
                }
                
                console.log(`\n📋 Full course object structure (first level):`);
                Object.keys(sampleCourse).forEach(key => {
                    const value = sampleCourse[key];
                    const type = Array.isArray(value) ? 'array' : typeof value;
                    console.log(`   ${key}: ${type} ${type === 'object' && value !== null ? `(${Object.keys(value).join(', ')})` : ''}`);
                });
                
            } else {
                console.log('❌ No courses found in Docebo');
            }
        } catch (doceboError) {
            console.error('❌ Error fetching Docebo course data:', doceboError.message);
        }

        // Step 3: Check current Course Category mapping in Salesforce
        console.log('\n🔍 CURRENT COURSE CATEGORY DATA IN SALESFORCE:');
        console.log('-'.repeat(50));
        
        const existingCourses = await conn.sobject("Docebo_Course__c")
            .find({}, ['Course_Category__c', 'Course_Category_Code__c', 'Course_Name__c'])
            .limit(10)
            .execute();
        
        if (existingCourses.length > 0) {
            console.log(`📋 Found ${existingCourses.length} existing courses:`);
            
            const categoryValues = new Set();
            const categoryCodeValues = new Set();
            
            existingCourses.forEach(course => {
                if (course.Course_Category__c) categoryValues.add(course.Course_Category__c);
                if (course.Course_Category_Code__c) categoryCodeValues.add(course.Course_Category_Code__c);
                
                console.log(`   • ${course.Course_Name__c}:`);
                console.log(`     Category: "${course.Course_Category__c}"`);
                console.log(`     Category Code: "${course.Course_Category_Code__c}"`);
            });
            
            console.log(`\n📊 Unique category values found:`);
            console.log(`   Course_Category__c values: ${Array.from(categoryValues).join(', ')}`);
            console.log(`   Course_Category_Code__c values: ${Array.from(categoryCodeValues).join(', ')}`);
            
        } else {
            console.log('❌ No existing courses found');
        }

        // Step 4: Analyze the current mapping issue
        console.log('\n🔍 CURRENT MAPPING ISSUE ANALYSIS:');
        console.log('-'.repeat(50));
        
        console.log('❌ PROBLEM IDENTIFIED:');
        console.log('   • Course_Category__c is hardcoded to "A"');
        console.log('   • Course_Category_Code__c is hardcoded to "A"');
        console.log('   • Real category data from Docebo is being ignored');
        
        console.log('\n💡 SOLUTION NEEDED:');
        console.log('   1. Check if Course_Category__c is a picklist with restricted values');
        console.log('   2. If picklist, map Docebo categories to valid Salesforce values');
        console.log('   3. If text field, use actual category data from Docebo');
        console.log('   4. Update createCourse.js to use real category mapping');

        // Step 5: Check if we can map real category data
        const categoryField = categoryFields.find(f => f.name === 'Course_Category__c');
        const categoryCodeField = categoryFields.find(f => f.name === 'Course_Category_Code__c');
        
        console.log('\n🔧 FIELD TYPE ANALYSIS:');
        console.log('-'.repeat(50));
        
        if (categoryField) {
            console.log(`Course_Category__c:`);
            console.log(`   Type: ${categoryField.type}`);
            console.log(`   Required: ${!categoryField.nillable}`);
            
            if (categoryField.type === 'picklist') {
                console.log(`   ⚠️ PICKLIST FIELD - Need to map Docebo categories to valid values`);
                if (categoryField.picklistValues && categoryField.picklistValues.length > 1) {
                    console.log(`   Available values: ${categoryField.picklistValues.map(v => v.value).join(', ')}`);
                } else {
                    console.log(`   ❌ Only "A" is available - field needs more picklist values`);
                }
            } else {
                console.log(`   ✅ TEXT FIELD - Can use actual Docebo category names`);
            }
        }
        
        if (categoryCodeField) {
            console.log(`\nCourse_Category_Code__c:`);
            console.log(`   Type: ${categoryCodeField.type}`);
            console.log(`   Required: ${!categoryCodeField.nillable}`);
            
            if (categoryCodeField.type === 'picklist') {
                console.log(`   ⚠️ PICKLIST FIELD - Need to map Docebo category codes to valid values`);
                if (categoryCodeField.picklistValues && categoryCodeField.picklistValues.length > 1) {
                    console.log(`   Available values: ${categoryCodeField.picklistValues.map(v => v.value).join(', ')}`);
                } else {
                    console.log(`   ❌ Only "A" is available - field needs more picklist values`);
                }
            } else {
                console.log(`   ✅ TEXT FIELD - Can use actual Docebo category codes`);
            }
        }

        // Step 6: Provide specific fix recommendations
        console.log('\n💡 SPECIFIC FIX RECOMMENDATIONS:');
        console.log('=' .repeat(50));
        
        if (categoryField && categoryField.type !== 'picklist') {
            console.log('✅ IMMEDIATE FIX AVAILABLE:');
            console.log('   Replace hardcoded "A" values with actual Docebo category data:');
            console.log('   tmpCourse.Course_Category__c = courseData.category ? courseData.category.name : "";');
            console.log('   tmpCourse.Course_Category_Code__c = courseData.category ? courseData.category.code : "";');
        } else {
            console.log('⚠️ PICKLIST MAPPING REQUIRED:');
            console.log('   1. Get list of all Docebo categories');
            console.log('   2. Add corresponding picklist values to Salesforce');
            console.log('   3. Create mapping function for category values');
            console.log('   4. Update course creation logic');
        }

    } catch (error) {
        console.error('💥 Error in course category test:', error);
    }
}

// Execute the test
console.log('🔄 Starting Docebo course category analysis...');
testDoceboCourseCategory()
    .then(() => {
        console.log('\n✅ Analysis completed');
        process.exit(0);
    })
    .catch(err => {
        console.error('💥 Analysis failed:', err);
        process.exit(1);
    });
