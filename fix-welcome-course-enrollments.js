require('dotenv').config();
const getConnection = require('./platform/salesforce/common/getConnection');

async function fixWelcomeCourseEnrollments() {
    try {
        console.log('🔧 FIXING "Welcome to the StriveTogether Network" COURSE ENROLLMENTS');
        console.log('=' .repeat(80));
        
        const conn = await getConnection();
        if (!conn || !conn.accessToken) {
            throw new Error('Failed to connect to Salesforce');
        }

        console.log('✅ Connected to Salesforce successfully');

        // Step 1: Get the Welcome course
        console.log('\n📚 STEP 1: Getting Course Information...');
        console.log('-'.repeat(50));
        
        const welcomeCourse = await conn.sobject("Docebo_Course__c")
            .findOne({ Course_External_Id__c: 72 });
            
        if (!welcomeCourse) {
            throw new Error('Welcome to the StriveTogether Network course not found');
        }
        
        console.log(`✅ Course found: ${welcomeCourse.Course_Name__c}`);
        console.log(`   Course ID: ${welcomeCourse.Id}`);

        // Step 2: Find users who should be enrolled in this course
        console.log('\n👥 STEP 2: Finding Users Who Should Be Enrolled...');
        console.log('-'.repeat(50));
        
        // Look for users who have enrollments in other courses but not this one
        // This suggests they are active users who might be missing this enrollment
        const activeUsers = await conn.sobject("Docebo_Users__c")
            .find({})
            .sort({ CreatedDate: -1 })
            .limit(50)
            .execute();
            
        console.log(`Found ${activeUsers.length} active Docebo users`);

        // Step 3: Check which users are missing enrollments for course 72
        console.log('\n🔍 STEP 3: Checking Missing Enrollments...');
        console.log('-'.repeat(50));
        
        const usersNeedingEnrollment = [];
        
        for (const user of activeUsers) {
            // Check if user already has enrollment for course 72
            const existingEnrollment = await conn.sobject("Docebo_CourseEnrollment__c")
                .findOne({ 
                    Course__c: welcomeCourse.Id, 
                    Docebo_User__c: user.Id 
                });
                
            if (!existingEnrollment) {
                // Check if user has any other enrollments (indicating they are active)
                const otherEnrollments = await conn.sobject("Docebo_CourseEnrollment__c")
                    .find({ Docebo_User__c: user.Id })
                    .limit(1)
                    .execute();
                    
                if (otherEnrollments.length > 0) {
                    usersNeedingEnrollment.push(user);
                }
            }
        }
        
        console.log(`Found ${usersNeedingEnrollment.length} users who need enrollment in Welcome course`);
        
        if (usersNeedingEnrollment.length === 0) {
            console.log('✅ No missing enrollments found - all active users are already enrolled');
            return { success: true, enrollmentsCreated: 0 };
        }

        // Step 4: Create missing enrollments
        console.log('\n📝 STEP 4: Creating Missing Enrollments...');
        console.log('-'.repeat(50));
        
        const enrollmentsToCreate = [];
        const currentDate = new Date().toISOString();
        
        for (const user of usersNeedingEnrollment.slice(0, 10)) { // Limit to first 10 for safety
            const enrollmentId = `72-${user.User_Unique_Id__c}`.substring(0, 16);
            
            const enrollment = {
                Course__c: welcomeCourse.Id,
                Docebo_User__c: user.Id,
                Enrollment_ID__c: enrollmentId,
                Status__c: 'enrolled',
                Enrollment_Date__c: currentDate,
                Completion__c: 0,
                Score__c: 0,
                Credits__c: 0,
                Time_in_course__c: 0,
                Completed_Learning_Objects__c: 0
            };
            
            enrollmentsToCreate.push(enrollment);
            console.log(`  Preparing enrollment for user: ${user.First_Name__c} ${user.Last_Name__c} (${user.User_Unique_Id__c})`);
        }

        // Step 5: Batch create enrollments
        console.log('\n💾 STEP 5: Saving Enrollments to Salesforce...');
        console.log('-'.repeat(50));
        
        let successCount = 0;
        let errorCount = 0;
        
        try {
            const results = await conn.sobject("Docebo_CourseEnrollment__c")
                .create(enrollmentsToCreate);
                
            // Handle both single result and array of results
            const resultArray = Array.isArray(results) ? results : [results];
            
            resultArray.forEach((result, index) => {
                if (result.success) {
                    successCount++;
                    const user = usersNeedingEnrollment[index];
                    console.log(`  ✅ Created enrollment for ${user.First_Name__c} ${user.Last_Name__c} - ID: ${result.id}`);
                } else {
                    errorCount++;
                    console.log(`  ❌ Failed to create enrollment: ${JSON.stringify(result.errors)}`);
                }
            });
            
        } catch (createError) {
            console.error('❌ Error creating enrollments:', createError.message);
            errorCount = enrollmentsToCreate.length;
        }

        // Step 6: Verify created enrollments
        console.log('\n🔍 STEP 6: Verifying Created Enrollments...');
        console.log('-'.repeat(50));
        
        const finalEnrollmentCount = await conn.sobject("Docebo_CourseEnrollment__c")
            .find({ Course__c: welcomeCourse.Id })
            .execute();
            
        console.log(`Total enrollments for Welcome course: ${finalEnrollmentCount.length}`);

        // Step 7: Update course enrollment webhook processing
        console.log('\n🔄 STEP 7: Recommendations for Webhook Processing...');
        console.log('-'.repeat(50));
        
        console.log('To prevent future missing enrollments:');
        console.log('1. 🔍 Monitor webhook logs for course ID 72 specifically');
        console.log('2. 🛠️ Ensure course enrollment webhooks are properly configured');
        console.log('3. 📊 Set up alerts for failed enrollments');
        console.log('4. 🔄 Consider running periodic enrollment sync for critical courses');

        return {
            success: true,
            enrollmentsCreated: successCount,
            errors: errorCount,
            totalEnrollments: finalEnrollmentCount.length
        };

    } catch (error) {
        console.error('💥 Error fixing enrollments:', error);
        return {
            success: false,
            error: error.message
        };
    }
}

// Execute the fix
console.log('🔄 Starting Welcome Course Enrollment Fix...');
fixWelcomeCourseEnrollments()
    .then((result) => {
        console.log('\n📋 FIX SUMMARY:');
        console.log('=' .repeat(50));
        
        if (result.success) {
            console.log(`✅ Enrollments Created: ${result.enrollmentsCreated}`);
            if (result.errors > 0) {
                console.log(`❌ Errors: ${result.errors}`);
            }
            if (result.totalEnrollments !== undefined) {
                console.log(`📊 Total Course Enrollments: ${result.totalEnrollments}`);
            }
            
            console.log('\n🎉 Welcome Course Enrollment Fix Completed Successfully!');
            
            if (result.enrollmentsCreated > 0) {
                console.log('\n🔗 Next Steps:');
                console.log('1. 🌐 Check the Salesforce course record to verify enrollments');
                console.log('2. 📊 Monitor future webhook processing for this course');
                console.log('3. 🔄 Consider setting up automated enrollment sync');
            }
            
        } else {
            console.log(`❌ Fix failed: ${result.error}`);
        }
        
        console.log('\n✅ Fix process completed');
        process.exit(0);
    })
    .catch(err => {
        console.error('💥 Fix process failed:', err);
        process.exit(1);
    });
