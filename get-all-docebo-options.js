require('dotenv').config();
const doceboService = require('./platform/docebo/services');

async function getAllDoceboOptions() {
    try {
        console.log('🔍 Fetching All Docebo Options for Picklist Fields');
        console.log('=' .repeat(70));
        
        const allOptions = {
            courseCategories: new Set(),
            courseCategoryCodes: new Set(),
            courseTypes: new Set(),
            courseStatuses: new Set(),
            languages: new Set(),
            userRoles: new Set(),
            employmentTypes: new Set(),
            genderIdentities: new Set(),
            raceIdentities: new Set(),
            initiatives: new Set(),
            organizationTypes: new Set(),
            branchNames: new Set(),
            userLevels: new Set(),
            userStatuses: new Set(),
            enrollmentStatuses: new Set(),
            completionStatuses: new Set()
        };

        // Step 1: Get all courses to analyze course-related options
        console.log('\n📚 ANALYZING COURSES...');
        console.log('-'.repeat(50));

        try {
            // Use the correct function name from docebo services
            const coursesData = await doceboService.getTotalCourseListedInfo();

            if (coursesData && coursesData.length > 0) {
                console.log(`📋 Analyzing ${coursesData.length} courses...`);

                // Analyze first 50 courses to avoid overwhelming output
                const coursesToAnalyze = coursesData.slice(0, 50);

                coursesToAnalyze.forEach(course => {
                    // Course categories
                    if (course.category) {
                        if (course.category.name) allOptions.courseCategories.add(course.category.name);
                        if (course.category.code) allOptions.courseCategoryCodes.add(course.category.code);
                    }

                    // Course types
                    if (course.type) allOptions.courseTypes.add(course.type);

                    // Course statuses
                    if (course.status) allOptions.courseStatuses.add(course.status);

                    // Languages
                    if (course.language && course.language.name) allOptions.languages.add(course.language.name);
                });

                // Get detailed info for a few courses to get more data
                const sampleCourses = coursesToAnalyze.slice(0, 10);
                for (const course of sampleCourses) {
                    try {
                        const courseDetails = await doceboService.getCourseInfo(course.id);
                        if (courseDetails && courseDetails.data) {
                            const courseData = courseDetails.data;

                            if (courseData.category) {
                                if (courseData.category.name) allOptions.courseCategories.add(courseData.category.name);
                                if (courseData.category.code) allOptions.courseCategoryCodes.add(courseData.category.code);
                            }

                            // Additional course data
                            if (courseData.type) allOptions.courseTypes.add(courseData.type);
                            if (courseData.status) allOptions.courseStatuses.add(courseData.status);
                            if (courseData.language && courseData.language.name) allOptions.languages.add(courseData.language.name);
                        }
                    } catch (detailError) {
                        console.log(`   ⚠️ Could not get details for course ${course.id}`);
                    }
                }
            }
        } catch (courseError) {
            console.error('❌ Error fetching courses:', courseError.message);
        }

        // Step 2: Get all users to analyze user-related options
        console.log('\n👥 ANALYZING USERS...');
        console.log('-'.repeat(50));

        try {
            // Use the correct function name from docebo services
            const usersData = await doceboService.getTotalUserListedInfo();

            if (usersData && usersData.length > 0) {
                console.log(`📋 Analyzing ${usersData.length} users...`);

                // Analyze first 50 users to avoid overwhelming output
                const usersToAnalyze = usersData.slice(0, 50);

                usersToAnalyze.forEach(user => {
                    // User levels
                    if (user.level) allOptions.userLevels.add(user.level);

                    // User statuses
                    if (user.valid !== undefined) {
                        allOptions.userStatuses.add(user.valid === '1' ? 'Active' : 'Inactive');
                    }

                    // Languages
                    if (user.language) allOptions.languages.add(user.language);
                });

                // Get detailed info for a few users to get additional fields
                const sampleUsers = usersToAnalyze.slice(0, 10);
                for (const user of sampleUsers) {
                    try {
                        const userDetails = await doceboService.getUserInfo(user.user_id);
                        if (userDetails && userDetails.data && userDetails.data.additional_fields) {
                            userDetails.data.additional_fields.forEach(field => {
                                if (field.enabled && field.value) {
                                    // Map field IDs to categories
                                    switch(field.id) {
                                        case "9": // Role Type
                                            allOptions.userRoles.add(field.value);
                                            break;
                                        case "10": // Employment Type
                                            allOptions.employmentTypes.add(field.value);
                                            break;
                                        case "12": // Race Identity
                                            allOptions.raceIdentities.add(field.value);
                                            break;
                                        case "13": // Gender Identity
                                            allOptions.genderIdentities.add(field.value);
                                            break;
                                        case "20": // Initiative
                                            allOptions.initiatives.add(field.value);
                                            break;
                                        case "21": // Organization Type
                                            allOptions.organizationTypes.add(field.value);
                                            break;
                                    }
                                }

                                // Handle option-based fields (like states)
                                if (field.options && Array.isArray(field.options)) {
                                    field.options.forEach(option => {
                                        if (field.id === "25") { // State field
                                            allOptions.userStatuses.add(option.label);
                                        }
                                    });
                                }
                            });
                        }

                        // Get branch information
                        if (userDetails && userDetails.data && userDetails.data.branches) {
                            userDetails.data.branches.forEach(branch => {
                                if (branch.name) allOptions.branchNames.add(branch.name);
                            });
                        }
                    } catch (userDetailError) {
                        console.log(`   ⚠️ Could not get details for user ${user.user_id}`);
                    }
                }
            }
        } catch (userError) {
            console.error('❌ Error fetching users:', userError.message);
        }

        // Step 3: Get enrollment data to analyze enrollment-related options
        console.log('\n📝 ANALYZING ENROLLMENTS...');
        console.log('-'.repeat(50));

        try {
            // Get enrollments from a sample user to analyze enrollment statuses
            const { getUserCourseEnrollments } = require('./platform/docebo/getUserEnrollments');

            // Use a sample user from the users we just fetched
            if (usersData && usersData.length > 0) {
                const sampleUser = usersData[0];
                console.log(`📋 Analyzing enrollments for sample user: ${sampleUser.user_id}...`);

                const enrollments = await getUserCourseEnrollments(sampleUser.user_id);

                if (enrollments && enrollments.length > 0) {
                    console.log(`📋 Found ${enrollments.length} enrollments for analysis...`);

                    enrollments.forEach(enrollment => {
                        // Enrollment statuses
                        if (enrollment.status) allOptions.enrollmentStatuses.add(enrollment.status);

                        // Completion statuses
                        if (enrollment.completion !== undefined) {
                            if (enrollment.completion === 100) {
                                allOptions.completionStatuses.add('Completed');
                            } else if (enrollment.completion > 0) {
                                allOptions.completionStatuses.add('In Progress');
                            } else {
                                allOptions.completionStatuses.add('Not Started');
                            }
                        }

                        // Progress-based statuses
                        if (enrollment.progress !== undefined) {
                            if (enrollment.progress === 100) {
                                allOptions.completionStatuses.add('Completed');
                            } else if (enrollment.progress > 0) {
                                allOptions.completionStatuses.add('In Progress');
                            } else {
                                allOptions.completionStatuses.add('Not Started');
                            }
                        }
                    });
                } else {
                    console.log(`⚠️ No enrollments found for user ${sampleUser.user_id}`);
                }
            }
        } catch (enrollmentError) {
            console.error('❌ Error fetching enrollments:', enrollmentError.message);
        }

        // Step 4: Get course categories specifically
        console.log('\n📂 ANALYZING COURSE CATEGORIES...');
        console.log('-'.repeat(50));

        try {
            // Check if getAllCourseCategories function exists
            if (typeof doceboService.getAllCourseCategories === 'function') {
                const categoriesResponse = await doceboService.getAllCourseCategories();

                if (categoriesResponse && categoriesResponse.status === 200 && categoriesResponse.data) {
                    console.log(`📋 Found ${categoriesResponse.data.length} course categories...`);

                    categoriesResponse.data.forEach(category => {
                        if (category.name) allOptions.courseCategories.add(category.name);
                        if (category.code) allOptions.courseCategoryCodes.add(category.code);
                    });
                }
            } else if (typeof doceboService.getCourseCategories === 'function') {
                const categoriesResponse = await doceboService.getCourseCategories();

                if (categoriesResponse && categoriesResponse.status === 200 && categoriesResponse.data) {
                    console.log(`📋 Found course categories...`);

                    const categories = Array.isArray(categoriesResponse.data) ? categoriesResponse.data : categoriesResponse.data.items || [];
                    categories.forEach(category => {
                        if (category.name) allOptions.courseCategories.add(category.name);
                        if (category.code) allOptions.courseCategoryCodes.add(category.code);
                    });
                }
            }
        } catch (categoryError) {
            console.error('❌ Error fetching course categories:', categoryError.message);
        }

        // Step 4: Display all collected options
        console.log('\n📊 ALL DOCEBO OPTIONS SUMMARY:');
        console.log('=' .repeat(70));
        
        const displayOptions = (title, optionsSet, fieldName) => {
            console.log(`\n🔧 ${title} (${optionsSet.size} values):`);
            console.log(`   Salesforce Field: ${fieldName}`);
            if (optionsSet.size > 0) {
                const sortedOptions = Array.from(optionsSet).sort();
                sortedOptions.forEach(option => {
                    console.log(`     • "${option}"`);
                });
            } else {
                console.log(`     ❌ No values found`);
            }
        };

        displayOptions('COURSE CATEGORIES', allOptions.courseCategories, 'Course_Category__c');
        displayOptions('COURSE CATEGORY CODES', allOptions.courseCategoryCodes, 'Course_Category_Code__c');
        displayOptions('COURSE TYPES', allOptions.courseTypes, 'Course_Type__c / Type__c');
        displayOptions('COURSE STATUSES', allOptions.courseStatuses, 'Course_Status__c');
        displayOptions('LANGUAGES', allOptions.languages, 'Language__c / Languages__c');
        displayOptions('USER ROLES', allOptions.userRoles, 'Role_Type__c');
        displayOptions('EMPLOYMENT TYPES', allOptions.employmentTypes, 'Employment_Type__c');
        displayOptions('GENDER IDENTITIES', allOptions.genderIdentities, 'GenderIdentity / Gender_Identity__c');
        displayOptions('RACE IDENTITIES', allOptions.raceIdentities, 'Race__c / Race_Identity__c');
        displayOptions('INITIATIVES', allOptions.initiatives, 'Initiative__c');
        displayOptions('ORGANIZATION TYPES', allOptions.organizationTypes, 'Organization_Type__c');
        displayOptions('BRANCH NAMES', allOptions.branchNames, 'Branch_Name__c');
        displayOptions('USER LEVELS', allOptions.userLevels, 'User_Level__c');
        displayOptions('ENROLLMENT STATUSES', allOptions.enrollmentStatuses, 'Status__c');
        displayOptions('COMPLETION STATUSES', allOptions.completionStatuses, 'Completion_Status__c');

        // Step 5: Generate Salesforce picklist values
        console.log('\n📋 SALESFORCE PICKLIST RECOMMENDATIONS:');
        console.log('=' .repeat(70));
        
        console.log('\n🔧 FOR SALESFORCE ADMIN - ADD THESE PICKLIST VALUES:');
        
        const generatePicklistValues = (title, optionsSet) => {
            if (optionsSet.size > 0) {
                console.log(`\n${title}:`);
                const sortedOptions = Array.from(optionsSet).sort();
                sortedOptions.forEach(option => {
                    console.log(`${option}`);
                });
            }
        };

        generatePicklistValues('Course_Category__c picklist values', allOptions.courseCategories);
        generatePicklistValues('Course_Category_Code__c picklist values', allOptions.courseCategoryCodes);
        generatePicklistValues('Course_Type__c picklist values', allOptions.courseTypes);
        generatePicklistValues('Role_Type__c picklist values', allOptions.userRoles);
        generatePicklistValues('Employment_Type__c picklist values', allOptions.employmentTypes);
        generatePicklistValues('GenderIdentity picklist values', allOptions.genderIdentities);
        generatePicklistValues('Race__c picklist values', allOptions.raceIdentities);

        // Step 6: Summary statistics
        console.log('\n📊 SUMMARY STATISTICS:');
        console.log('=' .repeat(70));
        
        const totalUniqueValues = Object.values(allOptions).reduce((sum, set) => sum + set.size, 0);
        console.log(`🎯 Total unique values found: ${totalUniqueValues}`);
        console.log(`📚 Course categories: ${allOptions.courseCategories.size}`);
        console.log(`👥 User-related options: ${allOptions.userRoles.size + allOptions.employmentTypes.size + allOptions.genderIdentities.size + allOptions.raceIdentities.size}`);
        console.log(`📝 Enrollment options: ${allOptions.enrollmentStatuses.size + allOptions.completionStatuses.size}`);

    } catch (error) {
        console.error('💥 Error in getAllDoceboOptions:', error);
    }
}

// Execute the analysis
console.log('🔄 Starting comprehensive Docebo options analysis...');
getAllDoceboOptions()
    .then(() => {
        console.log('\n✅ Analysis completed');
        process.exit(0);
    })
    .catch(err => {
        console.error('💥 Analysis failed:', err);
        process.exit(1);
    });
