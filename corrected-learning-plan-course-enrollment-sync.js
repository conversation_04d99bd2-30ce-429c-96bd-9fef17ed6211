require('dotenv').config();
const getConnection = require('./platform/salesforce/common/getConnection');
const getApiData = require("./common/docebo/fetcher");
const APP_BASE = process.env.DOCEBO_API_BASE_URL;

async function correctedLearningPlanCourseEnrollmentSync() {
    try {
        console.log('🚀 CORRECTED LEARNING PLAN COURSE ENROLLMENT SYNC');
        console.log('=' .repeat(80));
        console.log('🎯 Target: Docebo_Learning_Plan_Course_Enrollment__c object');
        console.log('📡 Strategy: Use correct field names and uidCourse mapping');
        
        const conn = await getConnection();
        if (!conn || !conn.accessToken) {
            throw new Error('Failed to connect to Salesforce');
        }

        console.log('✅ Connected to Salesforce successfully');

        // Step 1: Create the uidCourse to Salesforce mapping (reuse from previous success)
        console.log('\n🗺️ STEP 1: Creating uidCourse to Salesforce Mapping...');
        console.log('-'.repeat(50));
        
        // Get all courses from Docebo to create ID to uidCourse mapping
        let allDoceboCourses = [];
        let page = 1;
        let hasMoreData = true;
        
        while (hasMoreData && page <= 10) {
            const response = await getApiData(
                'GET', 
                `${APP_BASE}/course/v1/courses?page=${page}&page_size=200`, 
                null
            );
            
            if (response && response.status === 200) {
                const items = response.data?.items || [];
                allDoceboCourses.push(...items);
                
                hasMoreData = response.data?.has_more_data || false;
                if (items.length === 0) hasMoreData = false;
                page++;
            } else {
                hasMoreData = false;
            }
        }
        
        console.log(`Found ${allDoceboCourses.length} courses from Docebo`);
        
        // Create numeric ID to uidCourse mapping
        const numericIdToUidMap = new Map();
        allDoceboCourses.forEach(course => {
            if (course.id && course.uidCourse) {
                numericIdToUidMap.set(course.id.toString(), course.uidCourse);
            }
        });
        
        // Get Salesforce courses and create uidCourse to Salesforce ID mapping
        const sfCourses = await conn.sobject("Docebo_Course__c")
            .find({})
            .execute();
            
        const uidToSalesforceIdMap = new Map();
        sfCourses.forEach(sfCourse => {
            const numericId = sfCourse.Course_External_Id__c?.toString();
            if (numericId && numericIdToUidMap.has(numericId)) {
                const uidCourse = numericIdToUidMap.get(numericId);
                uidToSalesforceIdMap.set(uidCourse, {
                    id: sfCourse.Id,
                    name: sfCourse.Course_Name__c,
                    externalId: sfCourse.Course_External_Id__c
                });
            }
        });
        
        console.log(`✅ Created mapping for ${uidToSalesforceIdMap.size} courses`);

        // Step 2: Get existing Learning Plan Course Enrollments
        console.log('\n📊 STEP 2: Getting Existing Learning Plan Course Enrollments...');
        console.log('-'.repeat(50));
        
        const existingEnrollments = await conn.sobject("Docebo_Learning_Plan_Course_Enrollment__c")
            .find({})
            .execute();
            
        console.log(`Found ${existingEnrollments.length} existing learning plan course enrollments`);

        // Step 3: Get reference data
        console.log('\n📚 STEP 3: Getting Reference Data...');
        console.log('-'.repeat(50));
        
        // Learning Plans
        const sfLearningPlans = await conn.sobject("Docebo_Learning_Plan__c")
            .find({})
            .execute();
            
        const learningPlanMapping = new Map();
        sfLearningPlans.forEach(plan => {
            if (plan.Learning_Plan_External_Id__c) {
                learningPlanMapping.set(plan.Learning_Plan_External_Id__c.toString(), {
                    id: plan.Id,
                    name: plan.Learning_Plan_Name__c,
                    externalId: plan.Learning_Plan_External_Id__c
                });
            }
        });
        
        // Users
        const sfUsers = await conn.sobject("Docebo_Users__c")
            .find({})
            .execute();
            
        const userMapping = new Map();
        sfUsers.forEach(user => {
            if (user.User_Unique_Id__c) {
                userMapping.set(user.User_Unique_Id__c.toString(), {
                    id: user.Id,
                    email: user.Email__c,
                    uniqueId: user.User_Unique_Id__c
                });
            }
        });
        
        console.log(`📊 Learning Plans: ${learningPlanMapping.size}`);
        console.log(`📊 Courses: ${uidToSalesforceIdMap.size}`);
        console.log(`📊 Users: ${userMapping.size}`);

        // Step 4: Fetch Learning Plan Course Enrollments from Docebo (limit to first few for testing)
        console.log('\n🔍 STEP 4: Fetching Learning Plan Course Enrollments (TEST MODE)...');
        console.log('-'.repeat(50));
        
        let allLearningPlanCourseEnrollments = [];
        const learningPlanIds = Array.from(learningPlanMapping.keys());
        
        // TEST MODE: Only process first 3 learning plans to verify field mapping
        const testLearningPlanIds = learningPlanIds.slice(0, 3);
        console.log(`📊 TEST MODE: Processing ${testLearningPlanIds.length} learning plans for field verification...`);
        
        for (let i = 0; i < testLearningPlanIds.length; i++) {
            const learningPlanId = testLearningPlanIds[i];
            const planInfo = learningPlanMapping.get(learningPlanId);
            
            console.log(`   📄 Processing LP ${i + 1}/${testLearningPlanIds.length}: ${learningPlanId} (${planInfo.name})...`);
            
            try {
                let page = 1;
                let hasMoreData = true;
                let planEnrollments = [];
                
                while (hasMoreData && page <= 3) { // Limit pages for testing
                    const response = await getApiData(
                        'GET', 
                        `${APP_BASE}/learn/v1/enrollments?learning_plan_id=${learningPlanId}&page=${page}&page_size=50`, 
                        null
                    );
                    
                    if (response && response.status === 200) {
                        const items = response.data?.items || [];
                        
                        // Filter for valid enrollments using our mapping
                        const validItems = items.filter(item => 
                            item.uidCourse && item.user_id && 
                            uidToSalesforceIdMap.has(item.uidCourse) &&
                            userMapping.has(item.user_id.toString())
                        );
                        
                        planEnrollments.push(...validItems);
                        
                        console.log(`      📄 Page ${page}: ${items.length} items, ${validItems.length} valid course enrollments`);
                        
                        hasMoreData = response.data?.has_more_data || false;
                        if (items.length === 0) hasMoreData = false;
                        page++;
                    } else {
                        hasMoreData = false;
                    }
                }
                
                if (planEnrollments.length > 0) {
                    planEnrollments.forEach(enrollment => {
                        enrollment.learning_plan_id = learningPlanId;
                        enrollment.learning_plan_name = planInfo.name;
                    });
                    
                    allLearningPlanCourseEnrollments.push(...planEnrollments);
                    console.log(`      ✅ Added ${planEnrollments.length} course enrollments for LP ${learningPlanId}`);
                } else {
                    console.log(`      ⚪ No course enrollments found for LP ${learningPlanId}`);
                }
                
            } catch (planError) {
                console.log(`      ❌ Error processing LP ${learningPlanId}: ${planError.message}`);
                continue;
            }
        }
        
        console.log(`\n✅ Docebo fetch completed: ${allLearningPlanCourseEnrollments.length.toLocaleString()} course enrollments found`);

        if (allLearningPlanCourseEnrollments.length === 0) {
            console.log('⚠️ No learning plan course enrollments found');
            return {
                success: true,
                doceboTotal: 0,
                salesforceInitial: existingEnrollments.length,
                message: 'No data found'
            };
        }

        // Step 5: Prepare records for creation with CORRECT field names
        console.log('\n🔍 STEP 5: Preparing Learning Plan Course Enrollment Records (CORRECT FIELDS)...');
        console.log('-'.repeat(50));
        
        const enrollmentsToCreate = [];
        const existingKeys = new Set();
        
        // Track existing enrollments using correct field names
        existingEnrollments.forEach(enrollment => {
            if (enrollment.Learning_Plan_Enrollment_Id__c && enrollment.Course_Enrollment_Id__c) {
                const key = `${enrollment.Learning_Plan_Enrollment_Id__c}-${enrollment.Course_Enrollment_Id__c}`;
                existingKeys.add(key);
            }
        });
        
        let skippedCount = 0;
        let duplicatesSkipped = 0;
        
        for (const doceboEnrollment of allLearningPlanCourseEnrollments) {
            const learningPlanId = doceboEnrollment.learning_plan_id;
            const courseUid = doceboEnrollment.uidCourse;
            const userId = doceboEnrollment.user_id;
            
            const salesforceLearningPlan = learningPlanMapping.get(learningPlanId.toString());
            const salesforceCourse = uidToSalesforceIdMap.get(courseUid);
            const salesforceUser = userMapping.get(userId.toString());
            
            if (!salesforceLearningPlan || !salesforceCourse || !salesforceUser) {
                skippedCount++;
                continue;
            }
            
            // Create unique key for duplicate checking
            const uniqueKey = `${learningPlanId}-${salesforceCourse.externalId}-${salesforceUser.uniqueId}`;
            if (existingKeys.has(uniqueKey)) {
                duplicatesSkipped++;
                continue;
            }
            
            existingKeys.add(uniqueKey);
            
            // Parse dates
            let enrollmentDate = "";
            if (doceboEnrollment.enroll_date_of_enrollment) {
                try {
                    enrollmentDate = new Date(doceboEnrollment.enroll_date_of_enrollment.replace(' ', 'T')).toISOString();
                } catch (e) {
                    enrollmentDate = "";
                }
            }
            
            let completionDate = "";
            if (doceboEnrollment.course_complete_date) {
                try {
                    completionDate = new Date(doceboEnrollment.course_complete_date.replace(' ', 'T')).toISOString();
                } catch (e) {
                    completionDate = "";
                }
            }
            
            // Create record with CORRECT field names based on the actual Salesforce object
            const enrollmentRecord = {
                // Use the correct field names from the Salesforce object
                Learning_Plan_Enrollment_Id__c: `LP-${learningPlanId}-${salesforceUser.uniqueId}`,
                Course_Enrollment_Id__c: `CE-${salesforceCourse.externalId}-${salesforceUser.uniqueId}`,
                docebo_v3__CourseEnrollmentStatus__c: doceboEnrollment.status || 'enrolled',
                Enrollment_Date__c: enrollmentDate,
                Completion_Date__c: completionDate,
                Completion_Percentage__c: 0, // Will be updated later
                Effective__c: true,
                Completed__c: doceboEnrollment.course_complete_date ? true : false
            };
            
            enrollmentsToCreate.push(enrollmentRecord);
        }
        
        console.log(`Prepared ${enrollmentsToCreate.length.toLocaleString()} learning plan course enrollment records`);
        console.log(`Skipped ${skippedCount.toLocaleString()} enrollments (missing references)`);
        console.log(`🛡️ Duplicates prevented: ${duplicatesSkipped.toLocaleString()} enrollments`);

        if (enrollmentsToCreate.length === 0) {
            console.log('✅ All learning plan course enrollments are already synced!');
            return {
                success: true,
                doceboTotal: allLearningPlanCourseEnrollments.length,
                salesforceInitial: existingEnrollments.length,
                synced: 0,
                skipped: skippedCount,
                duplicates: duplicatesSkipped
            };
        }

        // Step 6: Create records in small batches for testing
        console.log('\n💾 STEP 6: Creating Learning Plan Course Enrollments (TEST MODE)...');
        console.log('-'.repeat(50));
        
        let successCount = 0;
        let errorCount = 0;
        const batchSize = 10; // Small batch for testing
        const totalBatches = Math.ceil(enrollmentsToCreate.length / batchSize);
        
        console.log(`TEST MODE: Processing ${enrollmentsToCreate.length.toLocaleString()} records in ${totalBatches.toLocaleString()} batches...`);
        
        for (let i = 0; i < enrollmentsToCreate.length; i += batchSize) {
            const batch = enrollmentsToCreate.slice(i, i + batchSize);
            const batchNum = Math.floor(i / batchSize) + 1;
            
            console.log(`   📦 Processing batch ${batchNum.toLocaleString()}/${totalBatches.toLocaleString()} (${batch.length} records)...`);
            
            // Show sample record for verification
            if (batchNum === 1) {
                console.log(`   🔍 Sample record structure:`);
                console.log(JSON.stringify(batch[0], null, 2));
            }
            
            try {
                const results = await conn.sobject("Docebo_Learning_Plan_Course_Enrollment__c")
                    .create(batch);
                    
                const resultArray = Array.isArray(results) ? results : [results];
                
                let batchSuccessCount = 0;
                let batchErrorCount = 0;
                
                resultArray.forEach((result) => {
                    if (result.success) {
                        successCount++;
                        batchSuccessCount++;
                    } else {
                        errorCount++;
                        batchErrorCount++;
                        
                        const errorMessage = result.errors?.[0]?.message || 'Unknown error';
                        console.log(`      ⚠️ Error: ${errorMessage}`);
                    }
                });
                
                console.log(`      ✅ Batch ${batchNum}: ${batchSuccessCount} success, ${batchErrorCount} errors`);
                
                // Stop after first batch if there are errors to investigate
                if (batchErrorCount > 0) {
                    console.log(`⚠️ Stopping after first batch due to errors - need to investigate field mapping`);
                    break;
                }
                
                // Rate limiting
                if (batchNum < totalBatches) {
                    await new Promise(resolve => setTimeout(resolve, 1000));
                }
                
            } catch (batchError) {
                console.error(`      ❌ Batch ${batchNum} failed:`, batchError.message);
                errorCount += batch.length;
                break; // Stop on batch error
            }
        }

        // Step 7: Final verification
        console.log('\n🔍 STEP 7: Final Verification...');
        console.log('-'.repeat(50));
        
        const finalEnrollments = await conn.sobject("Docebo_Learning_Plan_Course_Enrollment__c")
            .find({})
            .execute();

        return {
            success: true,
            doceboTotal: allLearningPlanCourseEnrollments.length,
            salesforceInitial: existingEnrollments.length,
            salesforceFinal: finalEnrollments.length,
            synced: successCount,
            errors: errorCount,
            skipped: skippedCount,
            duplicates: duplicatesSkipped,
            testMode: true
        };

    } catch (error) {
        console.error('💥 Error in corrected learning plan course enrollment sync:', error);
        return {
            success: false,
            error: error.message
        };
    }
}

// Execute the corrected sync
console.log('🔄 Starting Corrected Learning Plan Course Enrollment Sync...');
correctedLearningPlanCourseEnrollmentSync()
    .then((result) => {
        console.log('\n📋 CORRECTED LEARNING PLAN COURSE ENROLLMENT SYNC SUMMARY:');
        console.log('=' .repeat(70));
        
        if (result.success) {
            if (result.salesforceFinal !== undefined) {
                console.log(`📊 Total Course Enrollments Found: ${result.doceboTotal.toLocaleString()}`);
                console.log(`📊 Salesforce Initial: ${result.salesforceInitial.toLocaleString()}`);
                console.log(`📊 Salesforce Final: ${result.salesforceFinal.toLocaleString()}`);
                console.log(`✅ Successfully Synced: ${result.synced.toLocaleString()}`);
                console.log(`❌ Errors: ${result.errors.toLocaleString()}`);
                console.log(`⏭️ Skipped: ${result.skipped.toLocaleString()}`);
                console.log(`🛡️ Duplicates Prevented: ${result.duplicates.toLocaleString()}`);
                
                if (result.testMode) {
                    console.log(`🧪 TEST MODE: Limited to first few learning plans for field verification`);
                }
                
                const netIncrease = result.salesforceFinal - result.salesforceInitial;
                console.log(`\n🎉 NET RESULT: ${netIncrease.toLocaleString()} new learning plan course enrollments added!`);
                
                if (netIncrease > 0) {
                    console.log(`🎉 SUCCESS: Field mapping is working! Ready for full sync.`);
                } else if (result.errors === 0) {
                    console.log(`✅ No errors detected - field mapping appears correct.`);
                } else {
                    console.log(`⚠️ Field mapping issues detected - need to investigate errors.`);
                }
            } else {
                console.log(`📋 Result: ${result.message}`);
            }
        } else {
            console.log(`❌ Sync failed: ${result.error}`);
        }
        
        console.log('\n✅ Corrected learning plan course enrollment sync completed');
        process.exit(0);
    })
    .catch(err => {
        console.error('💥 Corrected learning plan course enrollment sync failed:', err);
        process.exit(1);
    });
