require('dotenv').config();
const getConnection = require('./platform/salesforce/common/getConnection');
const getApiData = require("./common/docebo/fetcher");
const APP_BASE = process.env.DOCEBO_API_BASE_URL;

async function finalWorkingLearningPlanCourseEnrollmentSync() {
    try {
        console.log('🚀 FINAL WORKING LEARNING PLAN COURSE ENROLLMENT SYNC');
        console.log('=' .repeat(80));
        console.log('🎯 Target: Docebo_Learning_Plan_Course_Enrollment__c object');
        console.log('📡 Strategy: Use correct field types for all lookups');
        
        const conn = await getConnection();
        if (!conn || !conn.accessToken) {
            throw new Error('Failed to connect to Salesforce');
        }

        console.log('✅ Connected to Salesforce successfully');

        // Step 1: Create the uidCourse to Salesforce mapping
        console.log('\n🗺️ STEP 1: Creating uidCourse to Salesforce Mapping...');
        console.log('-'.repeat(50));
        
        // Get all courses from Docebo to create ID to uidCourse mapping
        let allDoceboCourses = [];
        let page = 1;
        let hasMoreData = true;
        
        while (hasMoreData && page <= 10) {
            const response = await getApiData(
                'GET', 
                `${APP_BASE}/course/v1/courses?page=${page}&page_size=200`, 
                null
            );
            
            if (response && response.status === 200) {
                const items = response.data?.items || [];
                allDoceboCourses.push(...items);
                
                hasMoreData = response.data?.has_more_data || false;
                if (items.length === 0) hasMoreData = false;
                page++;
            } else {
                hasMoreData = false;
            }
        }
        
        console.log(`Found ${allDoceboCourses.length} courses from Docebo`);
        
        // Create numeric ID to uidCourse mapping
        const numericIdToUidMap = new Map();
        allDoceboCourses.forEach(course => {
            if (course.id && course.uidCourse) {
                numericIdToUidMap.set(course.id.toString(), course.uidCourse);
            }
        });
        
        // Get Salesforce courses and create uidCourse to Salesforce ID mapping
        const sfCourses = await conn.sobject("Docebo_Course__c")
            .find({})
            .execute();
            
        const uidToSalesforceIdMap = new Map();
        sfCourses.forEach(sfCourse => {
            const numericId = sfCourse.Course_External_Id__c?.toString();
            if (numericId && numericIdToUidMap.has(numericId)) {
                const uidCourse = numericIdToUidMap.get(numericId);
                uidToSalesforceIdMap.set(uidCourse, {
                    id: sfCourse.Id,
                    name: sfCourse.Course_Name__c,
                    externalId: sfCourse.Course_External_Id__c
                });
            }
        });
        
        console.log(`✅ Created mapping for ${uidToSalesforceIdMap.size} courses`);

        // Step 2: Get reference data with CORRECT field types
        console.log('\n📚 STEP 2: Getting Reference Data (Correct Field Types)...');
        console.log('-'.repeat(50));
        
        // Learning Plans (Learning_Plan_External_Id__c is double!)
        const sfLearningPlans = await conn.sobject("Docebo_Learning_Plan__c")
            .find({})
            .execute();
            
        const learningPlanMapping = new Map();
        sfLearningPlans.forEach(plan => {
            if (plan.Learning_Plan_External_Id__c) {
                // Convert to string for mapping
                learningPlanMapping.set(plan.Learning_Plan_External_Id__c.toString(), {
                    id: plan.Id,
                    name: plan.Learning_Plan_Name__c,
                    externalId: plan.Learning_Plan_External_Id__c
                });
            }
        });
        
        // Users (User_Unique_Id__c is double!)
        const sfUsers = await conn.sobject("Docebo_Users__c")
            .find({})
            .execute();
            
        const userMapping = new Map();
        sfUsers.forEach(user => {
            if (user.User_Unique_Id__c) {
                // Convert to string for mapping
                userMapping.set(user.User_Unique_Id__c.toString(), {
                    id: user.Id,
                    email: user.Email__c,
                    uniqueId: user.User_Unique_Id__c
                });
            }
        });
        
        // Learning Plan Enrollments
        const sfLearningPlanEnrollments = await conn.sobject("Docebo_Learning_Plan_Enrollment__c")
            .find({})
            .execute();
            
        const learningPlanEnrollmentMapping = new Map();
        sfLearningPlanEnrollments.forEach(enrollment => {
            if (enrollment.Learning_Plan_Id__c && enrollment.Docebo_User_Id__c) {
                const key = `${enrollment.Learning_Plan_Id__c}-${enrollment.Docebo_User_Id__c}`;
                learningPlanEnrollmentMapping.set(key, enrollment.Id);
            }
        });
        
        // Course Enrollments
        const sfCourseEnrollments = await conn.sobject("Docebo_CourseEnrollment__c")
            .find({})
            .execute();
            
        const courseEnrollmentMapping = new Map();
        sfCourseEnrollments.forEach(enrollment => {
            if (enrollment.Course__c && enrollment.Docebo_User__c) {
                const key = `${enrollment.Course__c}-${enrollment.Docebo_User__c}`;
                courseEnrollmentMapping.set(key, enrollment.Id);
            }
        });
        
        console.log(`📊 Learning Plans: ${learningPlanMapping.size}`);
        console.log(`📊 Courses: ${uidToSalesforceIdMap.size}`);
        console.log(`📊 Users: ${userMapping.size}`);
        console.log(`📊 Learning Plan Enrollments: ${learningPlanEnrollmentMapping.size}`);
        console.log(`📊 Course Enrollments: ${courseEnrollmentMapping.size}`);

        // Step 3: Get existing Learning Plan Course Enrollments
        console.log('\n📊 STEP 3: Getting Existing Learning Plan Course Enrollments...');
        console.log('-'.repeat(50));
        
        const existingEnrollments = await conn.sobject("Docebo_Learning_Plan_Course_Enrollment__c")
            .find({})
            .execute();
            
        console.log(`Found ${existingEnrollments.length} existing learning plan course enrollments`);

        // Step 4: Use the learning plan with most enrollments (External ID 9)
        console.log('\n🔍 STEP 4: Fetching Learning Plan Course Enrollments...');
        console.log('-'.repeat(50));
        
        const testLearningPlanId = "9";
        const planInfo = learningPlanMapping.get(testLearningPlanId);
        
        if (!planInfo) {
            throw new Error(`Learning Plan with External ID ${testLearningPlanId} not found`);
        }
        
        console.log(`📊 Processing Learning Plan: ${planInfo.name} (External ID: ${testLearningPlanId})`);
        
        let allLearningPlanCourseEnrollments = [];
        
        try {
            // Get multiple pages of enrollments for this learning plan
            let page = 1;
            let hasMoreData = true;
            
            while (hasMoreData && page <= 5) { // Process 5 pages for testing
                const response = await getApiData(
                    'GET', 
                    `${APP_BASE}/learn/v1/enrollments?learning_plan_id=${testLearningPlanId}&page=${page}&page_size=100`, 
                    null
                );
                
                if (response && response.status === 200) {
                    const items = response.data?.items || [];
                    console.log(`   📄 Page ${page}: Found ${items.length} enrollment items from Docebo`);
                    
                    // Filter for valid enrollments
                    const validItems = items.filter(item => {
                        const hasUidCourse = item.uidCourse && uidToSalesforceIdMap.has(item.uidCourse);
                        const hasUser = item.user_id && userMapping.has(item.user_id.toString());
                        return hasUidCourse && hasUser;
                    });
                    
                    validItems.forEach(enrollment => {
                        enrollment.learning_plan_id = testLearningPlanId;
                        enrollment.learning_plan_name = planInfo.name;
                    });
                    
                    allLearningPlanCourseEnrollments.push(...validItems);
                    console.log(`   ✅ Page ${page}: Found ${validItems.length} valid course enrollments`);
                    
                    hasMoreData = response.data?.has_more_data || false;
                    if (items.length === 0) hasMoreData = false;
                    page++;
                } else {
                    console.log(`   ❌ Page ${page}: Could not fetch enrollments: ${response?.status || 'No response'}`);
                    hasMoreData = false;
                }
            }
            
        } catch (planError) {
            console.log(`   ❌ Error processing LP ${testLearningPlanId}: ${planError.message}`);
        }
        
        console.log(`\n✅ Docebo fetch completed: ${allLearningPlanCourseEnrollments.length} course enrollments found`);

        if (allLearningPlanCourseEnrollments.length === 0) {
            console.log('⚠️ No learning plan course enrollments found');
            return {
                success: true,
                doceboTotal: 0,
                salesforceInitial: existingEnrollments.length,
                message: 'No data found'
            };
        }

        // Step 5: Prepare records with proper lookup relationships
        console.log('\n🔍 STEP 5: Preparing Records with Proper Lookups...');
        console.log('-'.repeat(50));
        
        const enrollmentsToCreate = [];
        let skippedCount = 0;
        let missingLpEnrollments = 0;
        let missingCourseEnrollments = 0;
        let duplicatesSkipped = 0;
        
        // Track existing enrollments to prevent duplicates
        const existingKeys = new Set();
        existingEnrollments.forEach(enrollment => {
            if (enrollment.Learning_Plan_Enrollment_Id__c && enrollment.Course_Enrollment_Id__c) {
                const key = `${enrollment.Learning_Plan_Enrollment_Id__c}-${enrollment.Course_Enrollment_Id__c}`;
                existingKeys.add(key);
            }
        });
        
        for (const doceboEnrollment of allLearningPlanCourseEnrollments) {
            const learningPlanId = doceboEnrollment.learning_plan_id;
            const courseUid = doceboEnrollment.uidCourse;
            const userId = doceboEnrollment.user_id;
            
            const salesforceLearningPlan = learningPlanMapping.get(learningPlanId.toString());
            const salesforceCourse = uidToSalesforceIdMap.get(courseUid);
            const salesforceUser = userMapping.get(userId.toString());
            
            if (!salesforceLearningPlan || !salesforceCourse || !salesforceUser) {
                skippedCount++;
                continue;
            }
            
            // Find Learning Plan Enrollment
            const lpEnrollmentKey = `${salesforceLearningPlan.id}-${salesforceUser.id}`;
            const learningPlanEnrollmentId = learningPlanEnrollmentMapping.get(lpEnrollmentKey);
            
            if (!learningPlanEnrollmentId) {
                missingLpEnrollments++;
                continue;
            }
            
            // Find Course Enrollment
            const courseEnrollmentKey = `${salesforceCourse.id}-${salesforceUser.id}`;
            const courseEnrollmentId = courseEnrollmentMapping.get(courseEnrollmentKey);
            
            if (!courseEnrollmentId) {
                missingCourseEnrollments++;
                continue;
            }
            
            // Check for duplicates
            const uniqueKey = `${learningPlanEnrollmentId}-${courseEnrollmentId}`;
            if (existingKeys.has(uniqueKey)) {
                duplicatesSkipped++;
                continue;
            }
            existingKeys.add(uniqueKey);
            
            // Parse dates
            let enrollmentDate = "";
            if (doceboEnrollment.enroll_date_of_enrollment) {
                try {
                    enrollmentDate = new Date(doceboEnrollment.enroll_date_of_enrollment.replace(' ', 'T')).toISOString();
                } catch (e) {
                    enrollmentDate = "";
                }
            }
            
            let completionDate = "";
            if (doceboEnrollment.course_complete_date) {
                try {
                    completionDate = new Date(doceboEnrollment.course_complete_date.replace(' ', 'T')).toISOString();
                } catch (e) {
                    completionDate = "";
                }
            }
            
            // Create record with proper lookup relationships
            const enrollmentRecord = {
                Learning_Plan_Enrollment_Id__c: learningPlanEnrollmentId,
                Course_Enrollment_Id__c: courseEnrollmentId,
                Enrollment_Date__c: enrollmentDate,
                Completion_Date__c: completionDate,
                Completion_Percentage__c: 0,
                Effective__c: true,
                Completed__c: doceboEnrollment.course_complete_date ? true : false
            };
            
            enrollmentsToCreate.push(enrollmentRecord);
        }
        
        console.log(`Prepared ${enrollmentsToCreate.length} learning plan course enrollment records`);
        console.log(`Skipped ${skippedCount} enrollments (missing references)`);
        console.log(`Missing LP Enrollments: ${missingLpEnrollments}`);
        console.log(`Missing Course Enrollments: ${missingCourseEnrollments}`);
        console.log(`🛡️ Duplicates prevented: ${duplicatesSkipped}`);

        if (enrollmentsToCreate.length === 0) {
            console.log('⚠️ No records to create - missing required lookup relationships');
            return {
                success: true,
                doceboTotal: allLearningPlanCourseEnrollments.length,
                salesforceInitial: existingEnrollments.length,
                synced: 0,
                skipped: skippedCount,
                missingLpEnrollments: missingLpEnrollments,
                missingCourseEnrollments: missingCourseEnrollments,
                duplicates: duplicatesSkipped
            };
        }

        // Step 6: Create records in batches
        console.log('\n💾 STEP 6: Creating Learning Plan Course Enrollments...');
        console.log('-'.repeat(50));
        
        console.log(`🔍 Sample record structure:`);
        console.log(JSON.stringify(enrollmentsToCreate[0], null, 2));
        
        let successCount = 0;
        let errorCount = 0;
        const batchSize = 50;
        const totalBatches = Math.ceil(enrollmentsToCreate.length / batchSize);
        
        console.log(`\nProcessing ${enrollmentsToCreate.length.toLocaleString()} records in ${totalBatches.toLocaleString()} batches...`);
        
        for (let i = 0; i < enrollmentsToCreate.length; i += batchSize) {
            const batch = enrollmentsToCreate.slice(i, i + batchSize);
            const batchNum = Math.floor(i / batchSize) + 1;
            
            console.log(`   📦 Processing batch ${batchNum.toLocaleString()}/${totalBatches.toLocaleString()} (${batch.length} records)...`);
            
            try {
                const results = await conn.sobject("Docebo_Learning_Plan_Course_Enrollment__c")
                    .create(batch);
                    
                const resultArray = Array.isArray(results) ? results : [results];
                
                let batchSuccessCount = 0;
                let batchErrorCount = 0;
                
                resultArray.forEach((result, index) => {
                    if (result.success) {
                        successCount++;
                        batchSuccessCount++;
                    } else {
                        errorCount++;
                        batchErrorCount++;
                        
                        if (batchErrorCount <= 3) {
                            const errorMessage = result.errors?.[0]?.message || 'Unknown error';
                            console.log(`      ⚠️ Record ${index + 1}: ${errorMessage}`);
                        }
                    }
                });
                
                console.log(`      ✅ Batch ${batchNum}: ${batchSuccessCount} success, ${batchErrorCount} errors`);
                
                // Progress indicator
                if (batchNum % 10 === 0 || batchNum === totalBatches) {
                    const progressPercent = ((i + batch.length) / enrollmentsToCreate.length * 100).toFixed(1);
                    console.log(`      📊 Progress: ${progressPercent}% (${successCount.toLocaleString()} created, ${errorCount.toLocaleString()} errors)`);
                }
                
                // Rate limiting
                if (batchNum < totalBatches) {
                    await new Promise(resolve => setTimeout(resolve, 500));
                }
                
            } catch (batchError) {
                console.error(`      ❌ Batch ${batchNum} failed:`, batchError.message);
                errorCount += batch.length;
            }
        }

        // Step 7: Final verification
        console.log('\n🔍 STEP 7: Final Verification...');
        console.log('-'.repeat(50));
        
        const finalEnrollments = await conn.sobject("Docebo_Learning_Plan_Course_Enrollment__c")
            .find({})
            .execute();

        return {
            success: true,
            doceboTotal: allLearningPlanCourseEnrollments.length,
            salesforceInitial: existingEnrollments.length,
            salesforceFinal: finalEnrollments.length,
            synced: successCount,
            errors: errorCount,
            skipped: skippedCount,
            missingLpEnrollments: missingLpEnrollments,
            missingCourseEnrollments: missingCourseEnrollments,
            duplicates: duplicatesSkipped
        };

    } catch (error) {
        console.error('💥 Error in final working learning plan course enrollment sync:', error);
        return {
            success: false,
            error: error.message
        };
    }
}

// Execute the final working sync
console.log('🔄 Starting Final Working Learning Plan Course Enrollment Sync...');
finalWorkingLearningPlanCourseEnrollmentSync()
    .then((result) => {
        console.log('\n📋 FINAL WORKING SYNC SUMMARY:');
        console.log('=' .repeat(60));
        
        if (result.success) {
            if (result.salesforceFinal !== undefined) {
                console.log(`📊 Total Course Enrollments Found: ${result.doceboTotal.toLocaleString()}`);
                console.log(`📊 Salesforce Initial: ${result.salesforceInitial.toLocaleString()}`);
                console.log(`📊 Salesforce Final: ${result.salesforceFinal.toLocaleString()}`);
                console.log(`✅ Successfully Synced: ${result.synced.toLocaleString()}`);
                console.log(`❌ Errors: ${result.errors.toLocaleString()}`);
                console.log(`⏭️ Skipped: ${result.skipped.toLocaleString()}`);
                console.log(`⚠️ Missing LP Enrollments: ${result.missingLpEnrollments.toLocaleString()}`);
                console.log(`⚠️ Missing Course Enrollments: ${result.missingCourseEnrollments.toLocaleString()}`);
                console.log(`🛡️ Duplicates Prevented: ${result.duplicates.toLocaleString()}`);
                
                const netIncrease = result.salesforceFinal - result.salesforceInitial;
                console.log(`\n🎉 NET RESULT: ${netIncrease.toLocaleString()} new learning plan course enrollments added!`);
                
                if (netIncrease > 0) {
                    console.log(`🎉 SUCCESS: Learning Plan Course Enrollment sync is working!`);
                    console.log(`🚀 Ready to run full sync for all learning plans!`);
                    console.log(`📊 From ${result.salesforceInitial} to ${result.salesforceFinal} records - MASSIVE improvement!`);
                } else if (result.errors === 0 && result.synced === 0) {
                    console.log(`⚠️ No records created - all may already exist or missing lookups`);
                } else {
                    console.log(`⚠️ Issues detected - review errors and missing lookups`);
                }
            } else {
                console.log(`📋 Result: ${result.message}`);
            }
        } else {
            console.log(`❌ Sync failed: ${result.error}`);
        }
        
        console.log('\n✅ Final working learning plan course enrollment sync completed');
        process.exit(0);
    })
    .catch(err => {
        console.error('💥 Final working learning plan course enrollment sync failed:', err);
        process.exit(1);
    });
