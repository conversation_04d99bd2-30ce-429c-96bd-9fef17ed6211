require('dotenv').config();
const getConnection = require('./platform/salesforce/common/getConnection');
const { createCourseEnrollment } = require('./platform/salesforce/courseEnrollment/createCourseEnrollment');

async function testStatusFix() {
    try {
        console.log('🧪 Testing Status Picklist Fix');
        console.log('=' .repeat(40));
        
        const conn = await getConnection();
        if (!conn || !conn.accessToken) {
            console.error("❌ Invalid Salesforce connection");
            return;
        }

        // Get sample course and user for testing
        const sampleCourse = await conn.sobject("Docebo_Course__c")
            .findOne({});
        
        const sampleUser = await conn.sobject("Docebo_Users__c")
            .findOne({});
        
        if (!sampleCourse || !sampleUser) {
            console.error("❌ No sample course or user found for testing");
            return;
        }
        
        console.log(`✅ Sample Course: ${sampleCourse.Course_Name__c} (External ID: ${sampleCourse.Course_External_Id__c})`);
        console.log(`✅ Sample User: ${sampleUser.First_Name__c} ${sampleUser.Last_Name__c} (Unique ID: ${sampleUser.User_Unique_Id__c})`);

        // Test enrollment creation with fixed status
        const testEnrollmentData = {
            course_id: sampleCourse.Course_External_Id__c,
            user_id: sampleUser.User_Unique_Id__c,
            enrollment_date: "2025-01-15 15:00:00",
            completion_date: null,
            status: "A", // Use the correct status value
            score: 85
        };
        
        console.log(`\n📋 Test enrollment data:`);
        console.log(`   Course ID: ${testEnrollmentData.course_id}`);
        console.log(`   User ID: ${testEnrollmentData.user_id}`);
        console.log(`   Status: ${testEnrollmentData.status}`);
        console.log(`   Expected Enrollment_ID__c: UE-${testEnrollmentData.course_id}-${testEnrollmentData.user_id}`);
        
        // Clean up any existing test enrollment
        const testEnrollmentId = `UE-${testEnrollmentData.course_id}-${testEnrollmentData.user_id}`;
        try {
            const existingEnrollment = await conn.sobject("Docebo_CourseEnrollment__c")
                .findOne({ Enrollment_ID__c: testEnrollmentId });
            
            if (existingEnrollment) {
                await conn.sobject("Docebo_CourseEnrollment__c").delete(existingEnrollment.Id);
                console.log(`🗑️ Deleted existing test enrollment: ${existingEnrollment.Id}`);
            }
        } catch (cleanupError) {
            console.log('No existing enrollment to clean up');
        }
        
        // Create the enrollment
        console.log('\n🔧 Creating enrollment with fixed status...');
        const result = await createCourseEnrollment(testEnrollmentData);
        
        if (result) {
            console.log('✅ Enrollment creation successful!');
            
            // Verify the created enrollment
            const createdEnrollment = await conn.sobject("Docebo_CourseEnrollment__c")
                .findOne({ Enrollment_ID__c: testEnrollmentId });
            
            if (createdEnrollment) {
                console.log(`\n✅ Enrollment verified: ${createdEnrollment.Id}`);
                console.log(`   Course__c: ${createdEnrollment.Course__c || 'MISSING ❌'}`);
                console.log(`   Docebo_User__c: ${createdEnrollment.Docebo_User__c || 'MISSING ❌'}`);
                console.log(`   Status__c: ${createdEnrollment.Status__c}`);
                console.log(`   Score__c: ${createdEnrollment.Score__c}`);
                console.log(`   Enrollment_ID__c: ${createdEnrollment.Enrollment_ID__c}`);
                console.log(`   Enrollment_Date__c: ${createdEnrollment.Enrollment_Date__c}`);
                
                if (createdEnrollment.Course__c && createdEnrollment.Docebo_User__c && createdEnrollment.Status__c === 'A') {
                    console.log('\n🎉 SUCCESS! All fixes are working correctly!');
                    console.log('✅ Course association: Working');
                    console.log('✅ User association: Working');
                    console.log('✅ Status picklist: Working');
                    console.log('✅ Enrollment ID format: Working');
                } else {
                    console.log('\n⚠️ Some issues remain');
                }
            } else {
                console.log('❌ Created enrollment not found');
            }
        } else {
            console.log('❌ Enrollment creation failed');
        }

    } catch (error) {
        console.error('💥 Error in test:', error);
    }
}

// Execute the test
testStatusFix()
    .then(() => {
        console.log('\n✅ Test completed');
        process.exit(0);
    })
    .catch(err => {
        console.error('💥 Test failed:', err);
        process.exit(1);
    });
