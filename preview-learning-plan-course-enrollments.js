require('dotenv').config();
const getApiData = require("./common/docebo/fetcher");
const APP_BASE = process.env.DOCEBO_API_BASE_URL;

async function previewLearningPlanCourseEnrollments() {
    try {
        console.log('🔍 PREVIEW LEARNING PLAN COURSE ENROLLMENTS FROM DOCEBO');
        console.log('=' .repeat(80));
        console.log('🎯 Goal: Show what data would be synced to Salesforce');
        console.log('📡 API: /learn/v1/enrollments?learning_plan_id=X');
        
        // Step 1: Get a few learning plans to test with
        console.log('\n📚 STEP 1: Getting Learning Plans...');
        console.log('-'.repeat(50));
        
        const lpResponse = await getApiData(
            'GET', 
            `${APP_BASE}/learningplan/v1/learningplans?page=1&page_size=10`, 
            null
        );
        
        if (!lpResponse || lpResponse.status !== 200) {
            throw new Error('Could not get learning plans');
        }
        
        const learningPlans = lpResponse.data?.items || [];
        console.log(`Found ${learningPlans.length} learning plans to test with`);
        
        if (learningPlans.length === 0) {
            throw new Error('No learning plans found');
        }
        
        // Step 2: Get course enrollments for each learning plan
        console.log('\n🔍 STEP 2: Getting Course Enrollments for Learning Plans...');
        console.log('-'.repeat(50));
        
        let totalCourseEnrollments = 0;
        let sampleEnrollments = [];
        
        for (let i = 0; i < Math.min(5, learningPlans.length); i++) {
            const lp = learningPlans[i];
            const lpId = lp.learning_plan_id;
            const lpTitle = lp.title;
            
            console.log(`\n📄 Learning Plan ${i + 1}: ${lpId} - ${lpTitle}`);
            
            try {
                let page = 1;
                let hasMoreData = true;
                let lpEnrollments = [];
                
                while (hasMoreData && page <= 3) { // Limit to 3 pages per LP for preview
                    const response = await getApiData(
                        'GET', 
                        `${APP_BASE}/learn/v1/enrollments?learning_plan_id=${lpId}&page=${page}&page_size=200`, 
                        null
                    );
                    
                    if (response && response.status === 200) {
                        const items = response.data?.items || [];
                        lpEnrollments.push(...items);
                        
                        console.log(`   📄 Page ${page}: ${items.length} course enrollments`);
                        
                        hasMoreData = response.data?.has_more_data || false;
                        if (items.length === 0) hasMoreData = false;
                        page++;
                    } else {
                        hasMoreData = false;
                    }
                }
                
                console.log(`   ✅ Total for LP ${lpId}: ${lpEnrollments.length} course enrollments`);
                totalCourseEnrollments += lpEnrollments.length;
                
                // Collect sample enrollments
                if (lpEnrollments.length > 0 && sampleEnrollments.length < 5) {
                    sampleEnrollments.push(...lpEnrollments.slice(0, Math.min(3, lpEnrollments.length)));
                }
                
            } catch (lpError) {
                console.log(`   ❌ Error for LP ${lpId}: ${lpError.message}`);
            }
        }
        
        console.log(`\n📊 PREVIEW SUMMARY:`);
        console.log(`   Learning Plans Tested: ${Math.min(5, learningPlans.length)}`);
        console.log(`   Total Course Enrollments Found: ${totalCourseEnrollments.toLocaleString()}`);
        
        // Step 3: Analyze sample enrollments
        console.log('\n🔍 STEP 3: Analyzing Sample Course Enrollments...');
        console.log('-'.repeat(50));
        
        if (sampleEnrollments.length > 0) {
            console.log(`Analyzing ${sampleEnrollments.length} sample enrollments...`);
            
            sampleEnrollments.slice(0, 3).forEach((enrollment, index) => {
                console.log(`\n📋 Sample Enrollment ${index + 1}:`);
                console.log(`   User ID: ${enrollment.user_id}`);
                console.log(`   Username: ${enrollment.username}`);
                console.log(`   Course UID: ${enrollment.uidCourse}`);
                console.log(`   Course Name: ${enrollment.name}`);
                console.log(`   Status: ${enrollment.status}`);
                console.log(`   Enrollment Date: ${enrollment.enroll_date_of_enrollment}`);
                console.log(`   Completion Date: ${enrollment.course_complete_date || 'Not completed'}`);
                console.log(`   Score: ${enrollment.score || 0}`);
                console.log(`   Total Time: ${enrollment.total_time || 0}`);
                console.log(`   Learning Plan ID: ${enrollment.learning_plan_id || 'N/A'}`);
            });
            
            // Analyze field availability
            console.log('\n📊 Field Analysis:');
            const fieldCounts = {};
            sampleEnrollments.forEach(enrollment => {
                Object.keys(enrollment).forEach(field => {
                    if (!fieldCounts[field]) fieldCounts[field] = 0;
                    if (enrollment[field] !== null && enrollment[field] !== undefined && enrollment[field] !== '') {
                        fieldCounts[field]++;
                    }
                });
            });
            
            const importantFields = [
                'user_id', 'uidCourse', 'status', 'enroll_date_of_enrollment', 
                'course_complete_date', 'score', 'total_time'
            ];
            
            importantFields.forEach(field => {
                const count = fieldCounts[field] || 0;
                const percentage = ((count / sampleEnrollments.length) * 100).toFixed(1);
                console.log(`   ${field}: ${count}/${sampleEnrollments.length} (${percentage}%)`);
            });
        }
        
        // Step 4: Estimate total scope
        console.log('\n📊 STEP 4: Estimating Total Scope...');
        console.log('-'.repeat(50));
        
        const avgEnrollmentsPerLP = totalCourseEnrollments / Math.min(5, learningPlans.length);
        const estimatedTotal = Math.round(avgEnrollmentsPerLP * learningPlans.length);
        
        console.log(`Average course enrollments per learning plan: ${avgEnrollmentsPerLP.toFixed(1)}`);
        console.log(`Total learning plans available: ${learningPlans.length}`);
        console.log(`🎯 ESTIMATED TOTAL LEARNING PLAN COURSE ENROLLMENTS: ${estimatedTotal.toLocaleString()}`);
        
        // Step 5: What would be synced to Salesforce
        console.log('\n💾 STEP 5: What Would Be Synced to Salesforce...');
        console.log('-'.repeat(50));
        
        console.log('Each course enrollment would create a Docebo_Learning_Plan_Course_Enrollment__c record with:');
        console.log('   ✅ Learning_Plan__c (lookup to Docebo_Learning_Plan__c)');
        console.log('   ✅ Course__c (lookup to Docebo_Course__c via uidCourse)');
        console.log('   ✅ User__c (lookup to Docebo_Users__c)');
        console.log('   ✅ Status__c (enrollment status)');
        console.log('   ✅ Enrollment_Date__c (when enrolled)');
        console.log('   ✅ Completion_Date__c (when completed, if applicable)');
        console.log('   ✅ Score__c (course score)');
        console.log('   ✅ Time_Spent__c (total time spent)');
        
        console.log('\n🔍 Current State:');
        console.log('   📊 Salesforce currently has: 2 Learning Plan Course Enrollment records');
        console.log(`   📊 Docebo has approximately: ${estimatedTotal.toLocaleString()} course enrollments`);
        console.log(`   📊 Gap to sync: ~${estimatedTotal.toLocaleString()} records`);
        
        return {
            success: true,
            learningPlansFound: learningPlans.length,
            sampleEnrollments: totalCourseEnrollments,
            estimatedTotal: estimatedTotal
        };
        
    } catch (error) {
        console.error('💥 Error previewing learning plan course enrollments:', error);
        return {
            success: false,
            error: error.message
        };
    }
}

// Execute the preview
console.log('🔄 Starting Learning Plan Course Enrollment Preview...');
previewLearningPlanCourseEnrollments()
    .then((result) => {
        console.log('\n📋 PREVIEW SUMMARY:');
        console.log('=' .repeat(50));
        
        if (result.success) {
            console.log(`✅ Preview completed successfully`);
            console.log(`📊 Learning Plans: ${result.learningPlansFound}`);
            console.log(`📊 Sample Enrollments: ${result.sampleEnrollments.toLocaleString()}`);
            console.log(`📊 Estimated Total: ${result.estimatedTotal.toLocaleString()}`);
            
            console.log('\n💡 NEXT STEPS:');
            console.log('1. 🔑 Fix Salesforce API password/credentials');
            console.log('2. 🚀 Run the complete sync script');
            console.log('3. 📊 Verify ~50,000+ Learning Plan Course Enrollment records are created');
            console.log('4. ✅ Set up webhooks for ongoing sync');
        } else {
            console.log(`❌ Preview failed: ${result.error}`);
        }
        
        console.log('\n✅ Learning plan course enrollment preview completed');
        process.exit(0);
    })
    .catch(err => {
        console.error('💥 Learning plan course enrollment preview failed:', err);
        process.exit(1);
    });
