require('dotenv').config();
const getConnection = require('./platform/salesforce/common/getConnection');

async function verifyWebhookCourse() {
    try {
        console.log('🔍 Verifying Course Created by Webhook');
        console.log('=' .repeat(50));
        
        const conn = await getConnection();
        if (!conn || !conn.accessToken) {
            console.error("❌ Invalid Salesforce connection");
            return;
        }

        // Check for the course created by webhook
        const course = await conn.sobject("Docebo_Course__c")
            .findOne({ Course_External_Id__c: 612 });
        
        if (course) {
            console.log('✅ WEBHOOK COURSE FOUND:');
            console.log(`   Course Name: "${course.Course_Name__c}"`);
            console.log(`   Course Category: "${course.Course_Category__c}"`);
            console.log(`   Course Category Code: "${course.Course_Category_Code__c}"`);
            console.log(`   Course Type: "${course.Course_Type__c}"`);
            console.log(`   Language: "${course.Language__c}"`);
            console.log(`   External ID: ${course.Course_External_Id__c}`);
            
            // Check if category data is properly mapped
            const hasCategoryData = course.Course_Category__c && 
                                  course.Course_Category__c !== "A" && 
                                  course.Course_Category__c !== "null" && 
                                  course.Course_Category__c !== "";
            
            if (hasCategoryData) {
                console.log('\n🎉 SUCCESS! Webhook course has real category data!');
                console.log(`   ✅ Category: "${course.Course_Category__c}"`);
            } else {
                console.log('\n⚠️ Webhook course category is empty or placeholder value');
            }
            
            return { success: true, course: course };
        } else {
            console.log('❌ Webhook course not found');
            return { success: false };
        }

    } catch (error) {
        console.error('💥 Error verifying webhook course:', error);
        return { success: false, error: error.message };
    }
}

// Execute verification
verifyWebhookCourse()
    .then((result) => {
        if (result.success) {
            console.log('\n✅ Webhook course verification completed successfully!');
        } else {
            console.log('\n❌ Webhook course verification failed');
        }
        process.exit(0);
    })
    .catch(err => {
        console.error('💥 Verification failed:', err);
        process.exit(1);
    });
