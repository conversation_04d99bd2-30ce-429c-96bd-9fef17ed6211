# 📚 Docebo Integration API Reference

## 🔗 Base URL
```
http://localhost:5000/webhook/docebo
```

## 🎯 Webhook Endpoints

### 1. User Management Webhook

#### `POST /user/manage`

**Description**: Handles user lifecycle events from Docebo

**Supported Events**:
- `user.created` - New user registration
- `user.updated` - User profile updates  
- `user.deleted` - User deactivation
- `user.selfregistrationrequest.approved` - Self-registration approval

**Request Headers**:
```
Content-Type: application/json
```

**Request Body**:
```json
{
  "message_id": "unique_message_identifier",
  "event": "user.created",
  "payloads": [
    {
      "user_id": 12345,
      "fired_at": "2024-01-01T10:00:00Z",
      "expiration_date": "2024-12-31T23:59:59Z",
      "update_date": "2024-01-01T10:00:00Z"
    }
  ]
}
```

**Alternative Single Payload Format**:
```json
{
  "message_id": "unique_message_identifier", 
  "event": "user.created",
  "payload": {
    "user_id": 12345,
    "fired_at": "2024-01-01T10:00:00Z",
    "expiration_date": "2024-12-31T23:59:59Z"
  }
}
```

**Response**:
```json
{
  "status": "success",
  "message": "New user created"
}
```

**Processing Logic**:
1. Prevents duplicate processing using `message_id`
2. Fetches complete user data from Docebo API
3. For `user.created`: Creates Lead + Docebo_Users__c records
4. For `user.updated`: Updates existing records or creates if not found
5. For `user.deleted`: Soft deletes user records
6. Handles both single and batch payloads

---

### 2. Session Management Webhook

#### `POST /session/manage`

**Description**: Handles ILT (Instructor-Led Training) session events

**Supported Events**:
- `ilt.session.created` - New session created
- `ilt.session.updated` - Session details updated
- `ilt.session.enrollment.created` - User enrolled in session
- `ilt.session.enrollment.updated` - Enrollment status updated
- `ilt.session.enrollment.deleted` - User unenrolled from session

**Request Body**:
```json
{
  "message_id": "session_message_id",
  "event": "ilt.session.created",
  "payloads": [
    {
      "session_id": 789,
      "course_id": 456,
      "user_id": 123,
      "fired_at": "2024-01-01T10:00:00Z"
    }
  ]
}
```

**Response**:
```json
{
  "status": "success",
  "message": "Webhook received, processing in background"
}
```

**Background Processing**: Uses queue-based processing to handle complex session operations

---

### 3. Learning Plan Management Webhook

#### `POST /lp/manage`

**Description**: Handles learning plan enrollment and completion events

**Supported Events**:
- `learningplan.enrollment.created` - User enrolled in learning plan
- `learningplan.enrollment.deleted` - User unenrolled from learning plan
- `learningplan.enrollment.completed` - Learning plan completed
- `badge.earned` - Badge awarded (placeholder)

**Request Body**:
```json
{
  "message_id": "lp_message_id",
  "event": "learningplan.enrollment.created",
  "payloads": [
    {
      "learning_plan_id": 101,
      "user_id": 123,
      "enrollment_date": "2024-01-01T10:00:00Z",
      "completion_date": null
    }
  ]
}
```

**Response**:
```json
{
  "status": "success", 
  "message": "Webhook received, processing in background"
}
```

---

### 4. Course Management Webhook

#### `POST /course/manage`

**Description**: Handles course lifecycle events

**Supported Events**:
- `course.created` - New course created
- `course.updated` - Course details updated
- `course.properties_changed` - Course properties modified
- `course.deleted` - Course deleted

**Request Body**:
```json
{
  "message_id": "course_message_id",
  "event": "course.created",
  "payloads": [
    {
      "course_id": 456,
      "fired_at": "2024-01-01T10:00:00Z"
    }
  ]
}
```

**Response**:
```json
{
  "status": "success",
  "message": "Course webhook received, processing in background"
}
```

**Processing Features**:
- Fetches complete course data including categories
- Handles soft deletion for deleted courses
- Maps course fields to Salesforce objects

---

### 5. Course Enrollment Webhooks

#### `POST /user/enrollment/created`

**Description**: Handles new course enrollments

**Request Body**:
```json
{
  "message_id": "enrollment_message_id",
  "payloads": [
    {
      "user_id": 123,
      "course_id": 456,
      "enrollment_date": "2024-01-01T10:00:00Z",
      "status": "subscribed",
      "completion_date": null,
      "score": 0,
      "total_time": 0
    }
  ]
}
```

**Response**:
```json
{
  "status": "success",
  "message": "Webhook received, processing in background"
}
```

**Special Features**:
- Automatic user creation if user doesn't exist in Salesforce
- Batch processing for multiple enrollments
- Comprehensive enrollment data mapping

#### `POST /user/enrollment/deleted`

**Description**: Handles course unenrollments

**Request Body**:
```json
{
  "message_id": "unenrollment_message_id",
  "payloads": [
    {
      "user_id": 123,
      "course_id": 456
    }
  ]
}
```

**Response**:
```json
{
  "status": "success",
  "message": "Course Enrolment deleted!"
}
```

#### `POST /user/enrollment/completed`

**Description**: Handles course completions

**Request Body**:
```json
{
  "message_id": "completion_message_id",
  "payload": {
    "user_id": 123,
    "course_id": 456,
    "completion_date": "2024-01-01T15:30:00Z",
    "score": 85,
    "total_time": 3600
  }
}
```

**Response**:
```json
{
  "status": "success",
  "message": "Webhook received, processing in background"
}
```

---

### 6. Instructor Assignment Webhook

#### `POST /session/instructor/assigned`

**Description**: Handles instructor assignments to course sessions

**Request Body**:
```json
{
  "message_id": "instructor_message_id",
  "payloads": [
    {
      "session_id": 789,
      "instructor_id": 456,
      "course_id": 123,
      "assigned_at": "2024-01-01T10:00:00Z"
    }
  ]
}
```

**Response**:
```json
{
  "status": "success",
  "message": "Instructor assignment webhook received, processing in background"
}
```

## 📊 API Endpoints

### 1. Get User Enrollments

#### `GET /user/:userId/enrollments`

**Description**: Retrieves all course enrollments for a specific user

**Path Parameters**:
- `userId` (required): The Docebo user ID

**Query Parameters**:
- `details` (optional): boolean - Include detailed course information (default: false)
- `format` (optional): string - Response format: `summary`, `basic`, `detailed` (default: summary)

**Example Requests**:
```bash
# Basic enrollments
GET /user/12345/enrollments

# Detailed enrollments  
GET /user/12345/enrollments?format=detailed&details=true

# Summary format
GET /user/12345/enrollments?format=summary
```

**Response Formats**:

**Summary Format**:
```json
{
  "success": true,
  "user_id": "12345",
  "format": "summary", 
  "total_enrollments": 5,
  "completed_courses": 3,
  "in_progress_courses": 2,
  "total_time_spent": 18000,
  "average_score": 78.5,
  "enrollments": [
    {
      "course_id": 456,
      "course_name": "Introduction to Leadership",
      "status": "completed",
      "completion_percentage": 100,
      "score": 85,
      "enrollment_date": "2024-01-01",
      "completion_date": "2024-01-15"
    }
  ]
}
```

**Detailed Format**:
```json
{
  "success": true,
  "user_id": "12345",
  "format": "detailed",
  "total_enrollments": 5,
  "enrollments": [
    {
      "enrollment_id": "456-12345",
      "course_id": 456,
      "course_name": "Introduction to Leadership",
      "course_description": "Comprehensive leadership training...",
      "course_type": "elearning",
      "course_duration": 120,
      "enrollment_date": "2024-01-01T10:00:00Z",
      "completion_date": "2024-01-15T14:30:00Z",
      "status": "completed",
      "completion_percentage": 100,
      "score": 85,
      "time_spent": 7200,
      "credits": 2,
      "certificate_earned": true
    }
  ]
}
```

**Error Responses**:
```json
{
  "success": false,
  "error": "User ID is required",
  "message": "Please provide a valid user ID in the URL path"
}
```

```json
{
  "success": false,
  "error": "User not found",
  "message": "No enrollments found for user ID: 12345"
}
```
