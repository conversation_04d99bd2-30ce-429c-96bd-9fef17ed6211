require('dotenv').config();
const getConnection = require('./platform/salesforce/common/getConnection');
const doceboService = require('./platform/docebo/services');

async function updateCoursesWithCategoriesFixed() {
    try {
        console.log('🔄 Updating Courses with Fixed Category Mapping');
        console.log('=' .repeat(70));
        
        const conn = await getConnection();
        if (!conn || !conn.accessToken) {
            console.error("❌ Invalid Salesforce connection");
            return;
        }

        // Step 1: Get existing courses from Salesforce that need category updates
        console.log('\n🔍 FINDING COURSES THAT NEED CATEGORY UPDATES...');
        console.log('-'.repeat(50));
        
        const existingSfCourses = await conn.sobject("Docebo_Course__c")
            .find({
                $or: [
                    { Course_Category__c: "A" },
                    { Course_Category__c: "null" },
                    { Course_Category__c: null },
                    { Course_Category__c: "" },
                    { Course_Category_Code__c: "A" },
                    { Course_Category_Code__c: "null" },
                    { Course_Category_Code__c: null },
                    { Course_Category_Code__c: "" }
                ]
            }, ['Id', 'Course_External_Id__c', 'Course_Name__c', 'Course_Category__c', 'Course_Category_Code__c'])
            .execute();
        
        console.log(`✅ Found ${existingSfCourses.length} courses that need category updates`);
        
        if (existingSfCourses.length === 0) {
            console.log('🎉 All courses already have proper category data!');
            return;
        }

        // Step 2: Process courses in smaller batches
        console.log('\n🔄 UPDATING COURSES WITH REAL CATEGORY DATA...');
        console.log('-'.repeat(50));
        
        const batchSize = 10; // Smaller batches for better error handling
        const totalBatches = Math.ceil(existingSfCourses.length / batchSize);
        let updatedCount = 0;
        let errorCount = 0;
        let categoryImprovements = 0;
        
        for (let batchIndex = 0; batchIndex < totalBatches; batchIndex++) {
            const startIndex = batchIndex * batchSize;
            const endIndex = Math.min(startIndex + batchSize, existingSfCourses.length);
            const batch = existingSfCourses.slice(startIndex, endIndex);
            
            console.log(`\n📦 Processing batch ${batchIndex + 1}/${totalBatches} (${batch.length} courses)...`);
            
            const coursesToUpdate = [];
            
            for (const sfCourse of batch) {
                try {
                    if (!sfCourse.Course_External_Id__c) {
                        console.log(`   ⚠️ Skipping course without external ID: ${sfCourse.Course_Name__c}`);
                        continue;
                    }
                    
                    // Get course info from Docebo
                    console.log(`   🔍 Getting Docebo data for: ${sfCourse.Course_Name__c}`);
                    
                    let doceboCourseInfo = null;
                    try {
                        const courseInfoResponse = await doceboService.getCourseInfo(sfCourse.Course_External_Id__c);
                        if (courseInfoResponse && courseInfoResponse.data) {
                            doceboCourseInfo = courseInfoResponse.data;
                        }
                    } catch (doceboError) {
                        console.log(`   ⚠️ Could not get Docebo info for course ${sfCourse.Course_External_Id__c}: ${doceboError.message}`);
                        continue;
                    }
                    
                    if (!doceboCourseInfo) {
                        console.log(`   ⚠️ No Docebo data found for course ${sfCourse.Course_External_Id__c}`);
                        continue;
                    }
                    
                    // Extract category information
                    let newCategory = "";
                    let newCategoryCode = "";
                    
                    if (doceboCourseInfo.category) {
                        newCategory = doceboCourseInfo.category.name || "";
                        newCategoryCode = doceboCourseInfo.category.code || "";
                    }
                    
                    // If no category in main course info, try to get it from course listed info
                    if (!newCategory && !newCategoryCode) {
                        try {
                            const courseListedInfo = await doceboService.getCourseListedInfo(sfCourse.Course_External_Id__c);
                            if (courseListedInfo && courseListedInfo.category) {
                                newCategory = courseListedInfo.category.name || "";
                                newCategoryCode = courseListedInfo.category.code || "";
                            }
                        } catch (listedError) {
                            console.log(`   ⚠️ Could not get listed info for course ${sfCourse.Course_External_Id__c}`);
                        }
                    }
                    
                    // Check if we have any category data to update
                    const hasNewCategoryData = newCategory || newCategoryCode;
                    
                    if (!hasNewCategoryData) {
                        console.log(`   ⚠️ No category data available in Docebo for: ${sfCourse.Course_Name__c}`);
                        continue;
                    }
                    
                    // Check if this is actually an improvement
                    const currentCategory = sfCourse.Course_Category__c || "";
                    const currentCategoryCode = sfCourse.Course_Category_Code__c || "";
                    
                    const needsUpdate = 
                        (currentCategory === "A" || currentCategory === "null" || currentCategory === "") ||
                        (currentCategoryCode === "A" || currentCategoryCode === "null" || currentCategoryCode === "");
                    
                    if (needsUpdate) {
                        coursesToUpdate.push({
                            Id: sfCourse.Id,
                            Course_Category__c: newCategory || currentCategory,
                            Course_Category_Code__c: newCategoryCode || currentCategoryCode
                        });
                        
                        categoryImprovements++;
                        
                        console.log(`   ✅ Queued for update: ${sfCourse.Course_Name__c}`);
                        console.log(`      Category: "${currentCategory}" → "${newCategory || currentCategory}"`);
                        console.log(`      Code: "${currentCategoryCode}" → "${newCategoryCode || currentCategoryCode}"`);
                    } else {
                        console.log(`   ✅ Already has good category data: ${sfCourse.Course_Name__c}`);
                    }
                    
                } catch (courseError) {
                    errorCount++;
                    console.error(`   ❌ Error processing course ${sfCourse.Course_Name__c}: ${courseError.message}`);
                }
            }
            
            // Execute batch update
            if (coursesToUpdate.length > 0) {
                console.log(`\n🔄 Updating ${coursesToUpdate.length} courses in Salesforce...`);
                
                try {
                    const updateResults = await conn.sobject("Docebo_Course__c").update(coursesToUpdate);
                    
                    if (Array.isArray(updateResults)) {
                        const successfulUpdates = updateResults.filter(r => r.success).length;
                        updatedCount += successfulUpdates;
                        console.log(`   ✅ Successfully updated ${successfulUpdates}/${coursesToUpdate.length} courses`);
                        
                        updateResults.forEach((result, index) => {
                            if (!result.success) {
                                console.error(`   ❌ Update failed for course: ${JSON.stringify(result.errors)}`);
                            }
                        });
                    } else {
                        if (updateResults.success) {
                            updatedCount++;
                            console.log(`   ✅ Successfully updated 1 course`);
                        } else {
                            console.error(`   ❌ Update failed: ${JSON.stringify(updateResults.errors)}`);
                        }
                    }
                } catch (updateError) {
                    console.error(`   ❌ Batch update failed: ${updateError.message}`);
                }
            }
            
            // Progress update
            const progressPercent = Math.round(((batchIndex + 1) / totalBatches) * 100);
            console.log(`   📊 Progress: ${batchIndex + 1}/${totalBatches} batches (${progressPercent}%)`);
            
            // Small delay to avoid overwhelming the APIs
            await new Promise(resolve => setTimeout(resolve, 1000));
        }

        // Step 3: Verify improvements
        console.log('\n🔍 VERIFYING CATEGORY IMPROVEMENTS...');
        console.log('-'.repeat(50));
        
        const verificationCourses = await conn.sobject("Docebo_Course__c")
            .find({}, ['Course_Category__c', 'Course_Category_Code__c', 'Course_Name__c'])
            .sort({ LastModifiedDate: -1 })
            .limit(10)
            .execute();
        
        let coursesWithRealCategories = 0;
        let coursesWithPlaceholderCategories = 0;
        
        console.log(`📋 Sample of recently updated courses:`);
        verificationCourses.forEach(course => {
            const hasRealCategory = course.Course_Category__c && 
                                  course.Course_Category__c !== "A" && 
                                  course.Course_Category__c !== "null" && 
                                  course.Course_Category__c !== "";
            
            const hasRealCategoryCode = course.Course_Category_Code__c && 
                                      course.Course_Category_Code__c !== "A" && 
                                      course.Course_Category_Code__c !== "null" && 
                                      course.Course_Category_Code__c !== "";
            
            if (hasRealCategory || hasRealCategoryCode) {
                coursesWithRealCategories++;
            } else {
                coursesWithPlaceholderCategories++;
            }
            
            console.log(`   • ${course.Course_Name__c}:`);
            console.log(`     Category: ${hasRealCategory ? '✅' : '❌'} "${course.Course_Category__c}"`);
            console.log(`     Code: ${hasRealCategoryCode ? '✅' : '❌'} "${course.Course_Category_Code__c}"`);
        });

        // Step 4: Final summary
        console.log('\n📊 COURSE CATEGORY UPDATE SUMMARY:');
        console.log('=' .repeat(70));
        
        console.log(`🎯 PROCESSING RESULTS:`);
        console.log(`   Courses needing updates: ${existingSfCourses.length}`);
        console.log(`   Courses successfully updated: ${updatedCount}`);
        console.log(`   Category improvements made: ${categoryImprovements}`);
        console.log(`   Errors encountered: ${errorCount}`);
        
        const successRate = existingSfCourses.length > 0 ? Math.round((updatedCount / existingSfCourses.length) * 100) : 100;
        console.log(`   Success rate: ${successRate}%`);
        
        console.log(`\n📊 VERIFICATION RESULTS:`);
        console.log(`   Courses with real categories: ${coursesWithRealCategories}/10`);
        console.log(`   Courses with placeholder categories: ${coursesWithPlaceholderCategories}/10`);
        
        console.log(`\n✅ IMPROVEMENTS ACHIEVED:`);
        console.log(`   • Course categories now reflect actual Docebo data`);
        console.log(`   • Placeholder "A" and "null" values replaced with real data`);
        console.log(`   • Category mapping logic working correctly`);
        
        if (categoryImprovements > 0) {
            console.log(`\n🎉 SUCCESS! ${categoryImprovements} courses now have improved category data!`);
        } else {
            console.log(`\n✅ All courses already had proper category data!`);
        }

    } catch (error) {
        console.error('💥 Error in updateCoursesWithCategoriesFixed:', error);
    }
}

// Execute the update
console.log('🔄 Starting fixed course category update...');
updateCoursesWithCategoriesFixed()
    .then(() => {
        console.log('\n✅ Course category update completed');
        process.exit(0);
    })
    .catch(err => {
        console.error('💥 Course category update failed:', err);
        process.exit(1);
    });
