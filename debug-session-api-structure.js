require('dotenv').config();
const getApiData = require("./common/docebo/fetcher");
const APP_BASE = process.env.DOCEBO_API_BASE_URL;

async function debugSessionAPIStructure() {
    try {
        console.log('🔍 DEBUGGING SESSION API STRUCTURE');
        console.log('=' .repeat(60));
        
        // Test different session-related API endpoints
        const endpoints = [
            '/course/v1/sessions/ilt',
            '/learn/v1/sessions',
            '/course/v1/sessions',
            '/learn/v1/sessions/ilt'
        ];
        
        for (const endpoint of endpoints) {
            console.log(`\n🧪 Testing endpoint: ${endpoint}`);
            console.log('-'.repeat(50));
            
            try {
                const response = await getApiData(
                    'GET', 
                    `${APP_BASE}${endpoint}?page=1&page_size=3`, 
                    null
                );
                
                if (response && response.status === 200) {
                    const items = response.data?.items || [];
                    console.log(`   ✅ Response received: ${items.length} items`);
                    console.log(`   📊 Total count: ${response.data?.count || 'N/A'}`);
                    console.log(`   📄 Has more data: ${response.data?.has_more_data}`);
                    
                    if (items.length > 0) {
                        const sample = items[0];
                        console.log(`   📋 Sample session structure:`);
                        console.log(`      Keys: ${Object.keys(sample).join(', ')}`);
                        
                        // Check for enrollment/attendance data
                        if (sample.enrollments) {
                            console.log(`      📊 Enrollments found: ${Array.isArray(sample.enrollments) ? sample.enrollments.length : 'Not array'}`);
                            if (Array.isArray(sample.enrollments) && sample.enrollments.length > 0) {
                                console.log(`      📋 Sample enrollment: ${Object.keys(sample.enrollments[0]).join(', ')}`);
                            }
                        } else {
                            console.log(`      ❌ No enrollments field found`);
                        }
                        
                        if (sample.attendance) {
                            console.log(`      📊 Attendance found: ${Array.isArray(sample.attendance) ? sample.attendance.length : 'Not array'}`);
                        } else {
                            console.log(`      ❌ No attendance field found`);
                        }
                        
                        // Show sample session details
                        console.log(`      📋 Session details:`);
                        console.log(`         ID: ${sample.id || sample.session_id}`);
                        console.log(`         Name: ${sample.name}`);
                        console.log(`         Course ID: ${sample.course_id}`);
                        console.log(`         Start Date: ${sample.start_date || sample.date_start}`);
                        console.log(`         End Date: ${sample.end_date || sample.date_end}`);
                    }
                } else {
                    console.log(`   ❌ No valid response`);
                }
            } catch (endpointError) {
                console.log(`   ❌ Error: ${endpointError.message}`);
            }
        }
        
        // Try to find session attendance specific endpoints
        console.log(`\n🔍 Testing session attendance specific endpoints...`);
        console.log('-'.repeat(50));
        
        const attendanceEndpoints = [
            '/learn/v1/sessions/attendance',
            '/course/v1/sessions/attendance',
            '/learn/v1/enrollments?session_id=1',
            '/course/v1/sessions/ilt/enrollments'
        ];
        
        for (const endpoint of attendanceEndpoints) {
            console.log(`\n🧪 Testing attendance endpoint: ${endpoint}`);
            
            try {
                const response = await getApiData(
                    'GET', 
                    `${APP_BASE}${endpoint}?page=1&page_size=3`, 
                    null
                );
                
                if (response && response.status === 200) {
                    const items = response.data?.items || [];
                    console.log(`   ✅ Response received: ${items.length} items`);
                    
                    if (items.length > 0) {
                        const sample = items[0];
                        console.log(`   📋 Sample attendance structure:`);
                        console.log(`      Keys: ${Object.keys(sample).join(', ')}`);
                        console.log(`      User ID: ${sample.user_id}`);
                        console.log(`      Session ID: ${sample.session_id}`);
                        console.log(`      Status: ${sample.status}`);
                    }
                } else {
                    console.log(`   ❌ No valid response`);
                }
            } catch (endpointError) {
                console.log(`   ❌ Error: ${endpointError.message}`);
            }
        }
        
        // Check if we can get session details with enrollments
        console.log(`\n🔍 Testing individual session details...`);
        console.log('-'.repeat(50));
        
        try {
            // First get a session ID
            const sessionsResponse = await getApiData(
                'GET', 
                `${APP_BASE}/course/v1/sessions/ilt?page=1&page_size=1`, 
                null
            );
            
            if (sessionsResponse && sessionsResponse.data?.items?.length > 0) {
                const sessionId = sessionsResponse.data.items[0].id || sessionsResponse.data.items[0].session_id;
                console.log(`🧪 Testing individual session details for session ${sessionId}`);
                
                const sessionDetailEndpoints = [
                    `/course/v1/sessions/ilt/${sessionId}`,
                    `/course/v1/sessions/${sessionId}`,
                    `/learn/v1/sessions/${sessionId}`,
                    `/course/v1/sessions/ilt/${sessionId}/enrollments`
                ];
                
                for (const endpoint of sessionDetailEndpoints) {
                    try {
                        const response = await getApiData('GET', `${APP_BASE}${endpoint}`, null);
                        
                        if (response && response.status === 200) {
                            console.log(`   ✅ ${endpoint}: Success`);
                            
                            if (response.data) {
                                const data = response.data;
                                console.log(`      Keys: ${Object.keys(data).join(', ')}`);
                                
                                if (data.enrollments) {
                                    console.log(`      📊 Enrollments: ${Array.isArray(data.enrollments) ? data.enrollments.length : 'Not array'}`);
                                }
                                if (data.items && Array.isArray(data.items)) {
                                    console.log(`      📊 Items: ${data.items.length}`);
                                    if (data.items.length > 0) {
                                        console.log(`      📋 Item keys: ${Object.keys(data.items[0]).join(', ')}`);
                                    }
                                }
                            }
                        } else {
                            console.log(`   ❌ ${endpoint}: No valid response`);
                        }
                    } catch (detailError) {
                        console.log(`   ❌ ${endpoint}: ${detailError.message}`);
                    }
                }
            }
        } catch (sessionError) {
            console.log(`   ❌ Could not get session for testing: ${sessionError.message}`);
        }

        return {
            success: true,
            message: 'API structure debugging completed'
        };

    } catch (error) {
        console.error('💥 Error in debug:', error);
        return {
            success: false,
            error: error.message
        };
    }
}

// Execute the debug
console.log('🔄 Starting Session API Structure Debug...');
debugSessionAPIStructure()
    .then((result) => {
        console.log('\n📋 DEBUG SUMMARY:');
        console.log('=' .repeat(50));
        
        if (result.success) {
            console.log(`✅ ${result.message}`);
            console.log('\n💡 RECOMMENDATIONS:');
            console.log('1. 🔍 Check which endpoint returned session attendance data');
            console.log('2. 📊 Verify the correct data structure for enrollments');
            console.log('3. 🔄 Update the bulk sync script with correct endpoint and structure');
        } else {
            console.log(`❌ Debug failed: ${result.error}`);
        }
        
        console.log('\n✅ Debug completed');
        process.exit(0);
    })
    .catch(err => {
        console.error('💥 Debug failed:', err);
        process.exit(1);
    });
