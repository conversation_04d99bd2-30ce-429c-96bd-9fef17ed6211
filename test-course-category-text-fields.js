require('dotenv').config();
const getConnection = require('./platform/salesforce/common/getConnection');
const { createNewCourse, tidyCourseData } = require('./platform/salesforce/courses/createCourse');

async function testCourseCategoryTextFields() {
    try {
        console.log('🧪 Testing Course Category with Text Fields');
        console.log('=' .repeat(70));
        
        const conn = await getConnection();
        if (!conn || !conn.accessToken) {
            console.error("❌ Invalid Salesforce connection");
            return;
        }

        // Step 1: Verify field types are now text
        console.log('\n🔍 VERIFYING FIELD TYPES IN SALESFORCE:');
        console.log('-'.repeat(50));
        
        const courseObjectDesc = await conn.sobject("Docebo_Course__c").describe();
        const categoryField = courseObjectDesc.fields.find(f => f.name === 'Course_Category__c');
        const categoryCodeField = courseObjectDesc.fields.find(f => f.name === 'Course_Category_Code__c');
        
        if (categoryField) {
            console.log(`✅ Course_Category__c:`);
            console.log(`   Type: ${categoryField.type}`);
            console.log(`   Required: ${!categoryField.nillable}`);
            console.log(`   Length: ${categoryField.length}`);
            
            if (categoryField.type === 'string' || categoryField.type === 'textarea') {
                console.log(`   🎉 SUCCESS! Field is now text - can accept any category value`);
            } else {
                console.log(`   ⚠️ WARNING! Field is still ${categoryField.type}`);
            }
        }
        
        if (categoryCodeField) {
            console.log(`\n✅ Course_Category_Code__c:`);
            console.log(`   Type: ${categoryCodeField.type}`);
            console.log(`   Required: ${!categoryCodeField.nillable}`);
            console.log(`   Length: ${categoryCodeField.length}`);
            
            if (categoryCodeField.type === 'string' || categoryCodeField.type === 'textarea') {
                console.log(`   🎉 SUCCESS! Field is now text - can accept any category code`);
            } else {
                console.log(`   ⚠️ WARNING! Field is still ${categoryCodeField.type}`);
            }
        }

        // Step 2: Test with comprehensive mock course data
        console.log('\n🔧 TESTING WITH COMPREHENSIVE CATEGORY DATA:');
        console.log('-'.repeat(50));
        
        const testCourses = [
            {
                id: "TEST-CAT-001",
                code: "LEAD-001",
                name: "Leadership Development Course",
                description: "<p>Advanced leadership training</p>",
                status: "published",
                type: "elearning",
                created_on: "2024-01-15T10:30:00Z",
                updated_on: "2024-12-01T15:30:00Z",
                time_options: {
                    duration: { days: 30 },
                    date_begin: "2024-01-15T10:30:00Z",
                    date_end: "2024-12-31T23:59:59Z"
                },
                language: { name: "English" },
                category: {
                    id: 42,
                    name: "Leadership & Management",
                    code: "LEAD-MGMT"
                },
                skills: ["Leadership", "Management"],
                slug_name: "leadership-development",
                thumbnail: { url: "https://example.com/leadership.jpg" },
                average_completion_time: 7200,
                credits: 3
            },
            {
                id: "TEST-CAT-002",
                code: "TECH-001",
                name: "Technical Skills Course",
                description: "<p>Technical training program</p>",
                status: "published",
                type: "classroom",
                created_on: "2024-02-01T09:00:00Z",
                updated_on: "2024-12-01T16:00:00Z",
                time_options: {
                    duration: { days: 14 },
                    date_begin: "2024-02-01T09:00:00Z",
                    date_end: "2024-12-15T17:00:00Z"
                },
                language: { name: "Spanish" },
                category: {
                    id: 58,
                    name: "Technology & Innovation",
                    code: "TECH-INNOV"
                },
                skills: ["Programming", "Innovation"],
                slug_name: "technical-skills",
                thumbnail: { url: "https://example.com/tech.jpg" },
                average_completion_time: 5400,
                credits: 2
            },
            {
                id: "TEST-CAT-003",
                code: "COMM-001",
                name: "Communication Excellence",
                description: "<p>Communication and presentation skills</p>",
                status: "published",
                type: "blended",
                created_on: "2024-03-01T11:00:00Z",
                updated_on: "2024-12-01T14:30:00Z",
                time_options: {
                    duration: { days: 21 },
                    date_begin: "2024-03-01T11:00:00Z",
                    date_end: "2024-12-22T18:00:00Z"
                },
                language: { name: "French" },
                category: {
                    id: 73,
                    name: "Communication & Soft Skills",
                    code: "COMM-SOFT"
                },
                skills: ["Communication", "Presentation"],
                slug_name: "communication-excellence",
                thumbnail: { url: "https://example.com/comm.jpg" },
                average_completion_time: 4800,
                credits: 1
            }
        ];

        // Step 3: Test tidyCourseData function with each test course
        console.log('\n📋 TESTING TIDYCOURSEDATA FUNCTION:');
        console.log('-'.repeat(50));
        
        testCourses.forEach((courseInfo, index) => {
            const mockCourseListedInfo = {
                actions: [{ id: 1 }, { id: 2 }],
                sessions_count: 2,
                is_deleted: false,
                removed_at: null,
                category: courseInfo.category
            };
            
            const processedData = tidyCourseData(courseInfo, mockCourseListedInfo);
            
            console.log(`\n📚 Test Course ${index + 1}: ${courseInfo.name}`);
            console.log(`   Course_Category__c: "${processedData.Course_Category__c}"`);
            console.log(`   Course_Category_Code__c: "${processedData.Course_Category_Code__c}"`);
            console.log(`   Course_Type__c: "${processedData.Course_Type__c}"`);
            console.log(`   Language__c: "${processedData.Language__c}"`);
            
            // Verify the category mapping is working
            const categorySuccess = processedData.Course_Category__c === courseInfo.category.name;
            const categoryCodeSuccess = processedData.Course_Category_Code__c === courseInfo.category.code;
            
            console.log(`   Category Mapping: ${categorySuccess ? '✅' : '❌'} ${categorySuccess ? 'SUCCESS' : 'FAILED'}`);
            console.log(`   Category Code Mapping: ${categoryCodeSuccess ? '✅' : '❌'} ${categoryCodeSuccess ? 'SUCCESS' : 'FAILED'}`);
        });

        // Step 4: Test actual course creation
        console.log('\n🔧 TESTING ACTUAL COURSE CREATION:');
        console.log('-'.repeat(50));
        
        const testCourse = testCourses[0]; // Use the first test course
        
        // Clean up any existing test course
        try {
            const existingCourse = await conn.sobject("Docebo_Course__c")
                .findOne({ Course_External_Id__c: testCourse.id });
            
            if (existingCourse) {
                await conn.sobject("Docebo_Course__c").delete(existingCourse.Id);
                console.log(`🗑️ Deleted existing test course: ${existingCourse.Id}`);
            }
        } catch (cleanupError) {
            console.log('No existing test course to clean up');
        }
        
        // Mock the doceboService.getCourseListedInfo function
        const originalDoceboService = require('./platform/docebo/services');
        const mockDoceboService = {
            ...originalDoceboService,
            getCourseListedInfo: async (courseId) => {
                if (courseId === testCourse.id) {
                    return {
                        actions: [{ id: 1 }, { id: 2 }],
                        sessions_count: 2,
                        is_deleted: false,
                        removed_at: null,
                        category: testCourse.category
                    };
                }
                return originalDoceboService.getCourseListedInfo(courseId);
            }
        };
        
        // Temporarily replace the service
        require.cache[require.resolve('./platform/docebo/services')] = {
            exports: mockDoceboService
        };
        
        try {
            console.log(`📚 Creating course: ${testCourse.name}`);
            const courseId = await createNewCourse(testCourse);
            
            if (courseId) {
                console.log(`✅ Course created successfully: ${courseId}`);
                
                // Verify the created course
                const createdCourse = await conn.sobject("Docebo_Course__c")
                    .findOne({ Id: courseId });
                
                if (createdCourse) {
                    console.log(`\n📋 CREATED COURSE VERIFICATION:`);
                    console.log('-'.repeat(50));
                    
                    const categorySuccess = createdCourse.Course_Category__c === testCourse.category.name;
                    const categoryCodeSuccess = createdCourse.Course_Category_Code__c === testCourse.category.code;
                    
                    console.log(`   Course Name: "${createdCourse.Course_Name__c}"`);
                    console.log(`   Course_Category__c: ${categorySuccess ? '✅' : '❌'} "${createdCourse.Course_Category__c}"`);
                    console.log(`   Course_Category_Code__c: ${categoryCodeSuccess ? '✅' : '❌'} "${createdCourse.Course_Category_Code__c}"`);
                    console.log(`   Course_Type__c: "${createdCourse.Course_Type__c}"`);
                    console.log(`   Language__c: "${createdCourse.Language__c}"`);
                    console.log(`   Type__c: "${createdCourse.Type__c}"`);
                    
                    if (categorySuccess && categoryCodeSuccess) {
                        console.log(`\n🎉 PERFECT! Course category mapping is working with text fields!`);
                    } else {
                        console.log(`\n❌ Issue with category mapping - checking values:`);
                        console.log(`   Expected Category: "${testCourse.category.name}"`);
                        console.log(`   Actual Category: "${createdCourse.Course_Category__c}"`);
                        console.log(`   Expected Code: "${testCourse.category.code}"`);
                        console.log(`   Actual Code: "${createdCourse.Course_Category_Code__c}"`);
                    }
                    
                } else {
                    console.log('❌ Created course not found');
                }
            } else {
                console.log('❌ Course creation failed');
            }
        } catch (createError) {
            console.error('❌ Error creating test course:', createError);
        }

        // Step 5: Check existing courses for improved category data
        console.log('\n🔍 CHECKING EXISTING COURSES FOR CATEGORY IMPROVEMENTS:');
        console.log('-'.repeat(50));
        
        const recentCourses = await conn.sobject("Docebo_Course__c")
            .find({}, ['Course_Category__c', 'Course_Category_Code__c', 'Course_Name__c', 'CreatedDate'])
            .sort({ CreatedDate: -1 })
            .limit(10)
            .execute();
        
        if (recentCourses.length > 0) {
            console.log('📋 Recent courses category data:');
            
            let coursesWithRealCategories = 0;
            let coursesWithPlaceholderCategories = 0;
            
            recentCourses.forEach(course => {
                const hasRealCategory = course.Course_Category__c && 
                                      course.Course_Category__c !== "A" && 
                                      course.Course_Category__c !== "null" && 
                                      course.Course_Category__c !== "";
                
                const hasRealCategoryCode = course.Course_Category_Code__c && 
                                          course.Course_Category_Code__c !== "A" && 
                                          course.Course_Category_Code__c !== "null" && 
                                          course.Course_Category_Code__c !== "";
                
                if (hasRealCategory || hasRealCategoryCode) {
                    coursesWithRealCategories++;
                } else {
                    coursesWithPlaceholderCategories++;
                }
                
                console.log(`   • ${course.Course_Name__c}:`);
                console.log(`     Category: ${hasRealCategory ? '✅' : '❌'} "${course.Course_Category__c}"`);
                console.log(`     Category Code: ${hasRealCategoryCode ? '✅' : '❌'} "${course.Course_Category_Code__c}"`);
            });
            
            console.log(`\n📊 CATEGORY DATA SUMMARY:`);
            console.log(`   Courses with real categories: ${coursesWithRealCategories}/${recentCourses.length}`);
            console.log(`   Courses with placeholder categories: ${coursesWithPlaceholderCategories}/${recentCourses.length}`);
            
            if (coursesWithRealCategories > 0) {
                console.log(`   🎉 SUCCESS! Some courses now have real category data!`);
            } else {
                console.log(`   ⚠️ All courses still have placeholder data - may need to re-sync`);
            }
        }

        // Step 6: Final summary
        console.log('\n📊 COURSE CATEGORY TEXT FIELDS SUMMARY:');
        console.log('=' .repeat(70));
        
        console.log('✅ FIELD TYPE CHANGES COMPLETED:');
        console.log('   • Course_Category__c: Changed from picklist to text');
        console.log('   • Course_Category_Code__c: Changed from picklist to text');
        
        console.log('\n✅ CODE IMPROVEMENTS WORKING:');
        console.log('   • tidyCourseData function maps real category data');
        console.log('   • createNewCourse function uses actual category values');
        console.log('   • Fallback logic still works for courses without categories');
        
        console.log('\n🚀 NEXT STEPS:');
        console.log('   • New courses will automatically capture real category data');
        console.log('   • Consider running historical data sync to update existing courses');
        console.log('   • Monitor webhook processing to ensure categories are captured');

    } catch (error) {
        console.error('💥 Error in course category text fields test:', error);
    }
}

// Execute the test
console.log('🔄 Starting course category text fields test...');
testCourseCategoryTextFields()
    .then(() => {
        console.log('\n✅ Test completed');
        process.exit(0);
    })
    .catch(err => {
        console.error('💥 Test failed:', err);
        process.exit(1);
    });
