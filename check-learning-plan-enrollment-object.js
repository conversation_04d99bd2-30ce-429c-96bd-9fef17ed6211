require('dotenv').config();
const getConnection = require('./platform/salesforce/common/getConnection');

async function checkLearningPlanEnrollmentObject() {
    try {
        console.log('🔍 CHECKING LEARNING PLAN ENROLLMENT OBJECT');
        console.log('=' .repeat(70));
        
        const conn = await getConnection();
        if (!conn || !conn.accessToken) {
            throw new Error('Failed to connect to Salesforce');
        }

        console.log('✅ Connected to Salesforce successfully');

        // Step 1: Check Docebo_Learning_Plan_Enrollment__c object
        console.log('\n📊 STEP 1: Checking Docebo_Learning_Plan_Enrollment__c Object...');
        console.log('-'.repeat(50));
        
        const lpEnrollmentMetadata = await conn.sobject("Docebo_Learning_Plan_Enrollment__c").describe();
        
        console.log(`Object Label: ${lpEnrollmentMetadata.label}`);
        console.log(`Object Name: ${lpEnrollmentMetadata.name}`);
        console.log(`Total Fields: ${lpEnrollmentMetadata.fields.length}`);
        
        console.log('\n📋 All Fields in Docebo_Learning_Plan_Enrollment__c:');
        lpEnrollmentMetadata.fields.forEach(field => {
            console.log(`   ${field.name} (${field.type}) - ${field.label}`);
        });

        // Step 2: Check relationship fields
        console.log('\n🔗 STEP 2: Checking Relationship Fields...');
        console.log('-'.repeat(50));
        
        const relationshipFields = lpEnrollmentMetadata.fields.filter(field => 
            field.type === 'reference'
        );
        
        console.log('Reference Fields:');
        relationshipFields.forEach(field => {
            console.log(`   ${field.name} (${field.type}) - ${field.label}`);
            if (field.referenceTo && field.referenceTo.length > 0) {
                console.log(`      → References: ${field.referenceTo.join(', ')}`);
            }
        });

        // Step 3: Get current count
        console.log('\n📊 STEP 3: Getting Current Count...');
        console.log('-'.repeat(50));
        
        const countResult = await conn.query("SELECT COUNT() FROM Docebo_Learning_Plan_Enrollment__c");
        console.log(`📊 Total Learning Plan Enrollments: ${countResult.totalSize.toLocaleString()}`);

        // Step 4: Get sample records if any exist
        console.log('\n📋 STEP 4: Getting Sample Records...');
        console.log('-'.repeat(50));
        
        const sampleRecords = await conn.sobject("Docebo_Learning_Plan_Enrollment__c")
            .find({})
            .limit(5)
            .execute();
            
        console.log(`Found ${sampleRecords.length} sample records`);
        
        if (sampleRecords.length > 0) {
            console.log('\n📋 Sample Learning Plan Enrollment Records:');
            sampleRecords.forEach((record, index) => {
                console.log(`\n   ${index + 1}. ${record.Name || record.Id}`);
                Object.keys(record).forEach(key => {
                    if (key !== 'attributes' && record[key] !== null && record[key] !== undefined) {
                        console.log(`      ${key}: ${record[key]}`);
                    }
                });
            });
        }

        // Step 5: Analysis
        console.log('\n🎯 STEP 5: Analysis...');
        console.log('-'.repeat(50));
        
        if (countResult.totalSize === 0) {
            console.log(`⚠️ CRITICAL ISSUE: No Learning Plan Enrollments exist!`);
            console.log(`💡 SOLUTION: Need to sync Learning Plan Enrollments first`);
            console.log(`📋 Required steps:`);
            console.log(`   1. Create Learning Plan Enrollments from Docebo`);
            console.log(`   2. Then create Learning Plan Course Enrollments`);
        } else {
            console.log(`✅ Found ${countResult.totalSize.toLocaleString()} Learning Plan Enrollments`);
        }

        return {
            success: true,
            totalRecords: countResult.totalSize,
            fields: lpEnrollmentMetadata.fields.map(f => ({ name: f.name, type: f.type, label: f.label }))
        };

    } catch (error) {
        console.error('💥 Error checking learning plan enrollment object:', error);
        return {
            success: false,
            error: error.message
        };
    }
}

console.log('🔄 Starting Learning Plan Enrollment Object Check...');
checkLearningPlanEnrollmentObject()
    .then((result) => {
        console.log('\n📋 LEARNING PLAN ENROLLMENT OBJECT CHECK SUMMARY:');
        console.log('=' .repeat(60));
        
        if (result.success) {
            console.log(`✅ Check completed successfully`);
            console.log(`📊 Total Records: ${result.totalRecords.toLocaleString()}`);
            console.log(`📋 Total Fields: ${result.fields.length}`);
            
            if (result.totalRecords === 0) {
                console.log(`\n❌ BLOCKING ISSUE: No Learning Plan Enrollments exist`);
                console.log(`🔧 NEXT STEP: Create Learning Plan Enrollment sync first`);
            } else {
                console.log(`\n✅ Learning Plan Enrollments exist - can proceed with Course Enrollments`);
            }
        } else {
            console.log(`❌ Check failed: ${result.error}`);
        }
        
        console.log('\n✅ Learning Plan Enrollment object check completed');
        process.exit(0);
    })
    .catch(err => {
        console.error('💥 Learning Plan Enrollment object check failed:', err);
        process.exit(1);
    });
