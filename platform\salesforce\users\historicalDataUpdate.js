const getConnection = require("../common/getConnection");
const { tidyData } = require("./createUser");

// Helper functions to map Docebo values to valid Salesforce picklist values
function mapGenderToValidValue(doceboGender) {
    const genderMap = {
        'Male': 'Man',
        'Female': 'Woman',
        'Man': 'Man',
        'Woman': 'Woman',
        'Non-Binary': 'Non-Binary or other gender identity',
        'Non-binary': 'Non-Binary or other gender identity',
        'Other': 'Prefer not to respond',
        'Prefer not to say': 'Prefer not to respond',
        'Prefer Not To Say': 'Prefer not to respond'
    };
    return genderMap[doceboGender] || 'Prefer not to respond';
}

function mapRaceToValidValue(doceboRace) {
    const raceMap = {
        'White': 'White',
        'Black': 'Black or African American',
        'Hispanic': 'Hispanic or Latine',
        'Asian': 'Asian',
        'Native American': 'American Indian or Alaskan Native',
        'Pacific Islander': 'Native Hawaiian or Other Pacific Islander',
        'Multi-Racial': 'Multi-Racial',
        'Other': 'Other'
    };
    return raceMap[doceboRace] || 'Other';
}

function mapRoleTypeToValidValue(doceboRole) {
    const roleMap = {
        'Administrative': 'Administrative',
        'Board': 'Board of Directors',
        'Communications': 'Communications',
        'Community': 'Community Engagement/Organizing',
        'Data': 'Data and Research',
        'Executive': 'Executive Director',
        'Facilitator': 'Facilitator',
        'Fundraising': 'Fundraising/Development',
        'Leadership': 'Leadership Table Member',
        'Operations': 'Operations/Business Management',
        'Partnership': 'Partnership Table Member',
        'Policy': 'Policy/Government',
        'Programs': 'Programs',
        'Youth': 'Youth/Families/Community'
    };
    return roleMap[doceboRole] || 'Other';
}

async function updateHistoricalData() {
    try {
        console.log('🔄 Starting Historical Data Update Process...');
        
        const conn = await getConnection();
        if (!conn || !conn.accessToken) {
            console.error("❌ Invalid Salesforce connection");
            return false;
        }

        // Step 1: Fetch all users from Docebo API
        console.log('\n📥 Fetching users from Docebo API...');
        const doceboUsers = await fetchAllDoceboUsers();
        
        if (!doceboUsers || doceboUsers.length === 0) {
            console.log('⚠️ No users found in Docebo API');
            return false;
        }

        console.log(`✅ Found ${doceboUsers.length} users in Docebo`);

        // Step 2: Process users in batches
        const batchSize = 50;
        let processedCount = 0;
        let updatedContacts = 0;
        let updatedLeads = 0;
        let createdLeads = 0;
        let updatedDoceboUsers = 0;
        let createdDoceboUsers = 0;

        for (let i = 0; i < doceboUsers.length; i += batchSize) {
            const batch = doceboUsers.slice(i, i + batchSize);
            console.log(`\n📦 Processing batch ${Math.floor(i / batchSize) + 1}/${Math.ceil(doceboUsers.length / batchSize)} (${batch.length} users)`);

            for (const doceboUser of batch) {
                try {
                    const result = await processUser(conn, doceboUser);
                    
                    if (result.contactUpdated) updatedContacts++;
                    if (result.leadUpdated) updatedLeads++;
                    if (result.leadCreated) createdLeads++;
                    if (result.doceboUserUpdated) updatedDoceboUsers++;
                    if (result.doceboUserCreated) createdDoceboUsers++;
                    
                    processedCount++;
                    
                    if (processedCount % 10 === 0) {
                        console.log(`   Processed ${processedCount}/${doceboUsers.length} users...`);
                    }
                    
                } catch (userError) {
                    console.error(`❌ Error processing user ${doceboUser.userInfo?.user_data?.user_id}:`, userError.message);
                }
            }
        }

        // Step 3: Summary
        console.log('\n📊 HISTORICAL DATA UPDATE SUMMARY:');
        console.log('=' .repeat(60));
        console.log(`📥 Total Docebo Users Processed: ${processedCount}`);
        console.log(`👥 Contacts Updated: ${updatedContacts}`);
        console.log(`🎯 Leads Updated: ${updatedLeads}`);
        console.log(`🆕 Leads Created: ${createdLeads}`);
        console.log(`📋 Docebo_Users__c Updated: ${updatedDoceboUsers}`);
        console.log(`🆕 Docebo_Users__c Created: ${createdDoceboUsers}`);
        console.log('\n✅ Historical data update completed successfully!');

        return true;

    } catch (error) {
        console.error('💥 Error in historical data update:', error);
        return false;
    }
}

async function processUser(conn, doceboUser) {
    const result = {
        contactUpdated: false,
        leadUpdated: false,
        leadCreated: false,
        doceboUserUpdated: false,
        doceboUserCreated: false
    };

    try {
        // Transform Docebo data using existing function
        const tmpUserInfo = tidyData(doceboUser.userInfo, doceboUser.userListedInfo);
        const email = tmpUserInfo.Email__c;

        if (!email) {
            console.log(`⚠️ Skipping user ${tmpUserInfo.User_Unique_Id__c} - no email`);
            return result;
        }

        // Step 1: Check if user exists as Contact
        const existingContacts = await conn.sobject("Contact")
            .find({ Email: email })
            .execute();

        if (existingContacts.length > 0) {
            // User exists as Contact - update it
            const contactId = existingContacts[0].Id;
            await updateContact(conn, contactId, tmpUserInfo);
            result.contactUpdated = true;
            console.log(`👥 Updated Contact: ${email}`);
        } else {
            // Step 2: Check if user exists as Lead
            const existingLeads = await conn.sobject("Lead")
                .find({ Email: email })
                .execute();

            if (existingLeads.length > 0) {
                // User exists as Lead - update it
                const leadId = existingLeads[0].Id;
                await updateLead(conn, leadId, tmpUserInfo);
                result.leadUpdated = true;
                console.log(`🎯 Updated Lead: ${email}`);
            } else {
                // Step 3: User doesn't exist - create new Lead
                await createNewLead(conn, tmpUserInfo);
                result.leadCreated = true;
                console.log(`🆕 Created Lead: ${email}`);
            }
        }

        // Step 4: Handle Docebo_Users__c record
        const userUniqueId = Number(tmpUserInfo.User_Unique_Id__c);
        const existingDoceboUsers = await conn.sobject("Docebo_Users__c")
            .find({ User_Unique_Id__c: userUniqueId })
            .execute();

        if (existingDoceboUsers.length > 0) {
            // Update existing Docebo_Users__c
            const doceboUserId = existingDoceboUsers[0].Id;
            tmpUserInfo.Id = doceboUserId;
            await conn.sobject("Docebo_Users__c").update(tmpUserInfo);
            result.doceboUserUpdated = true;
        } else {
            // Create new Docebo_Users__c
            await conn.sobject("Docebo_Users__c").create(tmpUserInfo);
            result.doceboUserCreated = true;
        }

    } catch (error) {
        console.error(`Error processing user ${doceboUser.userInfo?.user_data?.user_id}:`, error.message);
    }

    return result;
}

async function updateContact(conn, contactId, tmpUserInfo) {
    // DISABLED: Contact updates temporarily disabled to avoid field mapping errors
    console.log(`ℹ️ Historical: Contact update DISABLED for Contact ID: ${contactId}`);
    console.log(`ℹ️ Historical: Skipping Contact update to prevent First_Name__c errors`);
    return; // Exit early without updating Contact

    // COMMENTED OUT: Contact update logic
    /*
    const contactUpdateData = {
        Id: contactId,
        LastName: tmpUserInfo.Last_Name__c,    // Standard field
        FirstName: tmpUserInfo.First_Name__c,  // Standard field
        Email: tmpUserInfo.Email__c,           // Standard field
        Title: tmpUserInfo.Job_Title__c || "", // Standard field

        // FIX: Use ONLY approved Contact fields
        Created_by_Docebo_API__c: true,
        GenderIdentity: mapGenderToValidValue(tmpUserInfo.Gender_Identity__c),
        Role_Type__c: mapRoleTypeToValidValue(tmpUserInfo.Role_Type__c),
        Employment_Type__c: tmpUserInfo.Employment_Type__c || "",
        Race__c: mapRaceToValidValue(tmpUserInfo.Race_Identity__c),

        // Additional contact fields with correct names
        Description: `Docebo user - Level: ${tmpUserInfo.User_Level__c || 'N/A'}, Branch: ${tmpUserInfo.Branch_Name__c || 'N/A'}`,
        Fax: "",
        Phone: "",
        Languages__c: "",
        mailingcity__c: "", // FIX: Correct field name
        mailingcountry__c: "", // FIX: Correct field name
        mailingpostalcode__c: "", // FIX: Correct field name
        mailingstate__c: "", // FIX: Correct field name
        mailingstreet__c: "", // FIX: Correct field name
        Position_Role__c: tmpUserInfo.Role_Type__c || "",

        // FIX: Add missing required fields
        Annual_Revenue__c: tmpUserInfo.Annual_Revenue__c || 0,
        Company__c: tmpUserInfo.Organization_Name__c || "",
        Industry__c: tmpUserInfo.Industry__c || "Not For Profit",
        Initiative__c: tmpUserInfo.Initiative__c || "",
        LeadSource: "Docebo Platform",
        NumberOfEmployees__c: tmpUserInfo.NumberOfEmployees__c || 0,
        Organization_Employer__c: tmpUserInfo.Organization_Name__c || "",
        Rating__c: tmpUserInfo.Rating__c || "Warm",
        Time_Zone__c: tmpUserInfo.TimeZone__c || "",
        Type__c: "Backbone Staff",
        Website__c: tmpUserInfo.Organization_URL__c || "",
        Network_Partnership_Association__c: tmpUserInfo.Network_Partnership__c || "",

        Active_Portal_User__c: tmpUserInfo.Email_Validation_Status__c || false,
        FTE__c: "Full-Time",
        Gateway__c: "Docebo API",
        Inactive_Contact__c: !tmpUserInfo.Email_Validation_Status__c,
        Legacy_ID__c: String(tmpUserInfo.User_Unique_Id__c || ""), // FIX: Correct field name
        No_Longer_Leadership__c: false,
        No_Longer_Staff__c: false,
        Number_of_years_in_the_partnership__c: "0", // FIX: Correct field name
        Contact_Type__c: "Other"
    };

    await conn.sobject("Contact").update(contactUpdateData);
    */
}

async function updateLead(conn, leadId, tmpUserInfo) {
    const leadUpdateData = {
        Id: leadId,
        LastName: tmpUserInfo.Last_Name__c,
        FirstName: tmpUserInfo.First_Name__c,
        Email: tmpUserInfo.Email__c,
        Company: tmpUserInfo.Organization_Name__c || "-",
        Title: tmpUserInfo.Job_Title__c || "",
        Website: tmpUserInfo.Organization_URL__c || "",
        
        // All the comprehensive fields we established - FIXED FIELD NAMES
        Created_by_Docebo_API__c: true,
        GenderIdentity: mapGenderToValidValue(tmpUserInfo.Gender_Identity__c), // FIX: Use correct field name
        Role_Type__c: mapRoleTypeToValidValue(tmpUserInfo.Role_Type__c),
        Employment_Type__c: tmpUserInfo.Employment_Type__c || "",
        Race__c: mapRaceToValidValue(tmpUserInfo.Race_Identity__c),
        Description: `Docebo user - Level: ${tmpUserInfo.Level__c || 'N/A'}, Branch: ${tmpUserInfo.Branch_Name__c || 'N/A'}`,
        Fax: "",
        Salutation: "",
        Phone: "",
        Languages__c: "",
        mailingcity__c: "", // FIX: Correct field name
        mailingcountry__c: "", // FIX: Correct field name
        mailingpostalcode__c: "", // FIX: Correct field name
        mailingstate__c: "", // FIX: Correct field name
        mailingstreet__c: "", // FIX: Correct field name
        position_role__c: tmpUserInfo.Role_Type__c || "", // FIX: Correct field name

        // FIX: Add missing required fields
        accountid__c: tmpUserInfo.Account_ID__c || "",
        AnnualRevenue: tmpUserInfo.Annual_Revenue__c || 0, // Lead uses standard AnnualRevenue field
        Industry: tmpUserInfo.Industry__c || "Not For Profit",
        Initiative__c: tmpUserInfo.Initiative__c || "",
        NumberOfEmployees: tmpUserInfo.NumberOfEmployees__c || 0,
        Organization_employer__c: tmpUserInfo.Organization_Name__c || "",
        Rating: tmpUserInfo.Rating__c || "Warm",
        Time_Zone__c: tmpUserInfo.TimeZone__c || "",
        Type__c: "Backbone Staff",
        Network_Partnership_Association__c: tmpUserInfo.Network_Partnership__c || "",

        Active_Portal_User__c: tmpUserInfo.Email_Validation_Status__c || false,
        FTE__c: "Full-Time",
        Gateway__c: "Docebo API",
        Inactive_Contact__c: !tmpUserInfo.Email_Validation_Status__c,
        Legacy_ID__c: String(tmpUserInfo.User_Unique_Id__c || ""), // FIX: Correct field name
        No_Longer_Leadership__c: false,
        No_Longer_Staff__c: false,
        Number_of_years_in_the_partnership__c: "0", // FIX: Correct field name
        Contact_Type__c: "Other"
    };

    await conn.sobject("Lead").update(leadUpdateData);
}

async function createNewLead(conn, tmpUserInfo) {
    // Create Account first
    const accountData = {
        Name: tmpUserInfo.Full_Name__c + tmpUserInfo.User_Unique_Id__c,
        Website: tmpUserInfo.Organization_URL__c || "",
    };
    const accountResult = await conn.sobject("Account").create(accountData);
    
    if (accountResult.success) {
        tmpUserInfo.Account__c = accountResult.id;
        
        // Create Lead with comprehensive data
        const leadData = {
            LastName: tmpUserInfo.Last_Name__c && tmpUserInfo.Last_Name__c.trim() !== "" ? tmpUserInfo.Last_Name__c : "Unknown",
            FirstName: tmpUserInfo.First_Name__c,
            Email: tmpUserInfo.Email__c,
            Company: tmpUserInfo.Organization_Name__c || "-",
            Title: tmpUserInfo.Job_Title__c || "",
            Website: tmpUserInfo.Organization_URL__c || "",
            Status: "Open - Not Contacted",
            
            // All comprehensive fields - FIXED FIELD NAMES
            Created_by_Docebo_API__c: true,
            GenderIdentity: mapGenderToValidValue(tmpUserInfo.Gender_Identity__c), // FIX: Use correct field name
            Role_Type__c: mapRoleTypeToValidValue(tmpUserInfo.Role_Type__c),
            Employment_Type__c: tmpUserInfo.Employment_Type__c || "",
            Race__c: mapRaceToValidValue(tmpUserInfo.Race_Identity__c),
            Description: `Docebo user - Level: ${tmpUserInfo.Level__c || 'N/A'}, Branch: ${tmpUserInfo.Branch_Name__c || 'N/A'}`,
            Fax: "",
            Salutation: "",
            Phone: "",
            Languages__c: "",
            mailingcity__c: "", // FIX: Correct field name
            mailingcountry__c: "", // FIX: Correct field name
            mailingpostalcode__c: "", // FIX: Correct field name
            mailingstate__c: "", // FIX: Correct field name
            mailingstreet__c: "", // FIX: Correct field name
            position_role__c: tmpUserInfo.Role_Type__c || "", // FIX: Correct field name

            // FIX: Add missing required fields
            accountid__c: tmpUserInfo.Account_ID__c || "",
            AnnualRevenue: tmpUserInfo.Annual_Revenue__c || 0, // Lead uses standard AnnualRevenue field
            Industry: tmpUserInfo.Industry__c || "Not For Profit",
            Initiative__c: tmpUserInfo.Initiative__c || "",
            LeadSource: "Docebo Platform",
            NumberOfEmployees: tmpUserInfo.NumberOfEmployees__c || 0,
            Organization_employer__c: tmpUserInfo.Organization_Name__c || "",
            Rating: tmpUserInfo.Rating__c || "Warm",
            Time_Zone__c: tmpUserInfo.TimeZone__c || "",
            Type__c: "Backbone Staff",
            Network_Partnership_Association__c: tmpUserInfo.Network_Partnership__c || "",

            Active_Portal_User__c: tmpUserInfo.Email_Validation_Status__c || false,
            FTE__c: "Full-Time",
            Gateway__c: "Docebo API",
            Inactive_Contact__c: !tmpUserInfo.Email_Validation_Status__c,
            Legacy_ID__c: String(tmpUserInfo.User_Unique_Id__c || ""), // FIX: Correct field name
            No_Longer_Leadership__c: false,
            No_Longer_Staff__c: false,
            Number_of_years_in_the_partnership__c: "0", // FIX: Correct field name
            OwnerId: tmpUserInfo.OwnerId || "005O400000BxnnxIAB",
            Contact_Type__c: "Other"
        };

        await conn.sobject("Lead").create(leadData);
    }
}

// Import Docebo services
const doceboService = require("../../docebo/services");

async function fetchAllDoceboUsers() {
    try {
        console.log('📥 Fetching all users from Docebo API...');

        // Get all users from Docebo using existing service
        const allUsers = await doceboService.getTotalUserListedInfo();

        if (!allUsers || allUsers.length === 0) {
            console.log('⚠️ No users found in Docebo');
            return [];
        }

        console.log(`✅ Found ${allUsers.length} users in Docebo`);

        // For each user, get detailed info including additional fields
        const usersWithDetails = [];
        let processedCount = 0;

        for (const user of allUsers) {
            try {
                // Get detailed user info with additional fields
                const userInfo = await doceboService.getUserInfo(user.user_id);

                if (userInfo.status === 200) {
                    const userData = userInfo.data;
                    userData["fired_at"] = user.creation_date || null;
                    userData["expiration_date"] = user.expiration_date || null;

                    usersWithDetails.push({
                        userInfo: userData,
                        userListedInfo: user
                    });
                }

                processedCount++;

                if (processedCount % 50 === 0) {
                    console.log(`   Processed ${processedCount}/${allUsers.length} users...`);
                }

            } catch (userError) {
                console.error(`Error fetching details for user ${user.user_id}:`, userError.message);
            }
        }

        console.log(`✅ Successfully processed ${usersWithDetails.length} users with detailed info`);
        return usersWithDetails;

    } catch (error) {
        console.error('💥 Error fetching users from Docebo:', error);
        return [];
    }
}

module.exports = {
    updateHistoricalData,
    processUser,
    fetchAllDoceboUsers
};
