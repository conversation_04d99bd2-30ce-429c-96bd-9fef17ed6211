require('dotenv').config();

async function verifyContactOperationsDisabled() {
    try {
        console.log('🔍 Verifying Contact Operations Are Disabled');
        console.log('=' .repeat(70));
        console.log('🎯 CHECKING FOR:');
        console.log('   All Contact update operations should be disabled');
        console.log('   All Contact workaround calls should be commented out');
        console.log('   No active Contact field mapping errors should occur');
        console.log('=' .repeat(70));
        
        const fs = require('fs');
        const path = require('path');
        
        // Read the createUser.js file
        const createUserPath = './platform/salesforce/users/createUser.js';
        const fileContent = fs.readFileSync(createUserPath, 'utf8');
        const lines = fileContent.split('\n');
        
        console.log('\n📋 Step 1: Checking Contact update operations...');
        
        // Find all Contact update operations
        const contactUpdateLines = [];
        for (let i = 0; i < lines.length; i++) {
            const line = lines[i];
            if (line.includes('conn.sobject("Contact").update')) {
                contactUpdateLines.push({
                    lineNumber: i + 1,
                    content: line.trim(),
                    isCommented: isLineCommented(lines, i)
                });
            }
        }
        
        console.log(`📊 Found ${contactUpdateLines.length} Contact update operations:`);
        
        let activeContactUpdates = 0;
        for (const update of contactUpdateLines) {
            const status = update.isCommented ? '✅ DISABLED' : '❌ ACTIVE';
            console.log(`   Line ${update.lineNumber}: ${status}`);
            console.log(`      ${update.content}`);
            
            if (!update.isCommented) {
                activeContactUpdates++;
            }
        }
        
        console.log('\n📋 Step 2: Checking Contact workaround calls...');
        
        // Find all tryContactAssociationWorkaround calls
        const workaroundCallLines = [];
        for (let i = 0; i < lines.length; i++) {
            const line = lines[i];
            if (line.includes('tryContactAssociationWorkaround(') && line.includes('await')) {
                workaroundCallLines.push({
                    lineNumber: i + 1,
                    content: line.trim(),
                    isCommented: isLineCommented(lines, i)
                });
            }
        }
        
        console.log(`📊 Found ${workaroundCallLines.length} Contact workaround calls:`);
        
        let activeWorkaroundCalls = 0;
        for (const call of workaroundCallLines) {
            const status = call.isCommented ? '✅ DISABLED' : '❌ ACTIVE';
            console.log(`   Line ${call.lineNumber}: ${status}`);
            console.log(`      ${call.content}`);
            
            if (!call.isCommented) {
                activeWorkaroundCalls++;
            }
        }
        
        console.log('\n📋 Step 3: Checking updateExistingContactOrLead calls...');
        
        // Find all updateExistingContactOrLead calls
        const updateCallLines = [];
        for (let i = 0; i < lines.length; i++) {
            const line = lines[i];
            if (line.includes('updateExistingContactOrLead(') && line.includes('await')) {
                updateCallLines.push({
                    lineNumber: i + 1,
                    content: line.trim(),
                    isCommented: isLineCommented(lines, i)
                });
            }
        }
        
        console.log(`📊 Found ${updateCallLines.length} updateExistingContactOrLead calls:`);
        
        let activeUpdateCalls = 0;
        for (const call of updateCallLines) {
            const status = call.isCommented ? '✅ DISABLED' : '❌ ACTIVE';
            console.log(`   Line ${call.lineNumber}: ${status}`);
            console.log(`      ${call.content}`);
            
            if (!call.isCommented) {
                activeUpdateCalls++;
            }
        }
        
        console.log('\n📋 Step 4: Summary...');
        console.log('=' .repeat(50));
        
        const totalActiveOperations = activeContactUpdates + activeWorkaroundCalls + activeUpdateCalls;
        
        if (totalActiveOperations === 0) {
            console.log('🎉 ALL CONTACT OPERATIONS SUCCESSFULLY DISABLED!');
            console.log('✅ No active Contact update operations');
            console.log('✅ No active Contact workaround calls');
            console.log('✅ No active updateExistingContactOrLead calls');
            console.log('✅ No more "First_Name__c" errors should occur');
            
            console.log('\n💡 EXPECTED BEHAVIOR:');
            console.log('   • Docebo_Users__c records will be created successfully');
            console.log('   • No Contact associations will be attempted');
            console.log('   • No Lead associations will be attempted');
            console.log('   • No field mapping errors will occur');
            console.log('   • Integration will run error-free');
            
        } else {
            console.log('⚠️ SOME CONTACT OPERATIONS ARE STILL ACTIVE!');
            console.log(`❌ Active Contact updates: ${activeContactUpdates}`);
            console.log(`❌ Active workaround calls: ${activeWorkaroundCalls}`);
            console.log(`❌ Active update calls: ${activeUpdateCalls}`);
            console.log(`📊 Total active operations: ${totalActiveOperations}`);
            
            console.log('\n🔧 ACTION REQUIRED:');
            console.log('   These operations need to be disabled to prevent errors');
        }
        
        console.log('\n📊 VERIFICATION COMPLETE');
        console.log('=' .repeat(50));
        
        return {
            success: true,
            totalActiveOperations,
            activeContactUpdates,
            activeWorkaroundCalls,
            activeUpdateCalls,
            allDisabled: totalActiveOperations === 0
        };

    } catch (error) {
        console.error('💥 Error verifying Contact operations:', error);
        return {
            success: false,
            error: error.message
        };
    }
}

// Helper function to check if a line is commented out
function isLineCommented(lines, lineIndex) {
    // Check if the line itself starts with // or is inside /* */
    const line = lines[lineIndex].trim();
    
    // Check for single line comment
    if (line.startsWith('//')) {
        return true;
    }
    
    // Check for multi-line comment block
    // Look backwards to find opening /*
    let inCommentBlock = false;
    for (let i = lineIndex; i >= 0; i--) {
        const checkLine = lines[i];
        
        // If we find */ before /*, we're not in a comment block
        if (checkLine.includes('*/')) {
            break;
        }
        
        // If we find /*, we're in a comment block
        if (checkLine.includes('/*')) {
            inCommentBlock = true;
            break;
        }
    }
    
    return inCommentBlock;
}

// Execute the verification
console.log('🔄 Starting Contact operations verification...');
verifyContactOperationsDisabled()
    .then((result) => {
        if (result.success) {
            if (result.allDisabled) {
                console.log('\n🎉 VERIFICATION SUCCESSFUL!');
                console.log('🚀 All Contact operations are properly disabled');
                console.log('✅ No more Contact field mapping errors should occur');
            } else {
                console.log('\n⚠️ VERIFICATION FOUND ISSUES');
                console.log('🔧 Some Contact operations are still active and need to be disabled');
            }
        } else {
            console.log('\n❌ Verification failed. See error details above.');
        }
        process.exit(0);
    })
    .catch(err => {
        console.error('💥 Verification failed:', err);
        process.exit(1);
    });
