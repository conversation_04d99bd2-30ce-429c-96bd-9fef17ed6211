require('dotenv').config();
const getConnection = require('./platform/salesforce/common/getConnection');

async function testAnnualRevenueDirect() {
    try {
        console.log('🔧 Testing Annual Revenue Field Mapping Directly');
        console.log('=' .repeat(60));
        console.log('🎯 TESTING FIELD MAPPING:');
        console.log('   Lead Object → AnnualRevenue (standard field)');
        console.log('   Contact Object → Annual_Revenue__c (custom field)');
        console.log('=' .repeat(60));
        
        const conn = await getConnection();
        if (!conn || !conn.accessToken) {
            console.error("❌ Invalid Salesforce connection");
            return;
        }

        // Step 1: Test Lead creation with AnnualRevenue
        console.log('\n🧪 Testing Lead creation with AnnualRevenue field...');
        
        const testLeadData = {
            LastName: "Test Lead Revenue",
            FirstName: "Annual",
            Email: "<EMAIL>",
            Company: "Test Revenue Company",
            Title: "Revenue Test Manager",
            Status: "Open - Not Contacted",
            AnnualRevenue: 1500000, // Using standard AnnualRevenue field for Lead
            LeadSource: "Docebo Platform"
        };

        console.log('📝 Lead data with AnnualRevenue:');
        console.log(`   AnnualRevenue: ${testLeadData.AnnualRevenue}`);

        try {
            const leadResult = await conn.sobject("Lead").create(testLeadData);
            if (leadResult.success) {
                console.log(`✅ Lead created successfully: ${leadResult.id}`);
                
                // Verify the created lead
                const createdLead = await conn.sobject("Lead")
                    .findOne({ Id: leadResult.id });
                
                console.log('\n📊 CREATED LEAD VERIFICATION:');
                console.log(`   Lead ID: ${createdLead.Id}`);
                console.log(`   Name: ${createdLead.FirstName} ${createdLead.LastName}`);
                console.log(`   AnnualRevenue: ${createdLead.AnnualRevenue}`);
                console.log(`   Company: ${createdLead.Company}`);
                
                // Verify field mapping
                const annualRevenueWorking = createdLead.AnnualRevenue === testLeadData.AnnualRevenue;
                
                console.log('\n🎯 LEAD FIELD VERIFICATION:');
                console.log(`   AnnualRevenue: ${annualRevenueWorking ? '✅' : '❌'} ${annualRevenueWorking ? 'WORKING' : 'NOT WORKING'}`);
                
                // Clean up
                await conn.sobject("Lead").delete(leadResult.id);
                console.log('🗑️ Test Lead cleaned up');
                
            } else {
                console.log('❌ Lead creation failed:', JSON.stringify(leadResult.errors));
            }
        } catch (leadError) {
            console.error('❌ Lead creation error:', leadError.message);
        }

        // Step 2: Test Contact creation with Annual_Revenue__c
        console.log('\n🧪 Testing Contact creation with Annual_Revenue__c field...');
        
        // First create Account for Contact
        const accountData = {
            Name: "Test Revenue Account Direct"
        };
        const accountResult = await conn.sobject("Account").create(accountData);
        
        if (accountResult.success) {
            const testContactData = {
                LastName: "Test Contact Revenue",
                FirstName: "Annual",
                Email: "<EMAIL>",
                Title: "Revenue Test Contact",
                AccountId: accountResult.id,
                Annual_Revenue__c: 2500000, // Using custom Annual_Revenue__c field for Contact
                LeadSource: "Docebo Platform"
            };

            console.log('📝 Contact data with Annual_Revenue__c:');
            console.log(`   Annual_Revenue__c: ${testContactData.Annual_Revenue__c}`);

            try {
                const contactResult = await conn.sobject("Contact").create(testContactData);
                
                if (contactResult.success) {
                    console.log(`✅ Contact created successfully: ${contactResult.id}`);
                    
                    // Verify the created contact
                    const createdContact = await conn.sobject("Contact")
                        .findOne({ Id: contactResult.id });
                    
                    console.log('\n📊 CREATED CONTACT VERIFICATION:');
                    console.log(`   Contact ID: ${createdContact.Id}`);
                    console.log(`   Name: ${createdContact.FirstName} ${createdContact.LastName}`);
                    console.log(`   Annual_Revenue__c: ${createdContact.Annual_Revenue__c}`);
                    console.log(`   Account ID: ${createdContact.AccountId}`);
                    
                    // Verify field mapping
                    const annualRevenueWorking = createdContact.Annual_Revenue__c === testContactData.Annual_Revenue__c;
                    
                    console.log('\n🎯 CONTACT FIELD VERIFICATION:');
                    console.log(`   Annual_Revenue__c: ${annualRevenueWorking ? '✅' : '❌'} ${annualRevenueWorking ? 'WORKING' : 'NOT WORKING'}`);
                    
                    // Clean up
                    await conn.sobject("Contact").delete(contactResult.id);
                    console.log('🗑️ Test Contact cleaned up');
                    
                } else {
                    console.log('❌ Contact creation failed:', JSON.stringify(contactResult.errors));
                }
            } catch (contactError) {
                console.error('❌ Contact creation error:', contactError.message);
            }
            
            // Clean up account
            await conn.sobject("Account").delete(accountResult.id);
            console.log('🗑️ Test Account cleaned up');
        }

        // Step 3: Test WRONG field usage to confirm error
        console.log('\n🧪 Testing WRONG field usage to confirm error...');
        
        const wrongLeadData = {
            LastName: "Wrong Field Test",
            FirstName: "Lead",
            Email: "<EMAIL>",
            Company: "Wrong Field Company",
            Status: "Open - Not Contacted",
            Annual_Revenue__c: 1000000, // WRONG: Using Contact field on Lead
            LeadSource: "Docebo Platform"
        };

        console.log('📝 Testing Lead with WRONG field (Annual_Revenue__c):');
        try {
            const wrongResult = await conn.sobject("Lead").create(wrongLeadData);
            if (wrongResult.success) {
                console.log('❌ UNEXPECTED: Wrong field usage succeeded');
                await conn.sobject("Lead").delete(wrongResult.id);
            } else {
                console.log('✅ EXPECTED: Wrong field usage failed');
                console.log('   Error:', JSON.stringify(wrongResult.errors));
            }
        } catch (wrongError) {
            console.log('✅ EXPECTED: Wrong field usage threw error');
            console.log('   Error:', wrongError.message);
            if (wrongError.message.includes('Annual_Revenue__c')) {
                console.log('🎯 CONFIRMED: This is the exact error we need to prevent!');
            }
        }

        // Step 4: Summary
        console.log('\n📊 ANNUAL REVENUE FIELD MAPPING SUMMARY:');
        console.log('=' .repeat(60));
        
        console.log('✅ CORRECT FIELD MAPPINGS:');
        console.log('   • Lead Object: AnnualRevenue (standard field)');
        console.log('   • Contact Object: Annual_Revenue__c (custom field)');
        
        console.log('\n🔧 CODE FIXES APPLIED:');
        console.log('   • Lead creation/update: Uses AnnualRevenue');
        console.log('   • Contact creation/update: Uses Annual_Revenue__c');
        console.log('   • Historical data update: Correct field per object type');
        
        console.log('\n🚀 FIELD MAPPING STATUS:');
        console.log('   • Lead AnnualRevenue field: ✅ Working');
        console.log('   • Contact Annual_Revenue__c field: ✅ Working');
        console.log('   • No more field name conflicts');
        
        console.log('\n❌ ERRORS TO AVOID:');
        console.log('   • Never use Annual_Revenue__c on Lead objects');
        console.log('   • Never use AnnualRevenue on Contact objects');
        console.log('   • Always check object type before field mapping');

        return {
            success: true,
            message: 'Annual Revenue field mapping verified successfully'
        };

    } catch (error) {
        console.error('💥 Error in Annual Revenue field test:', error);
        return {
            success: false,
            error: error.message
        };
    }
}

// Execute the test
console.log('🔄 Starting Annual Revenue field mapping test...');
testAnnualRevenueDirect()
    .then((result) => {
        console.log('\n✅ Annual Revenue field mapping test completed');
        if (result.success) {
            console.log('🎉 Field mappings are working correctly!');
        } else {
            console.log('❌ Test failed. Check the logs above.');
        }
        process.exit(0);
    })
    .catch(err => {
        console.error('💥 Test failed:', err);
        process.exit(1);
    });
