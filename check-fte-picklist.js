require('dotenv').config();
const getConnection = require('./platform/salesforce/common/getConnection');

async function checkFTEPicklist() {
    try {
        console.log('🔍 Checking FTE__c picklist values...');
        
        const conn = await getConnection();
        if (!conn || !conn.accessToken) {
            console.error("❌ Invalid Salesforce connection");
            return;
        }

        const leadDescription = await conn.sobject("Lead").describe();
        const fteField = leadDescription.fields.find(f => f.name === 'FTE__c');
        
        if (fteField) {
            console.log(`\n📋 FTE__c Field Details:`);
            console.log(`   Type: ${fteField.type}`);
            console.log(`   Label: ${fteField.label}`);
            
            if (fteField.type === 'picklist' && fteField.picklistValues) {
                console.log(`\n🔧 Valid FTE__c Values:`);
                fteField.picklistValues.forEach((value, index) => {
                    const status = value.active ? '✅' : '❌';
                    const defaultFlag = value.defaultValue ? ' (default)' : '';
                    console.log(`   ${index + 1}. "${value.value}"${defaultFlag} ${status}`);
                });
            } else {
                console.log(`   Field type: ${fteField.type} (not a picklist)`);
            }
        } else {
            console.log('❌ FTE__c field not found');
        }

    } catch (error) {
        console.error('💥 Error checking FTE field:', error);
    }
}

checkFTEPicklist()
    .then(() => {
        console.log('\n✅ FTE check completed');
        process.exit(0);
    })
    .catch(err => {
        console.error('💥 FTE check failed:', err);
        process.exit(1);
    });
